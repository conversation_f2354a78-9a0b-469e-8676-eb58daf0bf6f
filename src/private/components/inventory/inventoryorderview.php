﻿<!DOCTYPE html>
<html>
<?php

require_once CONN;

$shopid = $_COOKIE["shopid"];
$partSupplier = isset($_REQUEST['supplier']) ? filter_var($_REQUEST['supplier'], FILTER_SANITIZE_STRING) : "";
$sub = isset($_POST['sub']) ? filter_var($_POST['sub'], FILTER_SANITIZE_STRING) : "";
$poNumber = isset($_REQUEST['ponumber']) ? filter_var($_REQUEST['ponumber'], FILTER_SANITIZE_STRING) : "";
$poid = isset($_REQUEST['id']) ? filter_var($_REQUEST['id'], FILTER_SANITIZE_STRING) : 0;
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

$showtaxonro = 'no';
$sstmt = "SELECT LCASE(showtaxonpo) FROM settings where shopid = ?";
if ($query = $conn->prepare($sstmt)){
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($showtaxonro);
    $query->fetch();
    $query->close();
}
?>

<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">
     <title><?= getPageTitle() ?></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon' />
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->
    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        .col-md-6 {
            border: 1px black solid
        }

        .col-md-8 {
            border: 1px black solid
        }

        .col-md-4 {
            border: 1px black solid
        }
    </style>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    <div id="mainalert" style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large" class="alert alert-success"></div>
    <div id="header"></div>
    <!-- Page Container -->
    <!--
    Available Classes:

    'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

    'sidebar-l'                  Left Sidebar and right Side Overlay
    'sidebar-r'                  Right Sidebar and left Side Overlay
    'sidebar-mini'               Mini hoverable Sidebar (> 991px)
    'sidebar-o'                  Visible Sidebar by default (> 991px)
    'sidebar-o-xs'               Visible Sidebar by default (< 992px)

    'side-overlay-hover'         Hoverable Side Overlay (> 991px)
    'side-overlay-o'             Visible Side Overlay by default (> 991px)

    'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

    'header-navbar-fixed'        Enables fixed header
-->
    <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">

        <!-- Sidebar -->
        <nav id="sidebar">
            <!-- Sidebar Scroll Container -->
            <div id="sidebar-scroll">
                <!-- Sidebar Content -->
                <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                <div class="sidebar-content">
                    <!-- Side Header -->
                    <div class="side-header side-content bg-white-op">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                            <i class="fa fa-times"></i>
                        </button>
                        <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                            <i class="text-primary">
                                <?php getLogo() ?></i>
                            <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                        </a>
                    </div>
                    <!-- END Side Header -->

                    <!-- Side Content -->
                    <div class="side-content-sbp-ro side-content">
                        <ul class="nav-main">
                            <li>
                                <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Work In Process</span></a>
                            </li>
                            <li>
                                <a href="inventory.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Inventory List</span></a>
                            </li>
                            
                            <?php if (!$shopIsReadOnly): ?>
                                <li>
                                    <a href="addinventory.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Add New Part</span></a>
                                </li>
                            <?php endif; ?>

                            <li>
                                <a href="inventoryorders.php"><i class="fa fa-calendar-check-o"></i><span class="sidebar-mini-hide">Manage PO's</span></a>
                            </li>
                            <li style="display:none">
                                <a href="#"><i class="fa fa-calculator"></i><span class="sidebar-mini-hide">Receive Inventory Order to Specific RO</span></a>
                            </li>
                            <li>
                                <a onclick="showAll()" href="#"><i class="fa fa-calculator"></i><span class="sidebar-mini-hide">Show All Inventory</span></a>
                            </li>
                            <li>
                                <a href="inventoryextract.php"><i class="fa fa-calculator"></i><span class="sidebar-mini-hide">Download Inventory</span></a>
                            </li>

                        </ul>
                    </div>
                    <!-- END Side Content -->
                </div>
                <!-- Sidebar Content -->
            </div>
            <!-- END Sidebar Scroll Container -->
        </nav>
        <!-- END Sidebar -->

        <!-- Header -->
        <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar" class="content-mini content-mini-full">

            <!-- Header Navigation Right -->
            View PO
            <!-- END Header Navigation Right -->

            <!-- Header Navigation Left -->

            <ul class="nav-header pull-left">
                <li class="hidden-md hidden-lg">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                        <i class="fa fa-navicon"></i>
                    </button>
                </li>
                <li class="hidden-xs hidden-sm">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                        <i class="fa fa-bars"></i>
                    </button>
                </li>
                <li>
                    <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                    <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                        <i class="si si-grid"></i>
                    </button>
                </li>
                <li class="visible-xs">
                    <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                    <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                        <i class="fa fa-search"></i>
                    </button>
                </li>
                <li>

                </li>
            </ul>

            <!-- END Header Navigation Left -->
        </header>
        <!-- END Header -->
        <!-- Main Container -->

        <!-- Main Container -->
        <main class="container-fluid" id="main-container" style="display:block;">
            <br><br>

            <?php
            $stmt = "select * from po where shopid = ? and id = ?";
            $poNumber = 0;
            $partSupplier = "";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $poid);
                $query->execute();
                $results = $query->get_result();
                $rs = $results->fetch_assoc();
                $poNumber = $rs["ponumber"];
                $partSupplier = $rs["issuedto"];
                $query->close();
            } else {
                echo "po Prepare Failed: (" . $conn->errno . ") " . $conn->error;
            }
            ?>

            <table class="table table-condensed table-striped">
                <tr>
                    <td class="style1"><strong>PO Number</strong></td>
                    <td class="style7"><strong><?php echo $rs['ponumber']; ?></strong>&nbsp;
                    </td>
                    <td class="style1"><strong>Issue Date</strong></td>
                    <td><?php echo $rs['issuedate']; ?>&nbsp;</td>
                </tr>
                <tr>
                    <td class="style1"><strong>Status</strong></td>
                    <td><?php echo $rs['status']; ?>&nbsp;</td>
                    <td class="style1"><strong>Issued To</strong>&nbsp;</td>
                    <td>
                        <div class="ui-widget">

                            <?php echo $rs['issuedto']; ?>

                        </div>

                    </td>
                </tr>
                <tr>
                    <td class="style1"><strong>Comments</strong></td>
                    <td>
                        <?php echo $rs['desc']; ?>
                    </td>
                    <td class="style1"><strong>Invoice #</strong></td>
                    <td>
                        <?php echo $rs['invnum']; ?>
                    </td>
                </tr>
                <tr>
                    <td colspan="4" class="style3">
                        <input class="btn btn-default" onclick="location.href='inventoryorders.php'" name="Button2" type="button" value="Done" />
                    </td>
                </tr>
            </table>
            <?php
            $stmt = "select * from partsinventoryorder where shopid = ? and ponumber = ?  order by PartDesc,PartNumber";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $poNumber);
                $query->execute();
                $results = $query->get_result();
            ?>
                <table id="partslist" class="table table-condensed table-striped table-header-bg">
                    <thead>
                        <tr>

                            <td class="style5" style="height: 29px"><strong>Part #</strong></td>
                            <td class="style5" style="height: 29px"><strong>Description</strong></td>
                            <td class="style5" style="height: 29px"><strong>Cost</strong></td>
                             <?php if ($showtaxonro == "yes"){ ?>
                                 <td class="style5" style="height: 29px"><strong>Tax</strong></td>
                             <?php } ?>
                            <td class="style5" style="height: 29px"><strong>Supplier</strong></td>
                            <td class="style5" style="height: 29px"><strong>Net On Hand</strong></td>
                        </tr>
                    </thead>
                    <?php
                    while ($rs = $results->fetch_assoc()) {
                    ?>

                        <tr>
                            <td><?php echo $rs['partnumber']; ?></td>
                            <td><?php echo $rs['partdesc']; ?>&nbsp;</td>
                            <td>
                                <?php echo asDollars($rs['partcost']); ?>
                            </td>
                            <?php if ($showtaxonro == "yes"){ ?>
                                <td><?= asDollars($rs['salestax']); ?></td>
                            <?php } ?>
                            <td><?php echo $rs['partsupplier']; ?>&nbsp;</td>
                            <td><?php echo $rs['netonhandatorder']; ?>&nbsp;</td>
                        </tr>
                <?php
                    }

                    $query->close();
                } else {
                    echo "partsinventory Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                }
                ?>

                </table>
                </form>
        </main>
        <!-- END Main Container -->

        <!-- Footer -->
        <!-- END Footer -->
    </div>
    <!-- END Page Container -->
    <!-- Modals -->

    <script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
    <script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
    <script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

    <!-- Page Plugins -->

    <!-- Page JS Code
<script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->

    <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>

</html>

<?php
mysqli_close($conn);
?>