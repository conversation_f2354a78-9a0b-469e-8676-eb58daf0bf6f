﻿<!DOCTYPE html>
<html>
<?php
require_once CONN;

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">
     <title><?= getPageTitle() ?></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon' />
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        .col-md-6 {
            border: 1px black solid
        }

        .col-md-8 {
            border: 1px black solid
        }

        .col-md-4 {
            border: 1px black solid
        }

        #hider {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 120%;
            background-color: gray;
            -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=70)";
            filter: alpha(opacity=70);
            -moz-opacity: .70;
            opacity: .7;
            z-index: 9998;
            display: none;

        }
    </style>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    <div id="mainalert" style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large" class="alert alert-success"></div>
    <div id="header"></div>
    <!-- Page Container -->
    <!--
    Available Classes:

    'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

    'sidebar-l'                  Left Sidebar and right Side Overlay
    'sidebar-r'                  Right Sidebar and left Side Overlay
    'sidebar-mini'               Mini hoverable Sidebar (> 991px)
    'sidebar-o'                  Visible Sidebar by default (> 991px)
    'sidebar-o-xs'               Visible Sidebar by default (< 992px)

    'side-overlay-hover'         Hoverable Side Overlay (> 991px)
    'side-overlay-o'             Visible Side Overlay by default (> 991px)

    'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

    'header-navbar-fixed'        Enables fixed header
-->
    <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">

        <!-- Sidebar -->
        <nav id="sidebar">
            <!-- Sidebar Scroll Container -->
            <div id="sidebar-scroll">
                <!-- Sidebar Content -->
                <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                <div class="sidebar-content">
                    <!-- Side Header -->
                    <div class="side-header side-content bg-white-op">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                            <i class="fa fa-times"></i>
                        </button>
                        <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                            <i class="text-primary">
                                <?php getLogo() ?></i>
                            <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                        </a>
                    </div>
                    <!-- END Side Header -->

                    <!-- Side Content -->
                    <div class="side-content-sbp-ro side-content">
                        <ul class="nav-main">
                            <li>
                                <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Work In Process</span></a>
                            </li>
                            <li>
                                <a href="inventory.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Inventory List</span></a>
                            </li>
                            <?php if (!$shopIsReadOnly): ?>
                                <li>
                                    <a href="addinventory.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Add New Part</span></a>
                                </li>
                            <?php endif; ?>
                            <li>
                                <a href="inventoryorders.php"><i class="fa fa-calendar-check-o"></i><span class="sidebar-mini-hide">Manage PO's</span></a>
                            </li>
                            <li>
                                <a href="inventoryfees.php"><i class="fa fa-calendar-check-o"></i><span class="sidebar-mini-hide">Manage Inventory Fees</span></a>
                            </li>
                            <li style="display:none">
                                <a href="#"><i class="fa fa-calculator"></i><span class="sidebar-mini-hide">Receive Inventory Order to Specific RO</span></a>
                            </li>
                            <li>
                                <a onclick="showAll()" href="#"><i class="fa fa-calculator"></i><span class="sidebar-mini-hide">Show All Inventory</span></a>
                            </li>
                            <li>
                                <a href="inventoryextract.php"><i class="fa fa-calculator"></i><span class="sidebar-mini-hide">Download Inventory</span></a>
                            </li>
                        </ul>
                    </div>
                    <!-- END Side Content -->
                </div>
                <!-- Sidebar Content -->
            </div>
            <!-- END Sidebar Scroll Container -->
        </nav>
        <!-- END Sidebar -->

        <!-- Header -->
        <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar" class="content-mini content-mini-full">

            <!-- Header Navigation Right -->
            Manage PO's
            <!-- END Header Navigation Right -->

            <!-- Header Navigation Left -->

            <ul class="nav-header pull-left">
                <li class="hidden-md hidden-lg">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                        <i class="fa fa-navicon"></i>
                    </button>
                </li>
                <li class="hidden-xs hidden-sm">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                        <i class="fa fa-bars"></i>
                    </button>
                </li>
                <li>
                    <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                    <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                        <i class="si si-grid"></i>
                    </button>
                </li>
                <li class="visible-xs">
                    <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                    <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                        <i class="fa fa-search"></i>
                    </button>
                </li>
                <li>

                </li>
            </ul>

            <!-- END Header Navigation Left -->
        </header>
        <!-- END Header -->
        <!-- Main Container -->

        <!-- Main Container -->
        <main class="container-fluid" id="main-container" style="display:block;">
            <?php if (!$shopIsReadOnly): ?>
                <br>
                <button class="btn btn-warning btn-lg" type="button" style="float:right" onclick="location.href='inventoryorder.php'">Add New PO</button>
            <?php endif; ?>

            <br>
            The following list of Inventory PO's have not been received or closed<br />
            &nbsp;&nbsp;
            <div class="row" style="margin:0px 10px 0px 20px;border:0px;padding:10px">
                <div class="col-md-6" style="border:0px"><input class="form-control" id="srch" placeholder="Search by PO Number or Vendor"></div>
                <div class="col-md-6" style="border:0px">
                    <select onchange="changeType(this.value)" class="form-control" id="potype">
                        <option value="INV">Inventory</option>
                        <option value="RO">Repair Orders</option>
                    </select>
                </div>
            </div>
            <table id="maintbl" class="table table-condensed table-striped table-header-bg">
                <thead>
                    <tr>
                        <td class="style3" style="height: 35px"><strong>PO #</strong></td>
                        <td class="style3" style="height: 35px"><strong>Date Issue</strong></td>
                        <td class="style3" style="height: 35px"><strong>Supplier</strong></td>
                        <td class="style3" style="height: 35px"><strong>Status</strong></td>
                        <td class="style3" style="height: 35px"><strong>Delivery</strong></td>
                        <td class="style3" style="height: 35px"><strong>Expected</strong></td>
                        <td class="style5" style="height: 35px"><strong>Total PO</strong></td>
                        <td class="style5" style="height: 35px"><strong>Ordered By</strong></td>
                        <td class="style5" style="height: 35px"><strong>Type</strong></td>
                        <td class="style7" style="height: 35px"><strong>Actions</strong></td>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $stmt = "select * from po where shopid = ? AND ordertype = 'INV' order by ponumber desc limit 1000";
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("s", $shopid);
                        $query->execute();
                        $results = $query->get_result();
                        $pistmt = "select sum(partcost*quantity) as pc from partsinventoryorder where shopid = ? and ponumber = ?";
                        if ($piquery = $conn->prepare($pistmt)) {
                            $pi_po_number = 0;
                            $piquery->bind_param("si", $shopid, $pi_po_number);
                            while ($rs = $results->fetch_assoc()) {
                                $pi_po_number = $rs["ponumber"];
                                $piquery->execute();
                                $piquery->store_result();
                                $piquery->bind_result($pc);
                                $piquery->fetch();
                                if (is_null($pc)) {
                                    $pc = asDollars(0);
                                } else {
                                    if ($pc < 0)
                                        $pc = '(' . asDollars(abs($pc)) . ')';
                                    else
                                        $pc = asDollars($pc);
                                }
                    ?>
                                <tr>
                                    <td style="height: 35px"><?= $rs['ponumber'] ?></td>
                                    <td style="height: 35px"><?php echo date('m/d/Y', strtotime($rs['issuedate'])); ?></td>
                                    <td style="height: 35px"><?php echo $rs['issuedto']; ?>&nbsp;</td>
                                    <td style="height: 35px"><?php echo strtoupper($rs['status']); ?>&nbsp;</td>
                                    <td><?php echo $rs['deliverymethod']; ?></td>
                                    <td><?php echo date('m/d/Y', strtotime($rs['expectedarrival'])); ?></td>
                                    <td class="style4" style="height: 35px"><?php echo $pc; ?>&nbsp;</td>
                                    <td class="style4" style="height: 35px"><?php echo strtoupper($rs['orderedby']); ?>
                                        &nbsp;
                                    </td>
                                    <td class="style4" style="height: 35px"><?php echo $rs['ordertype']; ?></td>
                                    <td class="style6" style="height: 35px;width:18%">
                                        <?php if ($rs["status"] != "Closed" && !$shopIsReadOnly): ?>
                                            <button type="button" class="btn btn-warning btn-xs" onclick="location.href='inventoryorderreceive.php?id=<?php echo $rs["id"]; ?>'">Receive</button>
                                            <button type="button" class="btn btn-success btn-xs" onclick="location.href='inventoryorderedit.php?id=<?php echo $rs['id']; ?>'">Edit</button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-xs" onclick="location.href='inventoryorderview.php?id=<?php echo $rs['id']; ?>'">View</button>
                                        <button type="button" class="btn btn-info btn-xs" onclick="printPO('<?php echo $rs["id"]; ?>')">Print</button>
                                    </td>
                                </tr>
                    <?php
                            }
                            $piquery->close();
                        }
                        $query->close();
                    } else {
                        echo "po Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                    }
                    ?>
                </tbody>
            </table>
            <input id="backbutton" style="z-index:9999;display:none;position:absolute;top:5px;left:5%;" onclick="closePrint()" type="button" class="btn btn-success btn-lg" value="Close Print" />
            <iframe id="pos" style="width:90%;position:absolute;left:5%;top:50px;height:90%;background-color:white;display:none;z-index:9999"></iframe>
            <div id="hider"></div>

        </main>
        <!-- END Main Container -->

        <!-- Footer -->
        <!-- END Footer -->
    </div>
    <!-- END Page Container -->
    <!-- Modals -->
    <div id="pmtmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
        <input id="customerid" type="hidden">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="block block-themed block-transparent remove-margin-b">
                    <div class="block-header bg-primary-dark">
                        <ul class="block-options">
                            <li>
                                <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                            </li>
                        </ul>
                        <h3 class="block-title">Modal Title</h3>
                    </div>
                    <div id="vehinfo" class="block-content"></div>
                    <div class="block-content">
                        <div class="row">
                            <div class="col-md-12">
                                <!-- duplicate this block for additional rows of form input -->
                                <div class="col-sm-12">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" id="pmtamount" name="pmtamount">
                                        <label for="pmtamount">Form Label</label>
                                    </div>
                                </div>
                            </div> <!-- Endd Duplicate area -->
                        </div>
                    </div>
                </div>
                <div style="margin-top:20px;" class="modal-footer">
                    <button class="btn btn-primary btn-md" type="button">Button</button>
                    <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
    <script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
    <script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

    <!-- Page Plugins -->

    <!-- Page JS Code
<script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
    <script>
        function changeType(t) {

            ds = "shopid=<?php echo $shopid; ?>&type=" + t
            $.ajax({
                data: ds,
                url: "<?= COMPONENTS_PRIVATE ?>/po/inventorypodata.php",
                type: "post",
                error: function(xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function(r) {
                    //console.log(r)

                    $('#maintbl').children("tbody").html(r)
                    $('#srch').focus()
                }
            })

        }

        $(document).ready(function() {

            $('#srch').focus()

        });

        $('#srch').keyup(function() {
            $('#spinner').show()
            searchTable($(this).val());

        });


        function searchTable(inputVal) {

            var table = $('#maintbl');

            table.find('tbody tr').each(function(index, row) {
                var allCells = $(row).find('td');
                if (allCells.length > 0) {
                    var found = false;
                    allCells.each(function(index, td) {
                        var regExp = new RegExp(inputVal, 'i');
                        if (regExp.test($(td).text())) {
                            found = true;
                            return false;
                        }
                    });
                    if (found == true) {
                        $(row).show();
                        $('#spinner').hide()
                    } else {
                        $(row).hide();
                        $('#hide').toggle()
                    }
                }
            });

        }


        function printPO(id, typ) {

            window.scroll(0, 0)
            $('#pos').show().attr("src", "<?= COMPONENTS_PRIVATE ?>/po/inventoryorderprintpdf.php?typ=" + typ + "&id=" + id + "&shopid=<?php echo $shopid; ?>")
            $('#hider').show()
            $('#backbutton').show()

            /*document.getElementById("pos").style.display='block'
            document.getElementById("hider").style.display = 'block'
            document.getElementById("backbutton").style.display='block'
            console.log("inventoryorderprintpdf.asp?typ="+typ+"&id="+id+"&shopid=<%=request.cookies("shopid")%>")
            document.getElementById("pos").src = "inventoryorderprintpdf.asp?typ="+typ+"&id="+id+"&shopid=<%=request.cookies("shopid")%>"*/

        }

        function closePrint() {

            document.getElementById("pos").style.display = 'none'
            document.getElementById("hider").style.display = 'none'
            document.getElementById("backbutton").style.display = 'none'


        }

        function showAll() {

            swal({
                    title: "Are you sure?",
                    text: "Depending on how many parts you have in stock, this list could take some time to load. Do you wish to continue?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonClass: "btn-danger",
                    confirmButtonText: "Yes, Load All",
                    closeOnConfirm: false
                },
                function() {
                    location.href = "inventory.php?showall=yes"

                });


        }
    </script>
    <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>

</html>

<?php
mysqli_close($conn);
?>