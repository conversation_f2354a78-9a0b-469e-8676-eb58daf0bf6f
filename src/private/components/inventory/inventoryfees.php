<?php
require_once CONN;

$shopid = $_COOKIE['shopid'];
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<!DOCTYPE html>

<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">
     <title><?= getPageTitle() ?></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='../php/customer/assets/img/<?= getFavicon()?>' type='image/x-icon' />
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        #header {
            text-align: center;
            font-weight: bold;
            height: 55px;

        }
    </style>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">

    <div id="mainalert" style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large" class="alert alert-success"></div>
    <div id="header"></div>
    <!-- Page Container -->
    <!--
    Available Classes:

    'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

    'sidebar-l'                  Left Sidebar and right Side Overlay
    'sidebar-r'                  Right Sidebar and left Side Overlay
    'sidebar-mini'               Mini hoverable Sidebar (> 991px)
    'sidebar-o'                  Visible Sidebar by default (> 991px)
    'sidebar-o-xs'               Visible Sidebar by default (< 992px)

    'side-overlay-hover'         Hoverable Side Overlay (> 991px)
    'side-overlay-o'             Visible Side Overlay by default (> 991px)

    'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

    'header-navbar-fixed'        Enables fixed header
-->
    <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">

        <!-- Sidebar -->
        <nav id="sidebar">
            <!-- Sidebar Scroll Container -->
            <div id="sidebar-scroll">
                <!-- Sidebar Content -->
                <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                <div class="sidebar-content">
                    <!-- Side Header -->
                    <div class="side-header side-content bg-white-op">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                            <i class="fa fa-times"></i>
                        </button>
                        <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                            <i class="text-primary">
                                <?php getLogo() ?></i>
                            <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                        </a>
                    </div>
                    <!-- END Side Header -->

                    <!-- Side Content -->
                    <div class="side-content-sbp-ro side-content">
                        <ul class="nav-main">
                            <li>
                                <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Work In Process</span></a>
                            </li>
                            <li>
                                <a href="inventory.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Inventory List</span></a>
                            </li>
                            <?php if (!$shopIsReadOnly): ?>
                                <li>
                                    <a href="addinventory.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Add New Part</span></a>
                                </li>
                            <?php endif; ?>
                            <li>
                                <a href="inventoryorders.php"><i class="fa fa-calendar-check-o"></i><span class="sidebar-mini-hide">Manage PO's</span></a>
                            </li>
                            <li>
                                <a href="inventoryfees.php"><i class="fa fa-calendar-check-o"></i><span class="sidebar-mini-hide">Manage Inventory Fees</span></a>
                            </li>
                            <li style="display:none">
                                <a href="#"><i class="fa fa-calculator"></i><span class="sidebar-mini-hide">Receive Inventory Order to Specific RO</span></a>
                            </li>
                            <li>
                                <a href="inventory.php"><i class="fa fa-calculator"></i><span class="sidebar-mini-hide">Show All Inventory</span></a>
                            </li>
                            <li>
                                <a href="inventoryextract.php"><i class="fa fa-calculator"></i><span class="sidebar-mini-hide">Download Inventory</span></a>
                            </li>
                        </ul>
                    </div>
                    <!-- END Side Content -->
                </div>
                <!-- Sidebar Content -->
            </div>
            <!-- END Sidebar Scroll Container -->
        </nav>
        <!-- END Sidebar -->

        <!-- Header -->
        <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar" class="content-mini content-mini-full">

            <!-- Header Navigation Right -->
            <span>Manage Inventory Fees</span>
            <!-- END Header Navigation Right -->

            <!-- Header Navigation Left -->

            <ul class="nav-header pull-left">
                <li class="hidden-md hidden-lg">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                        <i class="fa fa-navicon"></i>
                    </button>
                </li>
                <li class="hidden-xs hidden-sm">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                        <i class="fa fa-bars"></i>
                    </button>
                </li>
                <li>
                    <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                    <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                        <i class="si si-grid"></i>
                    </button>
                </li>
                <li class="visible-xs">
                    <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                    <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                        <i class="fa fa-search"></i>
                    </button>
                </li>
                <li>

                </li>
            </ul>

            <!-- END Header Navigation Left -->
        </header>
        <!-- END Header -->
        <!-- Main Container -->

        <!-- Main Container -->
        <main class="container-fluid" id="main-container" style="display:block;">
            <input type="hidden" id="id">
            <?php if (!$shopIsReadOnly): ?>
                <table class="table table-condensed table-striped" style="width: 50%;margin:auto">
                    <tr>
                        <td style="width: 50%">Fee Name</td>
                        <td style="width: 50%"><input id="name" class="form-control" style="width: 167px" type="text"></td>
                    </tr>
                    <tr>
                        <td style="width: 50%">Fee Amount</td>
                        <td style="width: 50%"><input id="amt" class="form-control" style="width: 167px" type="text"></td>
                    </tr>
                    <tr>
                        <td style="width: 50%">Percent or Dollar</td>
                        <td style="width: 50%"><select style="width:200px;" class="form-control" id="percentdollar">
                                <option value="dollar">Dollar</option>
                                <option value="percent">Percent</option>
                            </select></td>

                    </tr>

                    <tr>
                        <td style="width: 50%">Taxable</td>
                        <td style="width: 50%">
                            <select style="width:200px;" class="form-control" id="taxable">
                                <option value="yes">Yes</option>
                                <option value="no">No</option>
                            </select>
                        </td>


                    </tr>
                    <tr>
                        <td style="width: 50%">Multiply by the Part Quantity sold</td>
                        <td style="width: 50%">
                            <select style="width:200px;" class="form-control" id="qtyflag">

                                <option value="no">No</option>
                                <option value="yes">Yes</option>
                            </select>
                        </td>


                    </tr>
                    <tr>
                        <td class="text-center" colspan="2">
                            <input name="Button1" onclick="saveChanges()" class="btn btn-primary btn-md" type="button" value="Save">
                            <input name="Button3" type="button" onclick="location.href='inventory.php'" class="btn btn-default btn-md" value="Back to Inventory">
                        </td>
                    </tr>
                </table>
            <?php endif; ?>
            <strong>Existing Fees:</strong>

            <table class="table table-condensed table-striped table-hover">
                <tr>
                    <td class="bg-primary">Fee Name</td>
                    <td class="bg-primary">Fee Amount</td>
                    <td class="bg-primary">Percent/Dollar Amt</td>
                    <td class="bg-primary">Taxable</td>
                    <td class="bg-primary">Quantity Flag</td>
                    
                    <?php if (!$shopIsReadOnly): ?>
                        <td class="bg-primary">Delete</td>
                    <?php endif; ?>
                </tr>
                <?php
                $stmt = "select * from otherfees where shopid = '$shopid'";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $result = $query->get_result();
                    while ($row = $result->fetch_assoc()) {
                ?>
                        <tr>
                            <td onclick="editFee('<?php echo strtoupper($row['feename']) ?>','<?php echo strtoupper($row['feeamount']) ?>','<?php echo strtoupper($row['dollarpercent']) ?>','<?php echo strtoupper($row['id']) ?>','<?php echo strtolower($row['taxable']) ?>','<?php echo strtolower($row['qtyflag']) ?>' )"><?php echo strtoupper($row['feename']) ?>&nbsp;</td>
                            <td onclick="editFee('<?php echo strtoupper($row['feename']) ?>','<?php echo strtoupper($row['feeamount']) ?>','<?php echo strtoupper($row['dollarpercent']) ?>','<?php echo strtoupper($row['id']) ?>','<?php echo strtolower($row['taxable']) ?>','<?php echo strtolower($row['qtyflag']) ?>')"><?php echo number_format($row['feeamount'], 2) ?>&nbsp;</td>
                            <td onclick="editFee('<?php echo strtoupper($row['feename']) ?>','<?php echo strtoupper($row['feeamount']) ?>','<?php echo strtoupper($row['dollarpercent']) ?>','<?php echo strtoupper($row['id']) ?>','<?php echo strtolower($row['taxable']) ?>','<?php echo strtolower($row['qtyflag']) ?>')"><?php echo strtoupper($row['dollarpercent']) ?>&nbsp;</td>
                            <td onclick="editFee('<?php echo strtoupper($row['feename']) ?>','<?php echo strtoupper($row['feeamount']) ?>','<?php echo strtoupper($row['dollarpercent']) ?>','<?php echo strtoupper($row['id']) ?>','<?php echo strtolower($row['taxable']) ?>','<?php echo strtolower($row['qtyflag']) ?>')"><?php echo strtoupper($row['taxable']) ?>&nbsp;</td>
                            <td onclick="editFee('<?php echo strtoupper($row['feename']) ?>','<?php echo strtoupper($row['feeamount']) ?>','<?php echo strtoupper($row['dollarpercent']) ?>','<?php echo strtoupper($row['id']) ?>','<?php echo strtolower($row['taxable']) ?>','<?php echo strtolower($row['qtyflag']) ?>')"><?php echo strtoupper($row['qtyflag']) ?>&nbsp;</td>
                            
                            <?php if (!$shopIsReadOnly): ?>
                                <td><input onclick="deleteFee('<?php echo strtoupper($row['id']) ?>')" class="btn btn-primary btn-md" name="Button2" type="button" value="Delete"></td>
                            <?php endif; ?>
                        </tr>
                <?php
                    }
                }
                ?>
            </table>
        </main>
        <!-- END Main Container -->

        <!-- Footer -->
        <!-- END Footer -->
    </div>
    <!-- END Page Container -->
    <!-- Apps Modal -->
    <!-- Apps Modal -->


    <script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="<?= SCRIPT ?>/emodal.js?v=2.3"></script>
    <script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
    <script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
    <!--script src="<?= SCRIPT ?>/ping.js"></script-->
    <script>
        function editFee(n, a, p, id, taxable, q) {

            $('#name').val(n)
            $('#amt').val(a)
            $('#taxable').val(taxable)

            $('#percentdollar').val(p.toLowerCase())
            $('#qtyflag').val(q)


            $('#id').val(id)


        }

        function saveChanges() {


            var ds = 't=au' + '&shopid=<?php echo $shopid; ?>' + '&id=' + $('#id').val() + '&qtyflag=' + $('#qtyflag').val() + '&name=' + encodeURIComponent($('#name').val()) + '&taxable=' + $('#taxable').val() + '&amt=' + $('#amt').val() + '&pd=' + $('#percentdollar').val();

            console.log(ds)

            $.ajax({
                url: "saveinventoryfees.php",
                type: "post",
                data: ds,
                //data: "t=au&taxable="+$('#taxable').val()+"&qtyflag="+$('#qtyflag').val()+"&id="+$('#id').val()+"&name="+encodeURIComponent($('#name').val())+"&amt="+$('#amt').val()+"&pd="+$('#percentdollar').val()+"&shopid=<?php echo $shopid; ?>",
                error: function(xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function(r) {
                    console.log(r)
                    if (r == "success") {
                        parent.location.reload()
                    } else {
                        swal("There was an error saving");
                        console.log(r)
                    }
                }
            });

        }

        function deleteFee(id) {
            // add qtyflag
            swal({
                    title: "Delete Fee?",
                    text: "Are you sure you want to delete this fee?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonClass: "btn-danger",
                    confirmButtonText: "Yes, delete it",
                    closeOnConfirm: false
                },
                function() {
                    $.ajax({
                        url: "saveinventoryfees.php",
                        data: "name=&amt=&pd=&taxable=&qtyflag=&t=d&id=" + id + "&shopid=<?php echo $shopid; ?>",
                        type: "post",
                        error: function(xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        },
                        success: function(r) {
                            if (r == "success") {
                                location.reload()
                            } else {
                                alert("There was an error saving")
                                console.log(r)
                            }
                        }
                    });

                });

        }

        function managePO() {

            location.href = 'inventoryorders.php'

        }

        function addPO() {

            location.href = 'inventoryorder.php'

        }

        function addPart() {
            location.href = 'addinventory.php'
        }

        function invFees() {

            location.href = 'inventoryfees.php'

        }

        function showInv() {

            location.href = 'inventory.php'

        }
    </script>

</body>
<?php
mysqli_close($conn);
?>

</html>