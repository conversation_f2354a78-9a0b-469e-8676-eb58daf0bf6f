<script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
<script src='<?= SCRIPT ?>/plugins/jquery-ui/jquery-ui.js'></script>
<script src='<?= SCRIPT ?>/plugins/moment/moment.js'></script>
<script src='<?= ASSETS ?>/calendar/lib/fullcalendar.min.js'></script>
<script src='<?= ASSETS ?>/calendar/scheduler.min.js'></script>

<script>
    var shopIsReadOnly = <?= $shopIsReadOnly ? 'true' : 'false'; ?>;

    $(function () {

        mystartdate = moment().subtract(10, 'days')
        mystartdate = moment(mystartdate).format('YYYY-MM-DD')

        myenddate = moment().add(90, 'days')
        myenddate = moment(myenddate).format('YYYY-MM-DD')

        $('#external-events .fc-event').each(function () {

            // store data so the calendar knows to render an event upon drop
            currtext = $(this).text()
            currarray = currtext.split("|")
            thistitle = currarray[0]
            for (i = 1; i < currarray.length; i++) {
                laborid = currarray[1]
                labord = currarray[2]
                hrs = currarray[3]
                tech = currarray[4]
                roid = currarray[5]
                comid = currarray[6]
            }


            $(this).data('event', {
                title: $.trim(thistitle), 	// use the element's text as the event title
                id: laborid,
                labord: labord,
                hrs: hrs,
                tech: tech,
                roid: roid,
                comid: comid
            });

            // make the event draggable using jQuery UI
            if ( !shopIsReadOnly ){
                $(this).draggable({
                    opacity: .5,
                    zIndex: 9999,
                    scrollSensitivity: 40,
                    scrollSpeed: 100,
                    revert: true,      // will cause the event to go back to its
                    revertDuration: 0  //  original position after the drag
                });
            }
        });

        $('body').mousemove(function (event) {

            ns = $('#unscheduled')
            nsboxleft = ns.offset().left
            nsboxtop = ns.offset().top
            nsboxwidth = ns.outerWidth()
            nsboxheight = ns.height()
            nsboxright = parseFloat(nsboxleft) + parseFloat(nsboxwidth)
            nsboxbottom = parseFloat(nsboxtop) + parseFloat(nsboxheight)
            //console.log(nsboxleft+"|"+event.pageX+"|" +":"+ nsboxright +":"+ nsboxtop+"|"+event.pageY+"|" +":"+ nsboxbottom)

            if (event.pageX >= nsboxleft && event.pageX <= nsboxright && event.pageY >= nsboxtop && event.pageY <= nsboxbottom) {
                $('#unscheduled').addClass("unscheduled-hover")
                //console.log("true:"+$('#unscheduled').attr("class"))
            } else {
                //console.log("false:"+$('#unscheduled').attr("class"))
                if ($('#unscheduled').hasClass("unscheduled-hover")) {
                    $('#unscheduled').removeClass("unscheduled-hover")
                }
            }

        });


        var isEventOverDiv = function (x, y) {

            var external_events = $('#external-events');
            var offset = external_events.offset();
            offset.right = external_events.width() + offset.left;
            offset.bottom = external_events.height() + offset.top;

            // Compare
            if (x >= offset.left
                && y >= offset.top
                && x <= offset.right
                && y <= offset.bottom) {
                return true;
            }
            return false;

        }

        resourcedata = [
            <?php
            $rstr = "";
            $stmt = "select bay1,bay2,bay3,bay4,bay5,bay6,bay7,bay8,bay9,bay10,bay11,bay12,bay13,bay14,bay15,bay16,bay17,bay18,bay19,bay20 from shopbays where shopid = '$shopid'";
            //echo "//".$stmt;
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $query->store_result();
                $num_bay_rows = $query->num_rows;
                //echo $num_bay_rows;
                if ($num_bay_rows > 0) {
                    $query->bind_result($bay1, $bay2, $bay3, $bay4, $bay5, $bay6, $bay7, $bay8, $bay9, $bay10, $bay11, $bay12, $bay13, $bay14, $bay15, $bay16, $bay17, $bay18, $bay19, $bay20);
                    $query->fetch();
                    if (strlen($bay1) > 0) {
                        $rstr .= "{ id: 'a', title: '" . addslashes($bay1) . "', eventColor: \"#006699\" },";
                        $lastrid = 'b';
                        } else {
                            $rstr .= "{ id: 'a', title: 'BAY 1', eventColor: \"#006699\"},";
                            $lastrid = 'b';
                    }
                    if (strlen($bay2) > 0) {
                        $rstr .= "{ id: 'b', title: '" . addslashes($bay2) . "', eventColor: \"#b36b00\" },";
                        $lastrid = 'c';
                        } else {
                            $rstr .= "{ id: 'b', title: 'BAY 2', eventColor: '#b36b00' },";
                            $lastrid = 'c';
                    }
                    if (strlen($bay3) > 0) {
                        $rstr .= "{ id: 'c', title: '" . addslashes($bay3) . "', eventColor: \"#006600\" },";
                        $lastrid = 'd';
                        } else {
                            $rstr .= "{ id: 'c', title: 'BAY 3', eventColor: '#006600' },";
                            $lastrid = 'd';
                    }
                    if (strlen($bay4) > 0) {
                        $rstr .= "{ id: 'd', title: '" . addslashes($bay4) . "', eventColor: \"#800000\" },";
                        $lastrid = 'e';
                        } else {
                            $rstr .= "{ id: 'd', title: 'BAY 4', eventColor: '#800000' },";
                            $lastrid = 'e';
                    }
                    if (strlen($bay5) > 0) {
                        $rstr .= "{ id: 'e', title: '" . addslashes($bay5) . "', eventColor: \"#993300\" },";
                        $lastrid = 'f';
                        } else {
                            $rstr .= "{ id: 'e', title: 'BAY 5', eventColor: '#993300' },";
                            $lastrid = 'f';
                    }
                    if (strlen($bay6) > 0) {
                        $rstr .= "{ id: 'f', title: '" . addslashes($bay6) . "', eventColor: \"#4d004d\" },";
                        $lastrid = 'g';
                        } else {
                            $rstr .= "{ id: 'f', title: 'BAY 6', eventColor: '#4d004d' },";
                            $lastrid = 'g';
                    }
                    if (strlen($bay7) > 0) {
                        $rstr .= "{ id: 'g', title: '" . addslashes($bay7) . "', eventColor: \"#b38600\" },";
                        $lastrid = 'h';
                        } else {
                            $rstr .= "{ id: 'g', title: 'BAY 7', eventColor: '#b38600' },";
                            $lastrid = 'h';
                    }
                    if (strlen($bay8) > 0) {
                        $rstr .= "{ id: 'h', title: '" . addslashes($bay8) . "', eventColor: \"#006699\" },";
                        $lastrid = 'i';
                    }
                    if (strlen($bay9) > 0) {
                        $rstr .= "{ id: 'i', title: '" . addslashes($bay9) . "', eventColor: \"#006699\" },";
                        $lastrid = 'j';
                    }
                    if (strlen($bay10) > 0) {
                        $rstr .= "{ id: 'j', title: '" . addslashes($bay10) . "', eventColor: \"#006600\" },";
                        $lastrid = 'k';
                    }
                    if (strlen($bay11) > 0) {
                        $rstr .= "{ id: 'k', title: '" . addslashes($bay11) . "', eventColor: \"#800000\" },";
                        $lastrid = 'l';
                    }
                    if (strlen($bay12) > 0) {
                        $rstr .= "{ id: 'l', title: '" . addslashes($bay12) . "', eventColor: \"#993300\" },";
                        $lastrid = 'm';
                    }
                    if (strlen($bay13) > 0) {
                        $rstr .= "{ id: 'm', title: '" . addslashes($bay13) . "', eventColor: \"#4d004d\" },";
                        $lastrid = 'n';
                    }
                    if (strlen($bay14) > 0) {
                        $rstr .= "{ id: 'n', title: '" . addslashes($bay14) . "', eventColor: \"#b38600\" },";
                        $lastrid = 'o';
                    }
                    if (strlen($bay15) > 0) {
                        $rstr .= "{ id: 'o', title: '" . addslashes($bay15) . "', eventColor: \"#006699\" },";
                        $lastrid = 'p';
                    }
                    if (strlen($bay16) > 0) {
                        $rstr .= "{ id: 'p', title: '" . addslashes($bay16) . "', eventColor: \"#b36b00\" },";
                        $lastrid = 'q';
                    }
                    if (strlen($bay17) > 0) {
                        $rstr .= "{ id: 'q', title: '" . addslashes($bay17) . "', eventColor: \"#006600\" },";
                        $lastrid = 'r';
                    }
                    if (strlen($bay18) > 0) {
                        $rstr .= "{ id: 'r', title: '" . addslashes($bay18) . "', eventColor: \"#800000\" },";
                        $lastrid = 's';
                    }
                    if (strlen($bay19) > 0) {
                        $rstr .= "{ id: 's', title: '" . addslashes($bay19) . "', eventColor: \"#993300\" },";
                        $lastrid = 't';
                    }
                    if (strlen($bay20) > 0) {
                        $rstr .= "{ id: 't', title: '" . addslashes($bay20) . "', eventColor: \"#4d004d\" },";
                        $lastrid = 't';
                    }
                    $rstr = substr($rstr, 0, strlen($rstr) - 1);
                    echo $rstr;
                } else {
                    echo "{ id: 'a', title: 'BAY 1', eventColor: \"#006699\" },"
                        . "{ id: 'b', title: 'BAY 2', eventColor: '#b36b00' },"
                        . "{ id: 'c', title: 'BAY 3', eventColor: '#006600' },"
                        . "{ id: 'd', title: 'BAY 4', eventColor: '#800000' },"
                        . "{ id: 'e', title: 'BAY 5', eventColor: '#993300' },"
                        . "{ id: 'f', title: 'BAY 6', eventColor: '#4d004d' },"
                        . "{ id: 'g', title: 'BAY 7', eventColor: '#b38600' }";
                    $lastrid = 'h';

                }
                $query->close();
            } else {
                echo "{ id: 'a', title: 'BAY 1', eventColor: \"#006699\" },"
                    . "{ id: 'b', title: 'BAY 2', eventColor: '#b36b00' },"
                    . "{ id: 'c', title: 'BAY 3', eventColor: '#006600' },"
                    . "{ id: 'd', title: 'BAY 4', eventColor: '#800000' },"
                    . "{ id: 'e', title: 'BAY 5', eventColor: '#993300' },"
                    . "{ id: 'f', title: 'BAY 6', eventColor: '#4d004d' },"
                    . "{ id: 'g', title: 'BAY 7', eventColor: '#b38600' }";
                $lastrid = 'h';
            }
            ?>
        ]

        var isMobile = window.innerWidth < 768;
        
        var customButtons = {};

        if (!shopIsReadOnly){
            customButtons = {
                addColumn: {
                    text: 'Add Column',
                    click: function () {
                        $('#headermodal').modal('show');
                        $('#editrid').val('<?php echo $lastrid; ?>');
                        $('#edittitle').attr('placeholder', 'New Column Name');
                        setTimeout(function () {
                            $('#edittitle').focus();
                        }, 500);
                    }
                },
                resetColumn: {
                    text: "Reset All Columns",
                    click: function () {
                        sbconfirm("Are you sure?", "Are you sure you want to reset ALL columns?", function () {
                            $.ajax({
                                data: "t=clear&shopid=<?php echo $shopid; ?>",
                                url: "dispatchdata.php",
                                type: "post",
                                success: function (r) {
                                    console.log(r);
                                    if (r == "success") location.reload();
                                }
                            });
                        });
                    }
                }
            };
        }
        
        $('#calendar').fullCalendar({
            customButtons: customButtons,
            defaultView: 'agendaDay',
            defaultDate: '<?php echo date("Y-m-d"); ?>',
            minTime: "06:00:00",
            maxTime: "20:00:00",
            slotMinutes: 15,
            slotDuration: '00:15:00',
            slotLabelFormat: 'HH:mm',
            droppable: true,
            selectHelper: true,
            editable: <?= ($_COOKIE['mode'] == 'full' && !$shopIsReadOnly) ? 'true':'false'?>,
            selectable: !shopIsReadOnly,
            allDaySlot: false,
            columnFormat: isMobile ? 'ddd\nM/D' : 'ddd M/D/Y',
            eventLimit: true, // allow "more" link when too many events
            header: {
                left: 'prev,next today <?= $_COOKIE['mode'] == 'full'?"addColumn resetColumn":''?>',
                center: 'title',
                right: 'agendaDay,agendaTwoDay,agendaWeek,month'
            },

            views: {
                agenda: {
                    titleFormat: 'ddd MMM D'
                },
                agendaTwoDay: {
                    type: 'agenda',
                    duration: {days: 3},

                    groupByDateAndResource: true
                }
            },

            resources: resourcedata,
            events: {
                url: 'dispatchdata.php',
                type: 'POST',
                data: {
                    t: "getlist",
                    sd: mystartdate,
                    ed: myenddate
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },

            },

            resourceRender: function (resourceObj, labelTds, bodyTds) {
                labelTds.on('click', function () {
                    rid = resourceObj.id
                    rtitle = resourceObj.title
                    $('#headermodal').modal('show')
                    $('#editrid').val(rid)
                    $('#edittitle').val(rtitle)
                });
            },

            dayClick: function (date, jsEvent, view, resource) {
                if (!shopIsReadOnly && "<?= $_COOKIE['mode'] ?>" === "full") {
                    if (resource !== undefined) {
                        $('#addmodal').modal('show');
                        sd = date.format("MM/DD/YYYY, HH:mm A");
                        $('#addsd').val(sd);
                        $('#addcolumn').val(resource.id);
                    } else {
                        sbalert("You can only enter new dispatch items in the Day View or 3 Day View");
                    }
                }
            },

            eventClick: function (calEvent, jsEvent, view) {

                <?php if($_COOKIE['mode'] == 'full' && !$shopIsReadOnly){?>

                event_start = calEvent.start
                event_end = calEvent.end
                $('#editmodal').modal('show')
                $('#editmodalheader').css("backgroundColor", calEvent.backColor)
                $('#tech').html(calEvent.tech)
                $('#labor').html(calEvent.labor)
                $('#hrs').html(calEvent.hrs)
                $('#roid').html(calEvent.roid)
                $('#sd').val(event_start.format("MM/DD/YYYY, HH:mm A"))
                if (event_end != null) {
                    $('#ed').val(event_end.format("MM/DD/YYYY, HH:mm A"))
                } else {
                    d = moment();
                    $('#ed').val(d.format("MM/DD/YYYY, HH:mm A"))
                }
                hsd = $.fullCalendar.formatDate(calEvent.start, "YYYY-MM-DD HH:mm:ss")
                if (event_end != null) {
                    hed = $.fullCalendar.formatDate(calEvent.end, "YYYY-MM-DD HH:mm:ss")
                } else {
                    hed = $.fullCalendar.formatDate(moment(), "YYYY-MM-DD HH:mm:ss")
                }
                $('#calid').val(calEvent.id)

                <?php }?>

            },

            eventRender: function (event, element) {
                element.find('span.fc-title').html(element.find('span.fc-title').text());
            },

            eventDrop: function (event, delta, revertFunc) {

                sd = event.start.format()
                ed = event.end

                if (ed) {
                    ed = event.end.format()
                } else {
                    var ed = "none"
                }
                id = event.id
                rid = event.resourceId
                ds = "t=moveevent&id=" + id + "&ed=" + ed + "&sd=" + sd + "&rid=" + rid
                showLoader()
                if (Date.parse(sd)) {
                    $.ajax({
                        type: "post",
                        data: ds,
                        url: "dispatchdata.php",
                        success: function (r) {
                            hideLoader()
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        },
                    });
                }


            },
            eventDragStop: function (event, jsEvent) {

                /*
				trash = $('#trash')
				boxleft = trash.offset().left
				boxtop = trash.offset().top
				boxwidth = trash.outerWidth()
				boxheight = trash.height()
				boxright = parseFloat(boxleft) + parseFloat(boxwidth)
				boxbottom = parseFloat(boxtop) + parseFloat(boxheight)
                */
                //console.log("boxtop:"+boxtop+"\r\nboxbottom:"+boxbottom+"\r\nboxleft:"+boxleft+"\r\nboxright:"+boxright)

                ns = $('#unscheduled')
                nsboxleft = ns.offset().left
                nsboxtop = ns.offset().top
                nsboxwidth = ns.outerWidth()
                nsboxheight = ns.height()
                nsboxright = parseFloat(nsboxleft) + parseFloat(nsboxwidth)
                nsboxbottom = parseFloat(nsboxtop) + parseFloat(nsboxheight)
                //console.log(nsboxleft+"|"+event.pageX+"|" +":"+ nsboxright +":"+ nsboxtop+"|"+event.pageY+"|" +":"+ nsboxbottom)
                sd = new Date(event.start.format())
                ed = event.end
                if (ed) {
                    ed = new Date(event.end.format())
                    var hours = Math.abs(sd - ed) / 3600000
                    //console.log(hours)

                    /*
	
					if (jsEvent.pageX >= boxleft && jsEvent.pageX <= boxright && jsEvent.pageY >= boxtop && jsEvent.pageY <= boxbottom){
						swal({
							title: "Are you sure?",
							text: "You will not be able to add this to the Dispatch Schedule if you remove it",
							type: "warning",
							showCancelButton: true,
							confirmButtonClass: "btn-danger",
							confirmButtonText: "Yes, remove it",
							closeOnConfirm: true
						},
						function(){
							//console.log(event.id)
							// mark as scheduled
						    $.ajax({
						    	type: "post",
						    	data: "t=deletecalitem&id="+event.id,
						    	url: "dispatchdata.php",
						    	success: function(r){
						    		//console.log(r)
						    		if (r == "success"){
						                //$('#calendar').fullCalendar( 'removeEvents');
						                $('#calendar').fullCalendar( 'refetchEvents' );
	
									}else{
										console.log(r)
									}
						    	},
								error: function (xhr, ajaxOptions, thrownError) {
									console.log(xhr.status);
									console.log(xhr.responseText);
									console.log(thrownError);
								},
						    });
						});
					}
                    */


                    if (jsEvent.pageX >= nsboxleft && jsEvent.pageX <= nsboxright && jsEvent.pageY >= nsboxtop && jsEvent.pageY <= nsboxbottom) {

                        ds = "t=reschedulecalitem&laborid=" + event.laborid + "&dispatchid=" + event.id

                        console.log("query string:" + ds)
                        laborid = event.laborid
                        if (laborid != 0) {
                            $.ajax({
                                type: "post",
                                data: ds,
                                url: "dispatchdata.php",
                                success: function (r) {
                                    console.log(r)
                                    if (r == "success") {
                                        //$('#calendar').fullCalendar( 'removeEvents');
                                        $('#calendar').fullCalendar('refetchEvents');
                                        reloadJobs()
                                    } else {
                                        console.log(r)
                                    }
                                },
                                error: function (xhr, ajaxOptions, thrownError) {
                                    console.log(xhr.status);
                                    console.log(xhr.responseText);
                                    console.log(thrownError);
                                },
                            });
                        } else {
                            sbalert("This was not a labor job from an RO so you cannot add it to this list")
                        }
                    }
                }
            },
            viewRender: function (view) {

                $('.fc-prev-button,.fc-next-button,.fc-today-button,.fc-month-button,.fc-agendaWeek-button,.fc-agendaDay-button,.fc-agendaTwoDay-button,.fc-addColumn-button,.fc-resetColumn-button').removeClass().addClass('fc-button btn btn-secondary me-1');

                var axis = $('.fc-axis');
                var currview = $('#calendar').fullCalendar('getView')
                $('#currentview').val(view.type)
                for (var i = 0; i < axis.length; i++) {
                    var element = axis[i];
                    var p = element.parentElement;
                    var n = element.cloneNode(true);
                    p.appendChild(n);
                }
            },
            eventResize: function (event, jsEvent, ui, view) {
                ed = event.end.format()
                id = event.id
                showLoader()
                // post it with ajax, then
                $.ajax({
                    type: "post",
                    data: "t=changeevent&id=" + id + "&end=" + ed,
                    url: "dispatchdata.php",
                    success: function (r) {
                        hideLoader()
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },
                });

            },
            eventReceive: function (event) {

                startdate = event.start.format()
                sd = new Date(startdate)
                laborid = $('#laborid').val()
                rid = event.resourceId

                if (rid === null) {
                    sbalert("You must add events on the Day View or 3 Day View")
                    $("#calendar").fullCalendar('removeEvents', event.id);
                    return

                } else {
                    if (event.end === null) {

                        event.end = new Date(sd.setHours(sd.getHours() + 1));

                    }
                    //	$('#unsch-'+event.id).hide()
                    $('#calendar').fullCalendar('updateEvent', event)

                    // post it with ajax, then
                    ds = "t=addevent&rid=" + rid + "&laborid=" + laborid + "&start=" + startdate
                    console.log(ds)
                    $.ajax({
                        type: "post",
                        data: ds,
                        url: "dispatchdata.php",
                        success: function (r) {
                            console.log(r)
                            if (r == "success") {
                                setTimeout(function () {
                                    $('#calendar').fullCalendar('removeEvents');
                                    $('#calendar').fullCalendar('refetchEvents');
                                    //reloadJobs()
                                }, 200);

                            }
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        },
                    });
                }
            },

            drop: function (event) {
                rid = event.resourceId

                if (rid !== null) {
                    console.log("drop")
                    // is the "remove after drop" checkbox checked?
                    unschstr = $(this).attr('id')
                    unschid = unschstr.split('unsch-')
                    laborid = unschid[1]
                    $('#laborid').val(laborid)
                }
            }

        });


    });


    function reloadJobs() {

        $.ajax({
            type: "post",
            data: "t=jobs&approvedlaborondispatch=<?php echo $approvedlaborondispatch; ?>",
            url: "dispatchdata.php",
            success: function (r) {
                //console.log("running job list")
                $('#external-events-listing').html(r)
                setTimeout(function () {
                    $('#external-events .fc-event').each(function () {

                        // store data so the calendar knows to render an event upon drop
                        currtext = $(this).text()
                        currarray = currtext.split("|")
                        thistitle = currarray[0]
                        for (i = 1; i < currarray.length; i++) {
                            // $laborid|$labord|$hrs|$tech|$roid
                            laborid = currarray[1]
                            labord = currarray[2]
                            hrs = currarray[3]
                            tech = currarray[4]
                            roid = currarray[5]
                            comid = currarray[6]
                        }


                        $(this).data('event', {
                            title: $.trim(thistitle), 	// use the element's text as the event title
                            stick: true, 				// maintain when user navigates (see docs on the renderEvent method)
                            id: laborid,
                            labord: labord,
                            hrs: hrs,
                            tech: tech,
                            roid: roid,
                            comid: comid
                        });

                        // make the event draggable using jQuery UI
                        if ( !shopIsReadOnly ){
                            $(this).draggable({
                                opacity: .5,
                                zIndex: 9999,
                                scrollSensitivity: 40,
                                scrollSpeed: 100,
                                revert: true,      // will cause the event to go back to its
                                revertDuration: 0  //  original position after the drag
                            });   
                        }
                    });
                }, 200);

            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
        });


    }

    function deleteJob(laborid) {

        swal({
                title: "Are you sure?",
                text: "You will not be able to add this to the Dispatch Schedule if you remove it",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Yes, remove it",
                closeOnConfirm: true
            },
            function () {
                // mark as scheduled
                $.ajax({
                    type: "post",
                    data: "t=deletejob&laborid=" + laborid,
                    url: "dispatchdata.php",
                    success: function (r) {
                        if (r == "success") {
                            reloadJobs()
                        } else {
                            console.log(r)
                        }
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },
                });
            });

    }

    function resizeCalendar(calendarView) {
        //console.log(calendarView.name)
        if (calendarView.name === 'agendaWeek' || calendarView.name === 'agendaDay') {
            // if height is too big for these views, then scrollbars will be hidden
            calendarView.setHeight(9999);
        }
    }

    function addEvent() {

        roid = $('#addroid').val()
        tech = $('#addtech').val()
        labor = $('#addlabor').val()
        hours = $('#addhours').val()
        sd = $('#addsd').val().replace(',', '')
        ed = $('#added').val().replace(',', '')
        addcolumn = $('#addcolumn').val()

        if (roid.length > 0 && labor.length > 0 && hours.length > 0 && sd.length > 0 && ed.length > 0) {

            showLoader()
            $.ajax({
                data: "shopid=<?php echo $shopid; ?>&t=addnewitem&sd=" + sd + "&ed=" + ed + "&tech=" + tech.toUpperCase() + "&labor=" + labor.toUpperCase() + "&hours=" + hours + "&roid=" + roid + "&rid=" + addcolumn,
                type: "post",
                url: "dispatchdata.php",
                success: function (r) {
                    if (r.indexOf("success|") >= 0) {
                        rar = r.split("|")
                        newid = rar[1]

                        $('#addsd').val('')
                        $('#added').val('')
                        $('#addlabor').val('')
                        $('#addhours').val('')
                        $('#addroid').val('')
                        $('#addmodal').modal('hide')

                        var newEvent = {
                            id: newid,
                            title: "#" + roid + " - " + tech + " - " + labor + " (" + hours + " hours)",
                            start: sd,
                            end: ed,
                            resourceId: addcolumn,
                            tech: tech,
                            labor: labor,
                            hrs: hours,
                            roid: roid,
                            comid: 0,
                            laborid: 0,
                            allDay: 0
                        };
                        $('#calendar').fullCalendar('renderEvent', newEvent, 'stick');
                        hideLoader()
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        } else {
            sbalert("All fields are required")
        }

    }

    function isAlphaNumeric(str) {
        var code, i, len;

        for (i = 0, len = str.length; i < len; i++) {
            code = str.charCodeAt(i);
            if (!(code > 47 && code < 58) && // numeric (0-9)
                !(code > 64 && code < 91) && // upper alpha (A-Z)
                !(code > 96 && code < 123)) { // lower alpha (a-z)
                return false;
            }
        }
        return true;
    };

    function saveTitle() {

        title = $('#edittitle').val()
        rid = $('#editrid').val()
        if (title.length > 0) {
            showLoader()
            $.ajax({
                data: "t=title&shopid=<?php echo $shopid; ?>&rid=" + rid + "&title=" + title,
                type: "post",
                url: "dispatchdata.php",
                success: function (r) {
                    if (r == "success") {
                        location.reload()
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        } else {
            sbalert("You must enter a name for the column or cancel")
        }

    }

    function saveEvent() {

        sd = $('#sd').val().replace(',', '')
        ed = $('#ed').val().replace(',', '')
        id = $('#calid').val()

        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&t=changedate&sd=" + sd + "&ed=" + ed + "&id=" + id,
            type: "post",
            url: "dispatchdata.php",
            success: function (r) {
                if (r == "success") {
                    //$('#calendar').fullCalendar( 'removeEvents');
                    $('#calendar').fullCalendar('refetchEvents');
                    $('#sd').val('')
                    $('#ed').val('')
                    $('#calid').val('')
                    $('#editmodal').modal('hide')
                    window.location.reload()
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    function deleteEvent() {
        sbconfirm("Are you sure?", "You will not be able to add this to the Dispatch Schedule if you remove it",

            function () {

                id = $('#calid').val()

                showLoader()

                $.ajax({
                    type: "post",
                    data: "t=deletecalitem&id=" + id,
                    url: "dispatchdata.php",
                    success: function (r) {
                        if (r == "success")
                            $('#calendar').fullCalendar('refetchEvents');
                        $('#editmodal').modal('hide')
                        hideLoader()
                    }

                })
            })
    }

    $('.ui-widget-content').hover(function () {
        if (!$(this).html()) {
            for (i = 0; i < 7; i++) {
                $(this).append('<td class="temp_cell" style="border: 0px; width:' + (Number($('.fc-day').width()) + 2) + 'px"></td>');
            }

            $(this).children('td').each(function () {
                $(this).hover(function () {
                    $(this).css({'background-color': '#ffef8f', 'cursor': 'pointer'});
                }, function () {
                    $(this).prop('style').removeProperty('background-color');
                });
            });
        }
    }, function () {
        $(this).children('.temp_cell').remove();
    });


    $(document).ready(function () {

        $('.fc-button').hover(function () {
            $(this).removeClass('fc-state-hover');
        });

        /*
      const fields = document.querySelectorAll('.dtpicker');

        fields.forEach((field) => {
          new mdb.Datetimepicker(field, {
            datepicker: { format: 'mm/dd/yyyy' },
            timepicker: { format24: false },
            inline: true
          });
        });
        */

        $('#external-events-listing').on('click', '.labordel', function () {
            var id = $(this).attr('data-id')
            sbconfirm("Delete Job", "You will not be able to recover this file. Continue?",

                function () {

                    showLoader()

                    ds = "t=deletelaboritem&laborid=" + id
                    $.ajax({
                        type: "post",
                        data: ds,
                        url: "dispatchdata.php",
                        success: function (r) {
                            if (r == "success") {
                                $('#calendar').fullCalendar('refetchEvents');
                                reloadJobs()
                                hideLoader()
                            }
                        }

                    })

                }
            )
        })

        $('.fc-button').on('click', function () {

            $('.fc-button').removeClass('btn-primary').addClass('btn-secondary')
            $(this).removeClass('btn-secondary').addClass('btn-primary')
        });


    });


    setInterval(function () {
        reloadJobs()
    }, 10000);
</script>
