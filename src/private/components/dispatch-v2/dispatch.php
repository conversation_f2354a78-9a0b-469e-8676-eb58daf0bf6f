<?php
$component = "dispatch-v2";
include getRulesGlobal($component);
include getHeadGlobal($component);

$shopid = $_COOKIE['shopid'];
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

$stmt = "select installcode from company where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)){

	$query->execute();
	$query->bind_result($approvedlaborondispatch);
	$query->fetch();
	$query->close();
	
}

echo "<body>";
include getHeaderGlobal($component);
include getMenuGlobal($component);

?>

<main id="main-container">

<div class="container-fluid">

	<input type="hidden" id="laborid">
	<div class="row">
		<?php if($_COOKIE['mode'] == 'full'){?>
		<div class="col-md-2">
			<div id='external-events'>
				<div id='external-events-listing'>
					<div id="unscheduled"><h4 class="text-center">Jobs to Schedule</h4>
					<?php
					$stmt = "select labor,laborid,laborhours,l.tech,ro.roid from repairorders ro inner join labor l on ro.shopid = l.shopid and ro.roid = l.roid"
					. " inner join complaints c on l.shopid = c.shopid and l.roid = c.roid and l.complaintid = c.complaintid where"
					. " ro.datein >= '2017-03-22' and ro.status != 'closed' and ro.rotype != 'no approval' and ro.shopid = '$shopid' and schedulecat != 'scheduled'";
					if ($shopid == "4075"){
						$stmt .= " and ro.roid >= 4665";
					}
					if ($approvedlaborondispatch == "yes"){
						$stmt .= " and c.acceptdecline != 'pending' and c.acceptdecline != 'declined'";
					}
					$stmt .= " order by roid asc";
					if ($query = $conn->prepare($stmt)){
					    $query ->execute();
					    $result = $query->get_result();
					    while ($row = $result->fetch_array()) {
					    	$laborid = $row['laborid'];
					    	$labord = strtoupper(substr($row['labor'],0,30))."...";
					    	$hrs = $row['laborhours'];
					    	$roid = $row['roid'];
					    	$tech = strtoupper($row['tech']);

							$jobsToSchedule = "<div id='unsch-$laborid' class='fc-event'><b>#$roid - $tech</b> - $labord ($hrs Hours)<br>|$laborid";
							if ( !$shopIsReadOnly){
								$jobsToSchedule .= "<div class='position-absolute top-0 end-0 p-1 labordelwrapper'><a href='javascript:Void(null)' class='text-primary' data-mdb-toggle='tooltip' title='Delete Job'><i class='fas fa-trash fa-sm labordel' data-id='".$laborid."'></i></a></div></div>";
							}else{
								$jobsToSchedule .= "</div>";
							}
					    	echo $jobsToSchedule;
					    }
					}else{
						echo "MTD Sales failed: (" . $conn->errno . ") " . $conn->error;
					}

					?>
					</div>
				</div>
			</div>
		</div>
	    <?php }?>
		<div class="col-md-<?= $_COOKIE['mode'] == 'full'?10:12?>">
			<div id='calendar'></div>
		</div>
	</div>
</div>
</main>

<input type="hidden" id="currentview"><input type="hidden" id="hsd"><input type="hidden" id="hed">
	
<div id="editmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="editmodalLabel">Edit Event</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
				<input type="hidden" id="calid">
				<b>RO #:</b> <span id="roid"></span><br>
				<b>Tech:</b> <span id="tech"></span><br>
				<b>Labor:</b> <span id="labor"></span><br>
				<b>Hours:</b> <span id="hrs"></span><br><br>
				<div class="form-outline mb-4 dtpicker">
					<input type="text" id="sd" class="form-control dateTimePickerBS">
					<label class="form-label" for="sd">Start Date / Time</label>
				</div>

				<div class="form-outline dtpicker">
					<input type="text" id="ed" class="form-control dateTimePickerBS">
					<label class="form-label" for="ed">End Date / Time</label>
				</div>
			</div>
			
			<div class="modal-footer d-flex justify-content-center">
				<button type="button" class="btn btn-secondary" onclick="deleteEvent()">Delete</button>
				<button type="button" class="btn btn-secondary" onclick="location.href='<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?roid='+$('#roid').html()">Go To RO</button>
				<button type="button" class="btn btn-primary" onclick="saveEvent()">Save</button>
			</div>
		</div>		
	</div>
</div>

<div id="headermodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="headermodalLabel">Edit Header</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
				<input type="hidden" id="editrid">
				 <div class="form-outline">
					<input type="text" class="form-control" id="edittitle">
					<label class="form-label" for="edittitle">Letters and numbers only</label>
				 </div>
			</div>
				<div class="modal-footer d-flex justify-content-center">
					<button type="button" class="btn btn-primary" onclick="saveTitle()">Save</button>
				</div>
			</div>
		
		</div>
	</div>

<div id="addmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="addmodalLabel">Add Event</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            	<div class="form-outline mb-4">
					<input type="text" id="addroid" class="form-control">
					<label class="form-label" for="addroid">RO #</label>
				</div>

				<div class="form-group mb-4">
					<select id="addtech" class="select">
					<?php
						$stmt = "select employeelast, employeefirst from employees where shopid = ? and showtechlist = 'yes' and active = 'yes'";
						if ($query = $conn->prepare($stmt)){
							try{
								$query->bind_param("s",$shopid);
							    $query ->execute();
							    $query ->bind_result($emplast,$empfirst);
								$emplast = strtoupper($emplast);
								$empfirst = strtoupper($empfirst);
							    while ($query->fetch()) {
							        echo "<option value='$emplast, $empfirst'>$emplast, $empfirst</option>\n";
							    }
						    }
							catch(ErrorException $e) {
							 // echo 'error: '.$e->getMessage();
							}
						    
						}else{
							//echo "get employees Prepare failed: (" . $conn->errno . ") " . $conn->error;
						}

					?>
					</select>
					<label class="form-label select-label" for="addtech">Tech</label>
				</div>

				<div class="form-row mb-4">
					<select id="addcolumn" class="select">
					<?php
					$rstr = "";
					$stmt = "select bay1,bay2,bay3,bay4,bay5,bay6,bay7,bay8,bay9,bay10,bay11,bay12,bay13,bay14,bay15,bay16,bay17,bay18,bay19,bay20 from shopbays where shopid = '$shopid'";
					if ($query = $conn->prepare($stmt)){
					    $query->execute();
					    $query->store_result();
					    $num_roid_rows = $query->num_rows;
					    if ($num_roid_rows > 0){
					    	$query->bind_result($bay1,$bay2,$bay3,$bay4,$bay5,$bay6,$bay7,$bay8,$bay9,$bay10,$bay11,$bay12,$bay13,$bay14,$bay15,$bay16,$bay17,$bay18,$bay19,$bay20);
					    	$query->fetch();
				    		if (strlen($bay1) > 0){$rstr .= "<option value='a'>$bay1</option>";}
							if (strlen($bay2) > 0){$rstr .= "<option value='b'>$bay2</option>";}
							if (strlen($bay3) > 0){$rstr .= "<option value='c'>$bay3</option>";}
							if (strlen($bay4) > 0){$rstr .= "<option value='d'>$bay4</option>";}
							if (strlen($bay5) > 0){$rstr .= "<option value='e'>$bay5</option>";}
							if (strlen($bay6) > 0){$rstr .= "<option value='f'>$bay6</option>";}
							if (strlen($bay7) > 0){$rstr .= "<option value='g'>$bay7</option>";}
							if (strlen($bay8) > 0){$rstr .= "<option value='h'>$bay8</option>";}
							if (strlen($bay9) > 0){$rstr .= "<option value='i'>$bay9</option>";}
							if (strlen($bay10) > 0){$rstr .= "<option value='j'>$bay10</option>";}
							if (strlen($bay11) > 0){$rstr .= "<option value='k'>$bay11</option>";}
							if (strlen($bay12) > 0){$rstr .= "<option value='l'>$bay12</option>";}
							if (strlen($bay13) > 0){$rstr .= "<option value='m'>$bay13</option>";}
							if (strlen($bay14) > 0){$rstr .= "<option value='n'>$bay14</option>";}
							if (strlen($bay15) > 0){$rstr .= "<option value='o'>$bay15</option>";}
							if (strlen($bay16) > 0){$rstr .= "<option value='p'>$bay16</option>";}
							if (strlen($bay17) > 0){$rstr .= "<option value='q'>$bay17</option>";}
							if (strlen($bay18) > 0){$rstr .= "<option value='r'>$bay18</option>";}
							if (strlen($bay19) > 0){$rstr .= "<option value='s'>$bay19</option>";}
							if (strlen($bay20) > 0){$rstr .= "<option value='t'>$bay20</option>";}
				    		echo $rstr;
					    }else{
					    	echo "<option value='a'>BAY 1</option>"
								. "<option value='b'>BAY 2</option>"
								. "<option value='c'>BAY 3</option>"
								. "<option value='d'>BAY 4</option>"
								. "<option value='e'>BAY 5</option>"
								. "<option value='f'>BAY 6</option>"
								. "<option value='g'>BAY 7</option>";
	
					    }
					    $query->close();
					}else{
				    	echo "<option value='a'>BAY 1</option>"
							. "<option value='b'>BAY 2</option>"
							. "<option value='c'>BAY 3</option>"
							. "<option value='d'>BAY 4</option>"
							. "<option value='e'>BAY 5</option>"
							. "<option value='f'>BAY 6</option>"
							. "<option value='g'>BAY 7</option>";
					}
					?>
					</select>
					<label class="form-label select-label" for="addcolumn">Column</label>
				</div>

				<div class="form-outline mb-4">
					<input type="text" id="addlabor" class="form-control">
					<label class="form-label" for="addlabor">Labor</label>
				</div>

				<div class="form-outline mb-4">
					<input type="text" id="addhours" class="form-control">
					<label class="form-label" for="addhours">Hours</label>
				</div>

				<div class="form-outline mb-4 dtpicker">
					<input type="text" id="addsd" class="form-control dateTimePickerBS">
					<label class="form-label" for="addsd">Start Date / Time</label>
				</div>

				<div class="form-outline dtpicker">
					<input type="text" id="added" class="form-control dateTimePickerBS">
					<label class="form-label" for="added">End Date / Time</label>
				</div>
			</div>
			<div class="modal-footer d-flex justify-content-center">
				<button type="button" class="btn btn-primary" onclick="addEvent()">Save</button>
			</div>		
		</div>
	</div>
</div>

<script>
    var jq = $.noConflict();
    jq(document).ready(function () {

        jq('.dateTimePickerBS').datetimepickerbs({
            format: "MM/DD/YYYY hh:mm a",
            sideBySide: true,
        });
    });
</script>

<?php get_js_bs5(); ?>
<script defer src="<?= SCRIPT; ?>/sidenav.js"></script>

<?php  include "scripts.dispatch.php";?>

</body>

</html>
