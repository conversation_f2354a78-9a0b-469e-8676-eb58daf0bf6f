<!DOCTYPE html>
<html>
<?php
// error_reporting(0);
require CONN;

if (!defined('ASSETS')) define('ASSETS', "https://" . $_SERVER['SERVER_NAME'] . "/src/public/assets");
$shopid = $_COOKIE['shopid'];
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

if (isset($_GET['roid'])) {
    $roid = $_GET['roid'];
} else {
    $roid = 0;
}

if ($shopid != "10") {
    //header ("Location: https://www.sbpent.com/sbp/wip.php");
    //exit;
}

$stmt = "select companyname,companyphone,companyemail,schedulesendreminderdefault,calendardefault,carfaxlocation,companyaddress,companycity from company where shopid = '$shopid'";

if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    if ($num_roid_rows > 0) {
        $query->bind_result($shopname, $shopphone, $shopemail, $schedulesendreminderdefault, $calendardefault, $cfl, $companyaddress, $companycity);
        $query->fetch();
    } else {
        echo "error";
    }
    $query->close();
} else {
    die("Prepare failed: (" . $conn->errno . ") " . $conn->error);
}

$stmt = "select coalesce(MIN(START),'06:00:00'),coalesce(MAX(end),'20:00:00') FROM shophours WHERE shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->bind_result($minstart, $maxend);
    $query->fetch();
    $query->close();
}

$stmt = "select yearlabel,makelabel,modellabel,enginelabel,cylinderlabel,translabel,licenselabel,statelabel,fleetlabel,currmileagelabel,colorlabel,drivelabel,vinlabel from vehiclelabels where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($yearlabel, $makelabel, $modellabel, $enginelabel, $cylinderlabel, $translabel, $licenselabel, $statelabel, $fleetlabel, $currmileagelabel, $colorlabel, $drivelabel, $vinlabel);
    $query->fetch();
    $query->close();
}

if (empty($yearlabel)) $yearlabel = "Year";
if (empty($makelabel)) $makelabel = "Make";
if (empty($modellabel)) $modellabel = "Model";
if (empty($enginelabel)) $enginelabel = "Engine";
if (empty($cylinderlabel)) $cylinderlabel = "Cylinders";
if (empty($translabel)) $translabel = "Transmission";
if (empty($licenselabel)) $licenselabel = "License";
if (empty($statelabel)) $statelabel = "License State";
if (empty($fleetlabel)) $fleetlabel = "Fleet Number";
if (empty($currmileagelabel)) $currmileagelabel = "Current Mileage";
if (empty($colorlabel)) $colorlabel = "Color";
if (empty($drivelabel)) $drivelabel = "Drive Type";
if (empty($vinlabel)) $vinlabel = "VIN";

$remindermsg = "This is an appointment reminder from " . $shopname . "." . "\r\n" . "\r\n";
$remindermsg .= "You have an appointment on" . "\r\n" . "\r\n";
$remindermsg .= "\r\n" . "Additional Comments: " . "\r\n";

?>
<!--[if IE 9]>
<html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus"> <!--<![endif]-->

<head>
    <meta charset='utf-8'/>

    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href='<?= ASSETS ?>/calendar/lib/fullcalendar.min.css' rel='stylesheet'/>
    <link href='<?= ASSETS ?>/calendar/lib/fullcalendar.print.min.css' rel='stylesheet' media='print'/>
    <link href='<?= ASSETS ?>/calendar/scheduler.min.css' rel='stylesheet'/>
    <link href='<?= CSS ?>/bootstrap.min.css' rel='stylesheet'/>
    <link href='<?= SCRIPT ?>/plugins/jquery-ui/jquery-ui.min.css' rel='stylesheet'/>
    <link href='<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css' rel='stylesheet'/>
    <link href='<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.css' rel='stylesheet'/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">
    <script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
    <script src='<?= SCRIPT ?>/plugins/moment/moment.js'></script>
    <script src='<?= SCRIPT ?>/bootstrap.min.js'></script>
    <script src='<?= SCRIPT ?>/plugins/jquery-ui/jquery-ui.js'></script>
    <script src='<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js'></script>
    <script src='<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.js'></script>
    <script src='<?= SCRIPT ?>/plugins/jqtouch/touch.js'></script>
    <script src="<?= SCRIPT ?>/plugins/typeahead/typeahead_nomatch.js"></script>
    <title>Appointment Calendar</title>

    <script src='<?= ASSETS ?>/calendar/lib/fullcalendar.min.js'></script>
    <script src='<?= ASSETS ?>/calendar/scheduler.min.js'></script>


    <script>
        var shopIsReadOnly = <?= $shopIsReadOnly ? 'true' : 'false'; ?>;

        function skipSearch() {
            $('#addlastname').typeahead('destroy')
        }

        function preventDefault() {
            $('.fc-time-grid-event, .fc-v-event, .fc-event, .fc-start, .fc-end, .fc-draggable, .fc-resizable').click(function (e) {
                e.stopPropagation();
            });
        }

        $(function () { // document ready

            resid = ''

            resourcedata = [
                <?php
                $rstr = "";
                $stmt = "select colorhex,title from colorcoding where shopid = '$shopid'";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $query->store_result();
                    $c = 0;
                    $a = array('a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z');
                    $num_roid_rows = $query->num_rows;
                    if ($num_roid_rows > 0) {
                        $query->bind_result($colorhex, $title);
                        while ($query->fetch()) {
                            $currltr = $a[$c];
                            $title = addslashes($title);
                            $rstr .= "{ id: '$currltr', title: '$title' }," . chr(10) . chr(9) . chr(9) . chr(9) . chr(9);
                            $c = $c + 1;

                        }
                        echo $rstr;
                    }
                    $query->close();
                }
                ?>

            ]
            $('#calendar').fullCalendar({

                defaultView: '<?= $calendardefault?>',
                <?php
                if (isset($_GET['d'])){
                ?>
                defaultDate: '<?php echo $_GET['d']; ?>',
                <?php
                }else{
                ?>
                defaultDate: '<?php echo date("Y-m-d"); ?>',
                <?php
                }
                ?>
                minTime: "<?= $minstart?>",
                maxTime: "<?= $maxend?>",
                slotMinutes: 15,
                slotDuration: '00:15:00',
                slotLabelFormat: 'hh:mm',
                aspectRatio: 0.80,
                droppable: true,
                selectHelper: true,
                editable: !shopIsReadOnly,
                selectable: !shopIsReadOnly,
                allDaySlot: false,
                columnFormat: "ddd M/D/Y",
                //titleFormat: '[Appointment Schedule]',
                eventLimit: true, // allow "more" link when too many events
                customButtons: {
                    wipButton: {
                        text: 'Back to WIP',
                        click: function () {
                            location.href = '<?= COMPONENTS_PRIVATE ?>/wip/wip.php';
                        }
                    },
                    printbtn: {
                        text: 'Print View',
                        click: function () {
                            printView()
                        }
                    },
                    /*
                    printdaybtn: {
                        text: 'Print Day View',
                        click: function(){
                            printDayView()
                        }
                    }
                    */
                },
                header: {
                    left: 'wipButton,prev,next today printbtn',
                    center: 'title',
                    right: 'agendaDay,agendaTwoDay,agendaWeek,month'
                },

                views: {
                    agendaTwoDay: {
                        type: 'agenda',
                        duration: {days: 3},
                        groupByDateAndResource: true
                    },
                    agenda: {
                        titleFormat: 'dddd M/D/Y'
                    }
                },

                //// uncomment this line to hide the all-day slot
                //allDaySlot: false,

                resources: resourcedata,
                events: {
                    url: 'calendardata.php',
                    type: 'POST',
                    data: {
                        t: "getlist"
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },

                },

                /*select: function(start, end, jsEvent, view, resource) {
                    console.log(
                        'select',
                        start.format(),
                        end.format(),
                        resource ? resource.id : '(no resource)'
                    );
                },*/
                dayClick: function (date, jsEvent, view, resource) {
                    if ( !shopIsReadOnly ) {
                        if (view.name != "month" && view.name != "agendaWeek") {

                            if (localStorage.getItem("calendarroid") !== null) {
                                roid = localStorage.getItem("calendarroid")
                                $.ajax({
                                    data: "t=getroid&shopid=<?php echo $shopid; ?>&roid=" + roid,
                                    url: "calendardata.php",
                                    type: "post",
                                    success: function (r) {
                                        if (r.indexOf("|") !== -1) {
                                            rar = r.split("|")
                                            // $customerlast."|".$customerfirst."|".$cellphone."|".$vehyear."|".$vehmake."|".$vehmodel."|".$email."|".$customerid."|".$vehid."|".$compl."|".$hrs;
                                            ln = rar[0]
                                            fn = rar[1]
                                            cl = rar[2]
                                            yr = rar[3]
                                            mk = rar[4]
                                            md = rar[5]
                                            em = rar[6]
                                            cid = rar[7]
                                            vid = rar[8]
                                            svc = rar[9]
                                            hrs = rar[10]
                                            wri = rar[11]
                                            $('#addlastname').val(ln)
                                            $('#addfirstname').val(fn)
                                            $('#addcellphone').val(cl)
                                            $('#addyear').val(yr)
                                            $('#addmake').val(mk)
                                            $('#addmodel').val(md)
                                            $('#addemail').val(em)
                                            //$('#sendreminder').val('no')
                                            $('#addhours').val(hrs)
                                            $('#addservice').val(svc)
                                            $('#customerid').val(cid)
                                            $('#vehid').val(vid)
                                            if (wri != '') $("#sales option:contains(" + wri + ")").attr('selected', 'selected')
                                            temproid = $('#passedroid').val()
                                            console.log(temproid)
                                            $('#schroid').val(temproid)
                                            $('#passedroid').val('')

                                            localStorage.removeItem("calendarroid")
                                        }
                                    },
                                    error: function (xhr, ajaxOptions, thrownError) {
                                        console.log(xhr.status);
                                        console.log(xhr.responseText);
                                        console.log(thrownError);
                                    }
                                });

                            } else if (localStorage.getItem("calendarcvid") !== null) {
                                var cvarr = localStorage.getItem("calendarcvid").split('-')
                                var cid = cvarr[0]
                                var vid = cvarr[1]
                                console.log("t=getcvdetails&shopid=<?php echo $shopid; ?>&cid=" + cid + "&vid=" + vid)
                                $.ajax({
                                    data: "t=getcvdetails&shopid=<?php echo $shopid; ?>&cid=" + cid + "&vid=" + vid,
                                    url: "calendardata.php",
                                    type: "post",
                                    success: function (r) {
                                        if (r.indexOf("|") > 0) {
                                            rar = r.split("|")
                                            ln = rar[0]
                                            fn = rar[1]
                                            cl = rar[2]
                                            yr = rar[3]
                                            mk = rar[4]
                                            md = rar[5]
                                            em = rar[6]
                                            $('#addlastname').val(ln)
                                            $('#addfirstname').val(fn)
                                            $('#addcellphone').val(cl)
                                            $('#addyear').val(yr)
                                            $('#addmake').val(mk)
                                            $('#addmodel').val(md)
                                            $('#addemail').val(em)
                                            $('#addhours').val('')
                                            $('#addservice').val('')
                                            $('#customerid').val(cid)
                                            $('#vehid').val(vid)

                                            localStorage.removeItem("calendarcvid")
                                        }
                                    },
                                    error: function (xhr, ajaxOptions, thrownError) {
                                        console.log(xhr.status);
                                        console.log(xhr.responseText);
                                        console.log(thrownError);
                                    }
                                });

                            } else if (localStorage.getItem("calendarquoteid") !== null) {
                                quoteid = localStorage.getItem("calendarquoteid")
                                $.ajax({
                                    data: "t=getquoteid&shopid=<?php echo $shopid; ?>&quoteid=" + quoteid,
                                    url: "calendardata.php",
                                    type: "post",
                                    success: function (r) {
                                        if (r.indexOf("|") !== -1) {
                                            rar = r.split("|")
                                            ln = rar[0]
                                            fn = rar[1]
                                            cl = rar[2]
                                            yr = rar[3]
                                            mk = rar[4]
                                            md = rar[5]
                                            em = rar[6]
                                            cid = rar[7]
                                            vid = rar[8]
                                            svc = rar[9]
                                            hrs = rar[10]
                                            wri = rar[11]
                                            $('#addlastname').val(ln)
                                            $('#addfirstname').val(fn)
                                            $('#addcellphone').val(cl)
                                            $('#addyear').val(yr)
                                            $('#addmake').val(mk)
                                            $('#addmodel').val(md)
                                            $('#addemail').val(em)
                                            $('#addhours').val(hrs)
                                            $('#addservice').val(svc)
                                            $('#customerid').val(cid)
                                            $('#vehid').val(vid)

                                            localStorage.removeItem("calendarquoteid")
                                        }
                                    },
                                    error: function (xhr, ajaxOptions, thrownError) {
                                        console.log(xhr.status);
                                        console.log(xhr.responseText);
                                        console.log(thrownError);
                                    }
                                });

                            } else {

                                $('#addlastname').val('')
                                $('#addfirstname').val('')
                                $('#addcellphone').val('')
                                $('#addyear').val('')
                                $('#addmake').val('')
                                $('#addmodel').val('')
                                $('#addemail').val('')
                                //$('#sendreminder').val('no')
                                $('#addhours').val('')
                                $('#addservice').val('')
                                $('#customerid').val(0)
                                $('#vehid').val(0)
                                $('#btn-addveh').hide()
                            }

                            sd = date.format("MM/DD/YYYY HH:mm")

                            $.ajax({
                                data: "t=checkdt&shopid=<?php echo $shopid; ?>&dt=" + sd,
                                url: "calendardata.php",
                                type: "post",
                                success: function (r) {
                                    if (r == 'yes') $('#addmodal').modal('show'); else swal("Appointment time is outside the shop hours")
                                }
                            })


                            $('#schroid').val('')
                            $('#addsd').val(sd)

                            $("#addcolumn option:contains(" + resource.title + ")").attr('selected', 'selected');
                            setTimeout(function () {
                                $('#addlastname').focus()
                            }, 500)

                            $('#addlastname').autocomplete({
                                source: 'customersearch.php', minLength: 2,
                                select: function (event, ui) {
                                    tar = ui.item.orival.split("|")

                                    c = $.trim(tar[0])

                                    ctar = c.split("~")
                                    fleetno = ctar[0]
                                    cid = ctar[1]
                                    vid = ctar[2]
                                    onhold = ctar[3]

                                    n = $.trim(tar[1])
                                    ntar = n.split(",")
                                    ln = $.trim(ntar[0])
                                    fn = $.trim(ntar[1])
                                    em = $.trim(tar[3])
                                    ph = $.trim(tar[4])
                                    yr = $.trim(tar[5])
                                    mk = $.trim(tar[6])
                                    md = $.trim(tar[7])

                                    $('#addlastname').val(ln)
                                    $('#addfirstname').val(fn)
                                    $('#addcellphone').val(ph)
                                    $('#addyear').val(yr)
                                    $('#addmake').val(mk)
                                    $('#addmodel').val(md)
                                    $('#addemail').val(em)
                                    $('#customerid').val(cid)
                                    $('#vehid').val(vid)
                                    $('#addservice').focus()
                                    if (cid != '' && cid > 0)
                                        $('#btn-addveh').show()
                                    return false
                                }
                            });


                            setTimeout(function () {
                                $('#addroid').focus()
                                $('#addsd').datetimepicker({
                                    format: 'MM/DD/YYYY HH:mm',
                                    defaultDate: new Date(sd),
                                    sideBySide: true
                                });
                                $('.typeahead.dropdown-menu').css("max-height", "50px;").css("overflow-y", "scroll")
                                $('#added').datetimepicker({
                                    format: 'YYYY-MM-DD HH:mm',
                                    //useCurrent: true,
                                    sideBySide: false
                                });
                            }, 500)
                        } else {
                            if (view.name == "agendaWeek") {
                                // _i: (7) [2018, 0, 30, 6, 45, 0, 0]
                                tar = date._i
                                day = tar[1]
                                day = day + 1
                                newdateformat = moment(tar[0] + "-" + day + "-" + tar[2])
                                //console.log(newdateformat)
                                $('#calendar').fullCalendar('changeView', 'agendaDay');
                                //$('#calendar').fullCalendar('gotoDate',newdateformat)
                                $('#calendar').fullCalendar('gotoDate', date.format("MM/DD/YYYY 00:00:00"))
                                //swal("Please switch to Day View or 3 Day View to add a new appointment")
                            }
                            if (view.name == "month") {
                                //console.log(date.format("MM/DD/YYYY"))
                                $('#calendar').fullCalendar('changeView', 'agendaDay');
                                $('#calendar').fullCalendar('gotoDate', date.format("MM/DD/YYYY 00:00:00"))
                                //swal("Please switch to Day View or 3 Day View to add a new appointment")
                            }


                        }
                    }
                },


                eventClick: function (calEvent, jsEvent, view) {
                    if (shopIsReadOnly) return;

                    //setTimeout(function(){
                    //console.log("customerstatus:"+$('#customerstatus').val())
                    $('#statusid').val(calEvent.id)
                    if ($('#customerstatus').val() == "") {
                        $('.btn-mark').hide()
                        if (calEvent.schflag == 'complete') $('#btn-undone').show();
                        else $('#btn-done').show();
                        $('#editmodal').modal('show')
                        $('#editmodalheader').css("backgroundColor", calEvent.backColor)
                        $('#editservice').val(calEvent.service)
                        $('#editfirstname').val(calEvent.firstname)
                        $('#editlastname').val(calEvent.lastname)
                        $('#edithours').val(calEvent.hrs)
                        $('#edityear').val(calEvent.year)
                        $('#editmake').val(calEvent.make)
                        $('#editmodel').val(calEvent.model)
                        $('#editid').val(calEvent.id)
                        $('#editcustomerid').val(calEvent.customerid)
                        $('#editemail').val(calEvent.email)
                        $('#editcellphone').val(calEvent.cellphone)
                        $('#editsd').val(calEvent.start.format("MM/DD/YYYY HH:mm"))
                        $('#editschroid').val('')
                        schroid = calEvent.schroid
                        $('#editschroid').val(schroid)
                        $('#schroid').val(schroid)

                        $('#editaddress').val(calEvent.customer_address)
                        $('#editcity').val(calEvent.customer_city)
                        $('#editstate').val(calEvent.customer_state)
                        $('#editzip').val(calEvent.customer_zip)


                        if (typeof calEvent.empname !== 'undefined' && calEvent.empname != '')
                            $('#salesperson').html("<b>SALES PERSON:</b> " + calEvent.empname)
                        else
                            $('#salesperson').html("")

                        if (calEvent.schsource == 'O.A.S.' && calEvent.linkedori == '') {
                            $('#editrollspan').show()
                            $('#editrollcheck').prop('checked', false)
                        } else {
                            $('#editrollspan').hide()
                            $('#editrollcheck').prop('checked', false)
                        }
                        if (calEvent.linkedori != '') {
                            $('#linkedori').html(calEvent.linkedori)
                            $('#linkedori').show()
                        } else
                            $('#linkedori').hide()
                        determiner = parseFloat(schroid) >= 1000
                        if (determiner === true) {
                            $('#converttorobutton').hide();
                            $('#gotorobutton').show();
                        } else {
                            $('#converttorobutton').show();
                            $('#gotorobutton').hide();
                        }
                        $('#historybutton').hide()
                        hsd = $.fullCalendar.formatDate(calEvent.start, "YYYY-MM-DD HH:mm:ss")
                        // geting non 24hr
                        $('#editsd2').val(calEvent.start.format("MM/DD/YYYY hh:mm A"))
                        hsd2 = $.fullCalendar.formatDate(calEvent.start, "YYYY-MM-DD hh:mm:ss A")

                        // get the customer and vehicleid to decide whether to show the button for
                        // history or not

                        $.ajax({
                            data: "t=getcidvid&shopid=<?php echo $shopid; ?>&id=" + calEvent.id,
                            url: "calendardata.php",
                            type: "post",
                            success: function (r) {
                                rar = r.split("|")
                                cid = rar[0]
                                vid = rar[1]
                                onhold = rar[2]
                                if ($.isNumeric(cid) && $.isNumeric(vid)) {
                                    if (cid != 0 && vid != 0) {
                                        $('#historycid').val(cid)
                                        $('#historyvid').val(vid)
                                        $('#onhold2').val(onhold)
                                        $('#historybutton').show()
                                    }
                                }
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                            }
                        })

                        $('#calid').val(calEvent.id)
                        $('#customerstatus').val('')


                        $('#editsd').datetimepicker({
                            inline: false,
                            defaultDate: new Date(hsd),
                            format: 'MM/DD/YYYY HH:mm',
                            useCurrent: false,
                            sideBySide: true
                        });


                        $('#editsd2').datetimepicker({
                            inline: false,
                            defaultDate: new Date(hsd2),
                            format: 'MM/DD/YYYY hh:mm A ',
                            useCurrent: false,
                            sideBySide: true
                        });


                    } else {
                        $('#statusid').val(calEvent.id)
                        console.log($('#statusid').val() + "|" + $('#customerstatus').val())
                        if ($('#customerstatus').val() == "arrived") {
                            finishedCustomer()
                        } else if ($('#customerstatus').val() == "waiting") {
                            waitingCustomer()
                        } else if ($('#customerstatus').val() == "finished") {
                            customerArrived()
                        } else if ($('#customerstatus').val() == "noshow") {
                            customerNoShow()
                        }
                    }
                    //},200)

                },

                eventRender: function (event, element) {
                    var title = element.find('.fc-title, .fc-list-item-title');
                    var isdone = event.done
                    if (isdone == "no") {
                        title.html("<i id='customerArrived' onclick='setCustomerStatus(\"arrived\")' style='font-size:medium;border:1px white solid;padding:3px;background-color:white;color:red;' class='fa fa-hourglass'></i> " + title.text());
                    } else if (isdone == "finished") {
                        title.html("<i id='customerFinished' onclick='setCustomerStatus(\"finished\")' style='font-size:medium;border:1px white solid;padding:3px;background-color:white;color:blue;' class='fa fa-thumbs-up'></i> " + title.text());
                    } else if (isdone == "yes") {
                        title.html("<i id='waitingCustomer' onclick='setCustomerStatus(\"noshow\")' style='font-size:large;border:1px white solid;padding:3px;background-color:white;color:green' class='fa fa-check-square'></i> " + title.text());
                    } else if (isdone == "noshow") {
                        title.html("<i id='customernoshow' onclick='setCustomerStatus(\"waiting\")' style='font-size:large;border:1px white solid;padding:3px;background-color:white;color:black' class='fa fa-eye-slash'></i> " + title.text());
                    }
                    var schflag = event.schflag
                    if (schflag == "complete") {
                        element.css('color', 'white')
                        element.css('background-color', '#001a00')
                    }

                    if (event.schcolor != '')
                        element.css('background-color', event.schcolor)

                    var timespan = element.find('.fc-time span');
                    if (event.schflag == 'complete') timespan.append(" (DONE)")
                },

                eventDragStart: function (event) {
                    resid = event.resourceId
                },

                eventDrop: function (event, delta, revertFunc, oldEvent) {

                    sd = event.start.format()
                    ed = event.end.format()
                    id = event.id
                    rid = event.resourceId
                    hrs = event.hrs
                    colorcode = event.colorcode

                    if (event.linkedori != '' && resid != event.resourceId) {
                        swal({
                                title: "Confirmation",
                                text: "All future appointments for this job will follow into the bay you are moving it to. Do you still want to continue?",
                                type: "warning",
                                showCancelButton: true,
                                confirmButtonClass: "btn-success",
                                confirmButtonText: "Yes",
                                cancelButtonClass: "btn-danger",
                                cancelButtonText: "No",
                                closeOnConfirm: true,
                                closeOnCancel: true
                            },
                            function (isConfirm) {
                                if (isConfirm) {
                                    moveevent(sd, id, rid, colorcode, hrs)
                                } else
                                    revertFunc()
                            })

                    } else
                        moveevent(sd, id, rid, colorcode, hrs)


                },
                viewRender: function (view) {
                    var axis = $('.fc-axis');
                    var currview = $('#calendar').fullCalendar('getView')
                    if (view.type == "agendaDay") {

                        // get the total hours for the day
                        todaydate = $('#calendar').fullCalendar('getDate');
                        todaydate = moment(todaydate._d)
                        todaydate = moment(todaydate).add(1, 'days');
                        todaydate = todaydate.format("MM/DD/YYYY")

                        ds = "shopid=<?php echo $shopid; ?>&d=" + todaydate + "&t=getdaytotal"
                        //console.log(ds)
                        $.ajax({
                            data: ds,
                            url: "calendardata.php",
                            type: "post",
                            success: function (r) {
                                if ($.isNumeric(r)) {
                                    r = parseFloat(r)
                                    result = r.toFixed(2)
                                    $('.fc-center h2').css("font-size", "x-large").css("font-weight", "bold").append("<div class='schtitle' style='font-size:medium;padding-top:2px;font-weight:normal'>(" + result + " Scheduled Hours)</span>")
                                    //$('body').append("<div style='width:150px;position:absolute;top:0px;left:0px;color:black;background-color:white;border:1px silver solid;border-radius:5px;text-align:center;padding:5px;font-weight:bold'>"+result+" Total Hours</div>")
                                }
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                            }
                        });

                    } else if (view.type == "agendaTwoDay") {
                        // get the date range displayed in
                        dr = $('.fc-center h2').html()
                        tar = dr.split(" ")
                        ed = new Date(tar[4])
                        sed = new Date(tar[4])
                        ed = moment(ed)
                        sed = moment(sed)
                        sd = ed.subtract(2, "days")

                        // now get the totals for each day in the range
                        ds = "shopid=<?php echo $shopid; ?>&sd=" + sd.format("MM/DD/YYYY") + "&ed=" + sed.format("MM/DD/YYYY") + "&t=get3daytotal"
                        console.log(ds)
                        $.ajax({
                            data: ds,
                            url: "calendardata.php",
                            type: "post",
                            success: function (r) {
                                rar = r.split("|")
                                fval = rar[0]
                                fdar = fval.split("~")
                                fdate = fdar[0]
                                fhrs = fdar[1]

                                sval = rar[1]
                                sdar = sval.split("~")
                                sdate = sdar[0]
                                shrs = sdar[1]

                                tval = rar[2]
                                tdar = tval.split("~")
                                tdate = tdar[0]
                                thrs = tdar[1]

                                ths = document.getElementsByTagName("th")
                                n = 1
                                $.each(ths, function () {
                                    if ($(this).hasClass("fc-day-header") && $(this).attr("data-date") == fdate) {
                                        $(this).append(" (" + fhrs + " hrs)")
                                    }
                                    if ($(this).hasClass("fc-day-header") && $(this).attr("data-date") == sdate) {
                                        $(this).append(" (" + shrs + " hrs)")
                                    }
                                    if ($(this).hasClass("fc-day-header") && $(this).attr("data-date") == tdate) {
                                        $(this).append(" (" + thrs + " hrs)")
                                    }

                                })
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                            }
                        });


                    }
                    $('#currentview').val(view.type)
                    for (var i = 0; i < axis.length; i++) {
                        var element = axis[i];
                        var p = element.parentElement;
                        var n = element.cloneNode(true);
                        p.appendChild(n);
                    }
                },
                eventResize: function (event, jsEvent, ui, view) {
                    ed = event.end.format()
                    sd = event.start.format()
                    id = event.id

                    // post it with ajax, then
                    $.ajax({
                        type: "post",
                        data: "t=changeevent&id=" + id + "&end=" + ed + "&start=" + sd,
                        url: "calendardata.php",
                        success: function (r) {
                            console.log(r)
                            if (r != "success") {
                                swal(r);
                            }
                            $('#calendar').fullCalendar('refetchEvents');
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        },
                    });

                },

            });

            //console.log($('.fc-wipButton-button.fc-button.fc-state-default.fc-corner-left').html())
            $('.fc-wipButton-button.fc-button.fc-state-default.fc-corner-left').addClass('btn-danger').addClass('btn')

        });

        function resizeCalendar(calendarView) {
            //console.log(calendarView.name)
            if (calendarView.name === 'agendaWeek' || calendarView.name === 'agendaDay') {
                // if height is too big for these views, then scrollbars will be hidden
                calendarView.setHeight(9999);
            }
        }


    </script>

    <style>

        body {
            margin: 0;
            padding: 0;
            font-family: "Lucida Grande", Helvetica, Arial, Verdana, sans-serif;
            font-size: 14px;
        }

        .fc-nonbusiness {
            opacity: 1;
        }


        .fc-license-message {
            display: none
        }

        [data-resource-id=a] {
            background-color: #006699;
            opacity: .05
        }

        [data-resource-id=b] {
            background-color: #b36b00;
            opacity: .05
        }

        [data-resource-id=c] {
            background-color: #006600;
            opacity: .05
        }

        [data-resource-id=d] {
            background-color: #800000;
            opacity: .05
        }

        [data-resource-id=e] {
            background-color: #337ab7;
            opacity: .05
        }

        [data-resource-id=f] {
            background-color: #4d004d;
            opacity: .05
        }

        [data-resource-id=g] {
            background-color: #b38600;
            opacity: .05
        }

        [data-resource-id=h] {
            background-color: #666633;
            opacity: .05
        }

        [data-resource-id=i] {
            background-color: #5c5c8a;
            opacity: .05
        }

        [data-resource-id=j] {
            background-color: #5c8a8a;
            opacity: .05
        }

        [data-resource-id=k] {
            background-color: #808080;
            opacity: .05
        }

        [data-resource-id=l] {
            background-color: #660033;
            opacity: .05
        }

        [data-resource-id=m] {
            background-color: #c6538c;
            opacity: .05
        }

        [data-resource-id=n] {
            background-color: #e68a00;
            opacity: .05;
        }

        [data-resource-id=o] {
            background-color: #ff66cc;
            opacity: .05;
        }

        .modal-large {
            width: 80%; /* respsonsive width */
            margin-left: 10%; /* width/2) */
        }

        .fc-day-header:nth-child(even) {
            background-color: #CCCCCC;
            padding: 6px;
        }

        .fc-day-header:nth-child(odd) {
            background-color: #808080;;
            padding: 6px;
            color: white
        }

        th.fc-resource-cell {
            padding: 5px;
            opacity: .95;
            color: white;
            text-shadow: 0px 0px 3px rgba(0, 0, 0, 1);
        }

        #editmodalheader {
            background-color: #002F5E;
            color: white;
            border-radius: 3px;
        }


        .modal-content {
            border-radius: 3px
        }

        .fc-content {
            font-size: 9pt;
            color: white
        }

        .fc-time > span {
            font-size: 9pt;
        }

        [data-time].fc-minor {
            border-bottom: 1px gray solid
        }

        [data-time]:hover {
            background-color: #FFFFCC
        }

        #external-events {
            margin-top: 5px;

        }

        #external-events h4 {
            font-size: 14px;
        }

        #external-events .fc-event {
            margin: 10px 0px 10px 0px;
            cursor: pointer;
            padding: 7px 10px 7px 10px;

        }

        .highlighted {
            background-color: yellow;
            font-weight: bold;
            color: black;
            font-size: medium;
            padding: 2px;
            z-index: 9999999
        }

        .smallbutton {

            width: 20px;
            height: 15px;
        }

        .bigger {
        }

        .trash {
            padding: 2px 10px 2px 10px;
            border: 3px red dashed;
            width: 45%;
            height: 40px;
            display: block;
            float: right;
            text-align: center;
            font-size: x-large;
            color: red;
            border-radius: 3px;
        }

        .trash-hover {
            background-color: red;
            color: white;

        }

        .csv {
            display: none
        }

        .h3 {
            font-size: medium;
            font-weight: bold
        }

        #calendar {
            max-height: 100%;
        }

        #unscheduled {
            border: 1px silver dashed;
            min-height: 500px;
            padding: 5px;
            border-radius: 4px;
        }

        .unscheduled-hover {
            background-color: #F3F3F3;
            border: 2px black dashed;
        }

        h4 {
            font-weight: bold
        }

        #external-events .fc-event:nth-child(even) {
            background-color: #CCCCCC;
            padding: 6px;
            color: black
        }

        #external-events .fc-event:nth-child(odd) {
            background-color: #808080;;
            padding: 6px;

        }

        td {
            cursor: pointer
        }

        .my-btn1 {
            background: #0099cc;
            color: #ffffff;
        }

        .my-btn2 {
            background: #00cc99;
            color: #ffffff;
        }


        .auto-style1 {
            color: black;
            background-color: #FFFF00;
        }

        .modal {

            overflow: auto !important;
        }

        .ui-autocomplete {
            z-index: 3000 !important;
            width: 800px;
            height: 400px;
            overflow-y: scroll;
        }

    </style>
</head>
<body style="padding:0px;margin:0px;">
<?php include(COMPONENTS_PRIVATE_PATH . "/shared/analytics.php"); ?>
<input type="hidden" id="viewtype">
<input type="hidden" id="laborid">
<div style="padding:0px;margin:0px;" class="container-fluid">
    <div style="padding:0px;margin:0px;" class="row">
        <div style="padding:0px;margin:0px;" class="col-md-12">
            <div style="padding:0px;margin:0px;" class="row">
                <div style="padding:0px;margin:0px;" class="col-md-12">
                    <div style="padding:4px;text-align:center;color:white;background-color:#3C8DBC"><h3>Appointment Calendar</h3>
                        <span style="font-size:medium">To add a calendar item, click a time slot.&nbsp; You
							can then search for a customer by last name, first
							name or by phone number.<br>
							<strong><span class="auto-style1">NEW STATUSES:</span> </strong><i id='customerArrived' style='font-size:medium;border:1px white solid;padding:3px;background-color:white;color:red;' class='fa fa-hourglass'></i> = Waiting for Customer to Arrive  <i id='customerArrived' style='margin-left:20px;font-size:medium;border:1px white solid;padding:3px;background-color:white;color:blue;' class='fa fa-thumbs-up'></i> = Customer has Arrived&nbsp;&nbsp;&nbsp;
							<i id='customerArrived' style='margin-left:20px;font-size:medium;border:1px white solid;padding:3px;background-color:white;color:green;' class='fa fa-check-square'></i> = Work is Complete&nbsp;&nbsp;&nbsp;<i id='customernoshow' style='margin-left:20px;font-size:medium;border:1px white solid;padding:3px;background-color:white;color:black;' class='fa fa-eye-slash'></i> = Customer No Show</span>
                        <div style="position:absolute;top:0px;right:0px;width:410px;border:1px silver solid;border-radius:4px;padding:10px;background-color:white;color:black">
                            Search by year, make, model, first name or last name
                            <input type="text" style="color:black;padding:3px;border:1px silver solid;border-radius:3px; width: 300px;" placeholder="Search appointments by name or vehicle info" id="search-term">
                            <span onclick="searchAppt()" class="btn btn-sm btn-warning">Search</span>
                        </div>
                    </div>
                    <div id='calendar'></div>
                </div>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="currentview"><input type="hidden" id="hsd"><input type="hidden" id="hed">


<div id="editmodal" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <input type="hidden" id="editsd2" value="">
        <input type="hidden" id="historycid" value="">
        <input type="hidden" id="historyvid" value="">
        <input type="hidden" id="onhold2" value="">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" id="editmodalheader">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Edit Event</h4>
            </div>
            <div class="modal-body">
                <div id="eventinfo">
                    <div class="row">
                        <div class="col-md-6">
                            <input type="hidden" id="editid"><input type="hidden" id="editcustomerid">
                            <b>First Name:</b>
                            <input style="text-transform:uppercase" type="text" id="editfirstname" class="form-control"><br>
                            <b>Last Name:</b>
                            <input style="text-transform:uppercase" type="text" id="editlastname" class="form-control"><br>

                            <div class="hidden">
                                <b>Address:</b>
                                <input style="text-transform:uppercase" type="text" id="editaddress" class="form-control"><br>
                                <div class="row">
                                    <div class="col-md-6">
                                <b>City:</b>
                                <input style="text-transform:uppercase" type="text" id="editcity" class="form-control">
                                    </div>
                                    <div class="col-md-6">
                                <b>State:</b>
                                <input style="text-transform:uppercase" type="text" id="editstate" class="form-control"><br>
                                    </div>
                                </div>
                                <b>Zip:</b>
                                <input style="text-transform:uppercase" type="text" id="editzip" class="form-control"><br>
                            </div>
                            <b>Year:</b>
                            <input style="text-transform:uppercase" type="text" id="edityear" class="form-control"><br>
                            <b>Make:</b>
                            <input style="text-transform:uppercase" type="text" id="editmake" class="form-control"><br>
                            <b>Model:</b>
                            <input style="text-transform:uppercase" type="text" id="editmodel" class="form-control"><br>
                            <b>RO #:</b>
                            <input style="text-transform:uppercase" readonly type="text" id="editschroid" class="form-control"><br>
                            <span id="salesperson"></span>
                        </div>
                        <div class="col-md-6">
                            <b>Service:</b>
                            <textarea style="text-transform:uppercase;height:110px;" id="editservice" class="form-control"></textarea><br>
                            <b>Hours:</b>
                            <input style="text-transform:uppercase" type="text" id="edithours" class="form-control"><br>
                            <b>Email:</b>
                            <input style="text-transform:uppercase" type="text" id="editemail" class="form-control"><br>
                            <b>Cell Phone:</b>
                            <input style="text-transform:uppercase" type="text" id="editcellphone" class="form-control"><br>
                            <b>Date /
                                Time:</b>&nbsp;&nbsp;&nbsp;&nbsp;<span id="editrollspan" style="display: none;"><input type="checkbox" id="editrollcheck" value="1">&nbsp;<small>Auto Roll Over</small></span>
                            <input style="text-transform:uppercase" type="text" id="editsd" class="form-control">
                            <div id="linkedori" style="display: none; padding-top: 10px; font-style: italic; font-size: smaller;"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">

                <?php if ($_COOKIE['createro'] == 'yes') {
                    ?>
                    <button type="button" class="btn btn-warning" style="float:left" id="converttorobutton" onclick="convertToRO()">Convert to RO</button>
                    <?php
                }
                ?>


                <!-- <button type="button" class="btn btn-warning" style="float:left" id="converttorobutton" onclick="convertToRO()">Convert to RO</button> -->
                <button type="button" class="btn btn-warning" style="float:left;display:none" id="gotorobutton" onclick="goToRO()">Go to RO</button>
                <button type="button" class="btn btn-primary" style="float:left;display:none" id="historybutton" onclick="viewHistory()">View History</button>
                <button type="button" class="btn btn-success" style="float:left" onclick="showReminder('<?php echo str_replace("'", "\'", $shopname); ?>','<?php echo $shopphone; ?>')">Send Reminder
                </button>
                <button type="button" class="btn btn-default" onclick="noReminders()">No Reminders</button>
                <button type="button" class="btn btn-info btn-mark" id="btn-done" style="display:none" onclick="markDone('complete')">Mark Done</button>
                <button type="button" class="btn btn-info btn-mark" id="btn-undone" style="display:none" onclick="markDone('incomplete')">Mark Not Done</button>
                <button type="button" class="btn btn-danger" onclick="delEvent()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveEvent()">Save</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>

<div id="sendremindermodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" id="editmodalheader">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Send Reminder</h4>
            </div>
            <div class="modal-body">
                <div id="info">
                    <div class="row">

                        <div class="col-md-12">
                            <h4>This will send a reminder to your customer regarding their upcoming appointment</h4>
                            <br><br>
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <label for="updateemailto">Email Address for Email</label>
                                    <input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" id="updateemailto" name="updateemailto" value="" type="text">
                                </div>
                            </div>
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <label for="updatecellphone">Cell Phone</label>
                                    <input class="form-control sbp-form-control" type="text" style="padding:20px;" tabindex="1" value="" id="updatecellphone" name="updatecellphone">
                                </div>
                            </div>
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <label for="msgsubject">Email Subject</label>
                                    <input class="form-control sbp-form-control" type="text" style="padding:20px;" tabindex="1" value="A friendly appointment reminder from <?php echo ucwords($shopname); ?>" id="msgsubject" name="msgsubject">
                                </div>
                            </div>
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <label for="updateremindermsg">Message</label>
                                    <textarea class="form-control sbp-form-control" type="text" style="padding:20px;height:250px" tabindex="1" value="" id="updateremindermsg" name="updateremindermsg"></textarea>
                                </div>
                            </div>
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <label for="updateemail">Update Email in Appointment</label>
                                    <input type="checkbox" style="padding:20px;" tabindex="1" value="on" id="updateemail" name="updateemail">
                                </div>
                            </div>
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <label for="updatecell">Update Cell in Appointment</label>
                                    <input type="checkbox" style="padding:20px;" tabindex="1" value="on" id="updatecell" name="updatecell">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-md btn-info" type="button" onclick="sendReminder('email')">Send Email Update</button>
                <button class="btn btn-md btn-primary" type="button" onclick="sendReminder('text')">Send Text Message</button>
                <button class="btn btn-md btn-warning" type="button" onclick="sendReminder('both')">Send Both</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>

    </div>
</div>

<div id="searchmodal" class="modal fade" role="dialog">
    <div class="modal-dialog modal-large">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Search Results</h4>
            </div>
            <div class="modal-body">
                <h4>Click an appointment to go to that day</h4>
                <table id="srchresults" class="table table-condensed table-striped table-header-bg">
                    <thead>
                    <tr style="color:white;background-color:#336699">
                        <td>Customer</td>
                        <td>Vehicle</td>
                        <td>Date/Time</td>
                        <td>Reason</td>
                    </tr>
                    </thead>
                    <tbody>

                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>

<div id="addmodal" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <input type="hidden" id="customerid" value="0">
        <input type="hidden" id="vehid" value="0">
        <input type="hidden" id="onhold" value="0">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" id="editmodalheader">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Add Event</h4>
            </div>
            <div class="modal-body">
                <div id="eventinfo">
                    <div class="row">
                        <div style="margin:20px;">You can search by last name, first name or phone number.</div>
                        <span class="btn btn-danger" style="position:absolute;top:1px;right:1px" onclick="skipSearch()">Skip Search</span>
                        <div class="col-md-6">
                            <b>Column:</b>
                            <select id="addcolumn" class="form-control">
                                <?php
                                $rstr = "";
                                $stmt = "select colorhex,title from colorcoding where shopid = '$shopid'";
                                if ($query = $conn->prepare($stmt)) {
                                    $query->execute();
                                    $query->bind_result($colorhex, $title);
                                    while ($query->fetch()) {
                                        echo "<option value='$colorhex'>$title</option>";
                                    }
                                }
                                ?>
                            </select>
                            <br>
                            <b>Last Name:</b>
                            <input style="text-transform:uppercase" type="text" id="addlastname" list="customerlist" class="form-control" placeholder="Search by last name,first name or phone"><br>
                            <datalist id="customerlist"></datalist>
                            <b>First Name:</b>
                            <input style="text-transform:uppercase" type="text" id="addfirstname" class="form-control"><br>
                            <b>Email:</b>
                            <input style="text-transform:uppercase" type="text" id="addemail" class="form-control"><br>
                            <b>Cell Phone:</b>
                            <input style="text-transform:uppercase" type="text" id="addcellphone" class="form-control" placeholder=""><br>
                            <b>Year:</b>
                            <input style="text-transform:uppercase" type="text" id="addyear" class="form-control"><br>
                            <b>Make:</b>
                            <input style="text-transform:uppercase" type="text" id="addmake" class="form-control"><br>
                            <?php
                            $stmt = "select id from employees where shopid = '$shopid' and active = 'yes' and color!='' and color is not null";
                            if ($query = $conn->prepare($stmt)) {
                                $query->execute();
                                $query->store_result();
                                $num_empcolor = $query->num_rows;
                                $query->close();
                            }

                            if ($num_empcolor > 0) {
                                ?>
                                <b>Sales Person:</b>
                                <select class="form-control" id="sales">
                                    <option value="">Select</option>
                                    <?php
                                    $stmt = "select id, employeelast, employeefirst from employees where shopid = ? and active = 'yes'";
                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("s", $shopid);
                                        $query->execute();
                                        $query->bind_result($empid, $emplast, $empfirst);
                                        while ($query->fetch())
                                            echo "<option value='" . $empid . "' " . (isset($_COOKIE['empid']) && $_COOKIE['empid'] == $empid ? 'selected' : '') . ">" . $empfirst . ' ' . $emplast . "</option>";
                                    }
                                    ?>
                                </select><br>
                            <?php } else {
                                ?>
                                <input type="hidden" id="sales" value="">
                            <?php } ?>
                        </div>
                        <div class="col-md-6">
                            <b>Model:</b>
                            <input style="text-transform:uppercase" type="text" id="addmodel" class="form-control"><br>
                            <b>Service:</b>
                            <textarea style="text-transform:uppercase;height:110px;" id="addservice" class="form-control"></textarea><br>
                            <b># Hours:</b>
                            <input type="text" id="addhours" class="form-control"><br>
                            <b>Start Date / Time</b>&nbsp;&nbsp;&nbsp;&nbsp;<small><input type="checkbox" id="rollcheck" value="1">&nbsp;Auto Roll Over</small><br>
                            <input type="text" id="addsd" class="form-control"><br>
                            <b>Send Reminder</b><br>
                            <select onchange="reminderSet(this.value)" class="form-control" id="sendreminder">
                                <option <?php if (strtolower($schedulesendreminderdefault) == "no") {
                                    echo "selected='selected'";
                                } ?> value="no">No
                                </option>
                                <option <?php if (strtolower($schedulesendreminderdefault) == "text") {
                                    echo "selected='selected'";
                                } ?> value="sms">Text Message
                                </option>
                                <option <?php if (strtolower($schedulesendreminderdefault) == "email") {
                                    echo "selected='selected'";
                                } ?> value="email">Email
                                </option>
                                <option <?php if (strtolower($schedulesendreminderdefault) == "both") {
                                    echo "selected='selected'";
                                } ?> value="both">Both
                                </option>
                            </select><br>
                            <b>RO #</b><br>
                            <input type="text" id="schroid" value="" class="form-control"><br>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning pull-left" style="display:none;" id="btn-addveh" onclick="openAddVeh()">Add new Vehicle</button>
                <button type="button" class="btn btn-primary" onclick="addEvent()">Save</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>


<div id="addvehmodal" class="modal fade" role="dialog">
    <div class="modal-dialog modal-sm">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" id="editmodalheader">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Add Vehicle</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <b>VIN: <a href="javascript:void(null)" onclick="decodeVIN()"><small>(Decode
                                    VIN)</small></a>&nbsp;&nbsp;<i class="fa fa-spinner fa-spin" id="vehspinner" style="display:none;"></i></b>
                        <input style="text-transform:uppercase" type="text" id="vehvin" class="form-control"><br>
                        <b>Year:</b>
                        <input style="text-transform:uppercase" type="text" id="vehyear" class="form-control"><br>
                        <b>Make:</b>
                        <input style="text-transform:uppercase" type="text" id="vehmake" class="form-control"><br>
                        <b>Model:</b>
                        <input style="text-transform:uppercase" type="text" id="vehmodel" class="form-control">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-md" onclick="addVehicle()">Save</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>

<div id="custmodal" class="modal fade" role="dialog">
    <div class="modal-dialog">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" id="editmodalheader">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Add Event</h4>
                <p>If your customer and vehicle are listed here, click them to select.</p>
            </div>
            <div class="modal-body">
                <table class="table table-condensed table-striped table-hover">
                    <thead>
                    <tr>
                        <td>First</td>
                        <td>Last</td>
                        <td>Address</td>
                        <td>City</td>
                        <td>State</td>
                        <td>Zip</td>
                    </tr>
                    </thead>
                    <tbody id="custlist">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="customerstatus" value="">
<input type="hidden" id="statusid" value="">
<input type="hidden" id="passedroid" value="<?php echo $roid; ?>">


<script src="<?= SCRIPT ?>/emodal.js?v=6.1"></script>


<script>

    function viewHistory() {

        cid = $('#historycid').val()
        vid = $('#historyvid').val()
        eModal.iframe({
            title: 'Vehicle History - Click any line for more detail',
            url: 'historyclosed.php?shopid=<?php echo $shopid; ?>&cid=' + cid + '&vid=' + vid,
            size: eModal.size.xl,
            buttons: [
                {text: 'Close', style: 'warning', close: true}

            ]

        });


    }

    function FormatPhoneNumber(num) {
        var str = num.toString();

        var matched = str.match(/\d+\.?\d*/g);

        // 10 digit
        if (matched.length === 3) {
            return '(' + matched[0] + ') ' + matched[1] + '-' + matched[2];
            // 7 digit
        } else if (matched.length === 2) {
            return matched[0] + '-' + matched[1];
        }
        // no formatting attempted only found integers (i.e. 1234567890)
        else if (matched.length === 1) {
            // 10 digit
            if (matched[0].length === 10) {
                return '(' + matched[0].substr(0, 3) + ') ' + matched[0].substr(3, 3) + '-' + matched[0].substr(6);
            }
            // 7 digit
            if (matched[0].length === 7) {
                return matched[0].substr(0, 3) + '-' + matched[0].substr(3);
            }
        }

        // Format failed, return number back
        return num;
    }


    function goToRO() {
        roid = $('#schroid').val();
        if (roid >= 1000) {
            location.href = '<?= COMPONENTS_PRIVATE ?>/ro/ro.php?roid=' + roid
        }
    }

    function showReminder(shopname, shopphone) {

        $('#editmodal').modal('hide')

        //$('#sendremindermodal').modal('show')

        $('#sendremindermodal').modal('show');
        // Jquery draggable
        $('#sendremindermodal').draggable({
            handle: "#editmodalheader"
        });


        $('#updateemailto').val($('#editemail').val())


        if ($('#editcellphone').val().length > 0) {
            $('#updatecellphone').val(FormatPhoneNumber($('#editcellphone').val()))
            $('#nonformatcell').val($('#editcellphone').val())
        } else {
            $('#updatecellphone').val($('#editcellphone').val())
            $('#nonformatcell').val($('#editcellphone').val())
        }

        // need to disable or read only the cell number
        //$('#updatecellphone').attr('readonly', 'true');

        fname = $('#editfirstname').val()

        lname = $('#editlastname').val()


        //$('#updateremindermsg').val('This is an appointment reminder from ' + shopname + '.' + '\n' + '\n' + 'You have an appointment at ' + $('#editsd2').val() + ' for service on your '+ $('#edityear').val() + ' ' + $('#editmake').val() + ' ' + $('#editmodel').val() + '\n' + '\n' + 'If you have any questions, please give us a call at ' + FormatPhoneNumber(shopphone) + '\n' + '\n' + 'Sincerely, ' + '\n' + '\n' + shopname + '\n' + '\n' + 'Additional Comments: '   )
        $('#updateremindermsg').val(fname + ' ' + lname + ',' + '\n' + '\n' + 'This is an appointment reminder from ' + shopname + '. <?= addslashes($companyaddress . ', ' . $companycity)?>' + '\n' + '\n' + 'You have an appointment at ' + $('#editsd2').val() + ' for service on your ' + $('#edityear').val() + ' ' + $('#editmake').val() + ' ' + $('#editmodel').val() + '\n' + '\n' + 'If you have any questions, please give us a call at ' + FormatPhoneNumber(shopphone) + '\n' + '\n' + 'Sincerely, ' + '\n' + '\n' + shopname + '\n' + '\n' + 'Additional Comments: ')
    }

    function sendReminder(reminder) {

        em = $('#updateemailto').val()
        cp = $('#updatecellphone').val()
        nonformattedcell = $('#updatecellphone').val()
        nonformattedcell = nonformattedcell.replace(/[^\d]/g, '')

        msgsend = encodeURIComponent($('#updateremindermsg').val())
        id = $('#editid').val()

        if (reminder === "email" && em.length === 0) {
            swal({
                title: "Missing Information",
                text: "You must have an email to send an email reminder",
                type: "warning",
                confirmButtonClass: "btn-warning",
                confirmButtonText: "Ok",
                closeOnConfirm: true
            });
            return
        }
        if (reminder === "text" && cp.length === 0) {
            swal({
                title: "Missing Information",
                text: "You must have a cell phone to send an text message reminder",
                type: "warning",
                confirmButtonClass: "btn-warning",
                confirmButtonText: "Ok",
                closeOnConfirm: true
            });
            return
        }
        if ((reminder === "both" && em.length === 0) || (reminder === "both" && cp.length === 0)) {
            swal({
                title: "Missing Information",
                text: "You must have an email and cell phone to send a reminder to both",
                type: "warning",
                confirmButtonClass: "btn-warning",
                confirmButtonText: "Ok",
                closeOnConfirm: true
            });
            return
        }


        if ($("#updateemail").is(':checked')) {
            ue = "&updateemail=yes"
        } else {
            ue = "&updateemail=no"
        }

        if ($("#updatecell").is(':checked')) {
            uc = "&updatecell=yes"
        } else {
            uc = "&updatecell=no"
        }

        sub = encodeURIComponent($('#msgsubject').val())

        ds = "sub=" + sub + "&shopid=<?php echo $shopid; ?>&id=" + id + "&t=" + reminder + "&email=" + em + "&cell=" + nonformattedcell + ue + uc + "&msg=" + msgsend

        $.ajax({
            data: ds,
            url: "sendreminder.php",
            success: function (r) {
                if (r === "success") {
                    swal("Your reminder has been sent")
                    $('#sendremindermodal').modal('hide')
                } else {
                    swal(r)
                }
                $('#editmodal').modal('hide')
                $('#calendar').fullCalendar('refetchEvents')
                $('#updateeemail').attr('checked', false)
                $('#updatecell').attr('checked', false)
            }
        })
    }


    function setCustomerStatus(stat) {

        $('#customerstatus').val(stat)

    }

    function customerArrived() {
        //$('#customerstatus').val("arrived")
        id = $('#statusid').val()
        console.log("statusid:" + id)
        ds = "t=updatedone&s=yes&shopid=<?php echo $shopid; ?>&id=" + id
        console.log(ds)
        $.ajax({

            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {
                console.log(r)
                $('#statusid').val('')
                $('#customerstatus').val('')
                $('#calendar').fullCalendar('refetchEvents');
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#statusid').val('')
                $('#customerstatus').val('')
            }

        });

    }

    function customerNoShow() {
        id = $('#statusid').val()
        ds = "t=updatedone&s=noshow&shopid=<?php echo $shopid; ?>&id=" + id
        console.log(ds)
        $.ajax({

            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {
                console.log(r)
                $('#statusid').val('')
                $('#customerstatus').val('')
                $('#calendar').fullCalendar('refetchEvents');
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#statusid').val('')
                $('#customerstatus').val('')
            }

        });

    }

    function waitingCustomer() {
        //$('#customerstatus').val("waiting")
        id = $('#statusid').val()
        ds = "t=updatedone&s=no&shopid=<?php echo $shopid; ?>&id=" + id
        $.ajax({

            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {
                console.log(r)
                $('#statusid').val('')
                $('#customerstatus').val('')
                $('#calendar').fullCalendar('refetchEvents');
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#statusid').val('')
                $('#customerstatus').val('')
            }

        });

    }

    function printWeekView() {
        var view = $('#calendar').fullCalendar('getView');
        viewtype = view.name
        if (viewtype == "agendaWeek") {
            sd = view.intervalStart.format("MM/DD/YYYY");
        } else
            sd = '';

        eModal.iframe({
            title: "Show Calendar Entries for Week",
            url: "calendarprint.php?sd=" + sd,
            size: eModal.size.xl,
            buttons: [
                {text: 'Close', style: 'warning', close: true}
            ]
        });
    }

    function printView() {
        var view = $('#calendar').fullCalendar('getView');
        viewtype = view.name
        sd = view.intervalStart.format("MM/DD/YYYY");

        var type = viewtype.replace("agenda", "");
        if (viewtype == "agendaTwoDay") {
            type = "Three Days"
        }

        eModal.iframe({
            title: "Show Calendar Entries for "+type,
            url: "calendarprint.php?sd=" + sd + "&viewtype=" + viewtype,
            size: eModal.size.xl,
            buttons: [
                {text: 'Close', style: 'warning', close: true}
            ]
        });
    }

    /*
	function printDayView(){
		var view = $('#calendar').fullCalendar('getView');
		viewtype = view.name
		if (viewtype == "agendaDay"){
	        sd = view.intervalStart.format("MM/DD/YYYY");
	    }
	    else
	       sd='';

    		eModal.iframe({
				title:"Show Calendar Entries for the Day",
				url: "calendarprint.php?sd="+sd+"&range=day",
				size: eModal.size.xl,
				buttons: [
					{text: 'Close', style: 'warning', close:true}
		    	]
			});
	}

     */

    function finishedCustomer() {

        id = $('#statusid').val()
        ds = "t=updatefinished&s=no&shopid=<?php echo $shopid; ?>&id=" + id
        console.log(ds)
        $.ajax({

            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {
                console.log(r)
                $('#statusid').val('')
                $('#customerstatus').val('')
                $('#calendar').fullCalendar('refetchEvents');
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#statusid').val('')
                $('#customerstatus').val('')
            }

        });


    }


    function reminderSet(reminderval) {

        if (reminderval !== "no") {
            $('#reminderdiv').show()
        } else {
            $('#reminderdiv').hide()
        }

    }

    function selectCust(cid, vid) {

        custid = $('#customerid').val()
        vehid = $('#vehid').val()
        // putting on hold here
        onhold = $('#onhold').val()

        ds = "t=cidvid&shopid=<?php echo $shopid; ?>&cid=" + cid + "&vid=" + vid
        console.log(ds)
        $.ajax({

            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {
                console.log(r)
                $('#customerid').val(cid)
                $('#vehid').val(vid)
                $('#onhold').val(onhold)
                $('#onhold2').val(onhold)
                $('#custmodal').modal('hide')
                if (r.indexOf("|") >= 0) {
                    rar = r.split("|")
                    fn = rar[0]
                    ln = rar[1]
                    yr = rar[2]
                    mk = rar[3]
                    md = rar[4]
                    em = rar[5]
                    cl = rar[6]
                    onhold = rar[7]
                    $('#addfirstname').val(fn)
                    $('#addlastname').val(ln)
                    $('#addyear').val(yr)
                    $('#addmake').val(mk)
                    $('#addmodel').val(md)
                    $('#addemail').val(em)
                    $('#addcellphone').val(cl)
                    $('#onhold').val(onhold)

                    $('#addservice').focus()

                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }

        });
        //}
    }

    function setCookie(cname, cvalue, exdays) {
        var d = new Date();
        d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
        var expires = "expires=" + d.toUTCString();
        document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
    }

    function convertToRO() {
        var cid = $('#customerid').val(cid)
        var cid2 = $('#historycid').val()
        //console.log("Customerid is " + cid2)
        //console.log ("OnHold is " + onhold)
        var onhold2 = $('#onhold2').val(onhold2)
        //console.log ("OnHold2 is " + onhold2)

        if (onhold == "yes") {
            swal("This customer is on a credit hold. Please go to Accounting - Accounts Receivable to release this hold.")

        } else {

            fn = $('#editfirstname').val()
            ln = $('#editlastname').val()
            svc = $('#editservice').val()
            yr = $('#edityear').val()
            mk = $('#editmake').val()
            md = $('#editmodel').val()
            em = $('#editemail').val()
            cl = $('#editcellphone').val()
            cid = $('#editcustomerid').val()
            schid = $('#statusid').val()

            address = $('#editaddress').val()
            city = $('#editcity').val()
            state = $('#editstate').val()
            zipcode = $('#editzip').val()


            onhold = $('#editonhold')

            //console.log("schid:"+schid)
            //console.log(cid)

            if (typeof (Storage) !== "undefined") {
                localStorage.setItem("fn", fn);
                localStorage.setItem("ln", ln);
                localStorage.setItem("svc", svc);
                localStorage.setItem("year", yr);
                localStorage.setItem("make", mk);
                localStorage.setItem("model", md);
                localStorage.setItem("email", em);
                localStorage.setItem("cellphone", cl);
                // pick up onhold
                localStorage.setItem("onhold", cl);

                localStorage.setItem("address", address);
                localStorage.setItem("city", city);
                localStorage.setItem("state", state);
                localStorage.setItem("zip", zipcode);



                localStorage.setItem("cid", cid);
                localStorage.setItem("a2ro", "true");
                localStorage.setItem("schid", schid);

            } else {
                console.log("no local storage")
            }

            finishedCustomer()
            setTimeout(function () {
                location.href = '<?= COMPONENTS_PRIVATE ?>/customer/customer-search.php'
            }, 1000)

        }
    }

    function addEvent() {

        console.log("starting add")
        // check for all values
        ac = $('#addcolumn').val()
        afn = $('#addfirstname').val()
        aln = $('#addlastname').val()
        ay = $('#addyear').val()
        am = $('#addmake').val()
        amm = $('#addmodel').val()
        as = encodeURIComponent($('#addservice').val())
        ah = $('#addhours').val()
        asd = $('#addsd').val()
        vid = $('#vehid').val()
        cid = $('#customerid').val()
        em = $('#addemail').val()
        cl = $('#addcellphone').val()
        rm = $('#sendreminder').val()
        schroid = $('#schroid').val()
        sales = $('#sales').val()

        if ($('#rollcheck').is(":checked"))
            roll = '1'
        else
            roll = '0'
        //$('#schroid').val('')
        //$('#passedroid').val('')

        //console.log("Onhold value is " + onhold)

        if (onhold == "yes") {
            swal({
                    title: "Customer On Hold",
                    text: "This customer is on a credit hold. Please go to Accounting - Accounts Receiveable to release this hold or Create Appointment.",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonClass: "btn-success",
                    confirmButtonText: "Create Appointment",
                    cancelButtonClass: "btn-danger",
                    cancelButtonText: "Cancel",
                    closeOnConfirm: false,
                    closeOnCancel: true
                },
                function (isConfirm) {
                    if (isConfirm) {
                        swal("Creating Appointment");
                        if (rm != "no") {
                            if (rm == "sms" && cl == "") {
                                swal("If you have a reminder set for Text Message, you must enter a cell phone")
                                return
                            }
                            if (rm == "email" && em == "") {
                                swal("If you have a reminder set for Email, you must enter an email address")
                                return
                            }
                            if (rm == "both" && (em == "" || cl == "")) {
                                swal("If you have a reminder set for Both, you must enter an email address and a cell phone")
                                return
                            }
                        }

                        if (aln.length > 0 && ay.length > 0 && ah.length > 0 && asd.length > 0) {
                            ds = "schroid=" + schroid + "&shopid=<?php echo $shopid; ?>&t=addnewitem&ac=" + ac + "&sd=" + asd + "&afn=" + afn + "&aln=" + aln + "&ay=" + ay + "&am=" + am + "&amm=" + amm + "&as=" + as + "&ah=" + ah + "&cid=" + cid + "&vid=" + vid + "&em=" + em + "&cl=" + cl + "&rm=" + rm + "&empid=" + sales + "&roll=" + roll
                            console.log(ds)
                            $.ajax({
                                data: ds,
                                type: "post",
                                url: "calendardata.php",
                                success: function (r) {
                                    console.log(r)
                                    if (r == "success") {
                                        $('#addmodal').modal('hide')
                                        $('#schroid').val('')
                                        $('#passedroid').val('')
                                        $('#calendar').fullCalendar('refetchEvents')
                                    } else
                                        swal(r)
                                },
                                error: function (xhr, ajaxOptions, thrownError) {
                                    console.log(xhr.status);
                                    console.log(xhr.responseText);
                                    console.log(thrownError);
                                }
                            });
                        } else {
                            swal("Last name, vehicle year, number of hours and start date and time are required.")
                        }

                    }

                });


        } else {

            if (rm != "no") {
                if (rm == "sms" && cl == "") {
                    swal("If you have a reminder set for Text Message, you must enter a cell phone")
                    return
                }
                if (rm == "email" && em == "") {
                    swal("If you have a reminder set for Email, you must enter an email address")
                    return
                }
                if (rm == "both" && (em == "" || cl == "")) {
                    swal("If you have a reminder set for Both, you must enter an email address and a cell phone")
                    return
                }
            }

            if (aln.length > 0 && ay.length > 0 && ah.length > 0 && asd.length > 0) {
                ds = "schroid=" + schroid + "&shopid=<?php echo $shopid; ?>&t=addnewitem&ac=" + ac + "&sd=" + asd + "&afn=" + afn + "&aln=" + aln + "&ay=" + ay + "&am=" + am + "&amm=" + amm + "&as=" + as + "&ah=" + ah + "&cid=" + cid + "&vid=" + vid + "&em=" + em + "&cl=" + cl + "&rm=" + rm + "&empid=" + sales + "&roll=" + roll
                console.log(ds)
                $.ajax({
                    data: ds,
                    type: "post",
                    url: "calendardata.php",
                    success: function (r) {
                        console.log(r)
                        if (r == "success") {
                            $('#addmodal').modal('hide')
                            $('#schroid').val('')
                            $('#passedroid').val('')
                            $('#calendar').fullCalendar('refetchEvents')
                        } else
                            swal(r)
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                });
            } else {
                swal("Last name, vehicle year, number of hours and start date and time are required.")
            }
        }
    }

    function delEvent() {

        swal({
                title: "Are you sure?",
                text: "Are you sure you want to cancel this appointment?",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Yes, cancel it!",
                closeOnConfirm: true
            },
            function () {
                id = $('#editid').val()
                ds = "shopid=<?php echo $shopid; ?>&t=delete&id=" + id
                $.ajax({
                    data: ds,
                    type: "post",
                    url: "calendardata.php",
                    success: function (r) {
                        console.log(r)
                        if (r == "success") {
                            $('#editmodal').modal('hide')
                            $('#calendar').fullCalendar('refetchEvents')
                        }
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                });

            });

    }

    function noReminders() {

        id = $('#editid').val()
        ds = "shopid=<?php echo $shopid; ?>&t=noreminder&id=" + id
        $.ajax({
            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {
                //console.log(r)
                if (r == "success") {
                    $('#editmodal').modal('hide')
                    $('#calendar').fullCalendar('refetchEvents')
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    function markDone(flag) {

        id = $('#editid').val()
        ds = "shopid=<?php echo $shopid; ?>&t=markdone&flag=" + flag + "&id=" + id
        $.ajax({
            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {
                console.log(r)
                if (r == "success") {
                    $('#editmodal').modal('hide')
                    $('#calendar').fullCalendar('refetchEvents')
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }


    function saveEvent() {

        // check for all values
        efn = $('#editfirstname').val()
        eln = $('#editlastname').val()
        ey = $('#edityear').val()
        em = $('#editmake').val()
        emm = $('#editmodel').val()
        hrs = $('#edithours').val()
        es = encodeURIComponent($('#editservice').val())
        eml = $('#editemail').val()
        cl = $('#editcellphone').val()
        id = $('#editid').val()
        sd = $('#editsd').val()

        if ($('#editrollspan').is(':visible') && $('#editrollcheck').is(":checked"))
            roll = '1'
        else
            roll = '0'

        if (efn.length > 0 && eln.length > 0 && ey.length > 0) {
            ds = "shopid=<?php echo $shopid; ?>&t=saveall&efn=" + efn + "&eln=" + eln + "&ey=" + ey + "&em=" + em + "&emm=" + emm + "&es=" + es + "&id=" + id + "&eml=" + eml + "&cl=" + cl + "&sd=" + sd + "&hrs=" + hrs + "&roll=" + roll
            console.log(ds)
            $.ajax({
                data: ds,
                type: "post",
                url: "calendardata.php",
                success: function (r) {
                    console.log(r)
                    if (r == "success") {
                        $('#editmodal').modal('hide')
                        $('#calendar').fullCalendar('refetchEvents')
                    } else
                        swal(r)
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        } else {
            swal("First name, last name and vehicle year are required")
        }

    }

    function searchAppt() {

        st = $('#search-term').val()
        $("#srchresults > tbody").html("");
        if (st.length > 0) {

            $.ajax({

                data: "t=search&st=" + st + "&shopid=<?php echo $shopid; ?>",
                url: "calendardata.php",
                type: "post",
                success: function (r) {
                    $('#srchresults>tbody').append(r);
                    $('#searchmodal').modal('show')
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });

        }

    }

    function goto(d) {

        $('#calendar').fullCalendar('gotoDate', d)
        $('#searchmodal').modal('hide')
    }

    function moveevent(sd, id, rid, colorcode, hrs) {
        $.ajax({

            data: "t=moveevent&shopid=&sd=" + sd + "&id=" + id + "&rid=" + rid + "&colorcode=" + colorcode + "&hrs=" + hrs,
            url: "calendardata.php",
            type: "post",
            success: function (r) {
                if (r != "success") {
                    swal(r);
                }
                $('#calendar').fullCalendar('refetchEvents');
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });
    }

    function openAddVeh() {
        $('#vehyear').val('')
        $('#vehmake').val('')
        $('#vehmodel').val('')
        $('#vehvin').val('')
        $('#addvehmodal').modal('show')
    }

    function decodeVIN() {

        vin = $('#vehvin').val()
        p = false

        if (vin.length == 17) {
            p = true
        }

        if (p == true) {

            $('#vehspinner').show()
            $.ajax({
                data: "vin=" + vin + "&carfaxloc=<?= $cfl?>",
                url: "<?= COMPONENTS_PRIVATE ?>/customer/vehicle-vin-decode.php",
                dataType: "json",
                success: function (r) {

                    if (r.status == 'success') {
                        rvin = r.vin
                        yr = r.yr
                        mk = r.mk
                        md = r.md
                        tr = r.tr
                        bd = r.bd
                        en = r.en

                        $('#vehyear').val(yr)
                        $('#vehmake').val(mk)
                        $('#vehmodel').val(md + " " + tr + " " + bd)
                        $('#vehspinner').hide()
                    } else {
                        $('#vehspinner').hide()
                        swal("The VIN was not able to be decoded.  Please verify the VIN number")
                    }
                }
            });
        } else {
            swal("You must enter a 17 digit VIN to decode it")
        }
    }

    function addVehicle() {
        if ($('#vehyear').val() == "" || $('#vehmake').val() == "" || $('#vehmodel').val() == "") {
            swal("Please enter year, make, model of the vehicle");
            return;
        }

        if ($('#customerid').val() == "") {
            swal("Please select customer");
            return;
        }

        $('.btn-md').attr('disabled', 'disabled')

        ds = "shopid=<?= $shopid?>&cid=" + $('#customerid').val() + "&addyear=" + $('#vehyear').val() + "&addmake=" + $('#vehmake').val() + "&addmodel=" + $('#vehmodel').val() + "&addvin=" + $('#vehvin').val()
        $.ajax({

            data: ds + "&dupcheck=yes",
            type: "post",
            url: "<?= COMPONENTS_PRIVATE ?>/customer/customer-vehicle-add.php",
            success: function (r) {
                if (r.indexOf("success") >= 0) {
                    var veharr = r.split('success|');
                    $('.btn-md').attr('disabled', false)
                    $('#addyear').val($('#vehyear').val())
                    $('#addmake').val($('#vehmake').val())
                    $('#addmodel').val($('#vehmodel').val())
                    $('#addvehmodal').modal('hide')
                    $('#vehid').val(veharr[1])
                    swal("Vehicle Added")
                } else if (r.indexOf("duplicate") >= 0) {
                    var cusarr = r.split('duplicate|');
                    swal({
                        title: "Are you sure?",
                        text: "Vehicle with this VIN already belongs to " + cusarr[1] + ". Do you still want to continue?",
                        type: "warning",
                        showCancelButton: true,
                        cancelButtonClass: "btn-default",
                        confirmButtonClass: "btn-danger btn-conf",
                        confirmButtonText: "Yes",
                        cancelButtonText: "No",
                        closeOnConfirm: false
                    }, function () {

                        $('.btn-conf').attr('disabled', 'disabled')

                        $.ajax({

                            data: ds,
                            type: "post",
                            url: "<?= COMPONENTS_PRIVATE ?>/customer/customer-vehicle-add.php",
                            success: function (r) {
                                if (r.indexOf("success") >= 0) {
                                    var veharr = r.split('success|');
                                    $('#addyear').val($('#vehyear').val())
                                    $('#addmake').val($('#vehmake').val())
                                    $('#addmodel').val($('#vehmodel').val())
                                    $('#vehid').val(veharr[1])
                                    $('#addvehmodal').modal('hide')
                                    swal("Vehicle Added")
                                }
                                $('.btn-md').attr('disabled', false)
                                $('.btn-conf').attr('disabled', false)
                            }
                        });
                    });

                }
            }

        });

    }

    $(document).ready(function () {

        setInterval(function () {

            var currview = $('#calendar').fullCalendar('getView')

            if (currview.name == "agendaDay") {

                // get the total hours for the day
                todaydate = $('#calendar').fullCalendar('getDate');
                todaydate = moment(todaydate._d)
                todaydate = moment(todaydate).add(1, 'days');
                todaydate = todaydate.format("MM/DD/YYYY")

                ds = "shopid=<?php echo $shopid; ?>&d=" + todaydate + "&t=getdaytotal"
                $.ajax({
                    data: ds,
                    url: "calendardata.php",
                    type: "post",
                    success: function (r) {
                        if ($.isNumeric(r)) {
                            r = parseFloat(r)
                            result = r.toFixed(2)
                            $('.schtitle').remove()
                            $('.fc-center h2').css("font-size", "x-large").css("font-weight", "bold").append("<div class='schtitle' style='font-size:medium;padding-top:2px;font-weight:normal'>(" + result + " Scheduled Hours)</span>")

                        }
                        $('#calendar').fullCalendar('refetchEvents');
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                });

            } else
                $('#calendar').fullCalendar('refetchEvents');
            //console.log("refresh events")
        }, 30000);

        <?php
        if (isset($_GET['schdate'])) {
            echo "\r\ngoto('" . $_GET['schdate'] . "');\r\n";
        }
        ?>

    });

</script>
</body>
<?php if (isset($conn)) {
    mysqli_close($conn);
} ?>
</html>
