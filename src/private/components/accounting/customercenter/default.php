<?php
require CONN;
require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];

$email = "dummy";

?>
<!DOCTYPE html>
<html>
<?php
if (strtolower($_COOKIE["accounting"]) == "no") {
    redirect_to(COMPONENTS_PRIVATE . "/wip/wip.php");
}

$rpath = realpath(COMPONENTS_PUBLIC_PATH . "/invoices/$shopid");
$rpath2 = realpath(COMPONENTS_PUBLIC_PATH . "/invoices");
$rpath2 .= "{$shopid} ";

if (file_exists($rpath)) {
    $r =  $_COOKIE['shopid'] . "/printpdfro.php";
} else {

    //echo "Roid is " . $roid . "</br>";
    $r = "printpdfro.php";
}

// adding ppath (part sales)
$ppath = realpath('/windows/system32');
$ppath2 = realpath($_SERVER['DOCUMENT_ROOT'] . '/sbp/psinvoices');
$ppath2 .= "{$shopid} ";

if (!file_exists($ppath)) {
    $p = "psprintpdf.php";
} else {
    $p = "psprintpdf.php";
}

$stmt = "SELECT CompanyName,CompanyPhone,CompanyEMail FROM company WHERE shopid = ? ";
if ($squery = $conn->prepare($stmt)) {
    $squery->bind_param("s", $shopid);
    $squery->execute();
    $squery->bind_result($shopname, $shopphone, $shopemail);
    $squery->fetch();
    $squery->close();
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>

<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">
    <title><?= getPageTitle() ?></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <!--   <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon'/ > -->
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
    <link rel="stylesheet" type="text/css" href="<?= CSS ?>/rich_calendar.css" />
    <script language="JavaScript" type="text/javascript" src="<?= SCRIPT ?>/rich_calendar.js"></script>
    <script language="JavaScript" type="text/javascript" src="<?= SCRIPT ?>/rc_lang_en.js"></script>
    <script language="javascript" type="text/javascript" src="<?= SCRIPT ?>/domready.js"></script>

    <script type="text/javascript" src="<?= SCRIPT ?>/formvalidator.js"></script>
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        .col-md-6 {
            border: 1px black solid
        }

        .col-md-8 {
            border: 1px black solid
        }

        .col-md-4 {
            border: 1px black solid
        }

        #container {
            margin: 0 auto;
            width: 100%;
            background: #fff;
        }


        #header h1 {
            margin: 0;
        }

        #navigation {
            float: left;
            width: 100%;
            height: 60px;
            background-color: #F0F0F0;
        }

        #navigation ul {
            margin: 0;
            padding: 0;
            width: 100%;
        }

        #navigation ul li {
            list-style-type: none;
            display: inline;
            width: 25%
        }

        #navigation li a {
            display: block;
            float: left;
            padding: 5px 0px;
            color: #fff;
            text-decoration: none;
            border-right: 1px solid #fff;
            text-align: center;
            background-color: #F0F0F0;
            width: 21%;
            border: 1px gray outset;
            color: maroon;
            font-weight: bold;
            cursor: pointer;
            height: 47px;

        }

        #navigation li a:hover {
            background: white;
        }

        #content-container {
            float: left;
            width: 100%;
        }

        #content {
            clear: left;
            float: left;
            width: 20%;
            padding: 20px 0;
            margin: 0 0 0 1%;
            display: inline;
        }

        #content h2 {
            margin: 0;
        }

        #aside {
            float: right;
            width: 76%;
            padding: 20px 0;
            margin: 0 1% 0 0;
            display: inline;
            overflow-y: auto;
        }

        #aside h3 {
            margin: 0;
        }

        #footer {
            clear: left;
            background: #ccc;
            text-align: right;
            padding: 20px;
            height: 1%;
        }

        body {
            font-family: Arial, Helvetica, sans-serif
        }

        .active {
            font-weight: bold;
            /*border:1px black solid;*/
            margin-left: 30px;
            cursor: pointer;
            padding: 40px;
            color: black;
            background-color: #336699;
        }

        .headerrow {
            background-color: #7993B4;
            color: white;
            font-weight: bold;
            padding: 5px;
            height: 25px;
        }

        .psheaderrow {
            background-color: #7993B4;
            color: white;
            font-weight: bold;
            padding: 5px;
            height: 25px;
        }

        .adatarow {
            font-size: small;
            font-weight: bold;
        }

        .bdadatarow {
            background-color: red;
            color: white;
            font-weight: bold;
        }

        .datatr {
            padding: 1px;
            background-color: #FFFFCC;
        }

        .psdatatr {
            padding: 1px;
            background-color: #CCFFFF;
            cursor: pointer;
        }

        .psdatatr:hover {
            padding: 1px;
            background-color: #CCFFCC;
            cursor: pointer;
        }

        .datarow {
            border-top: 2px gray outset;
            font-size: small;
        }

        .psdatarow {
            border-top: 2px gray outset;
            font-size: small;
        }

        .datatr:hover {
            background-color: #CCFFCC;
            cursor: pointer;
        }

        .adatatr {
            padding: 1px;

        }

        .button {
            padding: 3px 10px 3px 10px;
            background-color: #30608F;
            border: 1px gray solid;
            border-radius: 3px;
            cursor: pointer;
            color: white;
        }

        .popwin {
            width: 50%;
            border: 1px black solid;
            border-radius: 5px;
            z-index: 9999;
            background-color: white;
            color: black;
            min-height: 300px;
            display: none;
            position: absolute;
            left: 25%;
            top: 100px;
            padding: 15px;
        }

        #hider {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 120%;
            background-color: gray;
            -ms-filter: "alpha(Opacity=90)";
            filter: alpha(opacity=90);
            -moz-opacity: .90;
            opacity: .90;
            z-index: 998;
            display: none
        }

        #cover {
            position: absolute;
            top: 1px;
            left: 0%;
            width: 100%;
            height: 100%;
            -ms-filter: "alpha(Opacity=90)";
            filter: alpha(opacity=90);
            -moz-opacity: .90;
            opacity: .90;
            background-color: white;
            z-index: 1500;
            display: none;
            color: black;
        }

        .auto-style1 {
            color: #FF0000;
        }
    </style>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<div id="mainalert" style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large" class="alert alert-success"></div>
<div id="header"></div>
<!-- Page Container -->
<!--
        Available Classes:

        'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

        'sidebar-l'                  Left Sidebar and right Side Overlay
        'sidebar-r'                  Right Sidebar and left Side Overlay
        'sidebar-mini'               Mini hoverable Sidebar (> 991px)
        'sidebar-o'                  Visible Sidebar by default (> 991px)
        'sidebar-o-xs'               Visible Sidebar by default (< 992px)

        'side-overlay-hover'         Hoverable Side Overlay (> 991px)
        'side-overlay-o'             Visible Side Overlay by default (> 991px)

        'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

        'header-navbar-fixed'        Enables fixed header
    -->
<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">

    <!-- Sidebar -->
    <nav id="sidebar">
        <!-- Sidebar Scroll Container -->
        <div id="sidebar-scroll">
            <!-- Sidebar Content -->
            <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
            <div class="sidebar-content">
                <!-- Side Header -->
                <div class="side-header side-content bg-white-op">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                        <i class="text-primary">
                            <?php getLogo() ?></i>
                        <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                    </a>
                </div>
                <!-- END Side Header -->

                <!-- Side Content -->
                <div class="side-content-sbp-ro side-content">
                    <ul class="nav-main">
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Work In Process</span></a>
                        </li>
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/accounting/default.php"><i class="fa fa-home"></i><span class="sidebar-mini-hide">Accounting Home</span></a>
                        </li>
                        <?php if (!$shopIsReadOnly): ?>
                            <li>
                                <a href="<?= COMPONENTS_PRIVATE ?>/accounting/qbointegrator/default.php"><i class="fa fa-book"></i><span class="sidebar-mini-hide">Quickbooks</span></a>
                            </li>
                        <?php endif; ?>
                        </li>
                        <a href="<?= COMPONENTS_PRIVATE ?>/accounting/company/default.php"><i class="fa fa-cog"></i><span class="sidebar-mini-hide">Preferences</span></a>
                        </li>
                        </li>
                        <a href="<?= COMPONENTS_PRIVATE ?>/accounting/customercenter/default.php"><i class="fa fa-user"></i><span class="sidebar-mini-hide">Customer Center</span></a>
                        </li>
                        </li>
                        <a href="<?= COMPONENTS_PRIVATE ?>/accounting/company/categories.php"><i class="fa fa-list"></i><span class="sidebar-mini-hide">Chart of Accounts</span></a>
                        </li>
                        </li>
                        <a href="<?= COMPONENTS_PRIVATE ?>/accounting/expenses/expenses.php"><i class="fa fa-money"></i><span class="sidebar-mini-hide">Expenses (A/P)</span></a>
                        </li>
                        </li>
                        <a href="<?= COMPONENTS_PRIVATE ?>/accounting/ar/ar.php"><i class="fa fa-usd"></i><span class="sidebar-mini-hide">Accounts Rec. (A/R)</span></a>
                        </li>
                        </li>
                        <a href="<?= COMPONENTS_PRIVATE ?>/accounting/undepositedfunds.php"><i class="fa fa-money"></i><span class="sidebar-mini-hide">Undeposited Funds</span></a>
                        </li>
                        </li>
                        <a href="<?= COMPONENTS_PRIVATE ?>/accounting/unpostedexpenses.php"><i class="fa fa-usd"></i><span class="sidebar-mini-hide">Unposted Expenses</span></a>
                        </li>
                        <?php if (!$shopIsReadOnly): ?>
                        </li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/accounting/addincome.php"><i class="fa fa-arrow-left"></i><span class="sidebar-mini-hide">Other Income</span></a>
                        </li>
                        <?php endif; ?>
                        </li>
                        <a href="<?= COMPONENTS_PRIVATE ?>/accounting/bankaccount/accountlist.php"><i class="fa fa-university"></i><span class="sidebar-mini-hide">Bank/CC Accounts</span></a>
                        </li>
                        </li>
                        <a href="<?= COMPONENTS_PRIVATE ?>/accounting/bankaccount/accountlist.php"><i class="fa fa-bell"></i><span class="sidebar-mini-hide">Acct. Reminders</span></a>
                        </li>
                        </li>
                        <a href="<?= COMPONENTS_PRIVATE ?>/accounting/reports/reports.php"><i class="fa fa-print"></i><span class="sidebar-mini-hide">View/Print Reports</span></a>
                        </li>

                    </ul>
                </div>
                <!-- END Side Content -->
            </div>
            <!-- Sidebar Content -->
        </div>
        <!-- END Sidebar Scroll Container -->
    </nav>
    <!-- END Sidebar -->

    <!-- Header -->
    <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar" class="content-mini content-mini-full">

        <!-- Header Navigation Right -->
        Customer Center
        <!-- END Header Navigation Right -->

        <!-- Header Navigation Left -->

        <ul class="nav-header pull-left">
            <li class="hidden-md hidden-lg">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                    <i class="fa fa-navicon"></i>
                </button>
            </li>
            <li class="hidden-xs hidden-sm">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                    <i class="fa fa-bars"></i>
                </button>
            </li>
            <li>
                <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                    <i class="si si-grid"></i>
                </button>
            </li>
            <li class="visible-xs">
                <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                    <i class="fa fa-search"></i>
                </button>
            </li>
            <li>

            </li>
        </ul>

        <!-- END Header Navigation Left -->
    </header>
    <!-- END Header -->
    <!-- Main Container -->

    <!-- Main Container -->
    <main class="container-fluid" id="main-container" style="display:block;">
        <div class="popwin"></div>

        <div style="width:40%;left:30%;border:1px silver solid;z-index:1000;position:absolute;top:100px;padding:20px;display:none;" id="customeremail">





        </div>
        <input id="returnlist" name="Hidden1" type="hidden" />
        <input id="returnlistps" name="Hidden1" type="hidden" />

        <div id="container">
            <nav class="navbar navbar-default">
                <?php if( !$shopIsReadOnly ): ?>
                    <button class="btn btn-warning" style="visibility:hidden" id="wo" onclick="SelectWriteOff()" type="button">Write off as Bad Debt</button>
                <?php endif; ?>
                
                <button class="btn btn-primary" style="visibility:hidden" id="sendInvoiceButton" onclick="showSendInvoices()" type="button">Email Selected Invoices</button>
                <button class="btn btn-info" style="visibility:hidden" id="PrintButton" onclick="sendInvoices('<?php echo $shopid; ?>','print')" type="button">Print Selected Invoices</button>
                
                <?php if( !$shopIsReadOnly ): ?>
                    <span id="womessage" style="visibility:hidden"><br />
                        Click specific Invoices that you wish to write off. Otherrwise click top box(transaction type) and all invoices that are eligible for writeoff will be written off for that customer.
                    </span>
                <?php endif; ?>
            </nav>
            <div id="content-container">
                <div id="content">
                    <h2>
                        Customer List
                    </h2>
                    <p>
                        Search: <input onkeyup="filterList(this.value)" id="customersearch" style="width: 243px;text-transform:uppercase" type="text" placeholder="Enter Name" />
                        <input name="Button1" type="button" style="font-size:x-small" value="Clear" onclick="filterList('');document.getElementById('customersearch').value=''" /><br />
                    </p>
                    <div id="customerlist" style="width:100%;overflow-y:auto;">
                        <?php
                        //$shopid = $_GET["shopid"]; // do not need it here

                        $_COOKIE["shopid"] = $shopid;

                        if (isset($_GET['v'])) {
                            $v = ($_GET['v']);
                            //echo "You are in the Get for v";
                        } else {

                            $v = "";
                        }

                        // took out demo and substituted {$shopid}  7/24/17
                        $stmt = "select customerid,lastname,firstname from customer  "
                            . " WHERE shopid = '{$shopid}' AND active != 'no' and (firstname like '$v%'  or lastname like '$v%'"
                            . " OR homephone like '$v%' or workphone like '$v%' or cellphone like '$v%' or address like '$v%') ORDER BY lastname,firstname ";

                        if ($query = $conn->prepare($stmt)) {
                        $query->execute();
                        $result = $query->get_result();
                        ?>

                        <table class="table table-condensed table-striped table-hover">
                            <?php
                            while ($row = $result->fetch_array()) {
                                ?>
                                <tr>
                                    <td id="c<?php echo $row["customerid"]; ?>" onclick="showCustomerDetails('<?php echo $row["customerid"]; ?>');"><?php echo strtoupper($row["lastname"] . ", " . $row["firstname"]); ?></td>
                                </tr>

                                <?php
                            } // end of while

                            } else {
                                echo "No customers found";
                            } // end of if
                            ?>

                        </table>
                    </div>
                </div>
                <div id="aside">
                    <img id="spinner" src="<?= IMAGE ?>/loaderbig.gif">
                    <h3>
                        Customer Details</h3>
                    <p class="auto-style1">
                        <strong>Select a customer on the left.&nbsp; Customer account details
                            will be displayed here.&nbsp; You can also click an RO or Part
                            Sale to view it.</strong>
                    </p>
                    <div id="customerdata"></div>
                </div>
            </div>
        </div>
        <iframe id="roframe" style="top:5%;left:2.5%;width:95%;height:90%;border:1px silver outset;display:none;position:absolute;background-color:white;z-index:9999"></iframe>
        <div style="position:absolute;top:0px;left:0px;background-color:#F0F0F0;width:100%;height:40px;z-index:9999;display:none;color:black" id="roheader">
            <table style="width: 90%;margin-left:5%">
                <tr>
                    <td style="text-align:left"><input id="framebutton" onclick="closeRO()" type="button" value="Close" style="width:200px;" class="btn btn-primary btn-lg" />
                        &nbsp;</td>
                    <td style="text-align:center"><input id="gotobutton" onclick="gotoRO()" type="button" value="Go To RO" style="width:200px;" class="btn btn-info btn-lg" />
                        &nbsp;</td>
                    <td style="text-align:right"><input id="gotobutton" onclick="postROPmt()" type="button" value="Post a Payment" style="width:200px;" class="btn btn-warning btn-lg" />
                        &nbsp;</td>
                </tr>
            </table>

        </div>
        <iframe id="psframe" style="top:5%;left:2.5%;width:95%;height:90%;border:1px silver outset;display:none;position:absolute;background-color:white;z-index:9999"></iframe>
        <div style="position:absolute;top:0px;left:0px;background-color:#F0F0F0;width:100%;height:40px;z-index:9999;display:none;color:black" id="psheader">
            <table style="width: 90%;margin-left:5%">
                <tr>
                    <td style="text-align:left"><input id="framebutton" onclick="closePS()" type="button" value="Close" style="width:200px;" class="btn btn-primary btn-lg" />
                        &nbsp;</td>
                    <td style="text-align:center"><input id="gotobutton" onclick="gotoPS()" type="button" value="Go To Part Sale" style="width:200px;" class="btn btn-info btn-lg" />
                        &nbsp;</td>
                    <td style="text-align:right"><input id="gotobutton" onclick="postPSPmt()" type="button" value="Post a Payment" style="width:200px;" class="btn btn-warning btn-lg" />
                        &nbsp;</td>
                </tr>
            </table>

        </div>

        <div id="hider"></div>
        <div id="cover"></div>
        <input id="selectedroid" type="hidden" />
        <input id="selectedpsid" type="hidden" />

        <input id="selectedcustomerid" type="hidden" />
        <input id="writeoffamount" type="hidden" />
        <input id="bdamount" type="hidden" />
        <input id="robdballist" type="hidden" />
        <input id="psbdballist" type="hidden" />


        <input id="activeroid" type="hidden" />
    </main>
    <!-- END Main Container -->

    <!-- Footer -->
    <!-- END Footer -->
</div>
<!-- END Page Container -->

<?php if( !$shopIsReadOnly ): ?>
<!-- Modals -->
<div id="pmtmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Modal Title</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-12">
                            <!-- duplicate this block for additional rows of form input -->
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" id="pmtamount" name="pmtamount">
                                    <label for="pmtamount">Form Label</label>
                                </div>
                            </div>
                        </div> <!-- Endd Duplicate area -->
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-primary btn-md" type="button">Button</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="emailinvmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Email Invoice To Customer</h3>
                </div>
                <div class="block-content">
                    <div class="row">

                        <div class="col-md-12">
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" id="sendingemail" name="sendingemail" value="<?php echo $email; ?>" type="text">
                                    <label for="sendingemail">Email Address </label>
                                </div>
                                </br></br>
                                <div>
                                    <input id="update" name="update" type="checkbox" value="yes" /> Update customer information with
                                    this email address?<br />
                                </div>

                            </div>
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <textarea class="form-control sbp-form-control" type="text" style="padding:20px;height:150px; text-transform: none;" tabindex="1" id="emailmessage" name="emailmessage" placeholder="Message">The following is an invoice or group of invoices from <?= $shopname ?>.&#13;&#10;If you have any questions, please contact us.  Phone: <?= $shopphone ?> or email: <?= $shopemail ?>.&#13;&#10;&#13;&#10;Sincerely,&#13;&#10;&#13;&#10;<?= $shopname ?></textarea>
                                    <label for="emailmessage">Message</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="btn btn-md btn-info" style="margin-left: 35px;" type="button" onclick="sendInvoices('<?php echo $shopid; ?>','email')">Send Invoice via Email</button>
            </div>

            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="splitpmtmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Split Payment</h3>
                </div>
                <div class="block-content">
                    <div id="results" class="row"></div>
                </div>

            </div>

            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- for use in getting invoice path -->
<input type="hidden" id="invpath" value=""><input type="hidden" id="cidfortransfer" value="">

<script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
<script src="<?= SCRIPT ?>/tipped.js"></script>

<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
<script src="<?= SCRIPT ?>/app.js"></script>
<script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
<script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
<script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
<script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

<!-- Page Plugins -->

<!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->

<script>
    <?php

    if (isset($_GET['cid'])) {

        $cidfortransfer = $_GET['cid'];
        echo "showCustomerDetails($cidfortransfer)";
    }



    ?>


    document.getElementById("customerlist").style.maxHeight = window.innerHeight - 290 + "px"
    //https://j.eremy.net/set-element-height-to-viewport/
    //  $("#customerlist").css("max-height",'windowHeight');

    document.getElementById("aside").style.maxHeight = window.innerHeight - 90 + "px"
    $('#customersearch').focus()

    function splitPmt(roid) {

        $('#activeroid').val(roid)
        ds = "t=getpayments&roid=" + roid + "&shopid=<?php echo $_COOKIE['shopid']; ?>"
        $.ajax({
            data: ds,
            url: "splitpmt.php",
            success: function(r) {
           //     console.log(r)
                window.scrollTo(0, 0)
                $('#results').html(r)
                $('#splitpmtmodal').modal('show');
                //$('.popwin').show().html(r)
                //$('#hider').show()
            }
        })
    }

    function selectRO(pmtid) {
        roid = $('#activeroid').val()
        ds = "t=getpmt&pmtid=" + pmtid + "&shopid=<?php echo $shopid; ?>&roid=" + roid
        $.ajax({
            data: ds,
            url: "splitpmt.php",
            success: function(r) {
                //console.log(r)
                $('.popwin').show().html(r)
                $('#hider').show()
                $('#splitpmtmodal').modal('hide');
            }
        })
    }

    function completeSplit(pmtid) {

        roid = $('#activeroid').val()
        totalro = $('#totalro' + roid).html()
        newroid = $('#selectro').val()

        ds = "newroid=" + newroid + "&totalro=" + totalro + "&shopid=<?php echo $_COOKIE['shopid']; ?>&t=splitpmt&pmtid=" + pmtid + "&roid=" + roid
        $.ajax({
            data: ds,
            url: "splitpmt.php",
            success: function(r) {
            //    console.log(r)
                if (r.indexOf("success|") >= 0) {
                    rar = r.split("|")
                    cid = rar[1]
                    cancel()
                    showCustomerDetails(cid)
                }
            }
        })
    }

    function cancel() {
        $('.popwin').hide()
        $('#hider').hide()
    }

    // added spinner
    function filterList(v) {
        //$("#spinner").show()
        var ds = 'v=' + v;

        $.ajax({
            type: "GET",
            url: "customerlistdata.php",
            data: ds,
            success: function(r) {
                $('#customerlist').html(r)
                $("#spinner").hide()
            }
        });
    }

    function showCustomerDetails(id) {

        $("#cidfortransfer").val(id)
        $("#spinner").show()

        $("#wo").css("visibility", 'hidden');
        $("#womessage").css("visibility", 'hidden')

        //$("#wo").hide();

        $("#selectedcustomerid").val(id)

        $('#c' + id).addClass("active");

        //This is the new code to replace the xmlhttp request
        var ds = 'cid=' + id;
        //console.log('Dataset is ' + ds);

        $.ajax({
            type: "GET",
            url: "customerdata_new.php",
            data: ds,
            success: function(r) {
                //console.log(r)


                $('#customerdata').html(r)

                $("#spinner").hide()

                testbal = Math.abs($('#rbal').val())

                rbal = $('#rbal').val()
                //added individual
                bdamount = $('#bdamount').val()
                //console.log('rbal' + rbal)

                $("#wo").css("visibility", 'visible')
                $("#womessage").css("visibility", 'visible')

                //$("#writeoffamount").val(rbal)

                // get the individual writeoff amount
                $("#writeoffamount").val(bdamount)
                $("#bdamount").val(bdamount)


                document.getElementById("balance").innerHTML = "Customer Balance: " + rbal

                if (testbal > 0) {
                    $('#balance').css("color", "red");
                } else if (testbal === 0) {
                    $('#balance').css("color", "black");
                }
                ntestbal = $('#rbal').val()
                //console.log('N test balance' + ntestbal)
                if (ntestbal.indexOf("-") == 0 && testbal > 0.01) {
                    $('#balance').innerHTML += " (Customer has a credit)"
                }

                document.getElementById("sendingemail").value = document.getElementById("customeremailfromdata").innerText
            }
        });

        //x = $(".active");
        x = document.getElementsByClassName("active")
        //console.log ("X value is : " + x);

        for (i = 0; i < x.length; i++) {
            if (x[i].id != "c" + id) {
                $(x[i].id).addClass("customerclass");
            }
        }

        $("#sendInvoiceButton").css("visibility", 'hidden');
        $("#PrintButton").css("visibility", 'hidden');
    }

    function showRO(roid, status) {

        status = status.toLowerCase()

        if (status == "closed") {
            eModal.iframe({
                title: "Closed RO",
                url: "<?= COMPONENTS_PRIVATE ?>/ro/ro.php?edit=no&roid=" + roid,
                size: eModal.size.xl,
                buttons: [{
                    text: "Receive Payment",
                    style: "primary",
                    close: true,
                    click: postROPmt
                },
                    {
                        text: 'Close',
                        style: 'warning',
                        close: true
                    }
                ]
            });
        } else {
            swal("You cannot open an RO that is still on your Work in Process")

        }

        $('#selectedroid').val(roid)

    }

    function showPS(psid) {

        // Bringing in a modal and a status to evaluate if part sale is closed and editable
        eModal.iframe({
            title: "Closed Part Sale",
            url: "<?= COMPONENTS_PRIVATE ?>/partsale/partsale.php?edit=no&psid=" + psid,
            size: eModal.size.xl,
            buttons: [{
                text: "Receive Payment",
                style: "primary",
                close: true,
                click: postPSPmt
            },
                {
                    text: 'Close',
                    style: 'warning',
                    close: true
                }
            ]
        });

        $('#selectedpsid').val(psid)
    }

    function closeRO() {
        document.getElementById("roframe").src = ""
        $('#roframe').hide()
        $('#roheader').hide()
        $('#cover').hide()
        $('#hider').hide()
        $('#selectedroid').val()
        $('#psheader').hide()
        $('#hider').hide()
    }

    function closePS() {
        document.getElementById("psframe").src = ""
        $('#psframe').hide()
        $('#psheader').hide()
        $('#cover').hide()
        $('#hider').hide()
        $('#selectedpsid').val()
        $('#psheader').hide()
        $('#hider').hide()
    }

    function gotoRO() {

        swal({
                title: "GO TO RO",
                text: "You will now exit the Customer Center and diplay this RO.  Are you sure?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, go to RO",
                closeOnConfirm: false
            },
            function() {
                roid = $('#selectedroid').val()
                location.href = '<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=' + roid

            });
    }

    function gotoPS() {

        swal({
                title: "GO TO Part Sale",
                text: "You will now exit the Customer Center and diplay this Part Sale.  Are you sure?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, go to Part Sale",
                closeOnConfirm: false
            },
            function() {
                psid = $('#selectedpsid').val()
                location.href = '<?= COMPONENTS_PRIVATE ?>/partsale/partsale.php?psid=' + psid

            });
    }

    function SelectWriteOff() {

        $("#womessage").css("visibility", 'visible')

        //console.log("robdballist:"+document.getElementById("robdballist").value)

        cid = $("#selectedcustomerid").val()
        console.log('Customer id is ' + cid);

        shopid = '<?php echo $shopid; ?>'


        clist = ""
        plist = ""

        // commented this out
        //bdamount = $('#bdamount').val();

        d = document.getElementsByClassName("checkinv")
        p = document.getElementsByClassName("checkinvps")

        bdamount - $('#bdamount').val()


        if (d.length > 0) {

            robdballist = ""
            robdballist = document.getElementById("robdballist").value

            robdballist = robdballist.substring(0, robdballist.length - 1)
            //console.log("modified robdballist:"+robdballist)
            robdarray = robdballist.split(",")

            for (j = 0; j < d.length; j++) {
                c = document.getElementById(d[j].id).checked
                if (c == true) {
                    itemid = document.getElementById(d[j].id).id
                    console.log("Inside  ro selected loop")
                    console.log(document.getElementById(d[j].id).id + ":" + c)
                    // reinstate original code here from sendInvoice as a model
                    if (itemid.substring(0, 2) == "ro") {
                        //console.log ("Item id is: " + itemid)
                        roid = itemid.replace("ro", "")

                        roidset = roid.split("|");

                        for (i = 0; i < roidset.length; i += 3) {
                            roid = roidset[0]
                            console.log("Roid is " + roid)

                            amt = roidset[1]
                            console.log("Roid amt is " + amt)
                            status = roidset[2]
                        }

                        if (status != "CLOSED") {
                            swal("This is not a Closed Invoice. Can not write off bad debt")
                            return
                        }

                        bal = $('#robal' + roid).val()

                        clist += roid + "|" + bal + ","
                    }
                }
            }

            clist = clist.substring(0, clist.length - 1)
            console.log("modified clist:" + clist)
            carray = clist.split(",")
            if (carray != "") {
                var str = ''
                for (j = 0; j < carray.length; j++) {
                    console.log("Just before robdarray")
                    cidset = carray[j].split("|");

                    for (k = 0; k < robdarray.length; k++) {
                        console.log("Just before carray")
                        console.log("Roid from bd list  is : " + robdarray[k])
                        console.log("Selected Roid is : " + cidset[0])
                        if (robdarray[k] == cidset[0]) {
                            str = str + carray[j] + ','
                        }
                    }
                }

                writeOff(str)

                $('#checkall').prop('checked', false)
            }
        }

        if (p.length > 0) {

            psbdballist = ""
            psbdballist = document.getElementById("psbdballist").value

            psbdballist = psbdballist.substring(0, psbdballist.length - 1)

            psbdarray = psbdballist.split(",")

            console.log("You on in part sale split")
            for (j = 0; j < p.length; j++) {
                c = document.getElementById(p[j].id).checked
                if (c == true) {
                    itemid = document.getElementById(p[j].id).id

                    //console.log(document.getElementById(p[j].id).id+":"+c)

                    if (itemid.substring(0, 2) == "PS") {
                        //console.log ("Item id is: " + itemid)
                        psid = itemid.replace("PS", "")

                        psidset = psid.split("|");

                        for (i = 0; i < psidset.length; i += 3) {
                            psid = psidset[0]
                            amt = psidset[1]
                            status = psidset[2]
                            // console.log ("Status is " + status)
                        }

                        if (status != "Closed") {
                            swal("This is not a Closed Invoice. Can not write off bad debt")
                            return
                        }

                        plist += psid + "|" + amt + ","
                    }
                }
            }

            plist = plist.substring(0, plist.length - 1)
            console.log("modified plist:" + plist)

            carray = plist.split(",")
            if (carray != "") {
                console.log("Inside the plist array")
                for (j = 0; j < carray.length; j++) {

                    for (k = 0; k < psbdarray.length; k++) {
                        console.log("We are inside the k array " + carray[j])

                        psidset = carray[j].split("|");

                        for (i = 0; i < psidset.length; i += 2) {
                            psid = psidset[0]
                            amt = psidset[1]
                            console.log("Status is " + status)
                        }




                    }
                }

                writeOffPS(plist)

                $('#checkallps').prop('checked', false)
            }
        }
    }

    function writeOff(str) {

        cid = $("#selectedcustomerid").val()
        var ds = 'cid=' + cid + '&str=' + str;

        if (cid.length == 0 || str.length == 0) {
            swal("You must select a customer and roid before you can write off an amount")
            return
        }

        swal({
                title: "",
                text: "Write off Bad Debt for the selected RO Invoice(s). Are you sure?",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Save",
                closeOnConfirm: false
            },

            function() {
                $.ajax({
                    type: "POST",
                    url: "writeoffbaddebt.php",
                    data: ds,
                    success: function() {
                        swal({
                                title: "Success",
                                text: "Amount has been written off",
                                type: "success",
                                showCancelButton: false,
                                confirmButtonClass: "btn-success",
                                confirmButtonText: "OK",
                                closeOnConfirm: false
                            },
                            function() {
                                showCustomerDetails(cid);
                                swal("The amount has been written off.  You will find it listed in Expenses")
                            });
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        swal("Error in Insert")
                    }
                });
            })

    }

    function writeOffPS(str) {
        //console.log("PSidAmount is " + amt)

        cid = $("#selectedcustomerid").val()

        //woa = $("#writeoffamount").val()
        //console.log('Write off amount is ' + bdamount);

        var ds = 'cid=' + cid + '&type=PS&str=' + str;

        if (cid.length == 0 || str.length == 0) {
            swal("You must select a customer and psid before you can write off an amount")
            return
        }

        swal({
                title: "",
                text: "Write off Bad Debt for the selected PS Invoice. Are you sure?",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Save",
                closeOnConfirm: false
            },

            function() {

                $.ajax({
                    type: "POST",
                    url: "writeoffbaddebt.php",
                    data: ds,
                    success: function() {
                        swal({
                                title: "Success",
                                text: "Amount has been written off",
                                type: "success",
                                showCancelButton: false,
                                confirmButtonClass: "btn-success",
                                confirmButtonText: "OK",
                                closeOnConfirm: false
                            },
                            function() {
                                showCustomerDetails(cid);
                                swal("The amount has been written off.  You will find it listed in Expenses")
                            });
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        swal("Error in Insert")
                    }
                });
            })
    }

    function postROPmt() {
        roid = $("#selectedroid").val()
        top.location.href = '<?= COMPONENTS_PRIVATE ?>/accounting/ar/editaccount.php?menu=yes&roid=' + roid

    }

    function postPSPmt() {
        psid = $("#selectedpsid").val()
        top.location.href = '<?= COMPONENTS_PRIVATE ?>/accounting/ar/editpsaccount.php?psid=' + psid

    }

    function checkBoxes() {

        x = setTimeout(function() {
            d = document.getElementsByClassName("checkinv")

            checkistrue = false
            for (j = 0; j < d.length; j++) {
                if (d[j].checked == true) {
                    $("#sendInvoiceButton").css("visibility", 'visible')
                    $("#PrintButton").css("visibility", 'visible')
                    checkistrue = true
                    return
                }
                if (checkistrue == false) {
                    $("#sendInvoiceButton").css("visibility", 'hidden')
                    $("#PrintButton").css("visibility", 'hidden')
                }
            }
        }, 100)

    }

    function checkBoxesps() {

        x = setTimeout(function() {
            p = document.getElementsByClassName("checkinvps")

            checkistrue = false
            for (j = 0; j < p.length; j++) {
                if (p[j].checked == true) {
                    $("#sendInvoiceButton").css("visibility", 'visible')
                    $("#PrintButton").css("visibility", 'visible')

                    checkistrue = true
                    return
                }
                if (checkistrue == false) {
                    $("#sendInvoiceButton").css("visibility", 'hidden')
                    $("#PrintButton").css("visibility", 'hidden')

                }
            }
        }, 100)

    }

    function checkAll() {

        x = setTimeout(function() {
            d = document.getElementsByClassName("checkinv")
            c = document.getElementById("checkall").checked

            for (j = 0; j < d.length; j++) {
                if (c == true) {
                    document.getElementById(d[j].id).checked = true
                } else {
                    document.getElementById(d[j].id).checked = false
                }
            }
        }, 10)
        checkBoxes();

    }

    function checkAllps() {

        x = setTimeout(function() {
            p = document.getElementsByClassName("checkinvps")
            c = document.getElementById("checkallps").checked

            for (j = 0; j < p.length; j++) {
                if (c == true) {
                    document.getElementById(p[j].id).checked = true
                } else {
                    document.getElementById(p[j].id).checked = false
                }
            }
        }, 10)
        checkBoxesps();

    }


    function sendInvoices(shopid, type) {
        //modifying significantly to determine if it is an RO or PS
        $("#spinner").show()

        cid = $("#selectedcustomerid").val()

        if (document.getElementById("sendingemail").value.length > 0 || type == 'print') {
            clist = ""
            clistps = ""

            d = document.getElementsByClassName("checkinv")
            console.log("This is the value of d " + d)
            if (d.length > 0) {

                for (j = 0; j < d.length; j++) {
                    c = document.getElementById(d[j].id).checked
                    if (c == true) {
                        itemid = document.getElementById(d[j].id).id

                        //console.log(document.getElementById(d[j].id).id+":"+c)

                        if (itemid.substring(0, 2) == "ro") {
                            //console.log ("Item id is: " + itemid)
                            roid = itemid.replace("ro", "")

                            roidset = roid.split("|");

                            for (i = 0; i < roidset.length; i += 3) {
                                roid = roidset[0]
                                amt = roidset[1]
                                status = roidset[2]

                                //console.log ("Roid in Send Invoice is " + roid)
                            }

                            clist += roid + ","
                        }
                    }
                }

                clist = clist.substring(0, clist.length - 1)
                console.log("modified clist:"+clist)
                carray = clist.split(",")
                if (carray != "") {
                    //console.log ("You have True ROs")
                    for (j = 0; j < carray.length; j++) {
                        //  console.log ("You are in the RO section")
                        //  console.log ("RO# is " + carray[j])
                        generateInvoices(carray[j])
                    }

                    $('#checkall').prop('checked', false)
                    if (type == 'email')
                        setTimeout("sendTheInvoices()", 5000)
                }
            }

            p = document.getElementsByClassName("checkinvps")

            // part sale
            if (p.length > 0) {
                for (j = 0; j < p.length; j++) {
                    c = document.getElementById(p[j].id).checked
                    //console.log(document.getElementById(p[j].id).id+":"+c)

                    if (c == true) {
                        itemid = document.getElementById(p[j].id).id
                        //console.log(document.getElementById(p[j].id).id+":"+c)

                        if (itemid.substring(0, 2) == "PS") {
                            psid = itemid.replace("PS", "")

                            psidset = psid.split("|");

                            for (i = 0; i < psidset.length; i += 3) {
                                psid = psidset[0]
                                amt = psidset[1]
                                status = psidset[2]
                                //console.log ("Psid in Send Invoice is " + psid)
                            }

                            clistps += psid + ","
                        }
                    }
                }

                clistps = clistps.substring(0, clistps.length - 1)
                console.log("modified clistps:"+clistps)

                carrayps = clistps.split(",")
                //console.log("Carray ps is " + carrayps)
                if (carrayps != "") {
                    for (j = 0; j < carrayps.length; j++) {
                        // this should launch thepsprintforemail and psprint
                        generateInvoicesPS(shopid, carrayps[j])
                    }

                    $('#checkallps').prop('checked', false)
                    if (type == 'email')
                        setTimeout("sendTheInvoicesPS()", 3000)
                }
            }

            if (type == 'print')
                PrintInvoices()
            // now create array from clist and loop it to create the invoices

        } else {
            swal("You must enter a valid email address to send invoices to")
        }
    }

    function generateInvoices(roid) {
        //console.log("creating RO invoice"+document.getElementById(d[j].id).id)
        r = Math.floor((Math.random() * 100000) + 1);

        //url = "../../../sbp/pdfinvoices/<?php echo $r; ?>?cc=y&reprint=yes&roid=" + roid + "&r=" + r

        var reprint = 'yes';
        var cc = 'y';

        var ds = 'cc=' + cc + '&reprint=' + reprint + '&roid=' + roid + '&r=' + r;
        //console.log(ds)

        $.ajax({
            type: "GET",
            url: "<?= COMPONENTS_PUBLIC . "/invoices/" . $r; ?>",
            data: ds,
            async: false,
            success: function(r) {
                rt = r + ","
                //console.log("RT:"+rt)
                document.getElementById("returnlist").value += rt
                //$('#returnlist').html(r)
                //console.log("returnlist:"+document.getElementById("returnlist").value)
            }
        });
    }

    function generateInvoicesPS(shopid, psid) {

        //console.log("creating PS invoice"+document.getElementById(p[j].id).id)
        r = Math.floor((Math.random() * 100000) + 1);

        var ds = 'shopid=' + shopid + '&psid=' + psid + "&fullpath=yes";
        //console.log(ds)

        $.ajax({
            type: "GET",
            //url: "<?= SBP ?>/psprintforemail.asp",
            url: "<?= COMPONENTS_PRIVATE ?>/partsale/psprintpdf.php",
            data: ds,
            async: false,
            success: function(r) {
                //console.log(r)
               // if (r.indexOf("success|") >= 0) {
                 //   rar = r.split("|")
                    pathfile = r
                    //console.log(pathfile)
                    rt = pathfile + ","
                    //console.log("RT:"+rt)
                    document.getElementById("returnlistps").value += rt
                    //$('#returnlist').html(r)
                    //console.log("returnlistps:"+document.getElementById("returnlistps").value)
               // }
            }
        });
    }

    function sendTheInvoices() {

        //validate the returnlist has a csv list in it
        if (document.getElementById("returnlist").value.length > 0) {
            //send the csvlist to sendinvoices.asp
            r = Math.floor((Math.random() * 100000) + 1);
            //email = document.getElementById("sendingemail").value
            email = $('#sendingemail').val()
            if (document.getElementById("update").checked == true) {
                upd = "yes"
            } else {
                upd = "no"
            }

            cid = $('#selectedcustomerid').val()
            msg = encodeURIComponent($('#emailmessage').val())

            var ds = 'customerid=' + cid + '&msg=' + msg + '&email=' + email + '&update=' + upd + '&invoices=' + $('#returnlist').val() + "&r=" + r

            $.ajax({
                type: "POST",
                url: "sendinvoices.php",
                data: ds,
                success: function(r) {
                   if (r == 'success') {
                       $('#emailinvmodal').modal('hide')
                       swal("Invoices sent")

                       $("#hider").hide()
                       $("#customeremail").hide()
                       $('#spinner').hide()

                       document.getElementById("returnlist").value = ""
                       $("#sendInvoiceButton").css("visibility", 'hidden')

                       d = document.getElementsByClassName("checkinv")
                       if (d.length > 0) {
                           for (j = 0; j < d.length; j++) {
                               c = document.getElementById(d[j].id).checked = false
                           }
                       }
                   } else {
                       swal(r)
                   }
                }
            });

        }
    }

    function PrintInvoices() {
        setTimeout(function() {
            list = $('#returnlist').val() + $('#returnlistps').val()
            eModal.iframe({
                title: 'Print Invoices',
                url: 'printinvoices.php?list=' + list,
                size: eModal.size.xl,
                buttons: [{
                    text: 'Close',
                    style: 'warning',
                    close: true
                }]

            });
            $('#spinner').hide()
            $('#returnlist').val('')
            $('#returnlistps').val('')

        }, 6000)
    }

    function sendTheInvoicesPS() {

        //validate the returnlistps has a csv list in it
     //   console.log("sending PartSales")
        if (document.getElementById("returnlistps").value.length > 0) {
            //send the csvlist to sendinvoices.asp
            r = Math.floor((Math.random() * 100000) + 1);
            //email = document.getElementById("sendingemail").value
            email = $('#sendingemail').val()
            if (document.getElementById("update").checked == true) {
                upd = "yes"
            } else {
                upd = "no"
            }

            cid = $('#selectedcustomerid').val()
            msg = encodeURIComponent($('#emailmessage').val())

            var ds1 = 'customerid=' + cid + '&msg=' + msg + '&email=' + email + '&update=' + upd + '&invoices=' + $('#returnlistps').val() + "&r=" + r
        //    console.log(ds1);

            $.ajax({
                type: "POST",
                url: "<?= COMPONENTS_PRIVATE ?>/partsale/sendpsinvoices.php",
                data: ds1,
                success: function() {
                    $('#emailinvmodal').modal('hide')
                    swal({
                            title: "Success",
                            text: "Send Successful",
                            type: "success",
                            showCancelButton: false,
                            confirmButtonClass: "btn-success",
                            confirmButtonText: "OK",
                            closeOnConfirm: false
                        },
                        function() {
                            location.reload();
                        });

                },
                error: function(xhr, ajaxOptions, thrownError) {
                    swal("Error in Send Email")
                }
            });

        }
    }


    function showSendInvoices() {
        $('#hider').hide()
        //$('#customeremail').hide()
        $('#emailinvmodal').modal('show')
        //$('#sendingemail').val(id)
        //document.getElementById("sendingemail").value = document.getElementById("customeremailfromdata").innerText
    }

    function cancelSendInvoices() {
        $('#hider').hide()
        $('#customeremail').hide()
    }
</script>

</body>
<?php
mysqli_close($conn);
?>

</html>
