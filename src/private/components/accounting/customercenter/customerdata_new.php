<?php
require CONN;

$shopid = $_COOKIE['shopid'];
$cid = $_GET['cid'];
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

$stmt = "SELECT LastName,FirstName, Address,City,State,Zip,HomePhone,WorkPhone,CellPhone,EMail FROM customer WHERE shopid = ? and customerid = ?" ;

if($query = $conn->prepare($stmt)){
	$query->bind_param("si",$shopid,$cid);
    $query->execute();
    $query->bind_result($ln,$fn,$addr,$city,$state,$zip,$home,$work,$cell,$email);
    $query->fetch();
    $query->close();
}else{
	echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "SELECT coalesce(sum(totallbr+totalprts+totalsublet+totalfees+salestax-discountamt),0) as tro "
 . "from repairorders "
 . "WHERE shopid = ?"
 . "  AND customerid = ?"
 . "  AND rotype != 'No Approval'";
if($query = $conn->prepare($stmt)){
	$query->bind_param("si",$shopid,$cid);
    $query->execute();
    $query->bind_result($totalros);
    $query->fetch();
    $query->close();
}else{
	echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

// subtract out writeoff debt
$stmt = "SELECT coalesce(sum(amount),0) as bdamount FROM expenses WHERE shopid = ? AND cid = ? AND expensename = 'Bad Debt'";
if($query = $conn->prepare($stmt)){
	$query->bind_param("si",$shopid,$cid);
    $query->execute();
    $query->bind_result($bdamount);
    $query->fetch();
    $query->close();
}else{
	echo "Expense Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$totalros = sbpround(($totalros - $bdamount),2) ;
?>
	<table class="table table-condensed table-striped">
	<tr>
		<td style="width:50%">
		<?php
			echo $cid."-".$ln . ", " . $fn . "<BR>";
			echo $addr .  "<BR>";
			echo $city . ", " . $state . ", " . $zip . "<BR>";
		?>
		</td>
		<td style="width:50%">
		<?php
			echo "Home: " . formatPhone($home) . "/ Work: " . formatPhone($work) . "/ Cell: " . formatPhone($cell) . "<BR>";
			echo "Email: <span id='customeremailfromdata'>" . $email . "</span><br>";
		    echo "<b>Total RO's: $" . number_format($totalros,2);
		?>
		</td>
	</tr>
	<tr>
		<td>
		<br/>
		<strong><span style="font-size:small;color:red">*Click checkboxes to email those invoices to the customer
		OR click an invoice to view it</span></strong></td>
		<td id="balance" style="color:red;font-weight:bold">&nbsp;</td>
	</tr>
	</table>

	<table class="table table-condensed table-striped table-header-bg">
	<thead>
	<tr class="">
		<?php if( !$shopIsReadOnly ): ?>
			<td class="headerrow" style="width: 0">
				<input onclick="checkAll()" id="checkall" name="checkall" type="checkbox">
			</td>
		<?php endif; ?>

		<td class="headerrow">Transaction Type</td>
		<td class="headerrow">Number</td>
		<td style="text-align:center" class="headerrow">Date</td>
		<td class="headerrow">Vehicle</td>
		<td class="headerrow">RO Status</td>
		<td class="headerrow" style="text-align:right">Amount</td>
	</tr>
	</thead>
	<tbody>
<?php
$runningdisplaybalance = 0;
$displaytotalro  = 0;
$rtotalro  = 0;
$displaybalance = 0;
$psdisplaybalance = 0;
$rbal = 0.00;
$psbal = 0.00;
$robdballist = "";
$psbdballist = "";

$stmt = "SELECT roid,StatusDate,vehinfo,TotalRO,TotalLbr,TotalPrts,TotalSublet,TotalFees,SalesTax,DiscountAmt,balance,status";
$stmt .= " FROM repairorders ";
$stmt .= "WHERE shopid = ? ";
$stmt .= "  AND customerid = ? ";
$stmt .= "  AND rotype != 'NO PROBLEM' and rotype != 'NO APPROVAL' ";
$stmt .= "  ORDER BY roid desc ";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("si",$shopid,$cid);
    $query->execute();
    $roresult = $query->get_result();

	while($row = $roresult->fetch_array()) {
		$bal = 0;
		$roid = $row['roid'];
		$statusdate = new DateTime($row["StatusDate"]);
		$displaybalance = 0;

		if (is_numeric(substr($row["status"],0,1)) ) {
			$rostatus = substr($row["status"],1);
		}else{
			$rostatus = $row["status"];
		}

		// if the ro is not quite zero, set to zero
		if ($row["balance"] > -0.01 and $row["balance"] < 0.01) {
			$stmt = "UPDATE repairorders SET ";
			$stmt .= "balance  = 0 ";
			$stmt .= "WHERE shopid = ? ";
			$stmt .= "  AND roid = ? ";
			$stmt .= "LIMIT 1";

			if ($query = $conn->prepare($stmt)){
					$query->bind_param("si",$shopid,$roid);
				if ($query->execute()){
					$conn->commit();
				}else{
					echo $conn->errno;
				}
			}else{
					echo "RO Update Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}

		$displaytotalro = $row["TotalLbr"] + $row["TotalPrts"] + $row["TotalSublet"] + $row["TotalFees"] + $row["SalesTax"] - $row["DiscountAmt"];
		$rtotalro += $displaytotalro;
		$displaybalance = $displaytotalro;

		$onclickRO = !$shopIsReadOnly 
			? "onclick=\"showRO('{$row["roid"]}','{$row["status"]}')\"" 
			: "";
	?>
		<tr class="datatr" <?= $onclickRO; ?>>
			<?php if( !$shopIsReadOnly ): ?>
				<td class="datarow" style="width: 0; height: 22px;">
					<input onclick="checkBoxes()" class="checkinv" id="ro<?php echo $row["roid"]; ?><?php echo "|"; ?><?php echo $displaytotalro; ?><?php echo "|"; ?><?php echo $row["status"]; ?>" type="checkbox">
				</td>
			<?php endif; ?>

			<td class="datarow" style="height: 22px">Invoice/RO&nbsp;</td>
			<td class="datarow" style="height: 22px">RO#<?php echo $row["roid"]; ?>&nbsp;</td>
			<td class="datarow" style="text-align:right;text-align:center; height: 22px;" ><?php echo $statusdate->format('m/d/Y'); ?>&nbsp;</td>
			<td id ="vehinfo" class="datarow" style="height: 22px"><?php echo $row["vehinfo"]; ?>&nbsp;</td>
			<td id ="status" class="datarow" style="height: 22px"><?php echo $rostatus; ?>&nbsp;</td>

			<td id="totalro<?php echo $row["roid"] ; ?>,'<?php echo $row["status"]; ?>'" onclick="showRO('<?php echo $row["roid"] ; ?>')" class="datarow" style="text-align:right; height: 22px;"><?php echo sbpround($displaytotalro,2); ?>&nbsp;</td>
		</tr>
	<?php

		//now get all payments.
		$tramt = 0;
		$ramt = 0;
		$pdate = "";

		$stmt = "select * ";
		$stmt .= "from accountpayments ";
		$stmt .= "WHERE shopid = ? ";
		$stmt .= "  AND roid = ? and ptype!='Bad Debt' ";
		$stmt .= "  ORDER BY pdate desc ";
		//echo $stmt;

		if($query = $conn->prepare($stmt)){
			$query->bind_param("si",$shopid,$roid);
			$query->execute();
			$result = $query->get_result();
			while($ap = mysqli_fetch_assoc($result)) {
				$ramt = $ramt + $ap["amt"];
				$tramt = $tramt + $ap["amt"];
				$pdate = new DateTime($ap["pdate"]);

	?>
				<tr class="adatatr">
					<?php if( !$shopIsReadOnly ): ?>
						<td style="padding-left:20px; width: 0;" class="adatarow">&nbsp;</td>
					<?php endif; ?>
					<td style="padding-left:20px" class="adatarow">Payment Received&nbsp;</td>
					<td class="adatarow">Ref# <?php echo $ap["pnumber"]; ?>&nbsp;</td>
					<td style="text-align:center" class="adatarow"><?php echo $pdate->format('m/d/Y'); ?>&nbsp;</td>
					<td class="adatarow">&nbsp;</td>
					<td class="adatarow"><?php echo $ap["ptype"]; ?>&nbsp;</td>
					<td class="adatarow" style="text-align:right"><?php echo number_format($ap["amt"],2); ?>&nbsp;</td>
				</tr>

	<?php
				$displaybalance = round($displaytotalro,2)-round($tramt,2);
				$runningdisplaybalance += $displaybalance;

	?>
<?php
			} // end of payment while

		} // end of payment if

		// now get the bad debt if any
		$stmt = "SELECT amount,paiddate,ref FROM expenses WHERE shopid = ? AND cid = ? AND roid = ? and expensename = 'Bad Debt'";
		if($query = $conn->prepare($stmt)){
			$query->bind_param("sii",$shopid,$cid,$row['roid']);
		    $query->execute();
		    $br = $query->get_result();
		    while ($brs = $br->fetch_assoc()){
				$displaybalance = round($displaybalance,2)-round($brs['amount'],2);
				$runningdisplaybalance += $displaybalance;

?>
				<tr class="adatatr">
					<?php if( !$shopIsReadOnly ): ?>
						<td style="padding-left:20px; width: 0;" class="adatarow">&nbsp;</td>
					<?php endif; ?>
					<td style="padding-left:20px;color:red;font-weight:bold" class="adatarow">** BAD DEBT WRITE OFF **&nbsp;</td>
					<td class="adatarow">Ref# <?php echo $brs["ref"]; ?>&nbsp;</td>
					<td style="text-align:center" class="adatarow"><?php echo date("m/d/Y",strtotime($brs['paiddate'])); ?>&nbsp;</td>
					<td class="adatarow">&nbsp;</td>
					<td class="adatarow">&nbsp;</td>
					<td class="adatarow" style="text-align:right"><?php echo number_format($brs["amount"],2); ?>&nbsp;</td>
				</tr>
<?php
		    }

		}
		if ($displaybalance >= 0.01) {
		    $robdballist = $robdballist . $roid . "," ;
			$colormod = "color:red;";
		}else{
			$colormod = "color:black;";
		}

?>
				<tr >
					<?php if( !$shopIsReadOnly ): ?>
						<td style="padding-left:20px; width: 0;">&nbsp;</td>
					<?php endif; ?>
					<td style="padding-left:20px">&nbsp;</td>
					<td>&nbsp;</td>
					<td>&nbsp;</td>
					<td style="text-align:center">&nbsp;</td>
					<td style="text-align:right">
						<?php if( !$shopIsReadOnly ): ?>
						<strong>
							<?php
							if ($displaybalance < 0 ){
								echo "<button onclick='splitPmt(" . $row["roid"] . ")' class='button' type='button'>Split Payment</button> &nbsp;&nbsp;&nbsp;";
							}
							?>
						<?php endif; ?>
						Balance This RO:&nbsp;
					</strong>
					</td>
					<td style="text-align:right;<?php echo $colormod; ?>">
					<strong><?php echo $displaybalance; ?>&nbsp;</strong></td>
				</tr>
				<input type="hidden" id="robal<?= $row["roid"]?>" value="<?= $displaybalance?>" >

<?php
        $rbal += $displaybalance;
	} // end of ro while loop
}else{
?>
		<tr>
		<td style="width: 0">&nbsp;</td>
		<td>No Repair Orders Found&nbsp;</td>
		<td colspan="4">&nbsp;</td>
		</tr>
<?php
}
?>
		<tr class="">
			<?php if( !$shopIsReadOnly ): ?>
				<td class="psheaderrow" style="width: 0">&nbsp;
					<input onclick="checkAllps()" id="checkallps" name="checkallps" type="checkbox">
				</td>
			<?php endif; ?>
			<td class="psheaderrow">Transaction Type Part Sale</td>
			<td class="psheaderrow">Number</td>
			<td style="text-align:center" class="psheaderrow">Date</td>
			<td class="psheaderrow"></td>
			<td class="psheaderrow">PS Status</td>
			<td class="psheaderrow" style="text-align:right">Amount</td>
		</tr>
<?php

$psdisplaytotalro = 0;
$psrtotalro = 0;

$stmt = "select psid,statusdate,total,balance,status ";
$stmt .= "from ps ";
$stmt .= "WHERE shopid = ? ";
$stmt .= "  AND cid = ? ";
$stmt .= "  AND status = 'CLOSED' ";
$stmt .= "  ORDER BY psid desc ";
if($query = $conn->prepare($stmt)){
	$query->bind_param("si",$shopid,$cid);
	$query->execute();
	$result = $query->get_result();
	while($psrow = mysqli_fetch_assoc($result)) {
		$psid = $psrow['psid'];
		$psdisplaytotalro = $psrow["total"];
		$psrtotalro = $psrtotalro + $psdisplaytotalro;
		$statusdate = new Datetime($psrow["statusdate"]);
		
		$onclickPS = !$shopIsReadOnly 
			? "onclick=\"showPS('{$psrow["psid"]}')\"" 
			: "";
?>
		<tr class="psdatatr" <?= $onclickPS; ?>>
			<?php if( !$shopIsReadOnly ): ?>
				<td class="psdatarow" style="width: 0">
					<input onclick="checkBoxesps()" class="checkinvps" id="PS<?php echo $psrow["psid"]; ?><?php echo "|"; ?><?php echo $psdisplaytotalro; ?><?php echo "|"; ?><?php echo $psrow["status"]; ?>" type="checkbox">
				</td>
			<?php endif; ?>
			<td class="psdatarow"><b>Invoice/Part Sale</b>&nbsp;</td>
			<td class="psdatarow">PS# <?php echo $psrow["psid"]; ?>&nbsp;</td>
			<td class="psdatarow" style="text-align:center" ><?php echo date_format($statusdate,'m/d/Y'); ?>&nbsp;</td>
			<td class="psdatarow">N/A&nbsp;</td>
			<td class="psdatarow"><?php echo $psrow["status"]; ?>&nbsp;</td>
			<td class="psdatarow" style="text-align:right"><?php echo number_format($psdisplaytotalro,2); ?>&nbsp;</td>
		</tr>
<?php

		$stmt = "select * ";
		$stmt .= "from `accountpayments-ps` ";
		$stmt .= "WHERE shopid = ? ";
		$stmt .= "  AND psid = ? ";
		$stmt .= "  ORDER BY pdate desc ";
		$psramt = 0;
		$pstramt = 0;
		if($query = $conn->prepare($stmt)){
			$query->bind_param("si",$shopid,$psid);
			$query->execute();
			$apresult = $query->get_result();
            $pstotalpayments = 0;
			while($ap = mysqli_fetch_assoc($apresult)) {
				$psramt = $psramt + $ap["amt"];
				$pstramt = $pstramt + $ap["amt"];
				$pdate = new Datetime($ap["pdate"]);
?>
		<tr >
			<?php if( !$shopIsReadOnly ): ?>
				<td style="padding-left:20px; width: 0;" class="adatarow">&nbsp;</td>
			<?php endif; ?>
			<td style="padding-left:20px" class="adatarow">Payment Received&nbsp;</td>
			<td class="adatarow">Ref# <?php echo $ap["pnumber"]; ?>&nbsp;</td>
			<td style="text-align:center" class="adatarow"><?php echo date_format($pdate,'m/d/Y'); ?>&nbsp;</td>
			<td class="adatarow"><?php echo $ap["ptype"]; ?>&nbsp;</td>
			<td class="adatarow" style="text-align:right"><?php echo number_format($ap["amt"],2); ?>&nbsp;</td>
		</tr>
<?php
                $pstotalpayments += $ap["amt"];
			} // end of ap part sale while
		}else{
			echo $conn->error;
		}

        $psdisplaybalance = 0;
        $psdisplaybalance = round($psrow["total"] - $pstotalpayments, 2);
        $runningdisplaybalance = $runningdisplaybalance + $psdisplaybalance;

        if ($psdisplaybalance <= 0.00) {
            $pscolormod = "color:black;";
        }else{
            $pscolormod = "color:red;";
        }

        $rbal += $psdisplaybalance;
        $psbal += $psdisplaybalance;
?>
		<tr class="adatatr">
			<?php if( !$shopIsReadOnly ): ?>
				<td style="padding-left:20px; width: 0;" class="adatarow">&nbsp;</td>
			<?php endif; ?>
			<td style="padding-left:20px" class="adatarow">&nbsp;</td>
			<td class="adatarow">&nbsp;</td>
			<td style="text-align:center" class="adatarow">&nbsp;</td>
			<td style="text-align:right" class="adatarow">Balance This Part Sale:&nbsp;</td>
			<td class="adatarow" style="text-align:right;<?php echo $pscolormod; ?>"><?php echo $psdisplaybalance; ?>&nbsp;</td>
		</tr>
<?php
	} // end of part sale while
}	// do not know put in on 6/27/17


$psamt = 0;
$ramt = 0;
$psramt = 0;
$bd = 0;

if ($bd > 0) {
?>
		<tr >
			<?php if( !$shopIsReadOnly ): ?>
				<td style="padding-left:20px; width: 0;" class="bdadatarow">&nbsp;</td>
			<?php endif; ?>
			<td style="padding-left:20px" class="bdadatarow">Total Written off as Bad Debt&nbsp;</td>
			<td class="bdadatarow">&nbsp;</td>
			<td class="bdadatarow">&nbsp;</td>
			<td style="text-align:right" class="bdadatarow">&nbsp;</td>
			<td class="bdadatarow">&nbsp;</td>
			<td class="bdadatarow" style="text-align:right"><?php echo number_format($bd,2); ?>&nbsp;</td>
		</tr>
<?php
}	// end of bd if

//$rbal = $rbal + ($psrtotalro-$psramt) + ($rtotalro-$ramt) - $bd;
//$rbal = $rbal + ($psrtotalro-$psramt) + ($rtotalro-$ramt) ;

$stmt = "SELECT roid, totallbr+totalprts+totalsublet+salestax+totalfees-discountamt as tro ";
$stmt .= "FROM repairorders ";
$stmt .= "WHERE shopid = ? ";
$stmt .= "  AND customerid = ? ";
$stmt .= "  AND ucase(rotype) != 'NO APPROVAL' ";
$stmt .= "  AND `status` = 'CLOSED' ";
//echo $stmt;

if($query = $conn->prepare($stmt)){
	$query->bind_param("si",$shopid,$cid);
	$query->execute();
	$result = $query->get_result();
}else{
	echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$numofrows = $result->num_rows;

// Check for data
$ttlro = 0;
$rolist = "";
$ttlps = 0;
$ttlpspmts = 0;

if ($numofrows > 0) {
	//echo "Number of Rows works: " . $numofrows;
	while($bad = mysqli_fetch_assoc($result)) {
		$rolist= $rolist . $bad['roid'] . ",";
		//cdbl
		$ttlro = $ttlro + $bad['tro'];

	} // end of bad loop
}else{
	$ttlro = 0;
	$rolist= "";

} // end of bad if

if (substr($rolist,-1) == ","){
	$rolist = substr($rolist,0,strlen($rolist)-1);
}

$ttlpmts = 0;

if (strlen($rolist) > 0) {
	//get ro payments for the list of closed ros

	$query = "SELECT coalesce(sum(amt),0) as ttlpmts";
	$query .= " FROM accountpayments ";
	$query .= "WHERE shopid = '{$shopid}'";
	//$query .= "  AND cid = {$cid} ";
	$query .= "  AND roid in ($rolist)";
	//echo $query;

	$apcresult = mysqli_query($conn, $query);

	if (!$apcresult) {
		die("Database accounts payment closed query failed.");
	}

	$apc = mysqli_fetch_assoc($apcresult);

	$ttlpmts = $apc["ttlpmts"];

}else{
	$ttlpmts = 0;
} // end of get ro payments for the list of closed ros

// Get the closed part sales
	$pslist = "";

	$stmt = "SELECT psid, total";
	$stmt .= " FROM ps ";
	$stmt .= "WHERE shopid = ? ";
	$stmt .= "  AND cid = ? ";
	$stmt .= "  AND `status` = 'CLOSED'";
	//echo $stmt;

	if($query = $conn->prepare($stmt)){
		$query->bind_param("si",$shopid,$cid);
	    $query->execute();
	    $result = $query->get_result();

	    $numofrows = $result->num_rows;

	    if ($numofrows > 0 ) {
		    while($row = $result->fetch_array()) {
		    	$pslist = $pslist . $row["psid"] . "," ;
		    	$ttlps = $ttlps +  $row["total"] ;

		    } // end of part sale loop
	    }else{
	        	$ttlps = 0 ;
				$pslist= "";
	    }

	}else {
		echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	if (substr($pslist,-1) == ","){
		$pslist = substr($pslist,0,strlen($pslist)-1);
	}

	if (strlen($pslist) > 0) {
		// cant substitute until I can figure out the in clause for bindingsubstitute
		$query = "SELECT coalesce(sum(amt),0) as ttlpspmts";
		$query .= " FROM `accountpayments-ps` ";
		$query .= "WHERE shopid = '{$shopid}'";
		$query .= "  AND cid = {$cid} ";
		$query .= "  AND psid in ($pslist)";
		//echo $query;

		$apcresult = mysqli_query($conn, $query);

		if (!$apcresult) {
			die("Database accounts payment ps closed query failed.");
		}

		$apc = mysqli_fetch_assoc($apcresult);

		$ttlpspmts = $apc["ttlpspmts"];

	}else {
		$ttlpspmts = 0;
	}

// added decrement of bad debt
//$rbal = number_format($ttlro+$ttlps-$ttlpmts-$ttlpspmts,2);
//$rbal = number_format($ttlro+$ttlps-$ttlpmts-$ttlpspmts-$bd,2);

if ($rbal < 0.02 and $rbal > -0.02 ){
	$rbal = 0.00;
}
?>
	</tbody>
</table>
	<input type="hidden" id="rbal" value="<?php echo asDollars($rbal) ; ?>">
	<input type="hidden" id="robdballist" value="<?php echo $robdballist ; ?>">
	<input type="hidden" id="psbdballist" value="<?php echo $psbdballist ; ?>">

    <?php
    mysqli_close($conn);
    ?>

