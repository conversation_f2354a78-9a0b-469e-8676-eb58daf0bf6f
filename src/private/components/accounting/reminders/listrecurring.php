<?php

require CONN;
require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];

$today = new DateTime('now');
$today = date_format($today, 'm/d/Y');

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<!DOCTYPE html>
<html>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">
     <title><?= getPageTitle() ?></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon' />
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        .col-md-6 {
            border: 1px black solid
        }

        .col-md-8 {
            border: 1px black solid
        }

        .col-md-4 {
            border: 1px black solid
        }
    </style>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    <div id="mainalert" style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large" class="alert alert-success"></div>
    <div id="header"></div>
    <!-- Page Container -->
    <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
    <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">

        <!-- Sidebar -->
        <nav id="sidebar">
            <!-- Sidebar Scroll Container -->
            <div id="sidebar-scroll">
                <!-- Sidebar Content -->
                <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                <div class="sidebar-content">
                    <!-- Side Header -->
                    <div class="side-header side-content bg-white-op">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                            <i class="fa fa-times"></i>
                        </button>
                        <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                            <i class="text-primary">
                                <?php getLogo() ?></i>
                            <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                        </a>
                    </div>
                    <!-- END Side Header -->

                    <!-- Side Content -->
                    <div class="side-content-sbp-ro side-content">
                        <ul class="nav-main">
                            <li>
                                <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Back to WIP</span></a>
                            </li>
                            <li>
                                <a href="<?= COMPONENTS_PRIVATE ?>/accounting/default.php"><i class="fa fa-home"></i><span class="sidebar-mini-hide">Accounting Home</span></a>
                            </li>
                            <li>
                                <a href="<?= COMPONENTS_PRIVATE ?>/accounting/reminders/reminders.php"><i class="fa fa-home"></i><span class="sidebar-mini-hide">Reminders Home</span></a>
                            </li>
                            <li>
                                <a href="<?= COMPONENTS_PRIVATE ?>/accounting/reminders/listrecurring.php"><i class="fa fa-book"></i><span class="sidebar-mini-hide">List Recurring Reminders</span></a>
                            </li>

                            <?php if (!$shopIsReadOnly): ?>
                                <li>
                                    <a href="#" onclick="newRem('<?php echo $shopid; ?>')"><i class="fa fa-book"></i><span class="sidebar-mini-hide">Add Recurring Reminder</span></a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <!-- END Side Content -->
                </div>
                <!-- Sidebar Content -->
            </div>
            <!-- END Sidebar Scroll Container -->
        </nav>
        <!-- END Sidebar -->

        <!-- Header -->
        <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar" class="content-mini content-mini-full">

            <!-- Header Navigation Right -->
            Recurring Reminders
            <!-- END Header Navigation Right -->

            <!-- Header Navigation Left -->

            <ul class="nav-header pull-left">
                <li class="hidden-md hidden-lg">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                        <i class="fa fa-navicon"></i>
                    </button>
                </li>
                <li class="hidden-xs hidden-sm">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                        <i class="fa fa-bars"></i>
                    </button>
                </li>
                <li>
                    <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                    <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                        <i class="si si-grid"></i>
                    </button>
                </li>
                <li class="visible-xs">
                    <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                    <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                        <i class="fa fa-search"></i>
                    </button>
                </li>
                <li>

                </li>
            </ul>

            <!-- END Header Navigation Left -->
        </header>
        <!-- END Header -->
        <!-- Main Container -->

        <!-- Main Container -->
        <main class="container-fluid" id="main-container" style="display:block;">
            <?php if (!$shopIsReadOnly): ?>
                <button class="btn btn-info" type="button" onclick="newRem('<?php echo $shopid; ?>')">Add Recurring Reminder</button>
            <?php endif; ?>
            <br>


            <table class="table table-condensed table-striped table-header-bg">
                <thead>
                    <tr>
                        <td class="style8"><strong>Subject</strong></td>
                        <td class="style5"><strong>Day Due</strong></td>
                        <!-- edit/delete -->
                    </tr>
                </thead>
                <?php

                $texp = 0;

                $stmt = "SELECT * ";
                $stmt .= " FROM `accountingremindersrecurring` ";
                $stmt .= "WHERE shopid = ? ";
                //echo $stmt;

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $acctresult = $query->get_result();
                } else {
                    echo "Account Reminders Recurring Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                if ($acctresult->num_rows > 0) {
                    while ($acct = $acctresult->fetch_array()) {
                        $onclickRem = !$shopIsReadOnly
                            ? "onclick=\"editRem(
                                '{$acct["id"]}',
                                '{$acct["shopid"]}',
                                '" . str_replace("'", "\\'", $acct["remindersubject"]) . "',
                                '{$acct["dueday"]}'
                            )\""
                            : "";
                ?>
                        <tr style="cursor:pointer" <?= $onclickRem; ?>>


					<td><?php echo strtoupper($acct["remindersubject"]); ?>&nbsp;</td>
					<td><?php echo $acct["dueday"]; ?>&nbsp;</td>


				</tr>

<?php
                    } // end of reminder while

                } else {
?>
		<tr>
			<td colspan=" 3">No Recurring Reminders</td>
                        </tr>
                    <?php
                } // end of if for reminders
                    ?>

            </table>


        </main>
        <!-- END Main Container -->

        <!-- Footer -->
        <!-- END Footer -->
    </div>
    <!-- END Page Container -->

    <?php if (!$shopIsReadOnly): ?>
        <!-- Modals -->
        <!-- Add Reminder Modal -->
        <div id="addremModal" class="modal fade" role="dialog">
            <input id="shopid" name="shopid" value="" type="hidden">

            <div class="modal-dialog">

                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">Add New Reminder </h4>
                    </div>
                    <div class="modal-body">

                        <table class="table table-condensed table-striped">
                            <tr>
                                <td class="text-left">Subject:</td>
                                <td class="style9"><input class="form-control" id="subject" name="subject" type="text" value="" /></td>
                            </tr>

                            <tr style="display:block" id="dueday">
                                <td>Day of Month to Pay</td>
                                <td>
                                    <select class="data" id="dayofmonth" name="dayofmonth">
                                        $j =1;
                                        <?php
                                        $j = 1;
                                        while ($j >= 1 && $j <= 31) {
                                        ?>
                                            <option value="<?php echo $j; ?>"><?php echo $j; ?></option>
                                        <?php
                                            $j = $j + 1;
                                        }
                                        ?>
                                    </select>
                                </td>

                            </tr>

                        </table>
                    </div>
                    <div class="modal-footer">
                        <button onclick="addRem()" type="button" class="btn btn-primary">Save Changes</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Recurring Reminder Modal -->
        <div id="remModal" class="modal fade" role="dialog">
            <input id="id" name="id" value="" type="hidden">
            <input id="shopid" name="shopid" value="" type="hidden">

            <div class="modal-dialog">

                <!-- Modal content-->
                <div class="modal-content">
                    <div class="block-header bg-primary-dark">
                        <ul class="block-options">
                            <li>
                                <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                            </li>
                        </ul>
                        <h4 class="block-title">Edit Recurring Reminder </h4>
                    </div>
                    <div class="modal-body">
                        <table class="table table-condensed table-striped">
                            <tr>
                                <td class="text-left">Subject:</td>
                                <td class="style9"><input type="subject2" class="form-control" id="subject2" value=""></td>
                            </tr>
                            <tr>
                                <td class="text-left">Day of Month to Pay:</td>
                                <td class="style9"><input class="form-control" id="dueday2" name="dueday2" type="text" value="" /></td>
                            </tr>

                        </table>
                    </div>
                    <div class="modal-footer">
                        <button onclick="saveRem()" type="button" class="btn btn-primary">Save Changes</button>
                        <!--button onclick="deleteRem()" type="button" class="btn btn-danger">Delete</button-->
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
    <script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
    <script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

    <!-- Page Plugins -->

    <!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
    <script>
        function newRem(shopid) {

            $('#addremModal').modal('show')

            $('#id').val(id)
            $('#shopid').val(shopid)
            $('#dueday').val(dueday)
        }

        function addRem(shopid) {


            var shopid = $("#shopid").val();
            var subject = $("#subject").val();
            var dayofmonth = $("#dayofmonth").val();

            console.log(shopid)
            console.log("add " + subject)

            var ds = 'shopid=' + shopid + '&subject=' + subject + '&recurring=Yes&dayofmonth=' + dayofmonth;

            console.log(ds)

            $.ajax({
                type: "post",
                url: "addreminder.php",
                data: ds,
                success: function() {
                    location.reload();
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    swal("Error in Save")
                }
            });

        }

        function editRem(id, shopid, subject, dueday) {
            console.log(shopid)
            console.log(subject)

            <?php
            if ($shopid == "demo") {
                echo "$('#remModal').modal('show')";
            } else {
                echo "$('#remModal').modal('show')";
            }
            ?>

            $('#id').val(id)
            $('#shopid').val(shopid)
            $('#subject2').val(subject)
            $('#dueday2').val(dueday)

        }

        function saveRem() {

            var id = $("#id").val();
            var shopid = $("#shopid").val();
            var subject = $("#subject2").val();
            var dueday = $("#dueday2").val();

            var ds = 'id=' + id + '&shopid=' + shopid + '&subject=' + subject + '&dueday=' + dueday;

            $.ajax({
                type: "post",
                url: "editreminder.php",
                data: ds,
                success: function() {
                    location.reload();
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    swal("Error in Save")
                }
            });



        }


        function recurringPayment(v) {

            if (v == "Yes") {
                document.getElementById("dueday").style.display = ""
            }
            if (v == 'No') {
                $('#dueday').hide()
            }
        }
    </script>
    <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>
<?php
mysqli_close($conn);
?>

</html>
