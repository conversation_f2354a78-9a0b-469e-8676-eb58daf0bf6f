<!DOCTYPE html>
<html>
<?php
require CONN;
require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$disabled = $shopIsReadOnly ? "disabled" : "";
?>
<!--[if IE 9]>
<html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus"> <!--<![endif]-->
<head>
    <meta charset="utf-8">
     <title><?= getPageTitle() ?></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon'
    / >
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        .col-md-6 {
            border: 1px black solid
        }

        .col-md-8 {
            border: 1px black solid
        }

        .col-md-4 {
            border: 1px black solid
        }

    </style>
</head>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<div id="mainalert"
     style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large"
     class="alert alert-success"></div>
<div id="header"></div>
<!-- Page Container -->
<!--
    Available Classes:

    'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

    'sidebar-l'                  Left Sidebar and right Side Overlay
    'sidebar-r'                  Right Sidebar and left Side Overlay
    'sidebar-mini'               Mini hoverable Sidebar (> 991px)
    'sidebar-o'                  Visible Sidebar by default (> 991px)
    'sidebar-o-xs'               Visible Sidebar by default (< 992px)

    'side-overlay-hover'         Hoverable Side Overlay (> 991px)
    'side-overlay-o'             Visible Side Overlay by default (> 991px)

    'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

    'header-navbar-fixed'        Enables fixed header
-->
<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">

    <!-- Sidebar -->
    <nav id="sidebar">
        <!-- Sidebar Scroll Container -->
        <div id="sidebar-scroll">
            <!-- Sidebar Content -->
            <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
            <div class="sidebar-content">
                <!-- Side Header -->
                <div class="side-header side-content bg-white-op">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                        <i class="text-primary">
                            <?php getLogo() ?></i>
                        <span class="h4 font-w600 sidebar-mini-hide">
                        </span>
                    </a>
                </div>
                <!-- END Side Header -->

                <!-- Side Content -->
                <div class="side-content-sbp-ro side-content">
                    <ul class="nav-main">
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Back to WIP</span></a>
                        </li>
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/accounting/default.php"><i class="fa fa-home"></i><span
                                        class="sidebar-mini-hide">Accounting Home</span></a>
                        </li>
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/accounting/company/categories.php"><i
                                        class="fa fa-book"></i><span class="sidebar-mini-hide">Chart of Accounts</span></a>
                        </li>

                    </ul>
                </div>
                <!-- END Side Content -->
            </div>
            <!-- Sidebar Content -->
        </div>
        <!-- END Sidebar Scroll Container -->
    </nav>
    <!-- END Sidebar -->

    <!-- Header -->
    <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar"
            class="content-mini content-mini-full">

        <!-- Header Navigation Right -->
        Company Preferences
        <!-- END Header Navigation Right -->

        <!-- Header Navigation Left -->

        <ul class="nav-header pull-left">
            <li class="hidden-md hidden-lg">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                    <i class="fa fa-navicon"></i>
                </button>
            </li>
            <li class="hidden-xs hidden-sm">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" id="close-sidebar"
                        data-action="sidebar_mini_toggle" type="button">
                    <i class="fa fa-bars"></i>
                </button>
            </li>
            <li>
                <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                <button style="display:none" class="btn btn-default pull-right" data-toggle="modal"
                        data-target="#apps-modal" type="button">
                    <i class="si si-grid"></i>
                </button>
            </li>
            <li class="visible-xs">
                <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search"
                        data-class="header-search-xs-visible" type="button">
                    <i class="fa fa-search"></i>
                </button>
            </li>
            <li>

            </li>
        </ul>

        <!-- END Header Navigation Left -->
    </header>
    <!-- END Header -->
    <!-- Main Container -->

    <!-- Main Container -->
    <main class="container-fluid" id="main-container" style="display:block;">
        <br><br>

        <input name="action" type="hidden" value="yes"/>
        <table class="table table-condensed table-striped table-header-bg" style="width: 60%;margin:auto"
               align="center">
            <tr>
                <td><strong>First Month of Fiscal/Accounting Year</strong></td>
                <td>
                    <?php
                    // get preferences
                    $merchantaccount = "";
                    $query = "select companyform,fiscalmonth,accountingmethod ";
                    $query .= "from company ";
                    $query .= "WHERE shopid = '{$shopid}' ";
                    //echo $query;

                    $coresult = mysqli_query($conn, $query);

                    if (!$coresult) {
                        die("Database company preferences query failed.");
                    }
                    $co = mysqli_fetch_assoc($coresult);

                    $month = $co["fiscalmonth"];
                    $compform = $co["companyform"];
                    $acctmeth = $co["accountingmethod"];

                    ?>


                    <select class="form-control sbp-form-control" name="fiscalmonth" id="fiscalmonth" <?= $disabled; ?>>
                        <option <?php if ($month == "January") {
                            echo "selected='selected'";
                        } ?> value="January">January
                        </option>
                        <option <?php if ($month == "February") {
                            echo "selected='selected'";
                        } ?> value="February">February
                        </option>
                        <option <?php if ($month == "March") {
                            echo "selected='selected'";
                        } ?> value="March">March
                        </option>
                        <option <?php if ($month == "April") {
                            echo "selected='selected'";
                        } ?> value="April">April
                        </option>
                        <option <?php if ($month == "May") {
                            echo "selected='selected'";
                        } ?> value="May">May
                        </option>
                        <option <?php if ($month == "June") {
                            echo "selected='selected'";
                        } ?> value="June">June
                        </option>
                        <option <?php if ($month == "July") {
                            echo "selected='selected'";
                        } ?> value="July">July
                        </option>
                        <option <?php if ($month == "August") {
                            echo "selected='selected'";
                        } ?> value="August">August
                        </option>
                        <option <?php if ($month == "September") {
                            echo "selected='selected'";
                        } ?> value="September">September
                        </option>
                        <option <?php if ($month == "October") {
                            echo "selected='selected'";
                        } ?> value="October">October
                        </option>
                        <option <?php if ($month == "November") {
                            echo "selected='selected'";
                        } ?> value="November">November
                        </option>
                        <option <?php if ($month == "December") {
                            echo "selected='selected'";
                        } ?> value="December">December
                        </option>

                    </select></td>
            </tr>
            <tr>
                <td><strong>Company Form</strong></td>
                <td><select class="form-control sbp-form-control" name="companyform" id="companyform" <?= $disabled; ?>>
                        <option <?php if ($compform == "Sole Proprietor") {
                            echo "selected='selected'";
                        } ?> value="Sole Proprietor">Sole Proprietor
                        </option>
                        <option <?php if ($compform == "Partnership") {
                            echo "selected='selected'";
                        } ?> value="Partnership">Partnership
                        </option>
                        <option <?php if ($compform == "LLC (Limited Liability Company)") {
                            echo "selected='selected'";
                        } ?> value="LLC (Limited Liability Company)">LLC (Limited Liability Company)
                        </option>
                        <option <?php if ($compform == "Chapter C Corporation") {
                            echo "selected='selected'";
                        } ?> value="Chapter C Corporation">Chapter C Corporation
                        </option>
                        <option <?php if ($compform == "Chapter S Corporation") {
                            echo "selected='selected'";
                        } ?> value="Chapter S Corporation">Chapter S Corporation
                        </option>
                    </select></td>
            </tr>
            <tr>
                <td><strong>Cash or Accrual Reporting</strong><br/>
                    <span class="style4">When you select Cash Reporting, all reports
			will be generated based <br>on payments received, not invoices
			generated.&nbsp; Accrual Reporting means that all <br>reports are based
			on invoices generated, not when payment is received.&nbsp; <br>You can
			override reports on a case-by-case basis if desired.</span></td>

                <td><select class="form-control sbp-form-control" name="accountingmethod" id="accountingmethod" <?= $disabled; ?>>
                        <option <?php if ($acctmeth == "Cash") {
                            echo "selected='selected'";
                        } ?> value="Cash">Cash
                        </option>
                        <option <?php if ($acctmeth == "Accrual") {
                            echo "selected='selected'";
                        } ?> value="Accrual">Accrual
                        </option>
                    </select></td>

            </tr>

            <?php if (!$shopIsReadOnly): ?>
                <tr>
                    <td colspan="2" class="style5">
                        <button onclick="savePref('<?php echo $shopid; ?>')" type="button" class="btn btn-primary">
                            Save Settings
                        </button>
                    </td>
                </tr>
            <?php endif; ?>
        </table>
        </form>
    </main>
    <!-- END Main Container -->

    <!-- Footer -->
    <!-- END Footer -->
</div>
<!-- END Page Container -->
<!-- Modals -->


<script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
<script src="<?= SCRIPT ?>/tipped.js"></script>

<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
<script src="<?= SCRIPT ?>/app.js"></script>
<script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
<script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
<script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
<script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

<!-- Page Plugins -->

<!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
<script>

    function savePref(shopid) {


        var fm = $("#fiscalmonth").val();
        var cf = $("#companyform").val();
        var am = $("#accountingmethod").val();

        var ds = 'shopid=' + shopid + '&fiscalmonth=' + fm + '&companyform=' + cf + '&accountingmethod=' + am;
        //console.log(ds);

        $.ajax({
            type: "post",
            url: "editpreferences.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                swal("Error in Save")
            }
        });

    }

    $(document).ready(function () {
    });

</script>
<img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>
<?php
mysqli_close($conn);
?>
</html>

