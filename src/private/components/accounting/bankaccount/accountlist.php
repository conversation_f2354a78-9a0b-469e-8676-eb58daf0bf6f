<?php

require CONN;
require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];
$today = new DateTime('now');

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<!DOCTYPE html>
<html>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
	<meta charset="utf-8">
	<title>Bank Accounts</title>
	<meta name="robots" content="noindex, nofollow">
	<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
	<link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon' />
	<!-- Icons -->
	<!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

	<link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
	<link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
	<link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
	<link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
	<link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
	<link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
	<link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
	<link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
	<link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
	<!-- END Icons -->

	<!-- Stylesheets -->
	<!-- Web fonts -->
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

	<!-- Page JS Plugins CSS -->
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

	<!-- Bootstrap and OneUI CSS framework -->
	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
	<link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
	<link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
	<link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
	<link rel="stylesheet" type="text/css" href="<?= CSS ?>/rich_calendar.css" />
	<script language="JavaScript" type="text/javascript" src="<?= SCRIPT ?>/rich_calendar.js"></script>
	<script language="JavaScript" type="text/javascript" src="<?= SCRIPT ?>/rc_lang_en.js"></script>
	<script language="javascript" type="text/javascript" src="<?= SCRIPT ?>/domready.js"></script>

	<script type="text/javascript" src="<?= SCRIPT ?>/formvalidator.js"></script>
	<!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
	<!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
	<!-- END Stylesheets -->
	<style>
		.col-md-6 {
			border: 1px black solid
		}

		.col-md-8 {
			border: 1px black solid
		}

		.col-md-4 {
			border: 1px black solid
		}

		.data {
			width: 250px;
			height: 30px;
			font-size: 18px;
			border: 1px silver solid;
			border-radius: 3px;
		}

		#right {
			width: 40%;
			margin-left: 60%;
			margin-top: -20px;
		}

		#left {
			width: 100%;
		}

		.style3 {
			text-align: center;
			color: #FFFFFF;
		}

		.style4 {
			color: #FFFFFF;

		}

		.style5 {
			text-align: right;
		}

		.style6 {
			color: #FFFFFF;
			text-align: right;
		}

		.style7 {
			text-align: center;
		}

		.style8 {
			color: #FFFFFF;

			text-align: center;
		}

		#popup {
			position: absolute;
			top: 100px;
			left: 30%;
			width: 400px;
			overflow-y: auto;
			border: medium navy outset;
			text-align: center;
			color: black;
			display: none;
			z-index: 999;
			background-color: white;
			padding: 20px;
			height: 200px;
		}

		#popuphider {
			position: absolute;
			top: 0px;
			left: 0px;
			width: 100%;
			height: 100%;
			background-color: gray;
			-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
			filter: alpha(opacity=70);
			-moz-opacity: .70;
			opacity: .7;
			z-index: 997;
			display: none;

		}

		.style9 {
			text-align: left;
		}
	</style>

	<script>
	</script>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
	<div id="mainalert" style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large" class="alert alert-success"></div>
	<div id="header"></div>
	<!-- Page Container -->
	<!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
	<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
		<!-- Sidebar -->
		<nav id="sidebar">
			<!-- Sidebar Scroll Container -->
			<div id="sidebar-scroll">
				<!-- Sidebar Content -->
				<!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
				<div class="sidebar-content">
					<!-- Side Header -->
					<div class="side-header side-content bg-white-op">
						<!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
						<button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
							<i class="fa fa-times"></i>
						</button>
						<a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"> <i class="text-primary">
								<?php getLogo() ?></i>
							<span class="h4 font-w600 sidebar-mini-hide">
							</span>
						</a>
					</div>
					<!-- END Side Header -->
					<!-- Side Content -->
					<div class="side-content-sbp-ro side-content">
						<ul class="nav-main">
							<li>
								<a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Back to WIP</span></a>
							</li>

							<li>
								<a href="<?= COMPONENTS_PRIVATE ?>/accounting/default.php"><i class="fa fa-home"></i><span class="sidebar-mini-hide">Accounting Home</span></a>
							</li>

							<li>
								<a href="<?= COMPONENTS_PRIVATE ?>/accounting/bankaccount/accountlist.php"><i class="fa fa-list"></i><span class="sidebar-mini-hide">Account List</span></a>
							</li>

							<?php if (!$shopIsReadOnly): ?>
								<li>
									<a href="#" id="go" data-target="#bankmodal" data-toggle="modal"><i class="fa fa-book"></i><span class="sidebar-mini-hide">Add New Account</span></a>
								</li>
							<?php endif; ?>
						</ul>
					</div>
					<!-- END Side Content -->
				</div>
				<!-- Sidebar Content -->
			</div>
			<!-- END Sidebar Scroll Container -->
		</nav>
		<!-- END Sidebar -->

		<!-- Header -->
		<header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar" class="content-mini content-mini-full">

			<!-- Header Navigation Right -->
			Bank Accounts
			<!-- END Header Navigation Right -->

			<!-- Header Navigation Left -->

			<ul class="nav-header pull-left">
				<li class="hidden-md hidden-lg">
					<!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
					<button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
						<i class="fa fa-navicon"></i>
					</button>
				</li>
				<li class="hidden-xs hidden-sm">
					<!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
					<button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
						<i class="fa fa-bars"></i>
					</button>
				</li>
				<li>
					<!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
					<button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
						<i class="si si-grid"></i>
					</button>
				</li>
				<li class="visible-xs">
					<!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
					<button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
						<i class="fa fa-search"></i>
					</button>
				</li>
				<li>

				</li>
			</ul>
			<!-- END Header Navigation Left -->
		</header>
		<!-- END Header -->
		<!-- Main Container -->

		<!-- Main Container -->
		<main class="container-fluid" id="main-container" style="display:block;">
			<?php if( !$shopIsReadOnly ): ?>
				<button id="bankbutton" type="button" onclick="$('#bankmodal').modal('show')" data-target="#bankmodal" class="btn btn-success btn-lg">Add New Account</button>
			<?php endif; ?>

			<br>
			<input name="dfield" id="dfield" type="hidden" />
			<table class="table table-condensed table-striped table-header-bg">
				<thead>
					<tr>
						<td class="style4"><strong>Bank Name</strong></td>
						<td class="style4"><strong>Account Description</strong></td>
						<td class="style4"><strong>Account Type</strong></td>
						<td class="style6"><strong>Balance</strong></td>
						<td class="style6"><strong>Is Default</strong></td>
						<?php if( !$shopIsReadOnly ): ?>
							<td class="style6"><strong>Edit</strong></td>
						<?php endif; ?>
					</tr>
				</thead>
				<?php

				$stmt = "SELECT isdefault,bankname,accountdescription,accounttype,id,acctstatus,shopid ";
				$stmt .= "FROM bankaccount ";
				$stmt .= "WHERE shopid = ? ";
				$stmt .= "  AND acctstatus != 'Closed'";
				//echo $stmt;

				if ($query = $conn->prepare($stmt)) {
					$query->bind_param("s", $shopid);
					$query->execute();
					$bankresult = $query->get_result();
				} else {
					echo "Bank Account Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}

				if ($bankresult->num_rows > 0) {
					while ($bank = $bankresult->fetch_array()) {
						$shopid = $bank["shopid"];
						$id = $bank["id"];

						$stmt = "SELECT sum(depositamount) as d,  ";
						$stmt .= "sum(paymentamount) as p  ";
						$stmt .= " FROM accountregister ";
						$stmt .= "WHERE shopid = ?";
						$stmt .= "  AND accountid = ? ";
						//echo $query;

						if ($query = $conn->prepare($stmt)) {
							$query->bind_param("ss", $shopid, $id);
							$query->execute();
							$acctregresult = $query->get_result();
						} else {
							echo "Account Register Prepare failed: (" . $conn->errno . ") " . $conn->error;
						}

						$acctreg = $acctregresult->fetch_array();

						$d = $acctreg["d"];
						$p = $acctreg["p"];
						$b = $d - $p;
						$b = asDollars($b);
						//echo " This is the total of deposits" . $b;

				?>
						<tr>
							<td onclick="location.href='../../accounting/bankaccount/transactions.php?bankid=<?php echo $id; ?>'"><?php echo $bank["bankname"]; ?>&nbsp;</td>
							<td onclick="location.href='../../accounting/bankaccount/transactions.php?bankid=<?php echo $id; ?>'"><?php echo $bank["accountdescription"]; ?>&nbsp;</td>
							<td onclick="location.href='../../accounting/bankaccount/transactions.php?bankid=<?php echo $id; ?>'"><?php echo $bank["accounttype"]; ?>&nbsp;</td>
							<td onclick="location.href='../../accounting/bankaccount/transactions.php?bankid=<?php echo $id; ?>'" class="style5"><?php echo $b; ?>&nbsp;</td>
							<td onclick="location.href='../../accounting/bankaccount/transactions.php?bankid=<?php echo $id; ?>'" class="style5"><?php echo strtoupper($bank['isdefault']); ?></td>
							
							<?php if( !$shopIsReadOnly ): ?>
								<td class="style5">
									<span onclick="editAccount(<?php echo $bank['id']; ?>)" class="btn btn-warning btn-sm">Edit Account</span>
								</td>
							<?php endif; ?>
						</tr>
					<?php

					} // end of do while loop
				} else { // else of if
					?>
					<tr>
						<td colspan="6">No Accounts Added&nbsp;</td>
						<td>&nbsp;</td>
					</tr>
				<?php
				} // end of if
				?>
			</table>
			<!-- Modals -->


			<div id="bankmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
				<input id="customerid" type="hidden">
				<div class="modal-dialog modal-md">
					<div class="modal-content">
						<div class="block block-themed block-transparent remove-margin-b">
							<div class="block-header bg-primary-dark">
								<ul class="block-options">
									<li>
										<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
									</li>
								</ul>
								<h3 class="block-title">Add New Bank Account</h3>
							</div>
							<div class="modal-body">
								<form>
									<tr>
										<td>
											<table class="table table-condensed table-striped">
												<tr>
													<td style="width: 268px">Bank Name</td>
													<td><input class="form-control" id="bankname" name="bankname" type="text" /><input type="hidden" id="accountid"></td>
												</tr>
												<tr>
													<td style="width: 268px">Account Type</td>
													<td>
														<select class="form-control" id="accounttype" name="accounttype" style="width: 200px">
															<option value="Checking">Checking</option>
															<option value="Saving">Saving</option>
															<option value="Credit Card">Credit Card</option>
															<option value="Money Market">Money Market</option>
															<option value="Certificate of Deposit">Certificate of Deposit
															</option>
														</select>
													</td>
												</tr>
												<tr>
													<td style="width: 268px">Description</td>
													<td><input class="form-control" id="accountdescription" name="accountdescription" type="text" value="" /> </td>
												</tr>
												<tr>
													<td style="width: 268px">Account Number</td>
													<td><input class="form-control" id="accountnumber" name="accountnumber" type="text" value="" /></td>
												</tr>
												<tr>
													<td style="width: 268px">Routing Number</td>
													<td><input class="form-control" id="routingnumber" name="routingnumber" type="text" value="" /></td>
												</tr>
												<tr>
													<td style="width: 268px">Date Opened</td>
													<td><input class="form-control" id="opendate" name="opendate" type="text" value="<?php echo date_format($today, 'm/d/Y'); ?>" /></td>
												</tr>
												<tr>
													<td style="width: 268px">Beginning Balance</td>
													<td>
														<input class="form-control" id="beginningbalance" name="beginningbalance" type="text" value="" />
													</td>
												</tr>
												<tr>
													<td class="style1" colspan="2">
														<button onclick="saveBankAccount('<?php echo $shopid; ?>')" type="button" class="btn btn-primary">Add Bank Account</button>
														<button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</form>
							</div>
							<div style="margin-top:20px;" class="modal-footer">
								<!--button class="btn btn-primary btn-md" type="button" >Button</button>
					<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button-->
							</div>
						</div>
					</div>
				</div>

			</div>

			<div id="editmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
				<input id="customerid" type="hidden">
				<div class="modal-dialog modal-md">
					<div class="modal-content">
						<div class="block block-themed block-transparent remove-margin-b">
							<div class="block-header bg-primary-dark">
								<ul class="block-options">
									<li>
										<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
									</li>
								</ul>
								<h3 class="block-title">Edit Bank Account</h3>
							</div>
							<div class="modal-body">
								<form>
									<tr>
										<td>
											<table class="table table-condensed table-striped">
												<tr>
													<td>Is Default for Deposits?</td>
													<td>
														<select class="form-control" id="editdefault">
															<option value="yes">Yes</option>
															<option value="no">No</option>
														</select>
													</td>
												</tr>
												<tr>
													<td style="width: 268px">Bank Name</td>
													<td><input class="form-control" id="editbankname" name="editbankname" type="text" /></td>
												</tr>
												<tr>
													<td style="width: 268px">Account Type</td>
													<td>
														<select class="form-control" id="editaccounttype" name="editaccounttype" style="width: 200px">
															<option value="Checking">Checking</option>
															<option value="Saving">Saving</option>
															<option value="Credit Card">Credit Card</option>
															<option value="Money Market">Money Market</option>
															<option value="Certificate of Deposit">Certificate of Deposit
															</option>
														</select>
													</td>
												</tr>
												<tr>
													<td style="width: 268px">Description</td>
													<td><input class="form-control" id="editaccountdescription" name="editaccountdescription" type="text" value="" /> </td>
												</tr>
												<tr>
													<td style="width: 268px">Account Number</td>
													<td><input class="form-control" id="editaccountnumber" name="editaccountnumber" type="text" value="" /></td>
												</tr>
												<tr>
													<td style="width: 268px">Routing Number</td>
													<td><input class="form-control" id="editroutingnumber" name="editroutingnumber" type="text" value="" /></td>
												</tr>
												<tr>
													<td class="style1" colspan="2">
														<button onclick="saveEditBankAccount()" type="button" class="btn btn-primary">Save Changes</button>
														<button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</form>
							</div>
							<div style="margin-top:20px;" class="modal-footer">
								<!--button class="btn btn-primary btn-md" type="button" >Button</button>
								<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button-->
							</div>
						</div>
					</div>
				</div>

		</main>

		<script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
		<script src="<?= SCRIPT ?>/tipped.js"></script>

		<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
		<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
		<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
		<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
		<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
		<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
		<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
		<script src="<?= SCRIPT ?>/app.js"></script>
		<script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
		<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
		<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
		<script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
		<script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
		<script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
		<script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

		<!-- Page Plugins -->
		<!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
		<script>
			function editAccount(id) {

				$.ajax({

					data: "t=getbank&shopid=<?php echo $shopid; ?>&bankid=" + id,
					url: "savebank.php",
					type: "post",
					success: function(r) {
						console.log(r)
						// echo $bankname."|".$accounttype."|".$accountdescription."|".$accountnumber."|".$routingnumber
						rar = r.split("|")
						bankname = rar[0]
						accounttype = rar[1]
						accountdescription = rar[2]
						accountnumber = rar[3]
						routingnumber = rar[4]
						isdefault = rar[5]
						$("#editbankname").val(bankname);
						$("#editaccounttype").val(accounttype);
						$("#editaccountdescription").val(accountdescription);
						$("#editaccountnumber").val(accountnumber);
						$("#editroutingnumber").val(routingnumber);

						if (isdefault == "yes") {
							$('#editdefault').val("yes")
						} else {
							$('#editdefault').val("no")
						}
					},

					error: function(xhr, ajaxOptions, thrownError) {
						console.log(xhr.status);
						console.log(xhr.responseText);
						console.log(thrownError);

					}

				})

				$('#editmodal').modal('show')
				$('#accountid').val(id)

			}
			$(document).ready(function() {
				$("#opendate").datetimepicker({
					format: 'MM/DD/YYYY'
				});

			});

			//$(".modal-body input").val("");

			function recurring(v) {

				if (v == "Yes") {
					document.getElementById("dayofmonth").style.display = ""
				}
				if (v == 'No') {
					$('#dayofmonth').hide()
				}

			}

			function saveEditBankAccount() {

				var bankname = $("#editbankname").val();
				var accttype = $("#editaccounttype").val();
				var acctdesc = $("#editaccountdescription").val();
				var acctnum = $("#editaccountnumber").val();
				var routenum = $("#editroutingnumber").val();
				var shopid = "<?php echo $shopid; ?>";
				var isdefault = $('#editdefault').val()
				var id = $('#accountid').val()

				var ds = 'id=' + id + '&t=edit&isdefault=' + isdefault + '&bankname=' + bankname + '&accounttype=' + accttype + '&accountdescription=' + acctdesc + '&accountnumber=' + acctnum + '&routingnumber=' + routenum + '&shopid=' + shopid;
				$.ajax({
					type: "post",
					url: "savebank.php",
					data: ds,
					success: function(r) {
						location.reload()
					},

					error: function(xhr, ajaxOptions, thrownError) {
						console.log(xhr.status);
						console.log(xhr.responseText);
						console.log(thrownError);

					}
				});



			}

			function saveBankAccount(shopid) {
				// note the balance has to be saved to the account register

				var bankname = $("#bankname").val();
				var accttype = $("#accounttype").val();
				var acctdesc = $("#accountdescription").val();
				var acctnum = $("#accountnumber").val();
				var routenum = $("#routingnumber").val();
				var dateopen = $("#opendate").val();
				var begbal = $("#beginningbalance").val();

				console.log("AccountNumber is " + acctnum)

				var ds = 'bankname=' + bankname + '&accounttype=' + accttype + '&accountdescription=' + acctdesc + '&accountnumber=' + acctnum + '&routingnumber=' + routenum + '&opendate=' + dateopen + '&shopid=' + shopid + '&begbal=' + begbal;

				$.ajax({
					type: "post",
					url: "savebank.php",
					data: ds,
					success: function() {
						location.reload();
					},
					error: function(xhr, ajaxOptions, thrownError) {
						swal("Error in Insert")
					}
				});

			}
		</script>
		<img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>
<?php
mysqli_close($conn);
?>

</html>
