<!DOCTYPE html>
<html>
<?php
require CONN;
require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];
if (isset($_GET['bankid'])) {
    $bankid = $_GET['bankid'];
}
if (isset($_POST['bankid'])) {
    $bankid = $_POST['bankid'];
}
if (isset($_GET['transid'])) {
    $transid = $_GET['transid'];
}
if (isset($_POST['transid'])) {
    $transid = $_POST['transid'];
}

$today = new DateTime('now');
$today = date_format($today, 'm/d/Y');

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$disabled = $shopIsReadOnly ? "disabled" : "";
?>

<!--[if IE 9]>
<html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus"> <!--<![endif]-->
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <!--	<link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon() ?>' type='image/x-icon'/ > -->
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" type="text/css" href="<?= CSS ?>/rich_calendar.css"/>
    <script language="JavaScript" type="text/javascript" src="<?= CSS ?>/rich_calendar.js"></script>
    <script language="JavaScript" type="text/javascript" src="<?= CSS ?>/rc_lang_en.js"></script>
    <script language="javascript" type="text/javascript" src="<?= CSS ?>/domready.js"></script>

    <script type="text/javascript" src="<?= CSS ?>/formvalidator.js"></script>

    <!-- Data tables -->

    <!--link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.css"-->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.css">

    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        .col-md-6 {
            border: 1px black solid
        }

        .col-md-8 {
            border: 1px black solid
        }

        .col-md-4 {
            border: 1px black solid
        }

        .data {
            width: 200px;
            font-size: 14px;
            padding: 5px;
            border: 1px silver solid;
            border-radius: 4px;
        }

        #popuphider {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 100%;
            background-color: gray;
            -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
            filter: alpha(opacity=70);
            -moz-opacity: .70;
            opacity: .7;
            z-index: 997;
            display: none;

        }

        #popup {
            position: absolute;
            top: 100px;
            left: 30%;
            width: 40%;
            overflow-y: auto;
            border: 1px silver outset;
            text-align: center;
            color: black;
            display: none;
            z-index: 999;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
        }

        #addtrans {
            position: absolute;
            top: 100px;
            left: 30%;
            width: 40%;
            overflow-y: auto;
            border: 1px silver solid;
            text-align: center;
            color: black;
            display: none;
            z-index: 999;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
        }

        .auto-style1 {
            text-align: center;
            color: #FFFFFF;
            font-size: large;
        }

        #banktable_wrapper {

        }

        .dataTables_paginate, .paging_simple_numbers, #banktable_paginate {
            text-align: center;
            font-weight: 600;
            margin-bottom: 25px;
        }

        .paginate_button {
            padding: 10px 15px;
            border: 1px solid #eeeeec;
            border-radius: 3px;
            cursor: pointer;
        }

        .paginate_button:hover {
            background-color: #80a3d1;
            color: white;
        }

        .paginate_button.current {
            background-color: #5c90d2;
            color: white;
        }

        .disabled, .disabled:hover {
            cursor: not-allowed;
            background-color: #eeeeec;
        }
        #banktable_filter{
           display: none;
        }
        #banktable_filter input, select[name=banktable_length] {
            display: inline-block;
            width: auto;
            height: 34px;
            padding: 6px 12px;
            font-size: 14px;
            line-height: 1.********;
            color: #555;
            background-color: #fff;
            background-image: none;
            border: 1px solid #ccc;
            border-radius: 4px;
            -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
            box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
            -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
            -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
            transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
        }
    </style>
    <script>
    </script>
</head>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<div id="mainalert"
     style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large"
     class="alert alert-success"></div>
<div id="header"></div>
<!-- Page Container -->
<!--
    Available Classes:

    'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

    'sidebar-l'                  Left Sidebar and right Side Overlay
    'sidebar-r'                  Right Sidebar and left Side Overlay
    'sidebar-mini'               Mini hoverable Sidebar (> 991px)
    'sidebar-o'                  Visible Sidebar by default (> 991px)
    'sidebar-o-xs'               Visible Sidebar by default (< 992px)

    'side-overlay-hover'         Hoverable Side Overlay (> 991px)
    'side-overlay-o'             Visible Side Overlay by default (> 991px)

    'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

    'header-navbar-fixed'        Enables fixed header
-->
<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
    <!-- Sidebar -->
    <nav id="sidebar">
        <!-- Sidebar Scroll Container -->
        <div id="sidebar-scroll">
            <!-- Sidebar Content -->
            <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
            <div class="sidebar-content">
                <!-- Side Header -->
                <div class="side-header side-content bg-white-op">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button"
                            data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                        <i class="text-primary">
                            <?php getLogo() ?></i>
                        <span class="h4 font-w600 sidebar-mini-hide">
							</span>
                    </a>
                </div>
                <!-- END Side Header -->

                <!-- Side Content -->
                <div class="side-content-sbp-ro side-content">
                    <ul class="nav-main">
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span
                                        class="sidebar-mini-hide">Back to WIP</span></a>
                        </li>
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/accounting/default.php"><i class="fa fa-home"></i><span
                                        class="sidebar-mini-hide">Accounting Home</span></a>
                        </li>
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/accounting/bankaccount/accountlist.php"><i
                                        class="fa fa-list"></i><span class="sidebar-mini-hide">Account List</span></a>
                        </li>

                        <?php if (!$shopIsReadOnly): ?>
                            <li>
                                <a href="#" onclick="addBank('<?php echo $shopid; ?>')"><i class="fa fa-book"></i><span
                                            class="sidebar-mini-hide">Add New Account</span></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
                <!-- END Side Content -->
            </div>
            <!-- Sidebar Content -->
        </div>
        <!-- END Sidebar Scroll Container -->
    </nav>
    <!-- END Sidebar -->

    <!-- Header -->
    <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar"
            class="content-mini content-mini-full">
        <!-- Header Navigation Right -->
        Transactions
        <!-- END Header Navigation Right -->

        <!-- Header Navigation Left -->

        <ul class="nav-header pull-left">
            <li class="hidden-md hidden-lg">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                    <i class="fa fa-navicon"></i>
                </button>
            </li>
            <li class="hidden-xs hidden-sm">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" id="close-sidebar"
                        data-action="sidebar_mini_toggle" type="button">
                    <i class="fa fa-bars"></i>
                </button>
            </li>
            <li>
                <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                <button style="display:none" class="btn btn-default pull-right" data-toggle="modal"
                        data-target="#apps-modal" type="button">
                    <i class="si si-grid"></i>
                </button>
            </li>
            <li class="visible-xs">
                <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search"
                        data-class="header-search-xs-visible" type="button">
                    <i class="fa fa-search"></i>
                </button>
            </li>
            <li>

            </li>
        </ul>

        <!-- END Header Navigation Left -->
    </header>
    <!-- END Header -->
    <!-- Main Container -->

    <!-- Main Container -->
    <main class="container-fluid" id="main-container" style="display:block;">
        <?php if( !$shopIsReadOnly ): ?>
            <div style="text-align:left"><br>
                <button class="btn btn-info" type="button" onclick="addTrans('<?php echo $shopid; ?>')">Add New
                    Transaction
                </button>
                <button id="bankbutton" type="button" onclick="closeAccount(bankid)" class="btn btn-danger ">Close Bank
                    Account
                </button>
                <button id="runbalbutton" type="button"
                        onclick="runbalRecalc('<?php echo $shopid; ?>',<?php echo $bankid; ?>)" class="btn btn-warning ">
                    Running Balance Recalc
                </button>
            </div>
            <br>
        <?php endif; ?>

        <div class="" style="float: right; width: 40%">
            <div class="input-group">
                <input type="text" class="form-control" id="searchbar" aria-label="Text input with multiple buttons" placeholder="Search">
                <div class="input-group-btn">
                    <span class="glyphicon glyphicon-remove" onclick="$('#searchbar').val('');  $('#searchbar').trigger('input');" style="left: -27px; z-index: 99; cursor: pointer; font-size: 16px; padding: 10px 5px;"></span>
                </div>
            </div>
        </div>
        <table id="banktable" class="table table-condensed table-bordered table-striped table-header-bg"
               style="width: 100%">
            <thead>
            <tr>
                <td class="text-left">Trans Date</td>
                <td class="text-left">Number</td>
                <td class="text-left">Paid To</td>
                <td class="text-left">Memo</td>
                <td class="text-right">Payment</td>
                <td class="text-right">Deposit</td>
                <td class="text-right">Running Balance</td>
                <td class="text-right">Date Posted</td>

            </tr>
            </thead>

        </table>

        <?php if( !$shopIsReadOnly ): ?>
            <!-- Modals -->

            <!-- Add Bank Modal -->
            <div id="bankmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
                <input id="customerid" type="hidden">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="block-header bg-primary-dark">
                            <ul class="block-options">
                                <li>
                                    <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                </li>
                            </ul>
                            <h3 class="block-title">Add New Bank Account</h3>
                        </div>
                        <div class="modal-body">
                            <table class="table table-condensed table-striped">
                                <tr>
                                    <td style="width: 268px">Bank Name</td>
                                    <td><input class="data" id="bankname" name="bankname" type="text"/></td>
                                </tr>
                                <tr>
                                    <td style="width: 268px">Account Type</td>
                                    <td>
                                        <select style="font-size:18px;" class="data" id="accounttype" name="accounttype">
                                            <option value="Checking">Checking</option>
                                            <option value="Saving">Saving</option>
                                            <option value="Credit Card">Credit Card</option>
                                            <option value="Money Market">Money Market</option>
                                            <option value="Certificate of Deposit">Certificate of Deposit
                                            </option>
                                        </select></td>
                                </tr>
                                <tr>
                                    <td style="width: 268px">Description</td>
                                    <td><input class="data" id="accountdescription" name="accountdescription" type="text"
                                            value="" style="width: 395px"/></td>
                                </tr>
                                <tr>
                                    <td style="width: 268px">Account Number</td>
                                    <td><input class="data" id="accountnumber" name="accountnumber" type="text" value=""
                                            style="width: 200px"/></td>
                                </tr>
                                <tr>
                                    <td style="width: 268px">Routing Number</td>
                                    <td><input class="data" id="routingnumber" name="routingnumber" type="text" value=""
                                            style="width: 200px"/></td>
                                </tr>
                                <tr>
                                    <td style="width: 268px">Date Opened</td>
                                    <td><input class="data" id="opendate" name="opendate" type="text" value=""
                                            style="width: 200px"/></td>
                                </tr>
                                <tr>
                                    <td style="width: 268px">Beginning Balance</td>
                                    <td>
                                        <input class="data" id="beginningbalance" name="beginningbalance" type="text"
                                            value="" style="width: 200px"/></td>
                                </tr>
                                <tr>
                                    <td class="style1" colspan="2">
                                        <button onclick="saveBankAccount('<?php echo $shopid; ?>')" type="button"
                                                class="btn btn-primary">Add Bank Account
                                        </button>
                                        <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div style="margin-top:20px;" class="modal-footer">
                            <!--button class="btn btn-primary btn-md" type="button" >Button</button>
                            <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button-->
                        </div>
                    </div>
                </div>
            </div>


            <!-- Add Transaction Modal -->
            <div id="addTransModal" class="modal fade" role="dialog">
                <input id="shopid" name="shopid" value="" type="hidden">
                <input id="transid" name="transid" type="hidden"/>

                <div class="modal-dialog">
                    <!-- Modal content-->
                    <div class="modal-content">
                        <div class="block-header bg-primary-dark">
                            <ul class="block-options">
                                <li>
                                    <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                </li>
                            </ul>
                            <h4 class="block-title">Add New Transaction </h4>
                        </div>
                        <div class="modal-body">
                            <table class="table table-condensed table-striped">
                                <tr>
                                    <td class="text-left">Transaction #:</td>
                                    <?php

                                    $maxtransnumber = 0;
                                    $stmt = "select max(transnumber) c from accountregister where shopid = '$shopid' and accountid = '$bankid'";
                                    if ($query = $conn->prepare($stmt)) {
                                        $query->execute();
                                        $query->bind_result($maxtransnumber);
                                        $query->fetch();
                                        $query->close();
                                    }

                                    $stmt = "SELECT runningbal ";
                                    $stmt .= " FROM accountregister ";
                                    $stmt .= " WHERE shopid = ? ";
                                    $stmt .= "   AND accountid = ? ";
                                    $stmt .= "  ORDER BY ts desc ";
                                    $stmt .= "  LIMIT 1 ";

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("ss", $shopid, $bankid);
                                        if ($query->execute()) {
                                            $conn->commit();
                                            //echo "success";
                                        } else {
                                            echo $conn->errno;
                                        }
                                    } else {
                                        echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    $result = $query->get_result();

                                    $row = $result->fetch_assoc();

                                    $runningbal = $row["runningbal"];

                                    $newtransnum = $maxtransnumber + 1;

                                    ?>

                                    <td class="style9"><input class="form-control" id="transnumber" name="transnumber"
                                                            value="<?php echo $newtransnum; ?>"></td>
                                </tr>
                                <tr>
                                    <td class="text-left">Date</td>
                                    <td class="style9"><input class="form-control" id="transdate" name="transdate"
                                                            type="text" value="<?php echo $today; ?>"/></td>
                                </tr>

                                <tr>
                                    <td class="text-left">Paid To:</td>
                                    <td class="style9"><input class="form-control" id="paidto" name="paidto" type="text"
                                                            value=""/></td>
                                </tr>
                                <tr>
                                    <td class="text-left">Payment Amount:</td>
                                    <td class="style9"><input class="form-control" id="pymtamount" name="pymtamount"
                                                            type="text" value=""/></td>
                                </tr>
                                <tr>
                                    <td class="text-left">Deposit Amount:</td>
                                    <td class="style9"><input class="form-control" id="depamount" name="depamount"
                                                            type="text" value=""/></td>
                                </tr>
                                <tr>
                                    <td class="text-left">Account:</td>
                                    <td class="style9">
                                        <select id="account" class="form-control" name="account" type="text" value="">
                                            <?php

                                            $stmt = "SELECT id,shopid,category,cattype,core ";
                                            $stmt .= " FROM chartofaccounts ";
                                            $stmt .= "WHERE shopid = ? ";
                                            $stmt .= " order by category";
                                            //echo $stmt;

                                            if ($query = $conn->prepare($stmt)) {
                                                $query->bind_param("s", $shopid);
                                                $query->execute();
                                                $coaresult = $query->get_result();
                                            } else {
                                                echo "Chart of Accounts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                            }

                                            if ($coaresult->num_rows > 0) {
                                                while ($coa = $coaresult->fetch_array()) {
                                                    ?>
                                                    <option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
                                                    <?php
                                                }    // end of while
                                            } // end if
                                            ?>
                                        </select>
                                    </td>

                                <tr>
                                    <td class="style9" style="text-align:left">Memo</td>
                                    <td>
                                        <input name="memo" id="memo" class="form-control" type="text"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-left">Runningbal</td>
                                    <td class="style9"><input class="form-control" id="runningbal" name="runningbal"
                                                            type="text" readonly
                                                            value="<?php echo asDollars($runningbal); ?>"/></td>
                                </tr>

                            </table>
                        </div>
                        <div class="modal-footer">
                            <button onclick="addtransaction('<?php echo $shopid; ?>',<?php echo $bankid; ?>,<?php echo $runningbal; ?>)"
                                    id="addtransbtn" type="button" class="btn btn-primary">Save Changes
                            </button>
                            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>


            <!-- Edit Transaction Modal -->
            <div id="editTransModal" class="modal fade" role="dialog">
                <input id="transid" name="transid" type="hidden"/>
                <input id="bankid" name="bankid" type="hidden" value="<?php echo $bankid; ?>"/>

                <!--input name="currpage" id="currpage" value="currpage" type="hidden"/-->
                <input name="sub" type="hidden" value="yes"/>

                <div class="modal-dialog">

                    <!-- Modal content-->
                    <div class="modal-content">
                        <div class="block-header bg-primary-dark">
                            <ul class="block-options">
                                <li>
                                    <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                </li>
                            </ul>
                            <h4 class="block-title">Edit Transaction</h4>
                        </div>
                        <div class="modal-body">
                            <table class="table table-condensed table-striped">
                                <tr>
                                    <td class="text-left">Transaction #</td>
                                    <td class="style9"><input class="form-control" id="transnumber2" name="transnumber2"
                                                            type="text"/></td>
                                </tr>
                                <tr>
                                    <td class="text-left">Date</td>
                                    <td class="style9"><input class="form-control" id="transdate2" name="transdate2"
                                                            type="text" value=""/></td>
                                </tr>
                                <tr>
                                    <td class="text-left">Paid To</td>
                                    <td class="style9"><input id="paidto2" name="paidto2" class="form-control" type="text"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-left">Payment Amount</td>
                                    <td class="style9"><input id="paymentamount2" name="paymentamount2" class="form-control"
                                                            type="text"/></td>
                                </tr>
                                <tr>
                                    <td class="text-left">Deposit Amount</td>
                                    <td class="style9"><input id="depositamount2" name="depositamount2" class="form-control"
                                                            type="text"/></td>
                                </tr>
                                <tr>
                                    <td class="text-left">Account</td>
                                    <td class="style9">
                                        <select id="account2" name="account2" class="form-control">
                                            <?php
                                            $stmt = "SELECT category ";
                                            $stmt .= "FROM chartofaccounts ";
                                            $stmt .= "WHERE shopid = ? ";
                                            //echo $stmt;

                                            if ($query = $conn->prepare($stmt)) {
                                                $query->bind_param("s", $shopid);
                                                $query->execute();
                                                $coaresult = $query->get_result();
                                            } else {
                                                echo "Chart of Accts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                            }

                                            if ($coaresult->num_rows > 0) {
                                                while ($coa = $coaresult->fetch_array()) {
                                                    ?>
                                                    <option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
                                                    <?php
                                                }    // end of while

                                            } // end if
                                            ?>
                                        </select>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="style9" style="text-align:left">Memo</td>
                                    <td>
                                        <input name="memo2" id="memo2" class="form-control" type="text"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-left">Runningbal</td>
                                    <td class="style9"><input class="form-control" id="runningbal2" name="runningbal2"
                                                            type="text" readonly
                                                            value="<?php echo asDollars($runningbal); ?>"/></td>
                                </tr>

                            </table>
                        </div>
                        <div class="modal-footer">
                            <button onclick="saveTrans('<?php echo $shopid; ?>',<?php echo $runningbal; ?>)" type="button"
                                    class="btn btn-primary">Save Changes
                            </button>
                            <button onclick="deleteTrans('<?php echo $shopid; ?>')" type="button" class="btn btn-danger">
                                Delete
                            </button>
                            <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>


            <!-- Delete Transaction Modal -->
            <div id="delModal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
                <!--input id="customerid" type="hidden"-->
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="block-header bg-primary-dark">
                            <ul class="block-options">
                                <li>
                                    <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                </li>
                            </ul>
                            <h4 class="block-title">Close Bank Account</h4>
                        </div>
                        <div class="modal-body">
                            <?php
                            $stmt = "SELECT id,bankname,accounttype,accountdescription ";
                            $stmt .= "FROM bankaccount ";
                            $stmt .= "WHERE shopid = ? ";
                            $stmt .= "  AND id = ?";
                            //echo $stmt;

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("si", $shopid, $bankid);
                                $query->execute();
                                $bankresult = $query->get_result();
                            } else {
                                echo "Bank Account Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            if ($bankresult->num_rows > 0) {

                                $bank = mysqli_fetch_assoc($bankresult);

                                $bankname = $bank['bankname'];
                                $accttype = $bank['accounttype'];
                                $acctdesc = $bank['accountdescription'];
                                $id = $bank['id'];

                            }
                            // added on 9/8/17
                            $stmt = "SELECT sum(depositamount) as deposits,  ";
                            $stmt .= "sum(paymentamount) as payments  ";
                            $stmt .= " FROM accountregister ";
                            $stmt .= "WHERE shopid = ?";
                            $stmt .= "  AND accountid = ? ";
                            //echo $stmt;

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("ss", $shopid, $id);
                                $query->execute();
                                $acctreg2result = $query->get_result();
                            } else {
                                echo "Account Register 2 Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            //printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$id);

                            $acctreg2 = $acctreg2result->fetch_array();

                            $deposits = $acctreg2["deposits"];
                            $payments = $acctreg2["payments"];
                            $balance = $deposits - $payments;
                            $balance = asDollars($balance);
                            ?>
                            <table class="table table-condensed table-striped">
                                <tr>
                                    <td style="width: 268px"><label for="bankname">Bank Name:</label></td>
                                    <td style="padding-left:10px;" id="bankname"
                                        name="bankname"><?php echo $bankname; ?></td>
                                </tr>
                                <tr>
                                    <td style="width: 268px"><label for="bankname">Description:</label></td>
                                    <td style="padding-left:10px;" id="acctdesc"
                                        name="acctdesc"><?php echo $acctdesc; ?></td>
                                </tr>
                                <tr>
                                    <td style="width: 268px">Account Type</td>
                                    <td style="padding-left:10px;" id="accttype"
                                        name="accttype"><?php echo $accttype; ?></td>
                                </tr>
                                <tr>
                                    <td style="width: 268px">Balance</td>
                                    <td style="padding-left:10px;" id="balance" name="balance"><?php echo $balance; ?></td>
                                </tr>
                                <tr>
                                    <td class="style1" colspan="2">
                                        <button onclick="deleteAccount('<?php echo $shopid; ?>',<?php echo $bankid; ?>)"
                                                type="button" class="btn btn-danger">Close Bank Account
                                        </button>
                                        <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div style="margin-top:20px;" class="modal-footer">
                            <!--button class="btn btn-primary btn-md" type="button" >Button</button>
                            <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button-->
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </main>
    <script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
    <script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
    <script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

    <!-- Page Plugins -->

    <!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
    <script>

        $(document).ready(function () {
            qs = "?bankid=<?= $bankid ?>";
            myTable = $('#banktable').DataTable({
                "lengthMenu": [[50, 100, 500, 1000, -1], [50, 100,500, "1,000", "All"]],
                "serverSide": true,
                ajax: {
                    url : 'transactions_data.php' + qs,
                    error: function (jqXHR, textStatus, errorThrown) {
                        // Do something here
                        console.log("Error", jqXHR, textStatus, errorThrown);
                    }
                },
                fixedHeader: true,
                stateSave: true,
             //   deferRender: true,
             //   searching : false.
                columnDefs: [
                    { className: 'text-right', targets: [1,4,5,6,7] },
                ],
            });

            $("#searchbar")
                .bind("input", function (e) {
                    val = $(this).val();
                    console.log(val);
                        if (val.length >= 3) {
                                          console.log("search for "+ val);
                            $('#banktable').DataTable().search(val).draw();
                        }
                        // Ensure we clear the search if they backspace far enough
                        if (val == "") {
                            $('#banktable').DataTable().search("").draw();
                        }
                });
            $.fn.dataTable.ext.errMode = 'none';
            $("#transdate").datetimepicker({
                format: 'MM/DD/YYYY'
            });

            $("#transdate2").datetimepicker({
                format: 'MM/DD/YYYY'
            });

            $("#banktable").on('processing.dt', function (e, settings, processing) {
                if (processing) {
                    $("#spinner").fadeIn();
                } else {
                    $("#spinner").fadeOut();
                }
            }).dataTable();

            $("#banktable_wrapper").on('click','.paginate_button',function(){
                console.log("paginition began");
                width = $(this).width();
                height = $(this).width();
                img = $('<img>');
                img.attr('src', '<?= IMAGE ?>/loaderbig.gif');
                $(img).width(width)
                $(img).height(height)
                $(this).html(img);
            });

        });

        $("#transdate").datetimepicker({
            format: 'MM/DD/YYYY'
        });

        $("#transdate2").datetimepicker({
            format: 'MM/DD/YYYY'
        });


        function checkForm(shopid) {

            $('#depositamount2').prop('disabled', false)
            $('#paymentamount2').prop('disabled', false)

            myid = $('#transid').val()

            <?php
            echo "$('#editTransModal').modal('show')";
            ?>
        }

        function saveTrans(shopid, runningbal) {


            var myid = $("#transid").val();
            var acctid = $("#bankid").val();
            console.log('Bankid is ' + acctid)
            //var shopid = $("#shopid").val();
            var transnumber = $("#transnumber2").val();
            var transdate = $("#transdate2").val();
            var paidto = $("#paidto2").val();
            var depositamount = $("#depositamount2").val();
            var paymentamount = $("#paymentamount2").val();
            var memo = encodeURIComponent($("#memo2").val());
            var account = $("#account2").val();

            var ds = 'id=' + myid + '&shopid=' + shopid + '&transnumber=' + transnumber + '&transdate=' + transdate + '&paidto=' + paidto + '&memo=' + memo + '&depositamount=' + depositamount + '&paymentamount=' + paymentamount + '&category=' + account + '&runningbal=' + runningbal;

            console.log(ds);

            $.ajax({
                type: "post",
                url: "edittransaction.php",
                data: ds,
                success: function () {
                    runbalRecalc(shopid, acctid)
                    //location.reload();
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    swal("Error in Save")
                }
            });


        }

        function editTrans(id, transdate, transnumber, paidto, paymentamount, depositamount, memo, mycat, shopid) {

            paidto = paidto.replace("`", "'")
            $('#transdate2').val(transdate)
            $('#account2').val(mycat)
            $('#transid').val(id)
            $('#transnumber2').val(transnumber)
            $('#paidto2').val(paidto)
            $('#paymentamount2').val(paymentamount)
            $('#depositamount2').val(depositamount)
            $('#memo2').val(memo)
            $('#popup').show()

            if (paymentamount == 0) {
                $('#paymentamount2').prop('disabled', true)
                $('#depositamount2').prop('disabled', false)
                $('#paidto2').prop('disabled', true)
            }
            if (depositamount == 0) {
                $('#depositamount2').prop('disabled', true)
                $('#paymentamount2').prop('disabled', false)
                $('#paidto2').prop('disabled', false)
            }

            checkForm(shopid);
        }

        function deleteTrans(shopid) {

            myid = $('#transid').val()
            acctid = $('#bankid').val()

            swal({
                    title: "Are you sure?",
                    text: "This transaction will be deleted.  Are you sure?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonClass: "btn-danger",
                    confirmButtonText: "Yes, delete it",
                    closeOnConfirm: false
                },
                function () {

                    var ds = 'id=' + myid + '&acctid=' + acctid + '&shopid=' + shopid;
                    $.ajax({
                        type: "post",
                        url: "deletetrans.php",
                        data: ds,
                        success: function () {


                            runbalRecalc(shopid, acctid)

                            //location.reload();
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            swal("Error in Deletion")
                        }
                    });
                });
        }

        function cancelTrans() {
            console.log('You are in the cancel trans')
            document.getElementById("transdate").value = ""
            $('#transnumber').val("")
            document.getElementById("paidto").value = ''
            document.getElementById("paymentamount").value = ''
            document.getElementById("depositamount").value = ''
            document.getElementById("memo").value = ''
            $('#popup').hide()
            $('#transid').val("")
            $('#depositamount').prop('disabled', false)
            $('#paymentamount').prop('disabled', false)
        }

        function addTrans() {
            $('#addtransModal').show()
        }

        function cancelAdd() {
            $('#addtransModal').hide()
        }

        function addBank(shopid) {
            $('#bankModal').modal('show')
        }

        function saveBankAccount(shopid) {
            // note the balance has to be saved to the account register

            var bankname = $("#bankname").val();
            var accttype = $("#accounttype").val();
            var acctdesc = $("#accountdescription").val();
            var acctnum = $("#accountnumber").val();
            var routenum = $("#routingnumber").val();
            var dateopen = $("#opendate").val();
            var begbal = $("#beginningbalance").val();

            console.log("AccountNumber is " + acctnum)

            var ds = 'bankname=' + bankname + '&accounttype=' + accttype + '&accountdescription=' + acctdesc + '&accountnumber=' + acctnum + '&routingnumber=' + routenum + '&opendate=' + dateopen + '&shopid=' + shopid + '&begbal=' + begbal;

            $.ajax({
                type: "post",
                url: "savebank.php",
                data: ds,
                success: function () {
                    location.reload();
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    swal("Error in Insert")
                }
            });
        }

        function closeAccount(bankid) {
            console.log("You are in the close account function")

            <?php
            echo "$('#delModal').modal('show')";
            ?>
        }

        function addTrans(shopid) {

            myid = $('#transid').val()

            $('#addTransModal').modal('show')
        }

        var Old_Val1;
        var Input_Field1 = $('#depamount');

        Input_Field1.focus(function () {
            Old_Val1 = Input_Field1.val();
        });

        Input_Field1.blur(function () {
            var new_input_val1 = Input_Field1.val();
            if (new_input_val1 != Old_Val1) {
                $('#pymtamount').prop('disabled', true)
            }
        });

        var Old_Val2;
        var Input_Field2 = $('#pymtamount');

        Input_Field2.focus(function () {
            Old_Val2 = Input_Field2.val();
        });

        Input_Field2.blur(function () {
            var new_input_val2 = Input_Field2.val();
            if (new_input_val2 != Old_Val2) {
                $('#depamount').prop('disabled', true)
            }
        });


        function addtransaction(shopid, id, runningbal) {
            $('#addtransbtn').attr("disabled", "true")

            //var myid = $('#transid').val(id)

            console.log("Account id " + id)

            var transnumber = $("#transnumber").val();
            var transdate = $("#transdate").val();
            var paidto = $("#paidto").val();
            var pymtamount = $("#pymtamount").val();
            var depamount = $("#depamount").val();
            var account = $("#account").val();
            var memo = $("#memo").val();
            //var displayrunningbal = $("#runningbal").val();

            //var runningbal = parseFloat("displayrunningbal");


            var ds = 'shopid=' + shopid + '&accountid=' + id + '&transnumber=' + transnumber + '&transdate=' + transdate + '&paidto=' + paidto + '&memo= ' + memo + '&depamount=' + depamount + '&pymtamount=' + pymtamount + '&account=' + account + '&runningbal=' + runningbal;
            console.log(ds)

            $.ajax({
                type: "post",
                url: "addtransaction.php",
                data: ds,
                success: function () {
                    location.reload();
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    swal("Error in Insert")
                }

            })
        }

        function runbalRecalc(shopid, bankid) {

            $('#spinner').show()
            var ds = 'shopid=' + shopid + '&accountid=' + bankid;
            console.log(ds)

            $.ajax({
                type: "post",
                url: "updateAcctRegistryRunBal_ByAcct.php",
                data: ds,
                success: function () {
                    $('#spinner').hide()
                    location.reload();
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    swal("Error in Insert")
                }

            })
        }

        function deleteAccount(shopid, id) {

            swal({
                    title: "",
                    text: "Are you sure you want to close this Bank Account?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonClass: "btn-danger",
                    confirmButtonText: "Yes, close it",
                    closeOnConfirm: false
                },
                function () {
                    var ds = 'shopid=' + shopid + '&id=' + id;
                    $.ajax({
                        type: "post",
                        url: "closeaccount.php",
                        data: ds,
                        success: function () {
                            location.href = "accountlist.php";
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            swal("Error in Delete")
                        }
                    });

                });
        }

    </script>
    <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner" style="top: 50% !important;">
</body>
</html>

