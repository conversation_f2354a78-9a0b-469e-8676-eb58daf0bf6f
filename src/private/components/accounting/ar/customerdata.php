<!DOCTYPE html>
<?php
require CONN;
function selectPhone($p,$c,$w) {
}

$shopid = $_COOKIE['shopid'];

// Get value passed from the apply filter input
if (isset($_GET['sf'])) {
	$sf = trim($_GET['sf']);
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
  
				<table class="table table-condensed table-striped table-header-bg">
					<thead>
					<!-- first row within table -->	
						<tr>
							<td><strong>Name</strong></td>
							<td><strong>Address</strong></td>
							<td><strong>Home/Cell/Work Phone</strong></td>
						</tr>		
					</thead>
<?php			
					
					
					if (empty($sf)) {

						$stmt = "SELECT c.lastname,c.firstname,c.address,c.homephone,c.workphone,c.cellphone,c.customerid,c.shopid";
						$stmt .= " from customer c ";
						$stmt .= "WHERE c.shopid = ? ";
						$stmt .= "  AND (length(lastname) > 0 or length(firstname) > 0)  ";
						$stmt .= "  AND  active != 'no'  ";
						$stmt .= " order by lastname asc LIMIT 100";

					}else {
						$sf = $sf . "%";
						$stmt = "SELECT c.lastname,c.firstname,c.address,c.homephone,c.workphone,c.cellphone,c.customerid,c.shopid";
						$stmt .= " from customer c ";
						$stmt .= "WHERE c.shopid = ? ";
						$stmt .= "  AND (concat(trim(lastname),',',trim(firstname)) like ?  or trim(lastname) like ? or trim(firstname) like ?  ";
						$stmt .= "   OR address like ? or homephone like ? ";
						$stmt .= "   OR workphone like ? or cellphone like ?) ";
						$stmt .= "  AND (length(lastname) > 0 or length(firstname) > 0)  ";
						$stmt .= "  AND  active != 'no'  ";
						$stmt .= " order by lastname asc";
					}
					//echo $stmt;
					if (empty($sf)) {
						if($query = $conn->prepare($stmt)){
							$query->bind_param("s",$shopid);
							$query->execute();
							$result = $query->get_result();
						}else{
							echo "Customer Prepare failed: (" . $conn->errno . ") " . $conn->error;
						}
					}else{
						if($query = $conn->prepare($stmt)){
							$query->bind_param("ssssssss",$shopid,$sf,$sf,$sf,$sf,$sf,$sf,$sf);
							$query->execute();
							$result = $query->get_result();
						}else{
							echo "Customer Prepare failed: (" . $conn->errno . ") " . $conn->error;
						}
						//printf(str_replace("?","'"."%s"."'",$stmt),$shopid);
					}
		
					while($row = mysqli_fetch_assoc($result)) {
						$hp = $row["homephone"];
						$cp = $row["cellphone"];
						$wp = $row["workphone"];
						
						$allphones = "";
						if (!empty($hp) && !empty($cp) && !empty($wp) ) {
							// all 3 present
							$allphones = formatPhone($hp) . "/" . formatPhone($cp) . "/" . formatPhone($wp);	
						}elseif (!empty($cp) && !empty($wp)) {
							// all 2 out of 3 present
							$allphones =  " /" . formatPhone($cp) . "/" . formatPhone($wp);	
						}elseif (!empty($wp)) {
							$allphones =  "//" . formatPhone($wp);	
						}elseif (!empty($hp)) {
							$allphones =  formatPhone($hp). "//";	
						}elseif (!empty($cp)) {
							$allphones =  "/" . formatPhone($cp). "/";	
						}	

						// the random thing for what reason
						$max=1000000;
						$min=10000;
						$r = 100000;
						//Randomize find this function in control panel pages
						//$r = Int(($max-$min+1)*Rnd+$min)
						
						$url = "postpaymentcustomer.php?r=$r&customerid={$row['customerid']}";
    					$onclickPay = !$shopIsReadOnly ? "onclick=\"location.href='$url'\"" : "";
?>																	
						<tr <?= $onclickPay; ?>>
							<td><?php echo strtoupper($row["lastname"]) . "," . strtoupper($row["firstname"]) ?>&nbsp;</td>
							<td><?php echo strtoupper($row["address"]) ;?>&nbsp;</td>	
							<td><?php echo $allphones; ?>&nbsp;</td>
						</tr>
<?php
					} // end of while
?>
				</table>  <!-- end of table-->
<?php
    mysqli_close($conn);
?>

