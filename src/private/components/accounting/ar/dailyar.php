<!DOCTYPE html>
<html>
<?php
require CONN;

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<head>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <link rel="stylesheet" href="<?= CSS ?>/bootstrap.min.css">
    <title>RO Payments Received Report</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css">
    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/adminLTE/css/AdminLTE.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.1.0/jquery.min.js"></script>
    <!-- AdminLTE Skins. Choose a skin from the css/skins
         folder instead of downloading all of them to reduce the load. -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/adminLTE/css/skins/_all-skins.min.css">
    <script src="<?= SCRIPT ?>/bootstrap.min.js"></script>
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">
	<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <!-- SlimScroll -->

<style type="text/css">
.auto-style2 {
	background-color: #1B6D85;
	color: #FFFFFF;
}
.auto-style3 {
	background-color: #1B6D85;
	color: #FFFFFF;
	text-align: right;
}
.auto-style4 {
	background-color: #1B6D85;
	color: #FFFFFF;
	text-align: left;
}
</style>

</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
	<div id="printdiv">
	<span class="btn btn-primary" onclick="printit()">Print</span>
	<span class="btn btn-default" onclick="location.href='ar.php'">Done</span>
	</div>
	<h3 style="margin:auto;width:313px;text-align:center">RO Payments Received Report<br><?php echo $_GET['sd']." to ".$_GET['ed']; ?></h3>
	<table class="table table-condensed table-striped" style="width:99%;margin:auto">
		<thead>
			<tr>
				<td class="auto-style2"><strong>RO#</strong></td>
				<td class="auto-style2"><strong>Customer</strong></td>
				<td class="auto-style2"><strong>Vehicle</strong></td>
				<td class="auto-style4"><strong>Payment Date</strong></td>
				<td class="auto-style4"><strong>RO Closed Date</strong></td>
				<td class="auto-style4"><strong>Type</strong></td>
				<td class="auto-style3"><strong>Amount</strong></td>
				<?php if (!$shopIsReadOnly): ?>
					<td class="auto-style3"><strong>Post to QBO</strong></td>
				<?php endif; ?>
			</tr>
		</thead>
		<tbody>
			<?php
			$shopid = $_COOKIE['shopid'];
			$sd = date("Y-m-d",strtotime($_GET['sd']));
			$ed = date("Y-m-d",strtotime($_GET['ed']));
			$runttl = 0;

			$stmt = "select id,amt,pdate,ptype,customer,vehinfo,a.roid,r.statusdate from accountpayments a"
			. " left join repairorders r on a.shopid = r.shopid and a.roid = r.roid"
			. " where a.pdate >= '$sd' and a.pdate <= '$ed' and a.shopid = '$shopid'";
			//echo $stmt;
			$totalar = 0;
			if ($query = $conn->prepare($stmt)){
				//$query->bind_param("s",$shopid);
				$query->execute();
				$result = $query->get_result();
				while ($rs = $result->fetch_array()){
					// get the ro closed date and see if it is older than the payment date.
					$rstmt = "select statusdate from repairorders where shopid = ? and roid = ?";
					if ($rquery = $conn->prepare($rstmt)){
						$rquery->bind_param("si",$shopid,$rs['roid']);
						$rquery->execute();
						$rquery->bind_result($statusdate);
						$rquery->fetch();
						$rquery->close();
					}
					$statdate = strtotime($statusdate);
					$pdate = strtotime($rs['pdate']);
					//if ($statdate < $pdate){
						$runttl += $rs['amt'];
			?>
			<tr>
				<td><?php echo $rs['roid']; ?></td>
				<td><?php echo $rs['customer']; ?></td>
				<td><?php echo $rs['vehinfo']; ?></td>
				<td><?php echo date("m/d/Y",strtotime($rs['pdate'])); ?></td>
				<td><?php echo date("m/d/Y",strtotime($rs['statusdate'])); ?></td>
				<td><?php echo $rs['ptype']; ?></td>
				<td class="text-right"><?php echo number_format($rs['amt'],2); ?></td>
				<?php if (!$shopIsReadOnly): ?>
					<td class="text-right"><span id="sendtoqbo<?php echo $rs['id']; ?>" class="btn btn-danger btn-sm" onclick="posttoQBO(<?php echo $rs['id']; ?>)">Post to QBO</span> </td>
				<?php endif; ?>
			</tr>
			<?php
					//}
				}
			}
			?>
		</tbody>
	</table>
	<div class="text-right">
	<h3>Total Payments Received: <?php echo number_format($runttl,2); ?></h3>
	</div>


	<h3 style="margin:auto;width:313px;text-align:center">PS Payments Received Report<br><?php echo $_GET['sd']." to ".$_GET['ed']; ?></h3>
	<table class="table table-condensed table-striped" style="width:99%;margin:auto">
		<thead>
			<tr>
				<td class="auto-style2"><strong>PS#</strong></td>
				<td class="auto-style2"><strong>Customer</strong></td>
				<td class="auto-style4"><strong>Payment Date</strong></td>
				<td class="auto-style4"><strong>Type</strong></td>
				<td class="auto-style3"><strong>Amount</strong></td>
			</tr>
		</thead>
		<tbody>
			<?php
			$runttl = 0;

			$stmt = "select a.psid,a.ptype,a.amt,a.pdate,concat(firstname,' ',lastname) as customer from `accountpayments-ps` a"
			. " left join ps on a.shopid = ps.shopid and a.psid = ps.psid"
			. " left join customer c on ps.shopid = c.shopid and ps.cid = c.customerid"
			. " where a.pdate >= '$sd' and a.pdate <= '$ed' and a.shopid = '$shopid'";

			if ($query = $conn->prepare($stmt)){
				$query->execute();
				$result = $query->get_result();
				while ($rs = $result->fetch_array()){

					$runttl += $rs['amt'];
			?>
			<tr>
				<td><?php echo $rs['psid']; ?></td>
				<td><?php echo $rs['customer']; ?></td>
				<td><?php echo date("m/d/Y",strtotime($rs['pdate'])); ?></td>
				<td><?php echo $rs['ptype']; ?></td>
				<td class="text-right"><?php echo number_format($rs['amt'],2); ?></td>
			</tr>
			<?php
					//}
				}
			}
			?>
		</tbody>
	</table>
	<div class="text-right">
	<h3>Total Payments Received: <?php echo number_format($runttl,2); ?></h3>
	</div>

	<h3 style="text-align:center">RO's Closed Without Payment</h3>
	<table class="table table-condensed table-striped" style="width:99%;margin:auto">
		<thead>
			<tr>
				<td class="auto-style2"><strong>RO#</strong></td>
				<td class="auto-style2"><strong>Customer</strong></td>
				<td class="auto-style2"><strong>Vehicle</strong></td>
				<td class="auto-style4"><strong>Closed Date</strong></td>
				<td class="auto-style4"><strong>Balance</strong></td>
			</tr>
		</thead>
		<tbody>
			<?php
			$rostmt = "select roid,customer,vehinfo,statusdate,balance,totalro from repairorders where shopid = ? and"
			. " status = 'closed' and rotype != 'no approval' and statusdate >= ? and statusdate <= ?";
			if ($roquery = $conn->prepare($rostmt)){
				$roquery->bind_param("sss",$shopid,$sd,$ed);
				$roquery->execute();
				$ror = $roquery->get_result();
				while ($rors = $ror->fetch_assoc()){
					// get total payments made before the ro was closed
					$stmt = "select coalesce(sum(amt),0) amt from accountpayments where shopid = ? and roid = ?"
					. " and pdate <= '".$rors['statusdate']."'";
					if ($query = $conn->prepare($stmt)){
						$query->bind_param("si",$shopid,$rors['roid']);
						$query->execute();
						$query->bind_result($tpmts);
						$query->fetch();
						$query->close();
					}
					if (($rors['totalro'] - $tpmts) > 0){
			?>
			<tr>
				<td><?php echo $rors['roid']; ?></td>
				<td><?php echo $rors['customer']; ?></td>
				<td><?php echo $rors['vehinfo']; ?></td>
				<td><?php echo $rors['statusdate']; ?></td>
				<td><?php echo $rors['balance']; ?></td>
			</tr>
			<?php
					}
				}
			}
			?>
		</tbody>
	</table>
	<script>
		function printit(){

			$('#printdiv').hide();
			window.print()
			$('#printdiv').show();

		}

		function posttoQBO(id){

			$('#sendtoqbo'+id).attr("disabled",true)
			$.ajax({
				data: "id="+id,
				url: "<?= COMPONENTS_PRIVATE; ?>/accounting/apination/postpaymentoqbo.php",
				type: "post",
				success: function(r){
					console.log(r)
					//$('#sendtoqbo'+id).attr("disabled",false)
					swal("Payment posted successfully")
				},
				error: function (xhr, ajaxOptions, thrownError) {
					console.log(xhr.status);
					console.log(xhr.responseText);
					console.log(thrownError);
					//$('#sendtoqbo'+id).attr("disabled",false)
				}
			})
			//$('#sendtoqbo'+id).attr("disabled",false)

		}
	</script>

</body>

</html>

