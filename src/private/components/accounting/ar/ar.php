<?php

require(CONN);

$shopid = $_COOKIE['shopid'];

if ($shopid == "1734") {
    redirect_to("1734ar.php");
}


$roid = "";
if (isset($_GET['roid'])) {
    $roid = $_GET['roid'];
}
if (isset($_GET['sf'])) {
    $sf = $_GET['sf'];
}

// Get value passed from the apply filter input
if (isset($_GET['n'])) {
    $searchstring = $_GET['n'];
} else {
    $searchstring = "";
}

if ((isset($_GET['sb'])) && ($_GET['sb'] == "ro")) {
    $sort = "ro";
} elseif ((isset($_GET['sb'])) && ($_GET['sb'] == "date")) {
    $sort = "date";
} elseif ((isset($_GET['sb'])) && ($_GET['sb'] == "days")) {
    $sort = "days";
} elseif ((isset($_GET['sb'])) && ($_GET['sb'] == "customer")) {
    $sort = "customer";
} elseif ((isset($_GET['sb'])) && ($_GET['sb'] == "total")) {
    $sort = "total";
} else {
    $sort = "";
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<!DOCTYPE html>
<html>
<!--[if IE 9]>
<html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">
    <title><?= getPageTitle() ?></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon() ?>' type='image/x-icon'/>
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS; ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS; ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS; ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/datatables/jquery.dataTables.css">
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        .col-md-6 {
            border: 1px black solid
        }

        .col-md-8 {
            border: 1px black solid
        }

        .col-md-4 {
            border: 1px black solid
        }

        .editacct-parent-expand {
            position: absolute;
            left: 230px;
            top: 0px;
            width: -moz-calc(100% - 231px);
            /* WebKit */
            width: -webkit-calc(100% - 231px);
            /* Opera */
            width: -o-calc(100% - 231px);
            /* Standard */
            width: calc(100% - 231px);
            height: 100%;
            z-index: 9997;
            background-color: #F5F5F5;
            -webkit-transition: all .4s;
            -moz-transition: all .4s;
            -o-transition: all .4s;
            transition: all .4s;
            padding: 20px;
        }

        .expand-sbp {
            float: right;
            font-size: 14pt;
            color: red;
            cursor: pointer;
            -webkit-transition: all .4s;
            -moz-transition: all .4s;
            -o-transition: all .4s;
            transition: all .4s;
        }

        .style1 {
            font-size: 14px;
            color: white;
            background-image: url('<?= IMAGE ?>/newimages/pageheader.jpg');
            font-weight: bold;
            text-align: right;
            background-color: #800000;
        }

        .style12 {
            font-size: 14px;
            color: white;
            background-image: url('<?= IMAGE ?>/newimages/wipheader.jpg');
            font-weight: bold;
            text-align: right;
        }

        .tbl2header {
            font-size: 14px;
            color: white;
            background-image: url('<?= IMAGE ?>/newimages/wipheader.jpg');
            font-weight: bold
        }

        .menustyle {
            text-align: center;
            background-color: #F0F0F0;
            width: 11%;
            border: 1px gray outset;
            color: maroon;
            font-weight: bold;
            cursor: pointer;
        }

        .menustyle:hover {
            background-color: white;
        }

        .style13 {
            background-color: #FFFFCC;
        }

        .style14 {
            color: navy;
        }

        .rowclicker {
            color: white;
            text-decoration: none;
            font-weight: bold
        }

        .rowclicker:visited {
            color: white;
            text-decoration: none;
            font-weight: bold;
        }

        .rowclicker:hover {
            color: yellow;
            text-decoration: none;
            font-weight: bold;

        }

        .style15 {
            border: 1px solid #000000;
            text-align: center;
        }

        .style16 {
            text-align: right;
        }

        #hider {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 100%;
            background-color: gray;
            -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
            filter: alpha(opacity=70);
            -moz-opacity: .70;
            opacity: .7;
            z-index: 997;
            display: none;

        }

        .auto-style1 {
            background-color: #800000;
        }

        .auto-style2 {
            font-size: 14px;
            color: white;
            background-image: url('<?= IMAGE ?>/newimages/wipheader.jpg');
            font-weight: bold;
            text-align: right;
            background-color: #800000;
        }

        .auto-style3 {
            font-size: 14px;
            color: white;
            background-image: url('<?= IMAGE ?>/newimages/wipheader.jpg');
            font-weight: bold;
            background-color: #800000;
        }

        .header {
            padding: 1px;
        }
    </style>

</head>
<script>
    function startStmts() {

        //$('#hider').show()
        //$('#stmtdate').show()
        $("#datepromptmodal").modal('show');
    }

    function cancelStmts() {

        $('#hider').hide()
        $('#stmtdate').hide()
        $('#stmtbutton').hide()
        $('#stmtframe').hide()
        $('#btndiv').hide()
        document.getElementById("stmtframe").src = ""
    }

    function runStmts() {

        var sd = $("#sd").val();
        var ed = $("#ed").val();

        url = '<?= COMPONENTS_PRIVATE ?>/accounting/ar/statementsforpdf.php?sd=' + sd + '&ed=' + ed + '&shopid=<?php echo $shopid ?>'
        console.log(url)

        document.getElementById("stmtframe").src = url
        $('#stmtbutton').show()
        $('#stmtframe').show()
        $('#btndiv').show()
        $('#stmtdate').hide()

    }

    function printStmts() {

        /*var frm = $('#stmtframe').contentWindow;
        frm.focus();// focus on contentWindow is needed on some ie versions
        frm.print();
        return false;*/

        document.getElementById("stmtframe").contentWindow.print()
    }
</script>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<div id="hider"></div>

<div id="mainalert"
     style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large"
     class="alert alert-success"></div>
<div id="header"></div>
<!-- Page Container -->
<!--
        Available Classes:

        'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

        'sidebar-l'                  Left Sidebar and right Side Overlay
        'sidebar-r'                  Right Sidebar and left Side Overlay
        'sidebar-mini'               Mini hoverable Sidebar (> 991px)
        'sidebar-o'                  Visible Sidebar by default (> 991px)
        'sidebar-o-xs'               Visible Sidebar by default (< 992px)

        'side-overlay-hover'         Hoverable Side Overlay (> 991px)
        'side-overlay-o'             Visible Side Overlay by default (> 991px)

        'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

        'header-navbar-fixed'        Enables fixed header
    -->
<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">

    <!-- Sidebar -->
    <nav id="sidebar">
        <!-- Sidebar Scroll Container -->
        <div id="sidebar-scroll">
            <!-- Sidebar Content -->
            <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
            <div class="sidebar-content">
                <!-- Side Header -->
                <div class="side-header side-content bg-white-op">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button"
                            data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                        <i class="text-primary">
                            <?php getLogo() ?></i>
                        <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                    </a>
                </div>
                <!-- END Side Header -->

                <!-- Side Content -->
                <div class="side-content-sbp-ro side-content">
                    <h3 style="color:white">Accounting Menu</h3>
                    <ul class="nav-main">
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span
                                        class="sidebar-mini-hide">Back to WIP</span></a>
                        </li>
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/accounting/default.php"><i
                                        class="fa fa-sign-out"></i><span
                                        class="sidebar-mini-hide">Accounting Home</span></a>
                        </li>
                        <li>
                            <a title='Pymts for Mulitple RO/PS' href="customerselect.php"> <i class="fa fa-usd"></i>Payments
                                for Mulitple RO/PS </a>
                        <li>
                        <li>
                            <a title='Manage Customer On Hold Status' href="customerselectonhold.php"> <i
                                        class="fa fa-ban"></i>Manage On Hold Status </a>
                        <li>
                        <li>
                            <a title='AR Aging' href="araging.php"><i class="fa fa-clipboard"></i>AR Aging Report </a>
                        </li>
                        <li>
                            <a title='Payments Received Report' href="dateprompt.php?r=dailyar.php"><i
                                        class="fa fa-clipboard"></i>Payments Rec'd Report</a>
                        </li>
                        <li>
                            <a class="nav-submenu" data-toggle="nav-submenu" href="#"><i
                                        class="fa fa-file-text-o"></i><span class="sidebar-mini-hide">Print</span></a>
                            <ul id="print">
                                <li>
                                    <a title='Print List' href="printar.php"> Print A/R List </a>
                                </li>
                                <li>
                                    <a title='Print Statements' href="#" onclick="startStmts()"> Print Statements</a>
                                </li>
                            </ul>
                        <li>
                            <?php
                            $path = getcwd();
                            $path = $path . "\\$shopid";
                            //echo $path;
                            if (file_exists($path)) {
                            ?>
                        <li>
                            <a href="<?php echo $shopid; ?>/filelist.php"><i class="fa fa-sign-out"></i><span
                                        class="sidebar-mini-hide">Upload from QB</span></a>
                        </li>
                        <?php
                        }
                        ?>
                    </ul>
                </div>
                <!-- END Side Content -->
            </div>
            <!-- Sidebar Content -->
        </div>
        <!-- END Sidebar Scroll Container -->
    </nav>
    <!-- END Sidebar -->

    <!-- Header -->
    <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar"
            class="content-mini content-mini-full">

        <!-- Header Navigation Right -->
        Accounts Receivable
        <!-- END Header Navigation Right -->
        <!-- Header Navigation Left -->

        <ul class="nav-header pull-left">
            <li class="hidden-md hidden-lg">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                    <i class="fa fa-navicon"></i>
                </button>
            </li>
            <li class="hidden-xs hidden-sm">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" id="close-sidebar"
                        data-action="sidebar_mini_toggle" type="button">
                    <i class="fa fa-bars"></i>
                </button>
            </li>
            <li>
                <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                <button style="display:none" class="btn btn-default pull-right" data-toggle="modal"
                        data-target="#apps-modal" type="button">
                    <i class="si si-grid"></i>
                </button>
            </li>
            <li class="visible-xs">
                <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search"
                        data-class="header-search-xs-visible" type="button">
                    <i class="fa fa-search"></i>
                </button>
            </li>
            <li>

            </li>
        </ul>

        <!-- END Header Navigation Left -->
    </header>
    <!-- END Header -->

    <!-- Main Container -->
    <main class="container-fluid" id="main-container">
        <table style="width: 100%" cellpadding="3" cellspacing="0">
            <tr>
                <td style="width: 61%">The following invoices are on account and have not been paid
                    (Blue header=RO's, Light Blue header=Parts Sales). </br />
                    To print a statement for a single account, click the RO# for that customer,then
                    click Statement<strong><br>NOTE: TO SORT LIST, CLICK ON A COLUMN HEADER.</strong>
                </td>
            </tr>
        </table>

        <div id="hider"></div>
        <div id="btndiv"
             style="text-align:center;background-color:white;height:65px;display:none;z-index:1500;position:absolute;width:100%;left:0px;top:0px">
            <br>
            <button type="button" style="width: 116px; height: 38px;" class="btn btn-default" onclick="cancelStmts()"
                    id="stmtbutton">Close
            </button>
            <button type="button" onclick="printStmts()" style="width: 116px; height: 38px;" class="btn btn-danger"
                    id="printstmtbutton">Print
            </button>
        </div>

        <iframe id="stmtframe"
                style="position:absolute;width:80%;height:90%;background-color:white;border:2px black solid;border-radius:5px;z-index:999;left:15%;top:65px;display:none"></iframe>


        <div id="datepromptmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">Select Start and End Dates</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group" style="width:50%;margin:auto">
                                <div class="row" style="margin-bottom: 10px">
                                    <label class="col-md-5 control-label text-right" for="sd">Select Start Date</label>
                                    <div class="col-md-5"><input type="text" class="form-control" id="sd"
                                                                 name="sd">
                                    </div>
                                </div>
                                <div class="row">
                                    <label class="col-md-5 text-right control-label" for="ed">Select End Date</label>
                                    <div class="col-md-5"><input type="text" class="form-control" id="ed"
                                                                 name="ed">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-5">
                                        <nav id="datenav" class="inputnav">
                                            <ul class="inputul list-unstyled">
                                                <li class="dropdown input-top">
                                                    <div id="datestat" style="color:white" class="btn btn-warning"
                                                         role="button"
                                                         class="dropdownselected" data-toggle="dropdown">Select Date
                                                        Range
                                                    </div>
                                                    <ul style="width:100%" id="statusselect" class="dropdown-menu">
                                                        <li id="assembly-li" class="input-li"><a href="#"
                                                                                                 class="nav-link"
                                                                                                 onclick="setdates('This Week')">This
                                                                Week</a></li>
                                                        <li id="q-check-li" class="input-li"><a href="#"
                                                                                                class="nav-link"
                                                                                                onclick="setdates('This Month')">This
                                                                Month</a></li>
                                                        <li id="final-li" class="input-li"><a href="#" class="nav-link"
                                                                                              onclick="setdates('This Year')">This
                                                                Year</a></li>
                                                        <li id="inspection-li" class="input-li"><a href="#"
                                                                                                   class="nav-link"
                                                                                                   onclick="setdates('Last Week')">Last
                                                                Week</a></li>
                                                        <li id="approval-li" class="input-li"><a href="#"
                                                                                                 class="nav-link active"
                                                                                                 onclick="setdates('Last Month')">Last
                                                                Month</a></li>
                                                        <li id="parts-li" class="input-li"><a href="#" class="nav-link"
                                                                                              onclick="setdates('Last Year')">Last
                                                                Year</a></li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div style="margin-top:20px;" class="modal-footer">
                        <button class="btn btn-md btn-primary" type="button" onclick="runStmts()"
                                data-dismiss="modal">
                            Run Statements
                        </button>
                        <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>


        <form style="padding:0px;margin:0px" class="form-horizontal push-10-t" action="base_forms_elements_modern.php"
              method="post" onsubmit="return false;">
            <div style="padding:0px;margin:0px" class="form-group">
                <div style="padding:0px;margin:0px" class="col-sm-12">
                    <div style="padding:0px;margin:0px" class="form-material">
                        <input class="form-control" style="border:1px silver solid;border-radius:3px;padding-left:10px;"
                               type="text"
                               placeholder="Search Account Receivables by (RO#, Date Complete, Days, Cust Phones, Total RO, Balance and Part Sales)"
                               id="material-text2" value="<?php if (strlen($roid) > 0) {
                            echo $roid;
                        } ?>" name="material-text2">
                    </div>
        </form>

        <div id="table-container-ar" class="table-responsive">
            <table id="arlist-table" class="table table-condensed table-striped table-header-bg">
                <thead>
                <tr class="header">
                    <td class="auto-style1"><a class="rowclicker" href='ar.php?sb=ro'>RO #</a></td>
                    <td class="auto-style1"><a class="rowclicker" href='ar.php?sb=date'>Date Complete</a></td>
                    <td class="auto-style1"><a class="rowclicker" href='ar.php?sb=days'>Days</a></td>
                    <td class="auto-style1"><a class="rowclicker" href='ar.php?sb=customer'>Customer Home/Work/Cell</a>
                    </td>
                    <td class="auto-style1"><a class="rowclicker" href='ar.php?sb=total'>Total RO</a></td>
                    <td class="auto-style1"><a class="rowclicker" href='ar.php?sb=balance'>Balance</a></td>
                </tr>
                </thead>
                <tbody>
                <?php
                //********* get ro's with balances *************

                $stmt = "SELECT storagefee,onhold,repairorders.customerid,totalprts,totallbr,totalsublet,userfee1,userfee2,userfee3";
                $stmt .= ",hazardouswaste,salestax,discountamt,rotype,totalfees";
                $stmt .= ",roid,statusdate,lastfirst,customerphone,repairorders.cellphone,customerwork,totalro,balance,repairorders.shopid ";
                $stmt .= "from repairorders ";
                $stmt .= "left join customer ";
                $stmt .= "  on repairorders.shopid = customer.shopid ";
                $stmt .= "  and repairorders.customerid = customer.customerid ";
                $stmt .= "WHERE repairorders.shopid = ? ";
                $stmt .= "  AND balance > 0.01 ";
                $stmt .= "  AND status = 'CLOSED' ";
                $stmt .= "  AND rotype != 'No Problem' and rotype != 'No Approval' ";

                if (isset($_GET['n'])) {
                    $searchstatus = "%" . $_GET['n'] . "%";
                    $stmt .= " AND lastfirst like ? ";
                }
                if ($sort == "ro") {
                    $stmt .= " order by roid desc";
                } elseif ($sort == "date") {
                    $stmt .= " order by statusdate desc";
                } elseif ($sort == "days") {
                    $stmt .= " order by datediff(curdate(),statusdate) desc";
                } elseif ($sort == "customer") {
                    $stmt .= " order by customerlast asc";
                } elseif ($sort == "total") {
                    $stmt .= " order by totalro desc";
                } else {
                    $stmt .= " order by balance desc ";
                }
                //echo "Statmeent is " . $stmt . "</br>";

                if (isset($_GET['n'])) {
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("ss", $shopid, $searchstatus);
                        $query->execute();
                        $result = $query->get_result();
                    } else {
                        echo "Repair Orders Prepare 1 failed: (" . $conn->errno . ") " . $conn->error;
                    }
                } else {
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("s", $shopid);
                        $query->execute();
                        $result = $query->get_result();
                    } else {
                        echo "Repair Orders Prepare 2 failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    //printf(str_replace("?","'"."%s"."'",$stmt),$shopid);

                }

                //echo $query;
                $bal = 0;

                $numofrows = mysqli_num_rows($result);

                $c = 0;
                $statusdate = "";
                $days = "";
                $lf = "";
                $runningbal = 0;
                $locationstring = "";

                // Check for data
                if ($numofrows > 0) {

                while ($row = mysqli_fetch_assoc($result)) {
                $roid = $row["roid"];

                $shopid = $row["shopid"];
                $dstart = null;
                //meant to be when the customer is paying on "Account" meaning they will pay later.  generally not used at all anymore
                $stmt = "SELECT sum(amt) as a ";
                $stmt .= "from accountpayments ";
                $stmt .= "WHERE shopid = ? ";
                $stmt .= "  AND roid = ?";
                //$stmt .= "  AND ptype != 'Account' ";
                //echo $stmt;

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("si", $shopid, $roid);
                    $query->execute();
                    $apresult = $query->get_result();
                } else {
                    echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $aprow = $apresult->fetch_array();

                $ap_amount = $aprow['a'];

                if ($ap_amount > 0) {
                    $ropayments = $ap_amount;
                } else {
                    $ropayments = 0;
                }

                // moved ttlro calc to here

                $ttlro = $row["totalprts"] + $row["totallbr"] + $row["totalsublet"] + $row["hazardouswaste"] + $row["userfee1"] + $row["userfee2"] + $row["userfee3"] + $row['storagefee'] + $row["salestax"] - $row["discountamt"];

                $bal = $ttlro - $ropayments;
                $displaybal = asDollars($bal);

                $ttlro = asDollars($ttlro);

                if ($bal > 0.01) {

                $runningbal = $runningbal + $bal;

                ?>
                <tr title="<?= $bal ?>">
                    <td style="width: 5%" class="roidcell" valign="top">
                        <span class="btn btn-primary btn-sm"><?php echo $row["roid"]; ?></span>
                    </td>
                    <?php
                    if ($row["statusdate"] == "0000-00-00" || $row["statusdate"] == null) {
                        $statusdate = "";
                        $days = "";
                    } else {
                        $statusdate = $row["statusdate"];
                        $dStart = new DateTime($statusdate);
                        $dEnd = new DateTime();
                        $diff = $dStart->diff($dEnd);
                        $days = $diff->format("%a");
                    }

                    // look for switch command
                    if (!$row["customerphone"]) {
                        $customerphone = "NA";
                    } else {
                        $customerphone = formatPhone(strtoupper($row["customerphone"]));
                    }
                    if (!$row["customerwork"]) {
                        $customerwork = "NA";
                    } else {
                        $customerwork = formatPhone(strtoupper($row["customerwork"]));
                    }
                    if (!$row["cellphone"]) {
                        $cellphone = "NA";
                    } else {
                        $cellphone = formatPhone(strtoupper($row["cellphone"]));
                    }

                    //                              $ttlro = $row["totalprts"]+$row["totallbr"]+$row["totalsublet"]+$row["hazardouswaste"]+$row["userfee1"]+$row["userfee2"]+$row["userfee3"]+$row["salestax"]-$row["discountamt"];
                    ?>
                    <!-- date complete -->
                    <td class="statdatecell" style="width: 20%" class="style6" valign="top">
                        <?php
                        if ($row["statusdate"] == "0000-00-00" || $row["statusdate"] == null) {

                            echo "";
                        } else {
                            echo date_format($dStart, 'm/d/Y');
                        }
                        ?>
                    </td>
                    <td class="dayscell" style="width: 5%" class="style6" valign="top"><?php echo $days; ?></td>
                    <?php
                    if (strlen($row["customerphone"]) > 0 || strlen($row["customerwork"]) > 0 || strlen($row["cellphone"]) > 0) {
                        $lf = $row["lastfirst"] . "<br />" . $customerphone . " / " . $customerwork . " / " . $cellphone;
                        if ((strtolower($row["onhold"])) == "yes") {
                            $lf = $lf . " <span style='color:red;font-weight:bold'>Customer On Hold</span>";
                        }
                        ?>
                        <td class="lfcell">
                            <?php echo $lf; ?>
                        </td>
                        <?php
                    } else {
                        $lf = $row["lastfirst"];
                        ?>
                        <td class="lfcell" style="width:30%" class="style7" valign="top">
                            <?php echo $lf; ?>
                        </td>
                        <?php
                    } // end of if

                    ?>
                    <td style="width: 15%" class="style5" valign="top"><?php echo sbpround($ttlro, 2); ?></td>
                    <td class="totalbox" onclick="" style="width: 15%"
                        text-align="right"><?php echo sbpround($displaybal, 2); ?></td>
                    </td>
                </tr>
                <?php if ($ropayments > 0) {
                    ?>
                    <tr>
                        <td colspan="7" style="height:400px;display:none;z-index:998" id="cell<?php echo $roid; ?>">
                            <iframe style="display:none;width:100%;height:400px;z-index:999"
                                    id="<?php echo $roid; ?>"></iframe>
                        </td>
                    </tr>
                    <?php
                } // end if
                ?>

                </tbody>
                <?php

                } // this is the end of the check for > 0.01 bal

                } // end of while

                } else {
                    //echo "We do not have data!!!!";
                    ?>
                    <tr>
                        No Accounts to be collected
                    </tr>
                    <?php
                } // end of check for data
                ?>
            </table>
        </div>
        <!-- END ar Page Container -->

        <!-- Start of the Parts sales -->
        <?php

        $stmt = "SELECT psid,psdate,total,balance,cid,lastname, ps.shopid ";
        $stmt .= " from ps ";
        $stmt .= " left outer join customer ";
        $stmt .= " on ps.shopid = customer.shopid ";
        $stmt .= " and ps.cid = customer.customerid ";
        $stmt .= "WHERE ps.shopid = ? ";
        $stmt .= "  AND psdate > '2012-10-31' ";
        $stmt .= "  AND balance > 0.01 ";
        $stmt .= "  AND ucase(status) = 'CLOSED' ";
        $stmt .= " ORDER BY balance desc ";


        if (isset($_GET['n'])) {
            $searchstatus = "%" . $_GET['n'] . "%";
            $stmt .= " AND lastfirst like ? ";
        }

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $psresult = $query->get_result();
        } else {
            echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        $psnumofrows = $psresult->num_rows;

        // Check for data

        if ($psnumofrows > 0) {


        ?>
        <div id="table-container-ps" class="table-responsive">
            <table id="pslist-table" class="table table-condensed table-striped table-header-bg">
                <thead>
                <tr class="header">
                    <td class="partcell"><a class="rowclicker" href='ar.php?sb=ro'>Invoice#</a></td>
                    <td class="auto-style1"><a class="rowclicker" href='ar.php?sb=date'>Date Sold</a></td>
                    <td class="auto-style1"><a class="rowclicker" href='ar.php?sb=days'>Days</a></td>
                    <td class="auto-style1"><a class="rowclicker" href='ar.php?sb=customer'>Customer Home/Work/Cell</a>
                    </td>
                    <td class="auto-style1"><a class="rowclicker" href='ar.php?sb=total'>Total Invoice</a></td>
                    <td class="auto-style1"><a class="rowclicker" href='ar.php?sb=balance'>Balance</a></td>

                </tr>
                </thead>
                <tbody>
                <?php
                $statusdate = "";
                $days = "";
                $lf = "";
                $lastname = "";
                $firstname = "";

                while ($ps = mysqli_fetch_assoc($psresult)) {
                    $customerid = $ps["cid"];
                    $shopid = $ps["shopid"];

                    $stmt = "SELECT sum(amt) as a ";
                    $stmt .= "from `accountpayments-ps` ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND psid = ?";
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $ps["psid"]);
                        $query->execute();
                        $apresult = $query->get_result();
                    } else {
                        echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    $aprow = $apresult->fetch_array();

                    $ap_amount = $aprow['a'];

                    if ($ap_amount > 0) {
                        $pspayments = $ap_amount;
                    } else {
                        $pspayments = 0;
                    }

                    $bal = $ps['total'] - $pspayments;

                    if ($bal > 0.01) {
                        ?>
                        <tr class="sbphover-row">
                            <?php
                            $dstart = "";
                            //echo "Total for Part Sale " . $ps["psid"] .  " and Cust " . $ps["cid"] . " is: " .  $ps["total"] . "</br>";

                            ?>

                            <td style="width: 5%" class="roidcell" valign="top">
                                <span class="btn btn-info btn-sm" 
                                    <?php echo !$shopIsReadOnly 
                                    ? "onclick=\"editpsAccount('{$ps['shopid']}','{$ps['psid']}')\"" 
                                    : ""; ?>>
                                    <?php echo $ps["psid"]; ?>
                                </span>
                            </td>
                            <?php
                            if ($ps["psdate"] == "0000-00-00") {
                                $psdate = "";
                                $days = "";
                            } else {
                                $psdate = $ps["psdate"];
                                $dStart = new DateTime($psdate);
                                $dEnd = new DateTime();
                                $diff = $dStart->diff($dEnd);
                                $days = $diff->format("%a");
                            }

                            // in the interest of performance only picking up 2 fields off customer
                            $stmt = "SELECT LastName,FirstName ";
                            $stmt .= "from customer ";
                            $stmt .= "WHERE shopid = ? ";
                            $stmt .= "  AND customerid = ?";
                            //echo $stmt;

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("si", $shopid, $customerid);
                                $query->execute();
                                $custresult = $query->get_result();
                            } else {
                                echo "Customer Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }


                            while ($cust = $custresult->fetch_array()) {

                                $custlname = $cust["LastName"];
                                $custfname = $cust["FirstName"];
                                $lf = $custlname . "," . $custfname;
                                //echo "balance:".$bal.":".$ps['balance'];
                                $runningbal = $runningbal + $ps["balance"];
                            } // end of cust loop

                            $total = $ps["total"];
                            $balance = $ps["balance"];
                            $total = asDollars($total);
                            //$bal = asDollars($bal);
                            ?>
                            <td style="width: 20%" class="style6"
                                valign="top"><?php echo date_format($dStart, 'm/d/Y'); ?></td>
                            <td style="width: 5%" class="style6" valign="top"><?php echo $days; ?></td>
                            <td style="width:30%" class="style7" valign="top"><?php echo $lf; ?></td>
                            <td style="width: 15%" class="style5" valign="top"><?php echo sbpround($total, 2); ?></td>

                            <td class="totalbox" onclick="" style="width: 15%"
                                text-align="right"><?php echo asDollars($balance); ?></td>
                        </tr>
                        <?php

                    } // end of while
                } // endof check for ps data
                }
                $runningbal = asDollars($runningbal);
                ?>
                </tbody>

            </table>

            <!-- End of the Parts sales -->

            <table style="width: 100%" cellspacing="0" cellpadding="3" align="center">
                <tr>
                    <td style="width: 5%"></td>
                    <td style="width: 20%"></td>
                    <td style="width: 5%"></td>
                    <td style="width: 45%"></td>

                    <td class="outstandingbox" style="text-align:left"><strong>Total
                            Outstanding: <?php echo sbpround($runningbal, 2); ?></strong></td>
                    <td style="width: 10%;text-align:right"></td>
                </tr>
            </table>


            <!--This is the new code for the load of the edit account page -->
            <div style="margin:10px 1px 1px 10px;" class="row">
                <div id="editacct-parent">
                    <!--div class="row">
                    <div id="editacctdiv" class="col-md-12 editacctheader depth" style="left: 0px; top: 0px;font-weight:bold;border-left:2px black solid;border-right:2px black solid"><span id="expandbutton" onclick="expand()" class="fa fa-arrows-alt expand-sbp" style="float:right"></span>EDIT ACCOUNT</div>
                </div-->
                    <div id="editacct" class="row"></div>
                </div>

            </div>

    </main>
    <!-- END Main Container -->
    <!-- Footer -->
    <!-- END Footer -->
</div>
<!-- END PS Page Container -->
<!-- Modals -->

</div>

<?php
mysqli_close($conn);
?>

<script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
<script src="<?= SCRIPT ?>/tipped.js"></script>

<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
<script src="<?= SCRIPT ?>/app.js"></script>
<script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
<script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
<script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
<script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

<!-- Page Plugins -->
<!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
<!-- Bootbox... need to check to see if I can use this -->
<script src="<?= SCRIPT; ?>/bootbox.min.js"></script>

<script>
    $(document).ready(function () {
        $('#material-text2').focus()

    });


    $('#material-text2').keyup(function () {
        $('#spinner').show()
        searchTable1($(this).val());
        searchTable2($(this).val());
    });


    function searchTable1(inputVal) {

        var table = $('#arlist-table>tbody');

        table.find('tr').each(function (index, row) {
            var allCells = $(row).find('td');
            if (allCells.length > 0) {
                var found = false;
                allCells.each(function (index, td) {
                    var regExp = new RegExp(inputVal, 'i');
                    if (regExp.test($(td).text())) {
                        found = true;
                        return false;
                    }
                });
                if (found == true) {
                    $(row).show();
                    $('#spinner').hide()
                } else {
                    $(row).hide();
                    $('#hide').toggle()
                }
            }
        });

        runttl = 0
        $('.totalbox').each(function () {
            tbval = $(this).html()
            tbval = tbval.replace("$", "")
            tbval = tbval.replace(",", "")
            if ($(this).closest('tr').css("display") != "none") {
                runttl += parseFloat(tbval)
                console.log(tbval + "|" + runttl)
            }
        });
        $('.outstandingbox').html("<b>Outstanding Balance: $" + runttl.toFixed(2) + "</b>")

    }

    function searchTable2(inputVal) {

        var table2 = $('#pslist-table>tbody');
        console.log(table2);

        table2.find('tr').each(function (index, row) {
            var allCells = $(row).find('td');
            console.log(allCells);
            if (allCells.length > 0) {
                var found = false;
                allCells.each(function (index, td) {
                    var regExp = new RegExp(inputVal, 'i');

                    if (regExp.test($(td).text())) {
                        found = true;
                        return false;
                    }
                });
                if (found == true) {
                    $(row).show();
                    $('#spinner').hide()
                    // put this in on 10/16/17 Need to test
                    //var tb = document.getElementById('table-container-ps');
                    //tb.getElementsByTagName("thead")[0].style.display = "block";

                } else {
                    $(row).hide();
                    $('#hide').toggle()
                }
            }
        });

    }

    function clearSearch() {
        $('#material-text2').val('')
        searchTable1('')
        searchTable2('')


    }


    function getsearchstring() {

        var searchstring = $("#n").val();
        var ds = 'n=' + searchstring;

        console.log(ds)

        $.ajax({
            type: "POST",
            url: "ar.php",
            data: ds,
            success: function (r) {
                console.log(r)
                bootbox.alert({
                    message: "Returning seachstring",
                    callback: function () {
                        location.reload()
                    }
                });
            }
        });

    }


    function editAccount(shopid, roid) {
        $('#editacct-parent').css


        eModal.iframe({
            title: 'Edit Account',
            url: 'editaccount.php?shopid=' + shopid + '&roid=' + roid,
            size: eModal.size.xl,
            buttons: [{
                text: 'Close',
                style: 'info',
                close: true
            },]

        });
    }

    function editpsAccount(shopid, psid) {
        $('#editacct-parent').css

        //console.log(psid)
        //console.log(shopid)
        eModal.iframe({
            title: 'Edit Part Sale',
            url: 'editpsaccount.php?shopid=' + shopid + '&psid=' + psid,
            size: eModal.size.lg,
            buttons: [{
                text: 'Close',
                style: 'info',
                close: true
            },]

        });
    }

    function expand() {
        if ($('#editacct-parent').hasClass('editacct-parent-expand')) {
            $('#editacct-parent').hide()
            $('#right-side-buttons').fadeIn();
            $('#header-customer').fadeIn()
            $('#editacct-parent').removeClass('editacct-parent-expand', 700)
            $('#editacct-parent').addClass('col-md-9', 700)
            $('#expandbutton').removeClass('fa-compress').addClass('fa-expand')
            $('#editacct-parent').fadeIn()
            //$('#hider').hide()
        } else {
            $('#editacct-parent').hide()
            $('#right-side-buttons').fadeOut();
            $('#header-customer').fadeOut()
            $('#editacct-parent').addClass('editacct-parent-expand', 500).removeClass('col-md-9', 500)
            $('#expandbutton').removeClass('fa-expand').addClass('fa-compress')
            $('#editacct-parent').fadeIn()
            //$('#hider').show()
        }
    }
</script>
<!-- Date range functions -->
<script>
    $(document).ready(function () {

        $('#sd').datetimepicker({
            format: 'MM/DD/YYYY'
        });
        $('#ed').datetimepicker({
            format: 'MM/DD/YYYY'
            //useCurrent: false
        });
        $("#sd").on("dp.change", function (e) {
            $('#ed').focus()
        });
    });

    function setdates(t) {
        <?php
        echo "\r\nvar lw = '" . date("m/d/Y", strtotime("last week monday")) . "|" . date("m/d/Y", strtotime("last week sunday")) . "';\r\n";
        echo "var lm = '" . date("m/d/Y", strtotime("first day of previous month")) . "|" . date("m/d/Y", strtotime("last day of previous month")) . "';\r\n";
        $ly = date("Y") - 1;
        echo "var ly = '" . date("m/d/Y", strtotime("01/01/" . $ly)) . "|" . date("m/d/Y", strtotime("12/31/" . $ly)) . "';\r\n";
        echo "var tw = '" . date("m/d/Y", strtotime("this week monday")) . "|" . date("m/d/Y", strtotime("this week sunday")) . "';\r\n";
        echo "var tm = '" . date("m/d/Y", strtotime("first day of this month")) . "|" . date("m/d/Y", strtotime("last day of this month")) . "';\r\n";
        $ty = date("Y");
        echo "var ty = '" . date("m/d/Y", strtotime("01/01/" . $ty)) . "|" . date("m/d/Y", strtotime("12/31/" . $ty)) . "';\r\n";
        ?>

        if (t == "This Week") {
            tar = tw.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "This Month") {
            tar = tm.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "This Year") {
            tar = ty.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "Last Week") {
            tar = lw.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "Last Month") {
            tar = lm.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "Last Year") {
            tar = ly.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
    }

    $('#sd').datetimepicker({
        ignoreReadonly: true,
        useCurrent: true,
        format: "MM/DD/Y"

    })

    $('#ed').datetimepicker({
        ignoreReadonly: true,
        useCurrent: true,
        format: "MM/DD/Y"

    })

</script>
<img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>

</html>
