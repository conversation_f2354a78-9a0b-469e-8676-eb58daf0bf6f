<?php
/*
4/2/18
Removed extranous dialog boxes for SWAL
refactored some query to prepare stmts
did some additional cleanup


3/8/16
Redid the sorting for high and low date
i.e Prev and Last
corrected expense name
<PERSON> redid the call for summing the expense amount to optimize the call I rolled it in.
go back 60 days prev and forward

Expense is ALL Expenses that have not been paid!!!
Tranasaction is ALL Expenses older than 20 days paid or not!!!
*/
require CONN;
require_once(COMPONENTS_PRIVATE_PATH."/accounting/functions.php");

$shopid = $_COOKIE['shopid'];

$today = new DateTime('now');
$today = date_format($today,'m/d/Y');

$today2 = new DateTime('now');
$today2 = date_format($today2,'Y-m-d');

$highdate = "";
$lowdate = "";
$searchby = "";

$date = new DateTime();

$dateminus20 = date('Y/m/d', strtotime("-20 day"));

$highdateminus60 = "";
$lowdateplus60 = "";
$ldplus60 = "";

if (isset($_POST['searchby'])) {
	$searchby = $_POST['searchby'];
}

if (isset($_POST['searchfor'])) {
	$searchfor = $_POST['searchfor'];
}

if (isset($_GET['highdate'])) {
	$date = $_GET['highdate'];
	
	$highdateminus60 = date('m/d/Y', strtotime($date . "-60 day"));
	$lowdateplus60 = date('m/d/Y', strtotime($date . "60 day"));
}else{
	$highdateminus60 = date('m/d/Y', strtotime("-60 day"));
}

if (isset($_GET['lowdate'])) {
	// if low date >= todays date populate todays date
				
		$date = $_GET['lowdate'];
		$lowdateplus60 = date('m/d/Y', strtotime($date));
		$ldplus60 = date('Y-m-d', strtotime($date));
		
		if ($ldplus60 >= $today2) {
			$lowdateplus60 = $today;
		}else{
			$lowdateplus60 = date('m/d/Y', strtotime($date . "60 day"));
		}
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<!DOCTYPE html>
<html>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!--> <html class="no-focus"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">
         <title><?= getPageTitle() ?></title>
        <meta name="robots" content="noindex, nofollow">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
		<link rel='shortcut icon' href='<?= IMAGE ?>/favicon.ico' type='image/x-icon'/ >
        <!-- Icons -->
        <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

        <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
        <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
        <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
        <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
        <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
        <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
        <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
        <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
        <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
        <!-- END Icons -->

        <!-- Stylesheets -->
        <!-- Web fonts -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

        <!-- Page JS Plugins CSS -->
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

        <!-- Bootstrap and OneUI CSS framework -->
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
        <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
        <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
        <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
		<link rel="stylesheet" type="text/css" href="<?= CSS ?>/rich_calendar.css"/>
		<script language="JavaScript" type="text/javascript" src="<?= SCRIPT ?>/rich_calendar.js"></script>
		<script language="JavaScript" type="text/javascript" src="<?= SCRIPT ?>/rc_lang_en.js"></script>
		<script language="javascript" type="text/javascript" src="<?= SCRIPT ?>/domready.js"></script>
		        
        <script type="text/javascript" src="<?= SCRIPT ?>/formvalidator.js"></script>
        <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
        <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
        <!-- END Stylesheets -->

        <!-- Stylesheet for Data tables took out the cdn.datatables.net version -->
        <link rel="stylesheet" type="text/css" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">

<style>
        .col-md-6{
			border: 1px black solid
		}
        .col-md-8{
			border: 1px black solid
		}
        .col-md-4{
			border: 1px black solid
		}
		.data{
			width:200px;
			font-size:14px;
			padding:5px;
			border:1px silver solid;
			border-radius:4px;
		}
#popuphider{
	position:absolute;
	top:0px;
	left:0px;
	width:100%;
	height:100%;
	background-color:gray;
	-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
	filter: alpha(opacity=70); 
	-moz-opacity:.70; 
	opacity: .7;
	z-index:997;
	display:none;

}
#popup{
	position:absolute;
	top:100px;
	left:30%;
	width:40%;
	overflow-y:auto;
	border:1px silver outset;
	text-align:center;
	color:black;
	display:none;
	z-index:999;
	background-color:white;
	padding:20px;
	border-radius:10px;
}
#addexp{
	position:absolute;
	top:100px;
	left:30%;
	width:40%;
	overflow-y:auto;
	border:1px silver solid;
	text-align:center;
	color:black;
	display:none;
	z-index:999;
	background-color:white;
	padding:20px;
	border-radius:10px;
}

.auto-style1 {
	text-align: center;
	color: #FFFFFF;
	font-size: large;
}

</style>
<script>
</script>
 </head>
    <body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    	<div id="mainalert" style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large" class="alert alert-success"></div>
    	<div id="header"></div>
        <!-- Page Container -->
        <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
        <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
            <!-- Sidebar -->
            <nav id="sidebar">
                <!-- Sidebar Scroll Container -->
                <div id="sidebar-scroll">
                    <!-- Sidebar Content -->
                    <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                    <div class="sidebar-content">
                        <!-- Side Header -->
                        <div class="side-header side-content bg-white-op">
                            <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                            <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                                <i class="fa fa-times"></i>
                            </button>
                            <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                            <i class="text-primary">
							<img alt="" src="<?= IMAGE ?>/shopboss-logo-nut.png" height="20" width="19"><img id="logomain" src="<?= IMAGE ?>/shopboss-logo-wh-nonut.png" height="18" width="170" ></i>
                            <span class="h4 font-w600 sidebar-mini-hide">
							</span>
                            </a>
                        </div>
                        <!-- END Side Header -->
                        <!-- Side Content -->
                            <div class="side-content-sbp-ro side-content">
                            <ul class="nav-main">
                                <li>
                                    <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Back to WIP</span></a>
                                </li>
                                <li>
                                    <a href="<?= COMPONENTS_PRIVATE ?>/accounting/default.php"><i class="fa fa-home"></i><span class="sidebar-mini-hide">Accounting Home</span></a>
                                </li>
                                <li>
                                    <a href="<?= COMPONENTS_PRIVATE ?>/accounting/expenses/expenses.php"><i class="fa fa-book"></i><span class="sidebar-mini-hide">Expenses Home</span></a>
                                </li>
								<?php if (!$shopIsReadOnly): ?>
									<li>
										<a href="#" onclick="newExp('<?php echo $shopid; ?>')"><i class="fa fa-book"></i><span class="sidebar-mini-hide">Add Bill/Expense</span></a>
									</li>
								<?php endif; ?>
                                <li>
                                    <a href="<?= COMPONENTS_PRIVATE ?>/accounting/expenses/listrecurring.php"><i class="fa fa-book"></i><span class="sidebar-mini-hide">List Recurring</span></a>
                                </li>
                                <li>
                                    <a href="<?= COMPONENTS_PRIVATE ?>/accounting/expenses/expenseexport.php"><i class="fa fa-book"></i><span class="sidebar-mini-hide">Export Expenses to Excel</span></a>
                                </li>
                            </ul>
                        </div>
                        <!-- END Side Content -->
                    </div>
                    <!-- Sidebar Content -->
                </div>
                <!-- END Sidebar Scroll Container -->
            </nav>
            <!-- END Sidebar -->

            <!-- Header -->
            <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar" class="content-mini content-mini-full">
            	
                <!-- Header Navigation Right -->
                List All Expenses
                <!-- END Header Navigation Right -->

                <!-- Header Navigation Left -->
                
                <ul class="nav-header pull-left">
                    <li class="hidden-md hidden-lg">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                            <i class="fa fa-navicon"></i>
                        </button>
                    </li>
                    <li class="hidden-xs hidden-sm">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                            <i class="fa fa-bars"></i>
                        </button>
                    </li>
                    <li>
                        <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                        <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                            <i class="si si-grid"></i>
                        </button>
                    </li>
                    <li class="visible-xs">
                        <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                        <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                            <i class="fa fa-search"></i>
                        </button>
                    </li>
					<li>
					</li>
                </ul>
                
                <!-- END Header Navigation Left -->
            </header>
            <!-- END Header -->
            <!-- Main Container -->

            <!-- Main Container -->
            <main class="container-fluid" id="main-container" style="display:block;">
   
<form action="listexpenses.php" method="post">
<table class="table table-condensed table-striped">
	<tr>
		<td class="style4">
			<span class="style10"><strong>Search for an Expense by
			<select id="searchby" class="data" name="searchby">
				<option value="amount">Amount</option>
				<option value="expensename">Name</option>
				<option value="paidto">Paid To</option>
				<option value="expensecategory">Category</option>
				<option value="datedue">Date Due</option>
				<option value="paiddate">Paid Date</option>
			</select>&nbsp; Search for: </strong>
			<input id="searchfor" name="searchfor" class="data" type="text" />&nbsp;
			<input class="btn btn-primary btn-sm" name="Button2" type="submit" value="Search" />
			<strong><span class="style5"><br />
			(enter date values as MM/DD/YYYY)</span></strong></span><br/>
		</td>
	</tr>
</table>
</form>
<table class="table table-condensed">
	<tr>
		<td class="style5" style="height: 29px;text-align:left;background-color:white;width:20%" colspan="3">
		<a style="text-decoration:none;color:#3366CC" href='listexpenses.php?highdate=<?php echo $highdateminus60; ?>'><strong>Previous
		<i class="fa fa-arrow-left" /></strong></a></td>
		<td class="style11" style="height: 29px;text-align:center;background-color:white;width:60%" colspan="3">Click any row 
		to edit the transaction</td>
		<td class="style5" style="height: 29px;text-align:right;background-color:white;width:20%" colspan="3">
		<a style="text-decoration:none;color:#3366CC" href='listexpenses.php?lowdate=<?php echo $lowdateplus60; ?>'><strong>Next
		<i class="fa fa-arrow-right"/></strong></a></td>
	</tr>
</table>
<table class="table table-condensed table-striped table-header-bg">
<?php

		$balamt = 0;

		if (isset($_GET['highdate'])) {
				$date = new DateTime($_GET['highdate']);
				$highdateminus60 = date_format($date,'Y-m-d'); 
		
				$lowdateplus60 = $highdateminus60;

				// adding in filter on sum amount to match detail
				$startsumdate = date_format($date,'Y-m-d');
				$endsumdate =  date('Y-m-d', strtotime($startsumdate . "60 day"));
	
				$stmt = "select id,shopid,expensename,expensecategory,amount,duedate,expensepaid,paidto,paiddate,paidwith,ref ";
				$stmt .= " from `expenses` ";
				$stmt .= " where shopid = ? ";
				$stmt .= "  and duedate >= ? ";
				$stmt .= "  and duedate <= date_add('$highdateminus60', interval 60 day) ";
				$stmt .= "  order by duedate asc ";
				
				if($query = $conn->prepare($stmt)){
					$query->bind_param("ss",$shopid,$highdateminus60 );
					$query->execute();
					$expresult = $query->get_result();
				}else{
					echo "Expense else Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}
				//printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$highdateminus60) . "</br>";
				
				$lowdateplus60 = $highdateminus60;
				
				if ($lowdateplus60 >= $today) {
					$lowdateplus60 = $today;
				}
				
		}elseif (isset($_GET['lowdate'])) {
		
				$balamt = 0;
		
				$date = new DateTime($_GET['lowdate']);
				$lowdateplus60 = date_format($date,'Y-m-d');

				// adding in filter on sum amount to match detail
				$startsumdate = date_format($date,'Y-m-d');
				$endsumdate =  date('Y-m-d', strtotime($startsumdate . "60 day"));

				$stmt = "select id,shopid,expensename,expensecategory,amount,duedate,expensepaid,paidto,paiddate,paidwith,ref ";
				$stmt .= " from `expenses` ";
				$stmt .= " where shopid = ? ";
				$stmt .= "  and duedate >= ? ";
				$stmt .= "  and duedate <= date_add('$lowdateplus60', interval 60 day) ";
				$stmt .= "  order by duedate asc ";
				
				if($query = $conn->prepare($stmt)){
					$query->bind_param("ss",$shopid,$lowdateplus60);
					$query->execute();
					$expresult = $query->get_result();
				}else{
					echo "Expense else Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}
		}elseif (strlen($searchby) > 0) {
				echo "<br>";
				
				$duedate = "";
				$stmt = "select id,shopid,expensename,expensecategory,amount,duedate,expensepaid,paidto,paiddate,paidwith,ref ";
				$stmt .= " from `expenses` ";
				$stmt .= " where shopid = ?";
				if (($searchby == 'datedue') || ($searchby == 'paiddate') ) {
					$searchfor = date('Y-m-d',strtotime($searchfor)); 
					//echo "Search for is : " . $searchfor;
					$stmt .= "  and duedate  = ? ";
					if($query = $conn->prepare($stmt)){
						$query->bind_param("ss",$shopid,$searchfor);
						$query->execute();
						$expresult = $query->get_result();
					}else{
						echo "Expense duedate Prepare failed: (" . $conn->errno . ") " . $conn->error;
					}
				}else{
					if ($searchby == 'paidto') { 
						$stmt .= "  and paidto  = ? ";
					}elseif ($searchby == 'expensename') {	
						$searchfor = "%" . $searchfor . "%";
						$stmt .= "  and expensename  like ? ";
					}elseif ($searchby == 'expensecategory') {	
						$searchfor = "%" . $searchfor . "%";
						$stmt .= "  and expensecategory  like ? ";
					}elseif ($searchby == 'amount') {	
						$stmt .= "  and amount = ? ";
					}else{
						$searchfor = 'nothing';
						$stmt .= "  and expensecategory  like ? ";
					}
					
					if($query = $conn->prepare($stmt)){
						$query->bind_param("ss",$shopid,$searchfor);
						$query->execute();
						$expresult = $query->get_result();
					}else{
						echo "Expense searchby Prepare failed: (" . $conn->errno . ") " . $conn->error;
					}
				}
				echo "<br>";
		}else  {
				//echo " in default date logic";

				$date = new DateTime($dateminus20);
				$dateminus20 = date_format($date,'Y-m-d');
				
				// adding in filter on sum amount to match detail
				$startsumdate = date_format($date,'Y-m-d');
				$endsumdate =$today2;

				// new version of listexpenses with prepare
				$stmt = "SELECT id,shopid,expensename,expensecategory,amount,duedate,expensepaid,paidto,paiddate,paidwith,ref ";
				$stmt .= " FROM `expenses` ";
				$stmt .= "WHERE shopid = ? ";
				$stmt .= "  AND duedate >= ? ";
				$stmt .= "  order by duedate asc ";
				//echo $stmt;	
				
				if($query = $conn->prepare($stmt)){
					$query->bind_param("ss",$shopid,$dateminus20);
					$query->execute();
					$expresult = $query->get_result();
				}else{
					echo "Expense else Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}
				//echo "else ";
				//printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$dateminus20);
				//echo "<br>";

				$lowdate = $dateminus20;
				$lowestdate = $dateminus20;
		}
		
		if ($expresult->num_rows > 0) {
			
			$rexp = mysqli_fetch_assoc($expresult);
			
			$lowdate = $rexp["duedate"]; 
			echo "<br>";
			$texp = 0;

			$stmt = "select id ";
			$stmt .= " from `expenses` ";
			$stmt .= " where shopid = ? ";
			$stmt .= "  and duedate <= ? ";
			//echo $stmt;	

			if($query = $conn->prepare($stmt)) {
				$query->bind_param("ss", $shopid,$lowdate);
				$query->execute();
				$texpresult = $query->get_result();
			
			}else{
				echo "Database texpenses prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			if ($texpresult->num_rows > 0) {
				$prevtrans = "no";
			}else{
				$prevtrans = "yes";
			}	
			
			// resetting the result set pointer to beginning	
			mysqli_data_seek($expresult, 0);	
?>		
		<thead>
		<tr>
		<td class="style4" style="height: 29px"><strong>Date Due</strong></td>
			<td class="style4" style="height: 29px" colspan="2"><strong>Date Paid</strong></td>
			<td class="style4" style="height: 29px"><strong>Paid To</strong></td>
			<td class="style4" style="height: 29px"><strong>Description</strong></td>
			<td class="style4" style="height: 29px"><strong>Account</strong></td>
			<td class="style4" style="height: 29px"><strong>Amount</strong></td>
			<td class="style4" style="height: 29px"><strong>Paid</strong></td>
			<td class="auto-style2" style="height: 29px"><strong>Balance</strong></td>
		</tr>
		</thead>
<?php		

			$cntr = 1;
			$balamt = 0;
			
			while($exp = mysqli_fetch_assoc($expresult)) {
			
			
				$showduedate = $exp["duedate"]; 
				$id = $exp["id"];
				//echo "Id is " . $id . "<br>";
				$balamt += $exp["amount"];

				/*			
				if ($cntr == 1) {
					$stmt = "SELECT sum(amount) as bal ";
					$stmt .= " FROM `expenses` ";
					$stmt .= "WHERE shopid = ? ";
					$stmt .= "  AND id <= ? ";
					$stmt .= "  AND duedate >= ? ";
					$stmt .= "  AND duedate <= ? ";
					
					
					if($query = $conn->prepare($stmt)){
						$query->bind_param("siss",$shopid,$id,$startsumdate,$endsumdate);
						$query->execute();
						$balresult = $query->get_result();
					}else{
						echo "Expense Bal Prepare failed: (" . $conn->errno . ") " . $conn->error;
					}
					printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$id,$startsumdate,$endsumdate);

					$bal = mysqli_fetch_assoc($balresult);
					$balamt += $bal["bal"];
			
				}else{
					$balamt += $exp["amount"];
				}
				
				$cntr  += 1;
				*/
				
				
				
				// put this in 7/20/17
				$duedate = new DateTime($exp["duedate"]);
				$paiddate = new DateTime($exp["paiddate"]);

				$paidDateFormatted = ($exp["paiddate"] === '0000-00-00') 
					? $today 
					: $paiddate->format('m/d/Y');

				$onclickExp = !$shopIsReadOnly
					? "onclick=\"editExp(
						'{$exp["id"]}',
						'" . $duedate->format('m/d/Y') . "',
						'{$paidDateFormatted}',
						'" . str_replace("'", "\\'", $exp["expensename"]) . "',
						'" . str_replace("'", "\\'", $exp["paidto"]) . "',
						'{$exp["amount"]}',
						'{$exp["expensecategory"]}',
						'{$exp["expensepaid"]}'
					)\""
					: "";
				
?>	
		<tr style="cursor:pointer" <?= $onclickExp; ?>>
		<td style="padding:4px;border-bottom:1px black solid">
		<?php 
			if ($exp["duedate"] == "0000-00-00") {
				$duedate = "";
				echo $duedate;
			}else{	
				$duedate = new DateTime($exp["duedate"]);
				echo $duedate->format('m/d/Y'); 
			}	
		?>
		&nbsp;</td>
		<td style="padding:4px;border-bottom:1px black solid" colspan="2" >
			<?php 
				if ($exp["paiddate"] == "0000-00-00") {
					$paiddate = "";
					echo $paiddate;
				}else{	
					$paiddate = new DateTime($exp["paiddate"]);
					echo $paiddate->format('m/d/Y'); 
				}	
			?>
			&nbsp;</td>
			
			<td style="padding:4px;border-bottom:1px black solid"><?php echo $exp["paidto"]; ?>&nbsp;</td>
			<td style="padding:4px;border-bottom:1px black solid"><?php echo $exp["expensename"]; ?>&nbsp;</td>
			<td style="padding:4px;border-bottom:1px black solid"><?php echo $exp["expensecategory"]; ?>&nbsp;</td>
			<td style="padding:4px;border-bottom:1px black solid" class="style5" ><?php echo asDollars($exp["amount"]) ; ?>&nbsp;</td>
			<td style="padding:4px;border-bottom:1px black solid" class="style5" ><?php echo $exp["expensepaid"]; ?></td>
			<td style="padding:4px;border-bottom:1px black solid" class="style5" ><?php echo asDollars($balamt); ?>	&nbsp;</td>
		</tr>
<?php
			}	// end of while
		}else{
?>		
		<tr>
			<td colspan="8">No Transactions Found&nbsp;</td>
		</tr>		
<?php
		} // end if
?>
</table>
		</main>

		<!-- Add Expense Modal -->
		<div id="addexpModal" class="modal fade" role="dialog">
		<input id="shopid" name="shopid"  value="<?php echo $shopid; ?>" type="hidden">
		
		<div class="modal-dialog">
		<!-- Modal content-->
		<div class="modal-content">
		<div class="block-header bg-primary-dark">
			<ul class="block-options">
				<li>
					<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
				</li>
			</ul>
			<h4 class="block-title">Add New Expense </h4>
		</div>
		<div class="modal-body">
		<table class="table table-condensed table-striped">
		<tr>
		<td class="text-left">Name/Description:</td>
		<td class="style9"><input class="form-control" id="expname" name="expname" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">Paid To:</td>
		<td class="style9"><input class="form-control" id="paidto" name="paidto" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">Amount:</td>
		<td class="style9"><input class="form-control" id="amount" name="amount" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">RO Number (if applicable):</td>
		<td class="style9"><input class="form-control" id="roid" name="roid" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">Date Due:</td>
		<td class="style9"><input class="form-control" id="duedate" name="duedate" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">Account:</td>
		<td class="style9">
		<select id="account" class="form-control" name="account" type="text" value="">
		<?php
			$stmt = "SELECT id,shopid,category,cattype,core ";
			$stmt .= " FROM chartofaccounts ";
			$stmt .= "WHERE shopid = ? ";
			$stmt .= "  AND cattype = 'Expense' ";
			$stmt .= " ORDER BY category ";
			
			if($query = $conn->prepare($stmt)){
				$query->bind_param("s",$shopid);
				$query->execute();
				$coaresult = $query->get_result();
			}else{
				echo "Chart of accounts Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}		
			
			if ($coaresult->num_rows > 0) {
				while($coa = $coaresult->fetch_array()) {
		?>					
					<option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
		<?php
				}	// end of while
			} // end if
		?>					
		</select>
		</td>
		</tr>
		<tr>
			<td>Recurring?</td>
			<td><select onchange="recurringPayment(this.value)" class="data" id="recurring" name="recurring">
			<option value="No">No</option>
			<option value="Yes">Yes</option>
			</select> </td>
		</tr>		
		<tr>
			<td "style:font:size 8px">If this is a recurring expense, select Yes and then select the day of month it is due</td>
		</tr>
		<tr style="display:none" id="dueday">
			<td style="width: 268px">Day of Month to pay</td>
			<td>
			<select style="font-size:18px" class="data" id="dayofmonth" name="dayofmonth">
<?php	
			$j =1;	
			while ($j >= 1 && $j <= 31) {
?>
				<option value="<?php echo $j; ?>"><?php echo $j; ?></option>
<?php	
				$j = $j + 1;
			}
?>
			</select> 
			</td>
			<td>Select day of month to pay</td>
		</tr>
		<tr>
			<td>Is this a bill to be paid later, or an 
			expense you have already paid?</td>
			<td><select onchange="showBankAcct(this.value)" class="data" id="type" name="type">
			<option value="Bill">Its a Bill to be Paid</option>
			<option value="Expense">Its an Expense That Has Been Paid</option>
			</select> </td>
			<td class="style4">If this is an expense, please select an account 
			to deduct the amount from.</td>
		</tr>
		<tr id="bankaccount" style="display:none">
			<td>Please select a Bank Account</td>
			<td>
			<?php
			$stmt = "SELECT id,bankname  ";
			$stmt .= "from bankaccount ";
			$stmt .= "where shopid = ? ";
			//echo $stmt;	
	
			if ($query= $conn->prepare($stmt)) {
				$query->bind_param("s",$shopid);
				$query->execute();
				$bankresult = $query->get_result();
			}else{
				"Database bank acct prepare failed: (" . $conn->errno . ")" . $conn->error;
			}
	
			if ($bankresult->num_rows > 0) {
?>
				<select class="data" id="accountid" name="accountid">
<?php
				while($bank = $bankresult->fetch_array()) {
?>
					<option value="<?php echo $bank["id"]; ?>"><?php echo $bank["bankname"]; ?></option>
<?php
				}
?>
				</select>
<?php
			}else{
				echo "You have no bank accounts added.  To automatically deduct this from a bank account, please add a new bank account first";
			}
?>
			</td>
			<td class="style4">&nbsp;</td>
		</tr>
		</table>
		</div>
		<div class="modal-footer">
		<button onclick="addexp()" type="button" class="btn btn-primary">Save Changes</button>
		<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
		</div>
		</div>
		</div>
		</div>
		
		<!-- Edit Transaction Modal -->
		<div id="edittransModal" class="modal fade" role="dialog">
		<input id="id" name="id"  value="" type="hidden">
		<input id="shopid2" name="shopid2"  value="<?php echo $shopid; ?>" type="hidden">
		
		<div class="modal-dialog">

		<!-- Modal content-->
		<div class="modal-content">
		<div class="block-header bg-primary-dark">
			<ul class="block-options">
				<li>
					<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
				</li>
			</ul>
			<h4 class="block-title">Edit Transaction </h4>
		</div>
		<div class="modal-body">
		
		<table class="table table-condensed table-striped">
		<tr>
		<td class="text-left">Date Due:</td>
		<td class="style9"><input class="form-control" id="duedate2" name="duedate2" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">Date Paid:</td>
		<td class="style9"><input class="form-control" id="datepaid2" name ="datepaid2 "type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">Paid To:</td>
		<td class="style9"><input class="form-control" id="paidto2" name="paidto2" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">Description:</td>
		<td class="style9"><input class="form-control" id="expname2" name="expname2" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">Payment Amount:</td>
		<td class="style9"><input class="form-control" id="amount2" name="amount2" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">Category:</td>
		<td class="style9">
		<select id="account2" class="form-control" name="account2" type="text" value="">
		<?php
			$stmt = "select id,shopid,category,cattype,core ";
			$stmt .= "from chartofaccounts ";
			$stmt .= "where shopid = ?";
			$stmt .= "  and cattype = 'Expense'";
			$stmt .= " order by category";			
			//echo $stmt;	

			if($query = $conn->prepare($stmt)) {
				$query->bind_param("s", $shopid);
				$query->execute();
				$coaresult = $query->get_result();
			}else{
				"Database chart of accounts query failed: (" . $conn->errno() . ") " . $conn->errno;			
			}
			
			if ($coaresult->num_rows > 0) {
				while($coa = $coaresult->fetch_array()) {
		?>					
					<option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
		<?php
				}	// end of while
			} // end if
		?>					
		</select>
		</td>
		</tr>

		<tr>
		
		<td class="text-left">Paid:</td>
		
		<td><select class="data" id="exppaid2" name="exppaid2">
			<option value="yes">yes</option>
			<option value="no">no</option>
			
		</select>
		</td>
				
		
		</td>		
		</tr>
		</table>
		</div>
		<div class="modal-footer">
		<button onclick="saveExp()" type="button" class="btn btn-primary">Save Changes</button>
		<button onclick="deleteExp('<?php echo $shopid; ?>')" type="button" class="btn btn-danger">Delete Transaction</button>
		<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
		</div>
		</div>
		</div>
		</div>
		
		<?php 
			mysqli_close($conn);
		?>

        <script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
        <script src="<?= SCRIPT ?>/tipped.js"></script>

        <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
        <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
        <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
        <script src="<?= SCRIPT ?>/app.js"></script>
        <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
        <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
        <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
        <script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
        <script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
        <script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
        <script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

        <!-- Data Tables  -->
		<script type="text/javascript" charset="utf8" src="//cdn.datatables.net/1.10.15/js/jquery.dataTables.js"></script>
		

        <!-- Page Plugins -->

        <!-- Page JS Code 
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
<script>
		
	$( "#duedate2" ).datetimepicker({ 
	    	format: 'MM/DD/YYYY'
	});
	
	$( "#duedate" ).datetimepicker({ 
	    	format: 'MM/DD/YYYY'
	});
	
	
	$( function() {
	    $( "#duedate2" ).datetimepicker({ 
	    	format: 'MM/DD/YYYY'
	    });
	    $( "#datepaid2" ).datetimepicker({ 
	    	format: 'MM/DD/YYYY'
	    });
	});          
	 
	function editExp(id,duedate,paiddate,expensename,paidto,amount,expensecategory,expensepaid){
	
		console.log("Expense paid is  " + expensepaid)
		console.log("Paid Date  is  " + paiddate)
			
		$('#edittransModal').modal('show')
		
		$('#id').val(id)
		$('#shopid').val(shopid)
		$('#duedate2').val(duedate)
		$('#datepaid2').val(paiddate)
		$('#paidto2').val(paidto)
		$('#amount2').val(amount)
		$('#expname2').val(expensename)
		$('#account2').val(expensecategory)
		$('#exppaid2').val(expensepaid)
	}

	function saveExp(){
			
		var id = $("#id").val();	
		var shopid = $("#shopid2").val();
		var name = $("#expname2").val();
		var amount = $("#amount2").val();
		var duedate = $("#duedate2").val();	
		var account = $("#account2").val();
		
		var expensepaid = $('#exppaid2').val();
		var paidto = $('#paidto2').val();
		var paiddate = $('#datepaid2').val();
				
		var ds = 'id='+ id + '&shopid='+ shopid +'&expensename='+ name  + '&amount=' + amount + '&duedate=' + duedate + '&expensecategory=' + account + '&expensepaid=' + expensepaid + '&paidto=' + paidto + '&paiddate=' + paiddate ;
		
			$.ajax({
				type: "post",
				url: "editexpense.php",
				data: ds,
				success: function(){
					swal({
						title: "Success",
						text: "Save Successful",
						type: "success",
						showCancelButton: false,
						confirmButtonClass: "btn-success",
						confirmButtonText: "OK",
						closeOnConfirm: false
					},
					function(){
						location.reload();
					});
				},
				error: function(xhr, ajaxOptions, thrownError) {
						swal("Error in Save")
				}
			});
	}     
	
	function cancelTrans(){

		$('#duedate').val("")
		$('lowdate').val("")
		$('highdate').val("")
		$('paidto').val("")
		$('amount').val("")
		$('expensecategory').val('Cost of Goods Sold')
		$('popup').hide()
		document.getElementById("popuphider").style.display = "none"
		document.trans.transid.value = ""

	}

	
	function newExp(shopid) {

		$('#addexpModal').modal('show')
	}

	
	function addexp(){
		var shopid 	= $("#shopid").val();	
		var name = $("#expname").val();	
		var paidto = $("#paidto").val();	
		var amount = $("#amount").val();	
		var roid = $("#roid").val();	
		var duedate = $("#duedate").val();	
		var account = $("#account").val();	
		var recurring = $("#recurring").val();	
		var dayofmonth = $("#dayofmonth").val();	
		var reqtype = $("#type").val();	
		var accountid = $("#accountid").val();	

		var noworlater = "now";	
				
		var ds = 'shopid='+ shopid +'&expensename='+ name + '&expensecategory=' + account + '&paidto=' + paidto + '&amount=' + amount + '&roid=' + roid + '&duedate=' + duedate + '&recurring=' + recurring + '&dayofmonth=' + dayofmonth + '&reqtype=' + reqtype + '&accountid=' + accountid ;
			
		console.log(ds)
			
			$.ajax({
				type: "post",
				url: "addexpense.php",
				data: ds,
				success: function(){
					swal({
						title: "Success",
						text: "Save Successful",
						type: "success",
						showCancelButton: false,
						confirmButtonClass: "btn-success",
						confirmButtonText: "OK",
						closeOnConfirm: false
					},
					function(){
						location.reload();
					});
	
				},
				error: function(xhr, ajaxOptions, thrownError) {
						swal("Error in Save")
				}
			});
	}

	function deleteExp(shopid){
			var id = $("#id").val();	
			
			swal({
				title: "Are you sure?",
				text: "This expense will be deleted.  Are you sure?",
				type: "warning",
				showCancelButton: true,
				confirmButtonClass: "btn-danger",
				confirmButtonText: "Yes, delete it",
				closeOnConfirm: false
			},
			function () {
					
				var ds = 'id=' + id  + '&shopid=' + shopid ;
				$.ajax({
					type: "post",
					url: "deleteexpense.php",
					data: ds,
					success: function(){
						swal({
							title: "Success",
							text: "Delete Successful",
							type: "success",
							showCancelButton: false,
							confirmButtonClass: "btn-success",
							confirmButtonText: "OK",
							closeOnConfirm: false
						},
						function(){
							location.reload();
						});
					},
					error: function(xhr, ajaxOptions, thrownError) {
							swal("Error in Deletion")
					}
				});
			});
		}
	function showBankAcct(v){
		if (v == "Expense"){
			document.getElementById("bankaccount").style.display = ""
			//document.mainform.dayofmonth.disabled = ""
		}
		if (v == 'Bill'){
			$('#bankaccount').hide()
			//document.mainform.dayofmonth.disabled = "disabled"
		}
	}

		</script>
        <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
    </body>
</html>
