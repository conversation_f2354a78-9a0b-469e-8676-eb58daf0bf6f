<?php

require_once CONN;
require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];

$today = new DateTime('now');
$today = date_format($today, 'm/d/Y');

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<!DOCTYPE html>
<html>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
	<meta charset="utf-8">
	 <title><?= getPageTitle() ?></title>
	<meta name="robots" content="noindex, nofollow">
	<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
	<link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon' />
	<!-- Icons -->
	<!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
	<link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

	<link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
	<link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
	<link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
	<link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
	<link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
	<link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
	<link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
	<link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
	<link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
	<!-- END Icons -->

	<!-- Stylesheets -->
	<!-- Web fonts -->
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

	<!-- Page JS Plugins CSS -->
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

	<!-- Bootstrap and OneUI CSS framework -->
	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
	<link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
	<link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
	<link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
	<!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
	<!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
	<!-- END Stylesheets -->
	<style>
		.col-md-6 {
			border: 1px black solid
		}

		.col-md-8 {
			border: 1px black solid
		}

		.col-md-4 {
			border: 1px black solid
		}
	</style>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
	<div id="mainalert" style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large" class="alert alert-success"></div>
	<div id="header"></div>
	<!-- Page Container -->
	<!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
	<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
		<!-- Sidebar -->
		<nav id="sidebar">
			<!-- Sidebar Scroll Container -->
			<div id="sidebar-scroll">
				<!-- Sidebar Content -->
				<!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
				<div class="sidebar-content">
					<!-- Side Header -->
					<div class="side-header side-content bg-white-op">
						<!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
						<button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
							<i class="fa fa-times"></i>
						</button>
						<a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
							<i class="text-primary">
								<?php getLogo() ?></i>
							<span class="h4 font-w600 sidebar-mini-hide">
							</span>
						</a>
					</div>
					<!-- END Side Header -->
					<!-- Side Content -->
					<div class="side-content-sbp-ro side-content">
						<ul class="nav-main">
							<li>
								<a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Back to WIP</span></a>
							</li>
							<li>
								<a href="<?= COMPONENTS_PRIVATE ?>/accounting/default.php"><i class="fa fa-home"></i><span class="sidebar-mini-hide">Accounting Home</span></a>
							</li>
							<li>
								<a href="<?= COMPONENTS_PRIVATE ?>/accounting/expenses/expenses.php"><i class="fa fa-book"></i><span class="sidebar-mini-hide">List Expenses</span></a>
							</li>
							<li>
								<a href="<?= COMPONENTS_PRIVATE ?>/accounting/expenses/listexpenses.php"><i class="fa fa-cog"></i><span class="sidebar-mini-hide">View All Expense</span></a>
							</li>
							<?php if (!$shopIsReadOnly): ?>
								<li>
									<a href="#" onclick="newExp('<?php echo $shopid; ?>')"><i class="fa fa-book"></i><span class="sidebar-mini-hide">Add Recurring</span></a>
								</li>
							<?php endif; ?>
						</ul>
					</div>
					<!-- END Side Content -->
				</div>
				<!-- Sidebar Content -->
			</div>
			<!-- END Sidebar Scroll Container -->
		</nav>
		<!-- END Sidebar -->

		<!-- Header -->
		<header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar" class="content-mini content-mini-full">

			<!-- Header Navigation Right -->
			Recurring Expenses
			<!-- END Header Navigation Right -->

			<!-- Header Navigation Left -->

			<ul class="nav-header pull-left">
				<li class="hidden-md hidden-lg">
					<!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
					<button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
						<i class="fa fa-navicon"></i>
					</button>
				</li>
				<li class="hidden-xs hidden-sm">
					<!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
					<button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
						<i class="fa fa-bars"></i>
					</button>
				</li>
				<li>
					<!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
					<button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
						<i class="si si-grid"></i>
					</button>
				</li>
				<li class="visible-xs">
					<!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
					<button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
						<i class="fa fa-search"></i>
					</button>
				</li>
				<li>
				</li>
			</ul>

			<!-- END Header Navigation Left -->
		</header>
		<!-- END Header -->
		<!-- Main Container -->

		<!-- Main Container -->
		<main class="container-fluid" id="main-container" style="display:block;">
			<br>
			<table class="table table-condensed table-striped table-header-bg">
				<thead>
					<tr>
						<td class="style8"><strong>Description</strong></td>
						<td class="style5"><strong>Amount</strong></td>
						<td class="style7"><strong>Category</strong></td>
						<td class="style7"><strong>Due Day</strong></td>
						<!-- edit/delete -->
					</tr>
				</thead>
				<?php

				$texp = 0;

				$stmt = "SELECT id,shopid,expensename,expensecategory,amount,dueday ";
				$stmt .= " FROM `expensesrecurring` ";
				$stmt .= "WHERE shopid = ? ";
				//echo $stmt;

				if ($query = $conn->prepare($stmt)) {
					$query->bind_param("s", $shopid);
					$query->execute();
					$expresult = $query->get_result();
				} else {
					echo "Expenses Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}

				if ($expresult->num_rows > 0) {
					while ($exp = $expresult->fetch_array()) {
						$texp = $texp + $exp["amount"];

						$onclickExp = !$shopIsReadOnly
							? "onclick=\"editExp(
								'{$exp["id"]}',
								'{$exp["shopid"]}',
								'" . str_replace("'", "\\'", $exp["expensename"]) . "',
								'{$exp["amount"]}',
								'{$exp["expensecategory"]}',
								'{$exp["dueday"]}'
							)\""
							: "";
				?>
				<tr style="cursor:pointer" <?= $onclickExp; ?>>
					<td><?php echo strtoupper($exp["expensename"]); ?>&nbsp;</td>
					<td><?php echo asDollars($exp["amount"]); ?>&nbsp;</td>
					<td>&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $exp["expensecategory"]; ?>&nbsp;</td>
					<td><?php echo $exp["dueday"]; ?>&nbsp;</td>
				</tr>
		<?php
					} // end of expense while
				} // end of if for expenses
		?>
				<tr>
					<td style=" font-weight:bold" class="style4">
							</td>
							<td style="font-weight:bold" class="style4">Total:&nbsp;&nbsp; <?php echo asDollars($texp); ?>&nbsp;</td>
							<td style="font-weight:bold" class="style4"></td>
							<td style="font-weight:bold" class="style4"></td>
							<td style="font-weight:bold" class="style4"></td>
						</tr>
			</table>

		</main>
		<!-- END Main Container -->

		<!-- Footer -->
		<!-- END Footer -->
	</div>
	<!-- END Page Container -->

	<!-- Modals -->

	<?php if (!$shopIsReadOnly): ?>
		<!-- Add Expense Modal -->
		<div id="addexpModal" class="modal fade" role="dialog">
			<input id="shopid" name="shopid" value="" type="hidden">
			<div class="modal-dialog">
				<!-- Modal content-->
				<div class="modal-content">
					<div class="block-header bg-primary-dark">
						<ul class="block-options">
							<li>
								<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
							</li>
						</ul>
						<h4 class="block-title">Add Recurring Expense </h4>
					</div>

					<div class="modal-body">
						<table class="table table-condensed table-striped">
							<tr>
								<td class="text-left">Name/Description:</td>
								<td class="style9"><input class="form-control" id="expname" name="expname" type="text" value="" /></td>
							</tr>
							<tr>
								<td class="text-left">Amount:</td>
								<td class="style9"><input class="form-control" id="amount" name="amount" type="text" value="" /></td>
							</tr>
							<tr>
								<td class="text-left">Account:</td>
								<td class="style9">
									<select id="account" class="form-control" name="account" type="text" value="">
										<?php
										$stmt = "select id,shopid,category,cattype,core ";
										$stmt .= "from chartofaccounts ";
										$stmt .= "where shopid = ?";
										$stmt .= "  and cattype = 'Expense'";
										$stmt .= " order by category";
										//echo $stmt;

										if ($query = $conn->prepare($stmt)) {
											$query->bind_param("s", $shopid);
											$query->execute();
											$coaresult = $query->get_result();
										} else {
											echo "Chart of Accounts prepare failed: (" . $conn->errno . ") " . $conn->error;
										}

										if ($coaresult->num_rows > 0) {
											while ($coa = $coaresult->fetch_array()) {
										?>
												<option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
										<?php
											}	// end of while
										} // end if

										?>
									</select>
								</td>
							</tr>

							<tr id="dueday">
								<td style="width: 268px">Day of Month to pay</td>
								<td>
									<select class="data" id="dayofmonth" name="dayofmonth">

										<?php
										$j = 1;
										while ($j >= 1 && $j <= 31) {
										?>
											<option value="<?php echo $j; ?>"><?php echo $j; ?></option>
										<?php
											$j = $j + 1;
										}
										?>
									</select>
								</td>
								<td>Select day of month to pay</td>
							</tr>

						</table>
					</div>
					<div class="modal-footer">
						<button onclick="addexp()" type="button" class="btn btn-primary">Save Changes</button>
						<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Edit Expense Modal -->
		<div id="expModal" class="modal fade" role="dialog">
			<input id="id" name="id" value="" type="hidden">
			<input id="shopid" name="shopid" value="" type="hidden">

			<div class="modal-dialog">

				<!-- Modal content-->
				<div class="modal-content">
					<div class="block-header bg-primary-dark">
						<ul class="block-options">
							<li>
								<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
							</li>
						</ul>
						<h4 class="block-title">Edit Recurring Expense </h4>
					</div>
					<div class="modal-body">
						<table class="table table-condensed table-striped">
							<tr>
								<td class="text-left">Name/Description:</td>
								<td class="style9"><input type="expname2" class="form-control" id="expname2" value=""></td>
							</tr>
							<tr>
								<td class="text-left">Amount:</td>
								<td class="style9"><input class="form-control" id="amount2" name="amount2" type="text" value="" /></td>
							</tr>
							<tr>
								<td class="text-left">Due Day:</td>
								<td class="style9"><input class="form-control" id="dueday2" name="dueday2" type="text" value="" /></td>
							</tr>
							<tr>
								<td class="text-left">Account:</td>
								<td class="style9">
									<select id="account2" class="form-control" name="account2" type="text" value="">
										<?php

										$stmt = "select id,shopid,category,cattype,core ";
										$stmt .= "from chartofaccounts ";
										$stmt .= "where shopid = ?";
										$stmt .= "  and cattype = 'Expense'";
										$stmt .= " order by category";
										//echo $stmt;

										if ($query = $conn->prepare($stmt)) {
											$query->bind_param("s", $shopid);
											$query->execute();
											$coaresult = $query->get_result();
										} else {
											echo "Chart of Accounts prepare failed: (" . $conn->errno . ") " . $conn->error;
										}

										if ($coaresult->num_rows > 0) {
											while ($coa = $coaresult->fetch_array()) {
										?>
												<option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
										<?php
											}	// end of while
										} // end if
										?>
									</select>
								</td>
							</tr>

						</table>
					</div>
					<div class="modal-footer">

						<button onclick="saveExp()" type="button" class="btn btn-primary">Save Changes</button>
						<button onclick="deleteExp()" type="button" class="btn btn-danger">Delete Expense</button>
						<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
					</div>
				</div>
			</div>
		</div>
	<?php endif; ?>

	<script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
	<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
	<script src="<?= SCRIPT ?>/tipped.js"></script>

	<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
	<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
	<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
	<script src="<?= SCRIPT ?>/app.js"></script>
	<script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
	<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
	<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
	<script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
	<script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
	<script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

	<!-- Page Plugins -->

	<!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
	<script>
		$("#duedate2").datetimepicker({
			format: 'MM/DD/YYYY'
		});

		$(document).ready(function() {
			$("#duedate").datetimepicker({
				format: 'MM/DD/YYYY'
			});

			$("#duedate2").datetimepicker({
				format: 'MM/DD/YYYY'
			});

			$("#datepaid").datetimepicker({
				format: 'MM/DD/YYYY'
			});

		});

		function deleteExp() {
			var id = $("#id").val();
			var shopid = $("#shopid").val();

			var ds = 'id=' + id + '&shopid=' + shopid;
			$.ajax({
				type: "post",
				url: "deleterecurring.php",
				data: ds,
				success: function() {
					swal({
							title: "Success",
							text: "Delete Successful",
							type: "success",
							showCancelButton: false,
							confirmButtonClass: "btn-success",
							confirmButtonText: "OK",
							closeOnConfirm: false
						},
						function() {
							location.reload();
						});
				},
				error: function(xhr, ajaxOptions, thrownError) {
					swal("Error in Deletion")
				}
			});
		}

		function getFormattedDate(date) {
			var year = date.getFullYear();
			var month = (1 + date.getMonth()).toString();
			month = month.length > 1 ? month : '0' + month;
			var day = date.getDate().toString();
			day = day.length > 1 ? day : '0' + day;
			return month + '/' + day + '/' + year;
		}

		function editExp(id, shopid, name, amount, category, dueday) {

			console.log(shopid)
			console.log(category)
			console.log(dueday)

			<?php
			if ($shopid == "demo") {
				echo "$('#expModal').modal('show')";
			} else {
				echo "$('#expModal').modal('show')";
			}
			?>

			$('#id').val(id)
			$('#shopid').val(shopid)
			$('#expname2').val(name)
			$('#amount2').val(amount)
			$('#account2').val(category)
			$('#dueday2').val(dueday)

		}

		function newExp(shopid) {

			$('#addexpModal').modal('show')

			$('#id').val(id)
			$('#shopid').val(shopid)
			var hoy = new Date(),
				mm = hoy.getMinutes(),
				d = hoy.getDate(),
				m = hoy.getMonth() + 1,
				y = hoy.getFullYear(),
				h = hoy.getHours(),
				data;

			if (d < 10) {
				d = "0" + d;
			};
			if (m < 10) {
				m = "0" + m;
			};

			d = m + "/" + d + "/" + y;

		}

		function saveExp() {

			var id = $("#id").val();
			var shopid = $("#shopid").val();
			var name = $("#expname2").val();
			var amount = $("#amount2").val();
			var dueday = $("#dueday2").val();
			var account = $("#account2").val();

			var ds = 'id=' + id + '&shopid=' + shopid + '&expensename=' + name + '&amount=' + amount + '&dueday=' + dueday + '&expensecategory=' + account;

			$.ajax({
				type: "post",
				url: "editrecurring.php",
				data: ds,
				success: function() {
					swal({
							title: "Success",
							text: "Save Successful",
							type: "success",
							showCancelButton: false,
							confirmButtonClass: "btn-success",
							confirmButtonText: "OK",
							closeOnConfirm: false
						},
						function() {
							location.reload();
						});
				},
				error: function(xhr, ajaxOptions, thrownError) {
					swal("Error in Save")
				}
			});
		}

		function addexp() {

			var shopid = $("#shopid").val();
			var name = $("#expname").val();
			var amount = $("#amount").val();
			var account = $("#account").val();
			var dayofmonth = $("#dayofmonth").val();

			var noworlater = "now";

			var ds = 'shopid=' + shopid + '&expensename=' + name + '&expensecategory=' + account + '&amount=' + amount + '&dayofmonth=' + dayofmonth;

			console.log(ds)

			$.ajax({
				type: "post",
				url: "addexpense.php",
				data: ds,
				success: function() {
					swal({
							title: "Success",
							text: "Save Successful",
							type: "success",
							showCancelButton: false,
							confirmButtonClass: "btn-success",
							confirmButtonText: "OK",
							closeOnConfirm: false
						},
						function() {
							location.reload();
						});

				},
				error: function(xhr, ajaxOptions, thrownError) {
					swal("Error in Save")
				}
			});
		}

		function postPymt() {

			swal({
					title: "Mark Paid",
					text: "",
					type: "warning",
					showCancelButton: true,
					confirmButtonClass: "btn-danger",
					confirmButtonText: "Save",
					closeOnConfirm: false
				},

				function() {

					var id = $("#id").val();
					var shopid = $("#shopid").val();
					var datepaid = $("#datepaid").val();

					console.log("Paid date is " + datepaid);

					var paidwith = $("#paidwith").val();
					var ref = $("#refnum").val();
					var accountid = $("#bankid").val();

					var expensename = $("#expname2").val();
					var amount = $("#amount2").val();
					var expensecat = $("#account2").val();

					var ds = 'id=' + id + '&shopid=' + shopid + '&datepaid=' + datepaid + '&paidwith=' + paidwith + '&ref=' + ref + '&accountid=' + accountid + '&expensename=' + expensename + '&amount=' + amount + '&expensecat=' + expensecat;
					console.log(ds);

					$.ajax({
						type: "post",
						url: "postpayment.php",
						data: ds,
						success: function() {
							swal({
									title: "Success",
									text: "Save Successful",
									type: "success",
									showCancelButton: false,
									confirmButtonClass: "btn-success",
									confirmButtonText: "OK",
									closeOnConfirm: false
								},
								function() {
									location.reload();
								});

						},
						error: function(xhr, ajaxOptions, thrownError) {
							swal("Error in Save")
						}
					});
				})
		}

		function recurringPayment(v) {
			if (v == "Yes") {
				document.getElementById("dueday").style.display = ""
			}
			if (v == 'No') {
				$('#dueday').hide()
			}
		}

		function showBankAcct(v) {
			if (v == "Expense") {
				document.getElementById("bankaccount").style.display = ""
			}
			if (v == 'Bill') {
				$('#bankaccount').hide()
			}
		}
	</script>
	<img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>
<?php
mysqli_close($conn);
?>

</html>
