<?php
require CONN;
require_once('functions.php');

$shopid = $_COOKIE['shopid'];
$today = new DateTime('now');

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$disabled = $shopIsReadOnly ? "disabled" : "";
?>

<!DOCTYPE html>
<html>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!--> <html class="no-focus"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">
        <title><?= getPageTitle() ?></title>
        <meta name="robots" content="noindex, nofollow">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
		<link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon'/ >
        <!-- Icons -->
        <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

        <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
        <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
        <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
        <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
        <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
        <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
        <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
        <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
        <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
        <!-- END Icons -->

        <!-- Stylesheets -->
    <!-- preload -->
    <!-- end preload -->
        <!-- Web fonts -->
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700&display=swap">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

        <!-- Page JS Plugins CSS -->
    <!--
    <link rel="preload" href="<?= SCRIPT; ?>/plugins/slick/slick.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript> <link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/slick/slick.min.css"></noscript>


    <link rel="preload" href="<?= SCRIPT; ?>/plugins/slick/slick-theme.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/slick/slick-theme.min.css"></noscript>
-->

    <link rel="preload" href="<?= SCRIPT; ?>/plugins/sweetalert/sweetalert.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript> <link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/sweetalert/sweetalert.min.css"></noscript>

    <link rel="preload" href="<?= CSS; ?>/oneui.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript> <link rel="stylesheet" id="css-main" href="<?= CSS; ?>/oneui.css"></noscript>

        <!-- Bootstrap and OneUI CSS framework -->
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <!--    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css"> -->

    <!--  <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css"> -->
    <!--    <link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css"> -->
        <link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/datatables/jquery.dataTables.css">
        <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
        <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
        <!-- END Stylesheets -->
    <!--   <link rel="STYLESHEET" type="text/css" href="<?= CSS ?>/rich_calendar.css"/> -->
<style>
.col-md-6{
	border: 1px black solid
}
.col-md-8{
	border: 1px black solid
}
.col-md-4{
	border: 1px black solid
}
.header {
	color:white;
	font-weight:bold;
	height:60px;
	text-align:center;
}

.data{
	width:250px;
	height:30px;
	font-size:14px;
	border:1px silver solid;
	border-radius:4px;
}

#right{
	width:40%;
	margin-left:60%;
	margin-top:-20px;
}
#left{
	width:100%;
}
.style3 {
	text-align: center;
	color: #FFFFFF;
}
.style4 {
	color: #FFFFFF;

}
.style6 {
	color: #FFFFFF;

	text-align: right;
}
.style7 {
	text-align: right;
}
.style8 {
	color: #FFFFFF;
	text-align: center;
}

#popup{
	position:absolute;
	top:100px;
	left:30%;
	width:400px;
	overflow-y:auto;
	border:medium navy outset;
	text-align:center;
	color:black;
	display:none;
	z-index:999;
	background-color:white;
	padding:20px;
	height:200px;
}
#popuphider{
	position:absolute;
	top:0px;
	left:0px;
	width:100%;
	height:100%;
	background-color:gray;
	-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
	filter: alpha(opacity=70);
	-moz-opacity:.70;
	opacity: .7;
	z-index:997;
	display:none;

}

.style9 {
	text-align: left;
}

.style10 {
	text-align: center;
}

.style11 {
	color: #000000;
}

.style12 {
	float: right;
}

</style>
    </head>
    <body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    	<div id="mainalert" style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large" class="alert alert-success"></div>
    	<div id="header"></div>
        <!-- Page Container -->
        <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
        <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">

            <!-- Sidebar -->
            <nav id="sidebar">
                <!-- Sidebar Scroll Container -->
                <div id="sidebar-scroll">
                    <!-- Sidebar Content -->
                    <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                    <div class="sidebar-content">
                        <!-- Side Header -->
                        <div class="side-header side-content bg-white-op">
                            <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                            <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                                <i class="fa fa-times"></i>
                            </button>
                            <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                            <i class="text-primary">
							<img src='<?php ECHO 'IMAGE /sblogo-white.svg';?>' width='190'></i>
                            <span class="h4 font-w600 sidebar-mini-hide">
							</span>
                            </a>
                        </div>
                        <!-- END Side Header -->

                        <!-- Side Content -->
                        <div class="side-content-sbp-ro side-content">
							<ul class="nav-main">
								<li>
									<a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Work In Process</span></a>
								</li>

								<li>
									<a href="default.php"><i class="fa fa-home"></i><span class="sidebar-mini-hide">Accounting Home</span></a>
								</li>

								<?php if (!$shopIsReadOnly): ?>
									<li>
										<a href="qbointegrator/default.php"><i class="fa fa-book"></i><span class="sidebar-mini-hide">Quickbooks</span></a>
									</li>
								<?php endif; ?>

								</li>
									<a href="company/default.php"><i class="fa fa-cog"></i><span class="sidebar-mini-hide">Preferences</span></a>
								</li>

								</li>
									<a href="customercenter/default.php"><i class="fa fa-user"></i><span class="sidebar-mini-hide">Customer Center</span></a>
								</li>

								</li>
									<a href="company/categories.php"><i class="fa fa-list"></i><span class="sidebar-mini-hide">Chart of Accounts</span></a>
								</li>

								</li>
									<a href="expenses/expenses.php"><i class="fa fa-money"></i><span class="sidebar-mini-hide">Expenses (A/P)</span></a>
								</li>

								</li>
									<a href="ar/ar.php"><i class="fa fa-usd"></i><span class="sidebar-mini-hide">Accounts Rec. (A/R)</span></a>
								</li>

								</li>
									<a href="undepositedfunds.php"><i class="fa fa-money"></i><span class="sidebar-mini-hide">Undeposited Funds</span></a>
								</li>

								</li>
									<a href="unpostedexpenses.php"><i class="fa fa-usd"></i><span class="sidebar-mini-hide">Unposted Expenses</span></a>
								</li>
								
								<?php if (!$shopIsReadOnly): ?>
									</li>
										<a href="addincome.php"><i class="fa fa-arrow-left"></i><span class="sidebar-mini-hide">Other Income</span></a>
									</li>
								<?php endif; ?>
								
								</li>
									<a href="bankaccount/accountlist.php"><i class="fa fa-university"></i><span class="sidebar-mini-hide">Bank/CC Accounts</span></a>
								</li>

								</li>
									<a href="reminders/reminders.php"><i class="fa fa-bell"></i><span class="sidebar-mini-hide">Acct. Reminders</span></a>
								</li>

								</li>
									<a href="reports/reports.php"><i class="fa fa-print"></i><span class="sidebar-mini-hide">View/Print Reports</span></a>
								</li>
							</ul>
                        </div>
                        <!-- END Side Content -->
                    </div>
                    <!-- Sidebar Content -->
                </div>
                <!-- END Sidebar Scroll Container -->
            </nav>
            <!-- END Sidebar -->

            <!-- Header -->
            <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar" class="content-mini content-mini-full">

                <!-- Header Navigation Right -->
                UnPosted Expenses
                <!-- END Header Navigation Right -->

                <!-- Header Navigation Left -->

                <ul class="nav-header pull-left">
                    <li class="hidden-md hidden-lg">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                            <i class="fa fa-navicon"></i>
                        </button>
                    </li>
                    <li class="hidden-xs hidden-sm">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                            <i class="fa fa-bars"></i>
                        </button>
                    </li>
                    <li>
                        <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                        <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                            <i class="si si-grid"></i>
                        </button>
                    </li>
                    <li class="visible-xs">
                        <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                        <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                            <i class="fa fa-search"></i>
                        </button>
                    </li>
					<li>

					</li>
                </ul>

                <!-- END Header Navigation Left -->
            </header>
            <!-- END Header -->
            <!-- Main Container -->

            <!-- Main Container -->
			
            <main class="container-fluid" id="main-container" style="display:block;">
            	<?php if (!$shopIsReadOnly): ?>
					<button class="btn btn-lg btn-warning" type="button" id="postexpbtn" style="float:right" onclick="postExpenses()">Post All Expenses</button>
					<input name="dfield" id="dfield" type="hidden" />
					<span class="style11">
						<br/>
						Please select all expenses you wish to post, select the expense category <br /> 
								and the appropriate bank to post the amounts to, then click Post
						Expenses.&nbsp; NOTE: Only 100 records are shown at a time<br />
					</span>
				<?php endif; ?>

	<form id="mainform" name="mainform" method="post" action="unpostedexpenses.php">
	<input name="sub" type="hidden" />
	<table class="table table-condensed table-striped table-header-bg">
		<thead>
		<tr>
			<?php if( !$shopIsReadOnly ): ?>
				<td class="style8"><input type="checkbox" onclick="checkAll()" id="master" checked></td>
			<?php endif; ?>
			<td class="style4"><strong>Date</strong></td>
			<td class="style4"><strong>Category</strong></td>
			<td class="style4"><strong>Paid To</strong></td>
			<td class="style4"><strong>How Paid</strong></td>
			<td class="style4"><strong>Ref #</strong></td>
			<td class="style4"><strong>Memo</strong></td>
			<td class="style4"><strong>RO #/PS #</strong></td>
			<td class="style6"><strong>Amount</strong></td>
			<td class="style8"><strong>Post To</strong></td>
			<?php if( !$shopIsReadOnly ): ?>
				<td class="style8"><strong>Delete</strong></td>
			<?php endif; ?>
		</tr>
		</thead>
<?php

		$stmt = "SELECT id,amount,`category`,memo,udate,roid,paidto ";
		$stmt .= "FROM unpostedexpenses ";
		$stmt .= "WHERE shopid = ? order by udate desc ";
		$stmt .= " LIMIT 100 ";
		//echo $stmt;

		if($query = $conn->prepare($stmt)){
			$query->bind_param("s",$shopid);
			$query->execute();
			$uperesult = $query->get_result();
		}else{
			echo "Undeposited Expenses Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		//echo "Number of rows " . $numofrows;
		$runningtotal = 0;
		$idlist = "";
		$showbtn = false;

		if ($uperesult->num_rows) {
			$showbtn = true;

			while($upe = mysqli_fetch_assoc($uperesult)) {
				$runningtotal = $runningtotal + $upe["amount"];
				$transdate = new DateTime($upe["udate"]);
?>
		<input type="hidden" name="transdate<?php echo $upe["id"]; ?>" value="<?php echo $upe["udate"]; ?>">
		<tr>
			<?php if( !$shopIsReadOnly ): ?>
				<td class="style10"><input value="on" checked="checked" name="post<?php echo $upe["id"]; ?>" type="checkbox" /></td>
			<?php endif; ?>
			<td id="transdate"><?php echo date_format($transdate,'m/d/Y'); ?>&nbsp;</td>
			<td id="category">
			<select class="data" name="category<?php echo $upe["id"]; ?>" <?= $disabled ?>>
<?php

			$stmt = "SELECT category ";
			$stmt .= "FROM chartofaccounts ";
			$stmt .= "WHERE shopid = ? ";
			$stmt .= "  AND cattype like 'Expense%'";
			//echo $stmt;

			if($query = $conn->prepare($stmt)){
				$query->bind_param("s",$shopid);
   				$query->execute();
    			$cofaresult = $query->get_result();
			}else{
				echo "Chart of Accts Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			$numofrows = mysqli_num_rows($cofaresult);

			if ($numofrows > 0) {
				while($cofa = mysqli_fetch_assoc($cofaresult)) {
					if ($cofa["category"] == $upe["category"]) {
						$cs = "selected='selected'";
					}else{
						$cs = "";
					}
?>
					<option <?php echo $cs; ?>value="<?php echo $cofa["category"]; ?>"><?php echo $cofa["category"]; ?></option>
<?php
				} //end of chart of accounts while
			} //end of chart of accounts if
?>
			</select>
			</td>
			<td>
			<input class="data" id="paidto" name='paidto<?php echo $upe["id"]; ?>' size="10" value="<?php echo $upe["paidto"]; ?>" type="text" style="width: 152px" <?= $disabled ?>/>&nbsp;
			</td>
			<td id="howpaid">
			<select class="data" name="howpaid<?php echo $upe["id"]; ?>" <?= $disabled ?>>
<?php
			$stmt = "SELECT method ";
			$stmt .= " FROM paymentmethods ";
			$stmt .= "WHERE shopid = ? ";
			//echo $stmt;

			if($query = $conn->prepare($stmt)){
				$query->bind_param("s",$shopid);
	   			$query->execute();
	    		$pmresult = $query->get_result();
			}else{
				echo "Payment Methods Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			if ($pmresult->num_rows > 0) {
				while($pm = mysqli_fetch_assoc($pmresult)) {
?>
					<option value="<?php echo $pm["method"]; ?>"><?php echo $pm["method"]; ?></option>
<?php
				} //end of payment methods while
			} //end of payment methods if
?>
			</select>
			</td>
			<td>
			<input class="data" name="ref<?php echo $upe["id"]; ?>" size="10" type="text" style="width: 55px" <?= $disabled ?>/>
			</td>

			<td><?php echo $upe["memo"]; ?>&nbsp;</td>
			<td class="style9"><?php echo $upe["roid"]; ?>&nbsp;</td>
			<td id="depositamount" class="style7"><?php echo asDollars($upe["amount"],2);?></td>
			<td class="style7">

<?php

			$stmt = "SELECT id, bankname ";
			$stmt .= "FROM bankaccount ";
			$stmt .= "WHERE shopid = ? ";
			$stmt .= "  AND acctstatus != 'Closed' order by isdefault desc";
			//echo $stmt;

			if($query = $conn->prepare($stmt)){
				$query->bind_param("s",$shopid);
   				$query->execute();
    			$bankresult = $query->get_result();
			}else{
				echo "Bank Account Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			if ($bankresult->num_rows > 0) {
?>
				<select class="data" name="bankaccount<?php echo $upe["id"]; ?>">

<?php
				while($bank = mysqli_fetch_assoc($bankresult)) {
?>
					<option value="<?php echo $bank["id"]; ?>"><?php echo $bank["bankname"]; ?></option>
<?php
				} //end of bank while

			}else {

				//echo "No bank accounts available to apply expenses. <a style='color:#336699' href='bankaccount/addbank.php'>Click Here to add</a>";
?>
			<a style="color:#336699" href="#" onclick="$('#bankmodal').modal('show')" data-target="#bankModal" data-toggle="modal">
			<strong>No bank accounts available to apply expenses. Click Here to Add&nbsp;</strong></a></td>
<?php
			} //end of bank if
?>
			</td>
			<?php if (!$shopIsReadOnly): ?>
				<td class="style10"><input onclick="deleteExpense('<?php echo $shopid; ?>','<?php echo $upe["id"]; ?>')" class="btn btn-danger btn-sm" name="Button1" type="button" value="Delete" />
				&nbsp;</td>
			<?php endif; ?>
		</tr>
<?php
				$idlist = $idlist . $upe["id"] . ",";
			} // end of upe whileloop
?>
		<tr>
			<?php if (!$shopIsReadOnly): ?>
				<td class="style8"></td>
			<?php endif; ?>

			<td class="style4"></td>
			<td class="style4"></td>
			<td class="style4"></td>
			<td class="style4"></td>
			<td class="style4"></td>
			<td class="style4"></td>
			<td class="style9"><strong>Total</strong></td>
			<td class="style7"><b><?php echo asDollars($runningtotal,2);?></b></td>

			<?php if (!$shopIsReadOnly): ?>
				<td class="style9">
					<input onclick="postExpenses()" class="btn btn-primary btn-sm" name="Button1" type="button" value="Post Selected Expenses">
				</td>
			
				<td class="style8">
					<input onclick="deleteallExpenses()" class="btn btn-danger btn-sm " name="Button2" type="button" style="float:right" value="Delete All Expenses">
				</td>
			<?php endif; ?>
		</tr>

<?php
		}else { // else of if records upe
?>
		<tr>
			<td colspan="6">No Unposted Expenses&nbsp;</td>
		</tr>
<?php
		} // end of UPE if
?>
	</table>
	<input name="idlist" value="<?php echo $idlist ;?>" type="hidden" />
	</form>
        </main>
        </div>
        <!-- END Page Container -->
        <!-- Modals -->

		<!-- Edit Expense Modal -->
		<div id="expModal" class="modal fade" role="dialog">
		<input id="id" name="id"  value="" type="hidden">
		<input id="shopid" name="shopid"  value="" type="hidden">
		<div class="modal-dialog">

		<!-- Modal content-->
		<div class="modal-content">
		<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal">&times;</button>
		<h4 class="modal-title">Edit Expense </h4>
		</div>
		<div class="modal-body">

		<table class="table table-condensed table-striped">
		<tr>
		<td class="text-left">Name/Description:</td>
		<td class="style9"><input class="form-control" id="expname" name="expname" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">Amount:</td>
		<td class="style9"><input class="form-control" id="amount" name="amount" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">RO Number (if applicable):</td>
		<td class="style9"><input class="form-control" id="roid" name="roid" type="text" value="" /></td>
		</tr>
		<tr>
		<td class="text-left">Date Due:</td>
		<td><input id="duedate" type="text" /></td>
		</tr>
		<tr>
		<td class="text-left">Account:</td>
		<td class="style9">
		<select id="account" class="form-control" name="account" type="text" value="">
		<?php
			$stmt = "SELECT id,shopid,category,cattype,core ";
			$stmt .= " FROM chartofaccounts ";
			$stmt .= "WHERE shopid = ? ";
			$stmt .= "  AND cattype = 'Expense' ";
			$stmt .= " ORDER BY category ";

			if($query = $conn->prepare($stmt)){
				$query->bind_param("s",$shopid);
				$query->execute();
				$coaresult = $query->get_result();
			}else{
				echo "Chart of accounts Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}

			if ($coaresult->num_rows > 0) {
				while($coa = $coaresult->fetch_array()) {
		?>
					<option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
		<?php
				}	// end of while
			} // end if
		?>
		</select>
		</td>
		</tr>

		</table>
		</div>
		<div class="modal-footer">
		<button onclick="saveExp()" type="button" class="btn btn-primary">Save Changes</button>
		<button onclick="deleteExp()" type="button" class="btn btn-danger">Delete Expense</button>
		<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
		</div>
		</div>
		</div>
		</div>


      	<div id="bankmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
		<input id="customerid" type="hidden">
		<div class="modal-dialog modal-md">
			<div class="modal-content">
				<div class="block block-themed block-transparent remove-margin-b">
					<div class="block-header bg-primary-dark">
						<ul class="block-options">
							<li>
								<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
							</li>
						</ul>
						<h3 class="block-title">Add New Bank Account</h3>
					</div>
					<div class="modal-body">
					<form>
					<tr>
						<td>
							<table class="table table-condensed table-striped">
								<tr>
									<td style="width: 268px">Bank Name</td>
									<td><input class="form-control" id ="bankname" name="bankname" type="text" /></td>
								</tr>
								<tr>
									<td style="width: 268px">Account Type</td>
									<td>
									<select class="form-control" id ="accounttype" name="accounttype" style="width: 200px">
									<option value="Checking">Checking</option>
									<option value="Saving">Saving</option>
									<option value="Credit Card">Credit Card</option>
									<option value="Money Market">Money Market</option>
									<option value="Certificate of Deposit">Certificate of Deposit
									</option>
									</select></td>
								</tr>
								<tr>
									<td style="width: 268px">Description</td>
									<td><input class="form-control" id="accountdescription" name="accountdescription" type="text" value=""/> </td>
								</tr>
								<tr>
									<td style="width: 268px">Account Number</td>
									<td><input class="form-control" id="accountnumber" name="accountnumber" type="text" value=""/></td>
								</tr>
								<tr>
									<td style="width: 268px">Routing Number</td>
									<td><input class="form-control" id="routingnumber" name="routingnumber" type="text" value=""/></td>
								</tr>
								<tr>
									<td style="width: 268px">Date Opened</td>
									<td><input class="form-control" id="opendate" name="opendate" type="text" value="<?php echo date_format($today,'m/d/Y'); ?>"/></td>
								</tr>
								<tr>
									<td style="width: 268px">Beginning Balance</td>
									<td>
									<input class="form-control" id="beginningbalance" name="beginningbalance" type="text" value=""/></td>
								</tr>
								<tr>
									<td class="style1" colspan="2">
									<button onclick="saveBankAccount('<?php echo $shopid; ?>')" type="button" class="btn btn-primary">Add Bank Account</button>
									<button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					</form>
				</div>
			<div style="margin-top:20px;" class="modal-footer">
				<!--button class="btn btn-primary btn-md" type="button" >Button</button>
				<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button-->
		</div>
		</div>
		</div>
		</div>

        <script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
        <script defer src="<?= SCRIPT ?>/tipped.js"></script>

        <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script defer src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script defer src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script defer src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script defer src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script defer src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script defer src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script defer src="<?= SCRIPT ?>/app.js"></script>
    <script defer src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    <script defer src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script defer src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script defer src="<?= SCRIPT ?>/emodal.js?v=6"></script>
    <script defer src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
    <!-- <script defer src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script> -->
    <script defer src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

        <!-- Page Plugins -->

        <!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
<script>
function checkAll(){

	if ($("#master").is(':checked')){
		$('input[type=checkbox]').each(function(){
			$(this).prop('checked', true);
		});
	}else{
		$('input[type=checkbox]').each(function(){
			$(this).prop('checked', false);
		});
	}
}


function postExpenses(){
	var s = $("#mainform").serialize();
	//console.log(s)
	var sub = "yes";

	$.ajax({
		type: "post",
		url: "<?= COMPONENTS_PRIVATE ?>/accounting/expenses/postexpenses.php",
		data: s+"&sub=" + sub + "&shopid=<?php echo $shopid; ?>" ,
		success: function(){
			location.reload();
		},
		error: function(xhr, ajaxOptions, thrownError) {
			swal("Error in Insert")
		}
	});
}

function deleteallExpenses(){
	var s = $("#mainform").serialize();
	//console.log(s)

	swal({
		title: "Are you sure?",
		text: "Are you sure you want to DELETE ALL EXPENSES?",
		type: "warning",
		showCancelButton: true,
		confirmButtonClass: "btn-danger",
		confirmButtonText: "Yes, delete it",
		closeOnConfirm: false
	},
	function () {
		var sub = "yes";

	$.ajax({
			type: "post",
			url: "<?= COMPONENTS_PRIVATE ?>/accounting/expenses/deleteallexpenses.php",
			data: s+"&sub=" + sub + "&shopid=<?php echo $shopid; ?>" ,
			success: function(){
				location.reload();
			},
			error: function(xhr, ajaxOptions, thrownError) {
				swal("Error in mass delete")
			}
		});
	});
}

function deleteExpense(shopid, id){
	swal({
		title: "Are you sure?",
		text: "Are you sure you want to delete this expense?",
		type: "warning",
		showCancelButton: true,
		confirmButtonClass: "btn-danger",
		confirmButtonText: "Yes, delete it",
		closeOnConfirm: false
	},
	function () {

		var ds = 'shopid=' + shopid + '&expenseid=' + id ;
		$.ajax({
			type: "post",
			url: "deleteexpense.php",
			data: ds,
			success: function(){
				location.reload();
			},
			error: function(xhr, ajaxOptions, thrownError) {
					swal("Error in Deletion")
			}
		});
	});
}

function editExp(id,shopid,name,amount,category,duedate,roid){
	$('#expModal').modal('show')

	$('#id').val(id)
	$('#shopid').val(shopid)
	$('#expname').val(name)
	$('#amount').val(amount)
	$('#roid').val(roid)
	$('#duedate').val(duedate)
	$('#account').val(category)
}

function saveBankAccount(shopid){
	// note the balance has to be saved to the account register
	var bankname = $("#bankname").val();
	var accttype = $("#accounttype").val();
	var acctdesc = $("#accountdescription").val();
	var acctnum = $("#accountnumber").val();
	var routenum = $("#routingnumber").val();
	var dateopen = $("#opendate").val();
	var begbal = $("#beginningbalance").val();

	var ds = 'bankname='+ bankname + '&accounttype=' + accttype + '&accountdescription=' + acctdesc + '&accountnumber=' + acctnum + '&routingnumber=' + routenum + '&opendate=' + dateopen + '&shopid=' + shopid + '&begbal=' + begbal;

	$.ajax({
		type: "post",
		url: "bankaccount/savebank.php",
		data: ds,
		success: function(){
			swal({
				title: "Success",
				text: "Bank Account Created",
				type: "success",
				showCancelButton: false,
				confirmButtonClass: "btn-success",
				confirmButtonText: "OK",
				closeOnConfirm: false
			},
			function(){
				location.reload();
			});
		},
		error: function(xhr, ajaxOptions, thrownError) {
				swal("Error in Insert")
		}
	});
}

<?php
if ($showbtn == false){
?>
$(document).ready(function(){
	$('#postexpbtn').hide()
});
<?php
}
?>
</script>
         <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
    </body>
<?php
    mysqli_close($conn);
?>
</html>

