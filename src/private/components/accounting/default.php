<!DOCTYPE html>
<html>
<?php

require_once CONN;

if (strtolower($_COOKIE['accounting']) == "no") {
    redirect_to(COMPONENTS_PRIVATE . "/wip/wip.php");
}
$shopid = $_COOKIE['shopid'];

$datepickerid = "datepicker";

$today = new DateTime('now');

$stmt = "select merchantaccount,contact from company where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->bind_result($merchantaccount,$contact);
    $query->fetch();
    $query->close();
}

$stmt = "select 360popup from settings where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($popup360);
    $query->fetch();
    $query->close();
}

if($_COOKIE['empid']!='Admin')
{
    $stmt = "select lower(jobdesc),concat(employeefirst,' ',employeelast) from employees where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ss",$shopid,$_COOKIE['empid']);
        $query->execute();
        $query->bind_result($jobdesc,$empname);
        $query->fetch();
        $query->close();
    }
}

$capitalarr = array();

if($popup360=='yes' && $merchantaccount!='cardknox' && !isset($_COOKIE['hidecapital']) && ($jobdesc=='owner' || strtoupper($contact)==strtoupper($empname) || $_COOKIE['empid']=='Admin'))
{
  if($merchantaccount!='360')
  $capitalarr = array('message' => "Get integrated payments with BOSS PAY", 'url' => 'https://360payments.com/partner-with-us/shop-boss/?tag=shopboss');
  else
  {
    $data = array("key" => "242dbb8a-6eab-46bf-ac9e-ec878d7aaa13", "merchant_id" => $shopid, "partner_id" => "a0Fi0000007vT9TEAU");
    $jsonEncodedData = json_encode($data);
    $curl = curl_init();
    $opts = array(
        CURLOPT_URL             => 'https://us-central1-capital-prod.cloudfunctions.net/x360capital/get-offers',
        CURLOPT_RETURNTRANSFER  => true,
        CURLOPT_CUSTOMREQUEST   => 'POST',
        CURLOPT_POST            => 1,
        CURLOPT_POSTFIELDS      => $jsonEncodedData,
        CURLOPT_HTTPHEADER  => array('Content-Type: application/json', 'Content-Length: ' . strlen($jsonEncodedData))
    );
    curl_setopt_array($curl, $opts);
    $result = curl_exec($curl);
    $json = json_decode($result);
    if (isset($json->signup_url) && !empty($json->signup_url) && !empty($json->message))
    $capitalarr = array('message' => $json->message, 'url' => $json->signup_url);
  } 
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>

<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">
     <title><?= getPageTitle() ?></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <!-- <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon'/ > -->
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE; ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE; ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE; ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE; ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE; ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE; ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE; ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE; ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE; ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE; ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE; ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE; ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE; ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE; ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700&display=swap">

    <link rel="preload" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">
    </noscript>
    <!-- Page JS Plugins CSS -->
    <!--    <link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/slick/slick.min.css"> -->
    <!--   <link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/slick/slick-theme.min.css"> -->

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS; ?>/oneui.min.css">
    <!--     <link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/datatables/jquery.dataTables.css"> -->
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <link rel="preload" href="<?= SCRIPT; ?>/plugins/sweetalert/sweetalert.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/sweetalert/sweetalert.min.css">
    </noscript>

    <link rel="preload" href="<?= CSS; ?>/tipped/tipped.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="<?= CSS; ?>/tipped/tipped.css">
    </noscript>

    <link rel="preload" href="<?= CSS; ?>/funkycheckboxes.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="<?= CSS; ?>/funkycheckboxes.css">
    </noscript>

    <link rel="preload" href="<?= SCRIPT; ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="<?= SCRIPT; ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    </noscript>


    <style>
        .col-md-6 {
            border: 1px black solid
        }

        .col-md-8 {
            border: 1px black solid
        }

        .col-md-4 {
            border: 1px black solid
        }

        .datepicker {
            z-index: 1151 !important;
        }

        .modal.modal-wide .modal-dialog {
            width: 30%;
        }

        .modal-wide .modal-body {
            overflow-y: auto;
        }
    </style>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    <div id="mainalert" style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large" class="alert alert-success"></div>
    <div id="header"></div>
    <!-- Page Container -->
    <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
    <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">

        <!-- Sidebar -->
        <nav id="sidebar">
            <!-- Sidebar Scroll Container -->
            <div id="sidebar-scroll">
                <!-- Sidebar Content -->
                <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                <div class="sidebar-content">
                    <!-- Side Header -->
                    <div class="side-header side-content bg-white-op">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                            <i class="fa fa-times"></i>
                        </button>
                        <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                            <i class="text-primary">
                                <?php getLogo() ?></i>
                            <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                        </a>
                    </div>
                    <!-- END Side Header -->

                    <!-- Side Content -->
                    <div class="side-content-sbp-ro side-content">
                        <h3 style="color:white; text-align:center">Accounting</h3>
                        <ul class="nav-main">
                            <li>
                                <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Work In Process</span></a>
                            </li>

                            <li>
                                <a href="default.php"><i class="fa fa-home"></i><span class="sidebar-mini-hide">Accounting Home</span></a>
                            </li>

                            <?php if (!$shopIsReadOnly): ?>
                                <li>
                                    <a href="qbointegrator/default.php"><i class="fa fa-book"></i><span class="sidebar-mini-hide">Quickbooks</span></a>
                                </li>
                            <?php endif; ?>

                            </li>
                                <a href="company/default.php"><i class="fa fa-cog"></i><span class="sidebar-mini-hide">Preferences</span></a>
                            </li>

                            </li>
                                <a href="customercenter/default.php"><i class="fa fa-user"></i><span class="sidebar-mini-hide">Customer Center</span></a>
                            </li>

                            </li>
                                <a href="company/categories.php"><i class="fa fa-list"></i><span class="sidebar-mini-hide">Chart of Accounts</span></a>
                            </li>

                            </li>
                                <a href="expenses/expenses.php"><i class="fa fa-money"></i><span class="sidebar-mini-hide">Expenses (A/P)</span></a>
                            </li>

                            </li>
                                <a href="ar/ar.php"><i class="fa fa-usd"></i><span class="sidebar-mini-hide">Accounts Rec. (A/R)</span></a>
                            </li>

                            </li>
                                <a href="undepositedfunds.php"><i class="fa fa-money"></i><span class="sidebar-mini-hide">Undeposited Funds</span></a>
                            </li>

                            </li>
                                <a href="unpostedexpenses.php"><i class="fa fa-usd"></i><span class="sidebar-mini-hide">Unposted Expenses</span></a>
                            </li>
                            
                            <?php if (!$shopIsReadOnly): ?>
                                </li>
                                    <a href="addincome.php"><i class="fa fa-arrow-left"></i><span class="sidebar-mini-hide">Other Income</span></a>
                                </li>
                            <?php endif; ?>
                            
                            </li>
                                <a href="bankaccount/accountlist.php"><i class="fa fa-university"></i><span class="sidebar-mini-hide">Bank/CC Accounts</span></a>
                            </li>

                            </li>
                                <a href="reminders/reminders.php"><i class="fa fa-bell"></i><span class="sidebar-mini-hide">Acct. Reminders</span></a>
                            </li>

                            </li>
                                <a href="reports/reports.php"><i class="fa fa-print"></i><span class="sidebar-mini-hide">View/Print Reports</span></a>
                            </li>
                        </ul>
                    </div>
                    <!-- END Side Content -->
                </div>
                <!-- Sidebar Content -->
            </div>
            <!-- END Sidebar Scroll Container -->
        </nav>
        <!-- END Sidebar -->

        <!-- Header -->
        <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar" class="content-mini content-mini-full">

            <?php if (!empty($capitalarr)) { ?><div class="alert alert-success text-center" style="font-size:16px;"><a href="#" title="This will hide the alert for next 7 days" class="close" onclick="resetTop()" data-dismiss="alert" aria-label="close">&times;</a><a href="<?= $capitalarr['url'] ?>" target='_blank'><?= $capitalarr['message'] ?></a></div><?php } ?>

            <!-- Header Navigation Right -->
            Accounting
            <!-- END Header Navigation Right -->

            <!-- Header Navigation Left -->


            <ul class="nav-header pull-left">
                <li class="hidden-md hidden-lg">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                        <i class="fa fa-navicon"></i>
                    </button>
                </li>
                <li class="hidden-xs hidden-sm">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                        <i class="fa fa-bars"></i>
                    </button>
                </li>
                <li>
                    <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                    <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                        <i class="si si-grid"></i>
                    </button>
                </li>
                <li class="visible-xs">
                    <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                    <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                        <i class="fa fa-search"></i>
                    </button>
                </li>
                <li>

                </li>
            </ul>

            <!-- END Header Navigation Left -->
        </header>
        <!-- END Header -->
        <!-- Main Container -->

        <!-- Main Container -->
        <main class="container-fluid" id="main-container" style="display:block;<?= !empty($capitalarr) ? "margin-top: 70px;" : '' ?>">
            <table class="table table-condensed">
                <tr>
                    <td id="accountsreceivable" style="width: 50%;text-align:left;" class="style2" valign="top"><strong>
                            <a style="color:#336699;text-decoration:none" href="ar/ar.php">
                                <img border="0" alt="" height="40" src="<?= IMAGE ?>/newimages/income.gif" width="40" align="top" />Current Accounts Receivable</a></strong>
                        <div style="max-height:400px;overflow-y:scroll">
                            <table class="table table-condensed table-striped table-header-bg">
                                <thead>
                                    <!-- first row within table -->
                                    <tr>
                                        <td width="10%" style="border-bottom:1px black solid;height:37px;" class="style17"><strong>&nbsp;RO </strong></td>
                                        <td style="border-bottom:1px black solid" class="style17"><strong>Date </strong></td>
                                        <td style="border-bottom:1px black solid" class="style17"><strong>Days</strong></td>
                                        <td style="border-bottom:1px black solid" class="style18"><strong>Name Home / Work / Cell</strong></td>
                                        <td style="border-bottom:1px black solid" class="style17"><strong>Total RO</strong></td>
                                        <td style="border-bottom:1px black solid" class="style17"><strong>Balance&nbsp;&nbsp; </strong></td>
                                    </tr>
                                </thead>

                                <!-- need to make prepare 5/21/19-->
                                <?php
                                $stmt = "SELECT totalprts,totallbr,totalsublet,userfee1,userfee2,userfee3,hazardouswaste,salestax,discountamt,rotype,totalfees";
                                $stmt .= ",roid,finaldate,lastfirst,customerphone,cellphone,customerwork,totalro,balance,shopid ";
                                $stmt .= "from repairorders ";
                                $stmt .= "WHERE shopid = ? ";
                                $stmt .= "  AND balance > 0.01 ";
                                $stmt .= "  AND ucase(status) = 'CLOSED' ";
                                $stmt .= "  AND (ucase(rotype) != 'NO PROBLEM' and ucase(rotype) != 'NO APPROVAL') ";
                                $stmt .= "order by balance desc";
                                //echo $query;

                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("s", $shopid);
                                    $query->execute();
                                    $result = $query->get_result();
                                } else {
                                    echo "Repair Orders Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                }

                                $bal = 0;
                                $c = 0;
                                $finaldate = "";
                                $days = "";
                                $lf = "";
                                $runningbal = 0;
                                $locationstring = "";
                                $statusdate = "";

                                // if accounts to be collected else send msg -->

                                if ($result->num_rows > 0) {
                                    while ($row = $result->fetch_array()) {
                                        $roid = $row["roid"];
                                        $shopid = $row["shopid"];
                                        $dstart = null;
                                        $lf = "";

                                        $stmt = "select sum(amt) as a  ";
                                        $stmt .= "from accountpayments ";
                                        $stmt .= "WHERE shopid = ? ";
                                        $stmt .= "  AND roid = ? ";
                                        //echo $stmt;

                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("si", $shopid, $roid);
                                            $query->execute();
                                            $apresult = $query->get_result();
                                        } else {
                                            echo "AccountPayments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }

                                        $ap = $apresult->fetch_array();

                                        $ap_amount = $ap['a'];

                                        if ($ap_amount > 0) {
                                            $ropayments = $ap_amount;
                                        } else {
                                            $ropayments = 0;
                                        }

                                        // moved ttlro calc to here

                                        $ttlro = $row["totalprts"] + $row["totallbr"] + $row["totalsublet"] + $row["hazardouswaste"] + $row["userfee1"] + $row["userfee2"] + $row["userfee3"] + $row["salestax"] - $row["discountamt"];

                                        $bal = $ttlro - $ropayments;
                                        $runningbal = $runningbal + $bal;
                                        //$bal = "$".number_format($bal); for some reason this rounded up to the dollar amt
                                        $displaybal = asdollars($bal);

                                        if ($bal > 0.01) {
                                            $editAccount = !$shopIsReadOnly
                                                ? "onclick=\"location.href='postpaymentcustomer.php?r={$r}&customerid={$row["customerid"]}'\""
                                                : "";
                                            ?>
                                            <!-- second row within table -->
                                            <tr class="sbphover-row">
                                                <td style="width: 5%; height: 39px;" class="style6" valign="top">
                                                    <input type="button" 
                                                        class="btn btn-primary btn-sm"
                                                        value="<?php echo $row["roid"]; ?>"
                                                        <?= $editAccount; ?>
                                                    >
                                                </td>
                                                <?php
                                                if ($row["finaldate"] == "0000-00-00" || $row["finaldate"] == null) {
                                                    $finaldate = "";
                                                    $days = "";
                                                } else {
                                                    $finaldate = $row["finaldate"];
                                                    $dStart = new DateTime($finaldate);
                                                    $dEnd  = new DateTime();
                                                    $diff = $dStart->diff($dEnd);
                                                    $days = $diff->format("%a");
                                                }
                                                if (!$row["customerphone"]) {
                                                    $customerphone = "NA";
                                                } else {
                                                    $customerphone = formatPhone(strtoupper($row["customerphone"]));
                                                }
                                                if (!$row["customerwork"]) {
                                                    $customerwork = "NA";
                                                } else {
                                                    $customerwork = formatPhone(strtoupper($row["customerwork"]));
                                                }
                                                if (!$row["cellphone"]) {
                                                    $cellphone = "NA";
                                                } else {
                                                    $cellphone = formatPhone(strtoupper($row["cellphone"]));
                                                }
                                                if (strlen($row["customerphone"]) > 0 || strlen($row["customerwork"]) > 0 || strlen($row["cellphone"]) > 0) {
                                                    $lf = $row["lastfirst"] . "<br />" . $customerphone . " / " . $customerwork . " / "
                                                        . $cellphone;
                                                } else {
                                                    $lf = $row["lastfirst"];
                                                }

                                                // moved up 7/25/17
                                                //$ttlro = $row["totalprts"]+$row["totallbr"]+$row["totalsublet"]+$row["hazardouswaste"]+$row["userfee1"]+$row["userfee2"]+$row["userfee3"]+$row["salestax"]-$row["discountamt"];

                                                if ($row["finaldate"] == "0000-00-00" || $row["finaldate"] == null) {
                                                    $finaldate = "";
                                                } else {
                                                    $finaldate = date_format($dStart, 'm/d/Y');
                                                }
                                                ?>
                                                <td style="width: 20%; height: 39px;" class="style6" valign="top"><?php echo $finaldate; ?>&nbsp;</td>
                                                <td style="width: 5%; height: 39px;" class="style6" valign="top"><?php echo $days; ?>&nbsp;</td>
                                                <td style="width: 30%; height: 39px;" class="style7" valign="top"><?php echo $lf; ?>&nbsp;</td>
                                                <?php

                                                $ttlro = asDollars($ttlro);
                                                ?>
                                                <td style="width: 15%; height: 39px;" class="style5" valign="top"><?php echo sbpround($ttlro, 2); ?>&nbsp;&nbsp;</td>
                                                <td style="width: 15%; height: 39px;" class="style5" valign="top"><?php echo sbpround($displaybal, 2); ?>&nbsp;&nbsp; </td>
                                            </tr>
                                    <?php
                                        } // this is the end of the check for > 0.01 bal

                                    } // end of while
                                } else {
                                    ?>
                                    <tr>
                                        <td colspan="6" class="style6" valign="top">No Accounts to be collected</td>
                                    </tr>
                                <?php
                                } // end of if checking for current accounts receivable
                                ?>
                                <!-- Beginning of Parts Sales -->
                                <?php
                                $stmt = "select * ";
                                $stmt .= "from ps ";
                                $stmt .= "WHERE shopid = ? ";
                                $stmt .= "  AND round(balance,2) > 0.01 ";
                                $stmt .= "  AND psdate > '2012-10-31' ";
                                $stmt .= "  AND ucase(status) = 'CLOSED' ";
                                //echo $stmt;

                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("s", $shopid);
                                    $query->execute();
                                    $psresult = $query->get_result();
                                } else {
                                    echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                }

                                $numofrows = $psresult->num_rows;

                                $finaldate = "";
                                $days = "";
                                $lf = "";
                                $bal = 0;
                                $runningbal = 0;
                                $lastname = "";
                                $firstname = "";
                                ?>
                                <tr>
                                    <td class="style12">&nbsp; <strong>Part Sale#</strong></td>
                                    <td class="style12"><strong>Date Sold</strong></td>
                                    <td class="style12"><strong>Days</strong></td>
                                    <td class="style12"><strong>Customer</strong></td>
                                    <td class="style12"><strong>Total Invoice</strong></td>
                                    <td class="style12"><strong>Balance</strong></td>
                                </tr>
                                <?php
                                while ($ps = $psresult->fetch_array()) {

                                    $stmt = "SELECT sum(amt) as a ";
                                    $stmt .= "from `accountpayments-ps` ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  AND psid = ?";
                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("si", $shopid, $ps["psid"]);
                                        $query->execute();
                                        $apresult = $query->get_result();
                                    } else {
                                        echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    $aprow = $apresult->fetch_array();

                                    $ap_amount = $aprow['a'];

                                    if ($ap_amount > 0) {
                                        $pspayments = $ap_amount;
                                    } else {
                                        $pspayments = 0;
                                    }

                                    $bal = $ps['total'] - $pspayments;

                                    if ($bal > 0.01) {

                                        $customerid = $ps["cid"];
                                        if ($ps["psdate"] == "0000-00-00") {
                                            $psdate = "";
                                            $days = "";
                                        } else {
                                            $psdate = $ps["psdate"];
                                            $dStart = new DateTime($psdate);
                                            $dEnd  = new DateTime();
                                            $diff = $dStart->diff($dEnd);
                                            $days = $diff->format("%a");
                                        }

                                        $stmt = "SELECT LastName, FirstName FROM customer WHERE shopid = ? and customerid = ?";

                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("si", $shopid, $customerid);
                                            $query->execute();
                                            $custresult = $query->get_result();
                                        } else {
                                            echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }


                                        while ($cust = $custresult->fetch_array()) {

                                            $custlname =  $cust["LastName"];
                                            $custfname =  $cust["FirstName"];
                                            $lf = $custlname . "," . $custfname;
                                        } // end of cust loop

                                        $bal =  $bal + $ps["balance"];
                                        $total = $ps["total"];
                                        $balance = $ps["balance"];
                                        $total = asDollars($total);
                                        //$bal = asDollars($bal);


                                        $total = $ps["total"];
                                        $balance = $ps["balance"];
                                        $total = asDollars($total);
                                        // changed code for Shop 4075 rick.shopbossdev.com
                                        //$bal = asDollars($bal);

                                        $editPSAccount = !$shopIsReadOnly
                                            ? "onclick=\"editpsAccount('{$ps['shopid']}','{$ps['psid']}')\""
                                            : "";

                                ?>
                                        <tr>
                                            <td style="width: 5%" class="style6"><input style="min-width:50px;" class="btn btn-sm btn-info" <input type="button" class="btn btn-primary btn-sm" <?= $editPSAccount; ?>>
                                            <td style="width: 20%" class="style6"><?php echo date_format($dStart, 'm/d/Y'); ?>&nbsp;</td>
                                            <td style="width: 5%" class="style6"><?php echo $days; ?>&nbsp;</td>
                                            <td style="width: 30%" class="style6"><?php echo $lf; ?>&nbsp;</td>
                                            <td style="width: 15%;" class="style5" valign="top"><?php echo sbpround($total, 2); ?>&nbsp;</td>
                                            <td style="width: 15%;" class="style5" valign="top"><?php echo sbpround($balance, 2); ?>&nbsp;</td>
                                        </tr>
                                <?php
                                    }
                                } // end of while
                                ?>
                            </table> <!-- end of ar table-->
                        </div> <!-- end of ar div-->
                    </td> <!-- end of ar td-->

                    <!-- Start of Bank Account-->

                    <td id="bankaccount" style="width: 50%;text-align:left;" class="style2" valign="top">
                        <strong>
                            <a style="color:#336699;text-decoration:none" href="../accounting/bankaccount/accountlist.php">
                                <img border="0" alt="" height="40" src="<?= IMAGE ?>/newimages/cbook.png" width="40" align="top" />Accounts and Balances</a></strong>
                        <table class="table table-condensed table-striped table-header-bg">
                            <thead>
                                <tr>
                                    <td class="style4" style="height: 34px"><strong>Bank Name</strong></td>
                                    <td class="style4" style="height: 34px"><strong>Account Description</strong></td>
                                    <td class="style4" style="height: 34px"><strong>Account Type</strong></td>
                                    <td class="style66" style="height: 34px"><strong>Balance</strong></td>
                                </tr>
                            </thead>
                            <?php

                            $stmt = "SELECT count(*) as c";
                            $stmt .= " FROM bankaccount WHERE shopid = ? ";

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $result = $query->get_result();
                            } else {
                                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            $row = $result->fetch_array();
                            $c = $row['c'];

                            $stmt = "SELECT sum(amount) as amt";
                            $stmt .= " FROM undepositedfunds WHERE shopid = ? ";

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $result = $query->get_result();
                            } else {
                                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            $udp = $result->fetch_array();

                            $numofrows = $result->num_rows;

                            if ($numofrows > 0) {
                                //if ($udpamount->num_rows > 0) {
                                if (!empty($udp["amt"])) {
                                    $udpamt = $udp["amt"];
                                    $udpamt = asDollars($udpamt);
                                } else {
                                    $udpamt = 0.00;
                                }
                            ?>
                                <tr>

                                    <td><a style="color:#336699" href="
            <?php
                                if ($c > 0) {
                                    echo "undepositedfunds.php";
                                } else {
                                    echo "javascript: swal('You must have a bank account to post Undeposited Funds')";
                                };
            ?>">

                                            <strong>Undeposited Funds&nbsp;</strong></a></td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td class="style5">
                                        <a style="color:#336699" href="undepositedfunds.php"><?php echo $udpamt; ?></a>&nbsp;
                                    </td>
                                </tr>

                            <?php
                            } // end of i amt of rows > 0

                            //$bankaccount = "no"; // commenting this out on 7/25/17 see notation this sets the unposted expenses to no if there no expenses

                            $stmt = "SELECT sum(amount) as amt";
                            $stmt .= " FROM unpostedexpenses WHERE shopid = ? ";

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $upeamount = $query->get_result();
                            } else {
                                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            $upe = $upeamount->fetch_array();

                            if ($upeamount->num_rows > 0) {
                                if (!empty($upe["amt"])) {
                                    $upeamt = $upe["amt"];
                                    $upeamt = asDollars($upeamt);
                                } else {
                                    $upeamt = 0.00;
                                }
                            ?>
                                <tr>

                                    <td><a style="color:#336699" href="
            <?php
                                if ($c > 0) {
                                    echo "unpostedexpenses.php";
                                } else {
                                    echo "javascript: swal('You must have a bank account to post Unposted Expenses')";
                                };
            ?>">
                                            <strong>Unposted Expenses&nbsp;</strong></a></td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td class="style5">
                                        <a style="color:#336699" href="unpostedexpenses.php"><?php echo $upeamt; ?></a>&nbsp;
                                    </td>
                                </tr>
                                <?php
                            } // end of upe amt of rows > 0

                            $stmt = "SELECT * ";
                            $stmt .= " FROM bankaccount WHERE shopid = ? ";
                            $stmt .= "  AND acctstatus != 'Closed'";

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $bankresult = $query->get_result();
                            } else {
                                echo "Bank Account Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            if ($bankresult->num_rows > 0) {

                                while ($bank = $bankresult->fetch_array()) {

                                    $id = $bank["id"];

                                    $stmt = "SELECT sum(depositamount) as d, ";
                                    $stmt .= "sum(paymentamount) as p  ";
                                    $stmt .= "from accountregister ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  AND accountid = ? ";
                                    //echo $stmt;

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("si", $shopid, $id);
                                        $query->execute();
                                        $acctregresult = $query->get_result();
                                    } else {
                                        echo "Acount Register Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    $acctreg = mysqli_fetch_assoc($acctregresult);

                                    $d = $acctreg["d"];
                                    $p = $acctreg["p"];
                                    $b = $d - $p;
                                    // check for numeric
                                    $b = asDollars($b);
                                ?>
                                    <tr>
                                        <td>
                                            <a style="color:#336699" href='../accounting/bankaccount/accountlist.php'>
                                                <strong><?php echo $bank["bankname"]; ?></strong></a>&nbsp;
                                        </td>
                                        <td><?php echo $bank["accountdescription"]; ?>&nbsp;</td>
                                        <td><?php echo $bank["accounttype"]; ?>&nbsp;</td>
                                        <td class="style5"><?php echo $b; ?>&nbsp;</td>
                                    </tr>
                                <?php
                                } // end of do while loop
                            } else if (!$shopIsReadOnly){ // else of if

                                ?>
                                <tr>
                                    <td colspan="4">
                                        <a style="color:#336699" href="#" onclick="$('#bankmodal').modal('show')" data-target="#bankModal" data-toggle="modal" class="btn btn-success btn-lg">
                                            <strong>No Bank Accounts Added. Click to Add&nbsp;</strong></a>
                                    </td>
                                </tr>
                            <?php
                            } // end of if
                            ?>
                        </table>
                    </td>
                </tr>

                <tr>
                    <td id="accountspayable" style="width: 50%;" class="style13" valign="top"><strong>
                            <a style="color:#336699;text-decoration:none" href="expenses/expenses.php">
                                <img border="0" alt="" height="40" src="<?= IMAGE ?>/newimages/expense.gif" width="45" align="top" />Current Accounts Payable</a></strong>
                        <table class="table table-condensed table-striped table-header-bg">
                            <thead>
                                <tr>
                                    <td class="style12" style="height: 33px"><strong>&nbsp;Payee&nbsp;</strong></td>
                                    <td class="style10" style="height: 33px"><strong>Amount</strong></td>
                                    <td class="style16" style="height: 33px"><strong>Due Date</strong></td>
                                </tr>
                            </thead>
                            <?php
                            $fontcolor = "";

                            // Cleaning up query from * to 7 cols 12/24/19

                            $stmt = "SELECT id,shopid,expensename,expensecategory,amount,duedate,roid ";
                            $stmt .= "from expenses ";
                            $stmt .= "WHERE shopid = ? ";
                            $stmt .= "  AND expensepaid = 'no'";
                            $stmt .= " order by duedate";
                            //echo $stmt;

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $expresult = $query->get_result();
                            } else {
                                echo "Expenses Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            if ($expresult->num_rows > 0) {
                                while ($exp = $expresult->fetch_array()) {

                                    $duedate = new DateTime($exp["duedate"]);
                                    $today = new Datetime();
                                    $expiredays = $today->diff($duedate);

                                    if ((int)$expiredays->format("%r%a") < 0) {
                                        $fontcolor = ";color:red;font-weight:bold;";
                                    } else {
                                        $fontcolor = "";
                                    }

                                    $onclickExp = !$shopIsReadOnly 
                                        ? "onclick=\"editExp(
                                            '{$exp["id"]}',
                                            '{$exp["shopid"]}',
                                            '" . str_replace("'", "\\'", $exp["expensename"]) . "',
                                            '{$exp["amount"]}',
                                            '{$exp["expensecategory"]}',
                                            '" . date_format($duedate, 'm/d/Y') . "',
                                            '{$exp["roid"]}'
                                        )\"" 
                                        : "";
                            ?>
                                <tr style="cursor:pointer" <?= $onclickExp; ?>>
                                    <td style=" height: 33px" class="style14">
                                        <strong><?php echo $exp["expensename"]; ?></strong>
                                    </td>

                                    <td style="height: 33px;<?php echo $fontcolor; ?>" class="style14">
                                        <?php echo asDollars($exp["amount"]); ?>
                                    </td>

                                    <td style="height:33px; <?= $fontcolor ?>" class="style15">
                                        <?= ($exp["duedate"] != '0000-00-00') 
                                            ? $duedate->format('m/d/Y') 
                                            : '' ?>
                                    </td>
                                </tr>
                                <?php
                                } //    rs.movenext

                            } else {
            ?>
            <tr>
                <td style="height: 33px">&nbsp;&nbsp;None Due</td>
                <td style="height: 33px" class="style14"></td>
                <td style="height: 33px" class="style15"></td>
            </tr>
        <?php
                            } //end if
        ?>
            </table>
            </td>
            <td id="reminders" style="width: 50%;" valign="top"><strong>
                    <a style="color:#336699;text-decoration:none" href="reminders/reminders.php">
                        <img border="0" alt="" height="30" style="margin:5px;" src="<?= IMAGE ?>/newimages/reminder.png" width="30" align="top" />Reminders</a></strong>
                <table class="table table-condensed table-striped table-header-bg">
                    <thead>
                        <tr>
                            <td class="style12" style="height: 33px; width: 75%;"><strong>&nbsp;Subject&nbsp;</strong></td>
                            <td class="style16" style="height: 33px; width: 25%;"><strong>Due Date</strong></td>
                        </tr>
                    </thead>
                    <?php
                    // Cleaning up query from * to 2 cols 12/24/19
                    $stmt = "SELECT remindersubject,reminderdate ";
                    $stmt .= "FROM accountingreminders ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND completed = 'no'";
                    $stmt .= " ORDER BY reminderdate";
                    //echo $stmt;

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("s", $shopid);
                        $query->execute();
                        $remresult = $query->get_result();
                    } else {
                        echo "Reminders Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    if ($remresult->num_rows > 0) {
                        while ($rem = $remresult->fetch_array()) {
                            $reminderdate = new DateTime($rem["reminderdate"]);

                            $remdays = $today->diff($reminderdate);

                            //echo "Reminder days " . (int)$remdays ->format("%r%a");

                            if ((int)$remdays->format("%r%a") < 0) {
                                $fontcolor = ";color:red;font-weight:bold;";
                            } else {
                                $fontcolor = "";
                            }

                    ?>
                            <tr onclick="location.href='reminders/reminders.php'">
                                <td style="height: 33px;<?php echo $fontcolor; ?> width: 75%;">&nbsp;&nbsp;<?php echo $rem["remindersubject"]; ?></td>
                                <td style="height:33px; <?= $fontcolor ?> width:25%;" class="style15">
                                    <?php
                                        if (!empty($rem['reminderdate']) && $rem['reminderdate'] != '0000-00-00') {
                                            $reminderdate = new DateTime($rem['reminderdate']);
                                            echo $reminderdate->format('m/d/Y');
                                        } else {
                                            echo '';
                                        }
                                    ?>
                                </td>
                            </tr>
                        <?php
                        } //rs.movenext

                    } else {
                        ?>
                        <tr>
                            <td style="height: 33px; width: 75%;">&nbsp;&nbsp;None</td>
                            <td style="height: 33px; width: 25%;" class="style15"></td>
                        </tr>
                    <?php
                    } //end if
                    ?>
                </table>
            </td>
            </tr>

            </tr>
            </table>
            <!-- <div id="newFeatureModal" class="modal modal-wide fade">
              <div class="modal-dialog">
                <div class="modal-content">
                    <div class="block-header bg-primary-dark">
                      <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title">NEW in Accounting</h4>
                      </div>
                      <div class="modal-body">
                        <p>Accounting Enhancements&hellip;</p>
                        <ul>
                            Balance now shows as Running Balance (1/1/2020)
                        </ul>
                        <ul>
                            Sort Order has changed to Post Date descending vs Trans Date desc (1/1/2020)
                        </ul>
                        <ul>
                            New Button called Recalc Running Balance (recalculates Running Balance) (1/1/2020)
                        </ul>
                        <ul>
                            Add/Edit transaction now have current Running Balance populated (1/1/2020)
                        </ul>
                        <ul>
                            <a href="https://shopboss.happyfox.com/kb/article/102-accounting-running-balance" target="_blank">Get more info...</a>
                        </ul>
                      </div>
                      <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        <button onclick="dismissNotification()" type="button" class="btn btn-primary">Dismiss this Notification</button>
                      </div>
                    </div><!-- /block header -->
    </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
    </div><!-- /.modal --> -->

    </main>
    <!-- END Main Container -->

    <!-- Footer -->
    <!-- END Footer -->
    </div>
    <!-- END Page Container -->
    <!-- Modals -->

    <!-- Edit Expense Modal -->
    <div id="expModal" class="modal fade" role="dialog">
        <input id="id" name="id" value="" type="hidden">
        <input id="shopid" name="shopid" value="" type="hidden">

        <div class="modal-dialog">

            <!-- Modal content-->
            <div class="modal-content">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h4 class="block-title">Edit Expense </h4>
                </div>
                <div class="modal-body">

                    <table class="table table-condensed table-striped">
                        <tr>
                            <td class="text-left">Name/Description:</td>
                            <td class="style9"><input class="form-control" id="expname" name="expname" type="text" value="" /></td>
                        </tr>
                        <tr>
                            <td class="text-left">Amount:</td>
                            <td class="style9"><input class="form-control" id="amount" name="amount" type="text" value="" /></td>
                        </tr>
                        <tr>
                            <td class="text-left">RO Number (if applicable):</td>
                            <td class="style9"><input class="form-control" id="roid" name="roid" type="text" value="" /></td>
                        </tr>
                        <tr>
                            <td class="text-left">Date Due:</td>
                            <td><input id="duedate" type="text" /></td>


                        </tr>
                        <tr>
                            <td class="text-left">Account:</td>
                            <td class="style9">
                                <select id="account" class="form-control" name="account" type="text" value="">
                                    <?php
                                    /*  Replacing with prepare

            $query = "select id,shopid,category,cattype,core ";
            $query .= "from chartofaccounts ";
            $query .= "where shopid = '{$shopid}'";
            $query .= "  and cattype = 'Expense'";
            $query .= " order by category";
            echo $query;

            $coaresult = mysqli_query($conn, $query);

            if (!$coaresult) {
                die("Database chart of accounts query failed.");
            }

            */
                                    $stmt = "SELECT id,shopid,category,cattype,core ";
                                    $stmt .= "FROM chartofaccounts ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  AND cattype = 'Expense'";
                                    $stmt .= " ORDER BY category";

                                    //echo $stmt;

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("s", $shopid);
                                        $query->execute();
                                        $coaresult = $query->get_result();
                                    } else {
                                        echo "Chart of Accts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }


                                    if ($coaresult->num_rows > 0) {
                                        while ($coa = $coaresult->fetch_array()) {

                                    ?>
                                            <option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
                                    <?php
                                        }   // end of while

                                    } // end if

                                    ?>
                                </select>
                            </td>
                        </tr>

                    </table>
                </div>
                <div class="modal-footer">
                    <button onclick="saveExp()" type="button" class="btn btn-primary">Save Changes</button>
                    <button onclick="deleteExp()" type="button" class="btn btn-danger">Delete</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <div id="bankmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
        <input id="customerid" type="hidden">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="block block-themed block-transparent remove-margin-b">
                    <div class="block-header bg-primary-dark">
                        <ul class="block-options">
                            <li>
                                <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                            </li>
                        </ul>
                        <h3 class="block-title">Add New Bank Account</h3>
                    </div>
                    <div class="modal-body">
                        <form>
                            <tr>
                                <td>
                                    <table class="table table-condensed table-striped">
                                        <tr>
                                            <td style="width: 268px">Bank Name</td>
                                            <td><input class="form-control" id="bankname" name="bankname" type="text" /></td>
                                        </tr>
                                        <tr>
                                            <td style="width: 268px">Account Type</td>
                                            <td>
                                                <select class="form-control" id="accounttype" name="accounttype" style="width: 200px">
                                                    <option value="Checking">Checking</option>
                                                    <option value="Saving">Saving</option>
                                                    <option value="Credit Card">Credit Card</option>
                                                    <option value="Money Market">Money Market</option>
                                                    <option value="Certificate of Deposit">Certificate of Deposit
                                                    </option>
                                                </select>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 268px">Description</td>
                                            <td><input class="form-control" id="accountdescription" name="accountdescription" type="text" value="" /> </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 268px">Account Number</td>
                                            <td><input class="form-control" id="accountnumber" name="accountnumber" type="text" value="" /></td>
                                        </tr>
                                        <tr>
                                            <td style="width: 268px">Routing Number</td>
                                            <td><input class="form-control" id="routingnumber" name="routingnumber" type="text" value="" /></td>
                                        </tr>
                                        <tr>
                                            <td style="width: 268px">Date Opened</td>
                                            <td><input class="form-control" id="opendate" name="opendate" type="text" value="<?php echo date_format($today, 'm/d/Y'); ?>" /></td>
                                        </tr>
                                        <tr>
                                            <td style="width: 268px">Beginning Balance</td>
                                            <td>
                                                <input class="form-control" id="beginningbalance" name="beginningbalance" type="text" value="" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style1" colspan="2">
                                                <button onclick="saveBankAccount('<?php echo $shopid; ?>')" type="button" class="btn btn-primary">Add Bank Account</button>
                                                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </form>
                    </div>
                    <div style="margin-top:20px;" class="modal-footer">
                        <!--button class="btn btn-primary btn-md" type="button" >Button</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button-->
                    </div>
                </div>
            </div>
        </div>


        <script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
        <script defer src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
        <script defer src="<?= SCRIPT ?>/tipped.js"></script>

        <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
        <script defer src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
        <script defer src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
        <script defer src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
        <script defer src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
        <script defer src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
        <script defer src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
        <script defer src="<?= SCRIPT ?>/app.js"></script>
        <script defer src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
        <script defer src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
        <script defer src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
        <script defer src="<?= SCRIPT ?>/emodal.js"></script>
        <script defer src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
        <script defer src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
        <!--        <script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script> -->
        <script defer src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

        <!-- Page Plugins -->

        <!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
        <?php
        $category = (isset($category)) ? $category : '';
        $dismiss = (isset($dismiss)) ? $dismiss : '';
        ?>
        <script type="text/javascript" async="false">
            $(document).ready(function() {
                // display if audit record is not dismissed
                $("#duedate").datetimepicker({
                    format: 'MM/DD/YYYY'
                });
                <?php if (($category == 'Acct Enhancements' && $dismiss == 'no') || empty($category)) { ?>
                    $('#newFeatureModal').modal('show');
                <?php } ?>
            });


            function dismissNotification() {
                dismiss = 'yes'
                var ds = 'shopid=' + shopid;

                $.ajax({
                    type: "post",
                    url: "savenotification.php",
                    data: ds,
                    success: function() {
                        $('#newFeatureModal').modal('hide');
                        //location.reload();
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        swal("Error in Save")
                    }
                });
            }


            function editAccount(shopid, roid) {
                $('#editacct-parent').css

                //console.log(roid)
                //console.log(shopid)
                eModal.iframe({
                    title: 'Edit Account',
                    url: 'ar/editaccount.php?shopid=' + shopid + '&roid=' + roid,
                    size: eModal.size.lg,
                    buttons: [{
                        text: 'Close',
                        style: 'info',
                        close: true
                    }, ]

                });
            }

            function editpsAccount(shopid, psid) {
                $('#editacct-parent').css

                eModal.iframe({
                    title: 'Edit Part Sale',
                    url: 'ar/editpsaccount.php?shopid=' + shopid + '&psid=' + psid,
                    size: eModal.size.lg,
                    buttons: [{
                        text: 'Close',
                        style: 'info',
                        close: true
                    }, ]

                });
            }

            function saveExp() {

                var id = $("#id").val();
                var shopid = $("#shopid").val();
                var name = $("#expname").val();
                var amount = $("#amount").val();
                var roid = $("#roid").val();
                var duedate = $("#duedate").val();
                var account = $("#account").val();


                var ds = 'id=' + id + '&shopid=' + shopid + '&expensename=' + name + '&amount=' + amount + '&roid=' + roid + '&duedate=' + duedate + '&expensecategory=' + account;

                $.ajax({
                    type: "post",
                    url: "../accounting/expenses/editexpense.php",
                    data: ds,
                    success: function() {
                        location.reload();
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        swal("Error in Save")
                    }
                });


            }

            function deleteExp() {
                var id = $("#id").val();
                var shopid = $("#shopid").val();

                swal({
                        title: "Are you sure?",
                        text: "This expense will be deleted.  Are you sure?",
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonClass: "btn-danger",
                        confirmButtonText: "Yes, delete it",
                        closeOnConfirm: false
                    },
                    function() {

                        var ds = 'id=' + id + '&shopid=' + shopid;
                        $.ajax({
                            type: "post",
                            url: "../accounting/expenses/deleteexpense.php",
                            data: ds,
                            success: function() {
                                location.reload();
                            },
                            error: function(xhr, ajaxOptions, thrownError) {
                                swal("Error in Deletion")
                            }
                        });
                    });
            }

            function editExp(id, shopid, name, amount, category, duedate, roid) {

                $('#expModal').modal('show')

                $("#id").val(id)
                $("#shopid").val(shopid)
                $("#expname").val(name)
                $("#amount").val(amount)
                $("#roid").val(roid)
                $("#duedate").val(duedate)
                $("#account").val(category)

            }

            function saveBankAccount(shopid) {
                // note the balance has to be saved to the account register

                var bankname = $("#bankname").val();
                var accttype = $("#accounttype").val();
                var acctdesc = $("#accountdescription").val();
                var acctnum = $("#accountnumber").val();
                var routenum = $("#routingnumber").val();
                var dateopen = $("#opendate").val();
                var begbal = $("#beginningbalance").val();

                console.log("AccountNumber is " + acctnum)

                var ds = 'bankname=' + bankname + '&accounttype=' + accttype + '&accountdescription=' + acctdesc + '&accountnumber=' + acctnum + '&routingnumber=' + routenum + '&opendate=' + dateopen + '&shopid=' + shopid + '&begbal=' + begbal;

                $.ajax({
                    type: "post",
                    url: "bankaccount/savebank.php",
                    data: ds,
                    success: function() {
                        location.reload();
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        swal("Error in Insert")
                    }
                });

            }

            function resetTop() {
            $('#main-container').css('margin-top', '0')
            $.ajax({url: "hidecapital.php"});
            }
        </script>
        <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>
<?php
mysqli_close($conn);
?>

</html>

