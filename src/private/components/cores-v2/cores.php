<!-- Main Container -->
<main id="main-container">
    <div class="d-flex justify-content-between mb-2">
        <div class="title">
            <h2>Cores
                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="The following cores need to be returned. Click core to return or delete it."></i>
            </h2>
        </div>
    </div>

    <div class="table-responsive">
        <table class="sbdatatable w-100 dtable">
            <thead>
                <tr>
                    <th>Supplier&nbsp;</th>
                    <th>RO #&nbsp;</th>
                    <th>Part #&nbsp;</th>
                    <td>Date</td>
                    <th>Description&nbsp;</th>
                    <th class="text-right">Charge Ea.&nbsp;</th>
                    <th class="text-right">Qty.&nbsp;</th>
                    <th class="text-right">Total&nbsp;</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $runtotal = 0;
                $stmt = "select roid,partnumber,partdesc,corecharge,id,supplier,partorderdate from cores where returnstatus = 'Not Returned' and shopid = '$shopid'";
                $result = $conn->query($stmt);
                while ($row = $result->fetch_array()) {

                    $row['quantity']=0;

                    if(!empty($row['partnumber']) && $row['partnumber']!='-')
                    {
                        $partfound = false;

                        $stmt = "select quantity from parts where shopid=? and roid=? and partnumber=?";
                        if ($query = $conn->prepare($stmt))
                        {
                            $query->bind_param("sis",$shopid,$row['roid'],$row['partnumber']);
                            $query->execute();
                            $query->store_result();
                            $num_roid_rows = $query->num_rows;
                            if ($num_roid_rows > 0){
                                $partfound = true;
                                $query->bind_result($row['quantity']);
                                $query->fetch();
                            }
                            else
                            {
                                $stmt = "select qty from psdetail where shopid=? and psid=? and pnumber=?";
                                if ($query = $conn->prepare($stmt))
                                {
                                    $query->bind_param("sis",$shopid,$row['roid'],$row['partnumber']);
                                    $query->execute();
                                    $query->store_result();
                                    $num_roid_rows = $query->num_rows;
                                    if ($num_roid_rows > 0){
                                        $partfound = true;
                                        $query->bind_result($row['quantity']);
                                        $query->fetch();
                                    }
                                }
                            }
                        }

                        if(!$partfound)continue;
                    }


                    $runtotal += $row['corecharge'] * $row['quantity'];
                    $onclickReturned = !$shopIsReadOnly 
                        ? "onclick=\"markReturned({$row['id']})\"" 
                        : "";
                ?>
                    <tr <?= $onclickReturned; ?>>
                        <td><?php echo ucwords(strtolower($row['supplier'])); ?>&nbsp;</td>
                        <td><?php echo $row['roid']; ?>&nbsp;</td>
                        <td><?php echo ucwords(strtolower($row['partnumber'])); ?>&nbsp;</td>
                        <td><?php echo  date('m/d/Y H:i:s', strtotime($row['partorderdate'])); ?>&nbsp;</td>
                        <td><?php echo ucwords(strtolower($row['partdesc'])); ?>&nbsp;</td>
                        <td class="text-right"><?php echo number_format($row['corecharge'], 2); ?>&nbsp;</td>
                        <td class="text-right"><?php echo number_format($row['quantity'], 2); ?>&nbsp;</td>
                        <td class="text-right"><?php echo number_format($row['corecharge'] * $row['quantity'], 2); ?>&nbsp;</td>
                    </tr>
                <?php
                }
                ?>
            </tbody>
        </table>
        <table class="table table-sm table-bordered table-striped table-hover table-header-bg">
            <tbody>
                <tr>
                    <td colspan="7" style="text-align:right;font-weight:bold">TOTAL: <?php echo number_format($runtotal, 2); ?></td>
                </tr>
            </tbody>
        </table>
    </div>

</main>
<!-- END Main Container -->
