<?php
// Set cookies first before any output
setcookie("interface", "2", time() + (86400 * 30), "/");

require_once CONN;

$wipstime = microtime(true);

if (!isset($_COOKIE['mode']) || !isset($_COOKIE['shopid']) || !isset($_COOKIE['shopname'])) {
    header("Location: https://" . $_SERVER['SERVER_NAME'] . "/logoff.php");
    exit; // Always exit after a redirect
}

$randnum = rand(111111, 999999);

$shopid = $_COOKIE['shopid'];
$usr = isset($_COOKIE['usr']) ? $_COOKIE['usr'] : ''; //checking usr
$empid = isset($_COOKIE['empid']) ? $_COOKIE['empid'] : '';
$date = date('m/d/Y');
if (isset($_COOKIE['full'])) {
    $mode = $_COOKIE['full'];
}

$lb = "no";
if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
    $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
} elseif (isset($_SERVER['HTTP_X_CLUSTER_CLIENT_IP'])) {
    $ip = $_SERVER['HTTP_X_CLUSTER_CLIENT_IP'];
    $lb = "yes";
} elseif (!empty($_SERVER['HTTP_CLIENT_IP'])) {
    $ip = $_SERVER['HTTP_CLIENT_IP'];
} else {
    $ip = $_SERVER['REMOTE_ADDR'];
}

$iPhone  = stripos($_SERVER['HTTP_USER_AGENT'], "iPhone");

$rocount = 0;
$stmt = "select count(*) c from repairorders where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->bind_result($rocount);
    $query->fetch();
    $query->close();
}

// set the interface on the employee record

if (!empty($empid) && $empid != "Admin" && $empid != "demo") {
    $stmt = "update employees set interface = '2' where id = " . $empid;
    //echo $stmt;
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
}

$stmt = "select readonly,shopnotice,autoshowta,newpackagetype,alldatausername,masterinterface,companystate,package,lower(status),datestarted,merchantaccount,upper(showstatsonwip),companyemail,logo,contact,ts,sortwipbylastfirst,firstlastonwip,showpromiseonwip,nexpartpassword as showcommlog,mailpassword as showbalanceonwip,showemailestimateonwip, showemailinvoiceonwip, showtimeclockonwipdata, showpaymentonwip, showinspectiononwip, showinpectionemailonwip, showtechoverhours,nexpartusername showpics,shopmgr,showtextemail,requirepayments,gponwiplist,showwaiting,showwtk,showvehcolor,showhrsonwip,profitboost,showsigonwip,ts from company where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($readonly, $shopnotice, $autoshowta, $plan, $motortype, $masterinterface, $shopstate, $sbpackage, $sbstatus, $datestarted, $shopmerchant, $showstatsonwip, $companyemail, $logo,$contact,$lastUpdated,$sortwipbylastfirst, $firstlastonwip, $showpromiseonwip, $showcommlog, $showbalanceonwip, $showestemail, $showinvemail, $showtechtime, $showmoney, $showinsp, $showinspemail, $showtechoverhours, $showpics, $showelapsed, $showtextemail, $requirepayments, $gponwiplist, $showwaiting, $showwtk, $showvehcolor, $showhrsonwip, $isprofitboost, $showsigonwip, $companyCreated);
    $query->fetch();
    $query->close();
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$pphtarget = '';

if ($isprofitboost == 'yes') {
    $stmt = "select target from profitboost where shopid = '$shopid'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($pphtarget);
        $query->fetch();
        $query->close();
    }
}

$stmt = "select motor,sortwipbysa,360popup,failedpayment, lower(pph) from settings where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($motor,$sortwipbysa,$popup360,$failedpayment, $hasPph);
    $query->fetch();
    $query->close();
}

$workflownewtab = "no";
$matco = $_COOKIE['matco'] ?? 'no';
$techreport = "no";
$viewschedule = "yes";
$accessworkflow = 'yes';
$showQuickVideoBanner = false;
$QSBannerDaysLimit = 5;
$days_started = ceil( (time() - strtotime($lastUpdated)) / 86400);
if($sbpackage == 'Trial' && $days_started <= $QSBannerDaysLimit){
    $showQuickVideoBanner = true;
}

$showmatcovideo = $quotesbeta = "no";

$video_url = "https://www.youtube.com/embed/_fpZWTqjhSc"; //shopboss quick start video URL

if ($matco == 'yes'){
    $video_url = "https://www.youtube.com/embed/84sSGgz5GUc";

    if ($sbpackage == 'Trial')
    {
        $stmt = "select roid from repairorders where shopid = ? and shopid = origshopid limit 1";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $query->store_result();
            $num_roid_rows = $query->num_rows;
            if ($num_roid_rows < 1)
            $showmatcovideo = 'yes';
        }
    }
}

$stmt = "select id from quotesbeta where shopid = ? limit 1";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    if ($num_roid_rows > 0)
    $quotesbeta = 'yes';
}

$stmt = "select opennewtab from kanbansettings where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($workflownewtab);
    $query->fetch();
    $query->close();
}

$pphAccess = 'yes';

if ($empid == "Admin") {
    $ChangeNotice = "YES";
    $accountingaccess = "YES";
    $reportsaccess = "YES";
    $settingsaccess = "YES";
    $empemail = '<EMAIL>';
    $dashboardaccess = 'YES';
    $EditInventory = 'YES';
    $isfullmode = "YES";
} else {
    $stmt = "select employeelast,employeefirst,upper(changeshopnotice),upper(accounting),upper(ReportAccess),upper(CompanyAccess),EmployeeEmail,upper(DashboardAccess),upper(EditInventory),lower(jobdesc),mode,lower(pphaccess) from employees where id = ? and shopid = ?";

    if ($query = $conn->prepare($stmt)) {

        $query->bind_param("is", $empid, $shopid);
        $query->execute();
        $query->bind_result($employeelast,$employeefirst,$ChangeNotice, $accountingaccess, $reportsaccess, $settingsaccess, $empemail, $dashboardaccess, $EditInventory,$jobdesc,$empmode,$pphAccess);
        $query->fetch();
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $isfullmode = (strtoupper($empmode)=='FULL'?'YES':'NO');
    $empname = $employeefirst.' '.$employeelast;
    $techname = strtoupper($employeelast.", ".$employeefirst);
}

if($_COOKIE['mode'] == 'tech2' && $empid!='Admin')
{
    $stmt = "select techreport,lower(viewschedule),lower(accessworkflow) from techpermissions where shopid = '$shopid' and empid = $empid";
    if ($query = $conn->prepare($stmt))
    {
       $query->execute();
       $query->bind_result($techreport,$viewschedule,$accessworkflow);
       $query->fetch();
       $query->close();
    }
}

$motorfull = "no";
$motorest = "no";
$pid = "";

if ($plan == "gold") {

    $motorfull = "no";
    $motorest = "yes";
    $pid = "40";
} elseif ($plan == "platinum" || $plan == "platinum trial" || $plan == "premier" || $plan == "premier plus") {

    $motorfull = "yes";
    $motorest = "no";
    $pid = "39";
} elseif ($plan == "silver") {

    $motorfull = "no";
    $motorest = "no";
    $pid = "";
} elseif ($plan == "none") {

    if ($motortype == "motorfull") {

        $motorfull = "yes";
        $motorest = "no";
        $pid = "39";
    } elseif ($motortype == "motorest") {

        $motorfull = "no";
        $motorest = "yes";
        $pid = "40";
    } else {

        $motorfull = "no";
        $motorest = "no";
        $pid = "";
    }
}


if ($autoshowta == "yes") {
    $showta = "";      // display:block for tech div
    $showtabtn = "none";    // display:none for button
} else {
    $showta = "none";       // display:none for tech div
    $showtabtn = "";   // display:block for button
}

if ($failedpayment == 'yes') {


    if ((stripos($sn, 'matcosms.com') !== false))
     $servername = '.matcosms.com';
     else
     $servername = '.shopbosspro.com';

     $expiration = time() + (24 * 3600);

     $_COOKIE['failedpayment'] = setcookie("failedpayment", "yes", $expiration, "/", $servername);

     $paybalancelink = '';

    $url = "https://api.armatic.com/customers?query=" . $shopid;

    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

    $headers = array(
        "Accept: application/json",
        "Authorization: Bearer YcRuU2VlFBs25r99avyhJC1fsV25Uoia8cOJwWHshq9u_sJCCs9y0vJ1sGOjxaiJ8ljKfbgqCrqpW25Z6eCgfAPgfo8VeBE1WXg=",
    );
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

    $res = curl_exec($curl);
    curl_close($curl);

    $json = json_decode($res, true);

    $invoicecount = 0;

    if (isset($json['customers'])) {
        foreach ($json['customers'] as $shop) {
            if ($shop['account_number'] == $shopid) {
                $paybalancelink = $shop['pay_balance_link'];
                break;
            }
        }
    }

    if(empty($paybalancelink))$failedpayment = 'no';

}

$capitalarr = array();

// Initialize variables to prevent undefined variable notices
$jobdesc = isset($jobdesc) ? $jobdesc : '';
$empname = isset($empname) ? $empname : '';

if($popup360=='yes' && $shopmerchant!='cardknox' && $failedpayment == "no" && !isset($_COOKIE['hidecapital']) && ($jobdesc=='owner' || strtoupper($contact)==strtoupper($empname) || $_COOKIE['empid']=='Admin'))
{
  if($shopmerchant!='360')
  $capitalarr = array('message' => "Get integrated payments with BOSS PAY", 'url' => 'https://360payments.com/partner-with-us/shop-boss/?tag=shopboss');
  else
  {
    $data = array("key" => "242dbb8a-6eab-46bf-ac9e-ec878d7aaa13", "merchant_id" => $shopid, "partner_id" => "a0Fi0000007vT9TEAU");
    $jsonEncodedData = json_encode($data);
    $curl = curl_init();
    $opts = array(
        CURLOPT_URL             => 'https://us-central1-capital-prod.cloudfunctions.net/x360capital/get-offers',
        CURLOPT_RETURNTRANSFER  => true,
        CURLOPT_CUSTOMREQUEST   => 'POST',
        CURLOPT_POST            => 1,
        CURLOPT_TIMEOUT         => 5,
        CURLOPT_POSTFIELDS      => $jsonEncodedData,
        CURLOPT_HTTPHEADER  => array('Content-Type: application/json', 'Content-Length: ' . strlen($jsonEncodedData))
    );
    curl_setopt_array($curl, $opts);
    $result = curl_exec($curl);
    $json = json_decode($result);
    if (isset($json->signup_url) && !empty($json->signup_url) && !empty($json->message))
    $capitalarr = array('message' => $json->message, 'url' => $json->signup_url);
  }
}

// Onboadring Stuff
    $customer_onboarding_statement="SELECT * FROM customer_onboarding WHERE shopid = ?";

    if ($query = $conn->prepare($customer_onboarding_statement)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $result = $query->get_result();
        if ($result->num_rows > 0) {
            $customer_onboarding = $result->fetch_assoc();
        }
        $query->close();
    } else {
        echo "Customer Onboarding Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    // Initialize customer_onboarding to prevent undefined variable notices
    $customer_onboarding = isset($customer_onboarding) ? $customer_onboarding : [
        'general_info' => false,
        'settings' => false,
        'employees' => false,
        'suppliers' => false,
        'customize' => false,
        'animation' => false
    ];

    $onboardingCompleted = false;
    if ( $customer_onboarding['general_info'] && $customer_onboarding['settings'] && $customer_onboarding['employees'] && $customer_onboarding['suppliers'] && $customer_onboarding['customize'] ){
        $onboardingCompleted = true;
    }

    $companyCreatedDate = new DateTime($companyCreated);
    $currentDate = new DateTime();
    $interval = $currentDate->diff($companyCreatedDate);
    $onboardingActiveDays = $interval->days;
    $onboardingClass = $onboardingActiveDays <= 14 ? "onboarding-link-item" : "";

    if ( $customer_onboarding['animation'] ){
        $onboardingClass = "";
    }

    if ($companyCreatedDate <= new DateTime('2024-08-20')){
        $onboardingCompleted = true;
    }
// END Onboadring Stuff
$num_writer = $num_techs = 0;
$filter_count_text = "";
if (isset($_COOKIE['filter_writer']) && !empty($_COOKIE['filter_writer'])) {
    $num_writer = count(explode(":", $_COOKIE['filter_writer']));
    $filter_count_text = $num_writer."W ";
}
if (isset($_COOKIE['filter_tech']) && !empty($_COOKIE['filter_tech'])){
    $num_techs = count(explode(":", $_COOKIE['filter_tech']));
    $filter_count_text .= $num_techs."T";
}


$num_writer = $num_techs = 0;
$filter_count_text = "";
if (isset($_COOKIE['filter_writer']) && !empty($_COOKIE['filter_writer'])) {
    $num_writer = count(explode(":", $_COOKIE['filter_writer']));
    $filter_count_text = $num_writer."W ";
}
if (isset($_COOKIE['filter_tech']) && !empty($_COOKIE['filter_tech'])){
    $num_techs = count(explode(":", $_COOKIE['filter_tech']));
    $filter_count_text .= $num_techs."T";
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';