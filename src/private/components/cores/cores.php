<!DOCTYPE html>
<?php
require CONN;
$shopid = $_COOKIE['shopid'];
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$stmt = "select shopnotice from company where shopid = ?";

if ($query = $conn->prepare($stmt)){

	$query->bind_param("s",$shopid);
    $query->execute();
    //echo $query->fullQuery."<BR>";
    $query->bind_result($shopnotice);
	//echo $query->fullQuery;
    $query->fetch();
    $query->close();

}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

if (isset($_GET['id'])){
	$coreid = $_GET['id'];
	$stmt = "update cores set returnstatus  = 'Returned' where shopid = '$shopid' and id = $coreid";
	if ($query = $conn->prepare($stmt)){
	    $query->execute();
	    $conn->commit();
	    $query->close();
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
}
?>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!--> <html class="no-focus"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">

        <title><?= getPageTitle() ?></title>

        <meta name="robots" content="noindex, nofollow">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
		<link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon'/ >
        <!-- Icons -->
        <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

        <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
        <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
        <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
        <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
        <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
        <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
        <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
        <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
        <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
        <!-- END Icons -->

        <!-- Stylesheets -->
        <!-- Web fonts -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

        <!-- Page JS Plugins CSS -->
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">

        <!-- Bootstrap and OneUI CSS framework -->
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
        <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
        <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">

		<link rel="stylesheet" href="<?= CSS ?>/prettyPhoto.css" type="text/css" media="screen">


        <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
        <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
        <!-- END Stylesheets -->
        <style>
    	.rocell, .stcell, .dacell, .cucell, .phcell, .vecell, .tocell, .licell, .tycell{
			padding:1px;
		}
		.table-medium{
			font-size:14px
		}
		.table-small{
			font-size:10px
		}
		.table-large{
			font-size:18px
		}
		.shopnotice{
			resize:both;

		}
		.draggable{
			position: absolute;
			z-index: 100
		}
		.draggable-handler{
			cursor: pointer
		}
		.dragging{
			cursor: move;
			z-index: 200 !important
		}
        </style>
    </head>
    <body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    <img src="<?= IMAGE ?>/loaderbig.gif" style="display:block" id="spinner">
        <!-- Page Container -->
        <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
        <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
            <!-- Side Overlay-->
            <aside id="side-overlay">
                <!-- Side Overlay Scroll Container -->
                <div id="side-overlay-scroll">
                    <!-- Side Header -->
                    <div class="side-header side-content">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default pull-right" type="button" data-toggle="layout" data-action="side_overlay_close">
                            <i class="fa fa-times"></i>
                        </button>
                        <span>
                            <img class="img-avatar img-avatar32" src="<?= IMAGE ?>/avatars/avatar10.jpg" alt="">
                            <span class="font-w600 push-10-l">Walter Fox</span>
                        </span>
                    </div>
                    <!-- END Side Header -->

                </div>
                <!-- END Side Overlay Scroll Container -->
            </aside>
            <!-- END Side Overlay -->

            <!-- Sidebar -->
            <nav id="sidebar">
                <!-- Sidebar Scroll Container -->
                <div id="sidebar-scroll">
                    <!-- Sidebar Content -->
                    <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                    <div class="sidebar-content">
                        <!-- Side Header -->
                        <div class="side-header side-content bg-white-op">
                            <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                            <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                                <i class="fa fa-times"></i>
                            </button>
                            <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                            <i class="text-primary">
														<img src='<?php ECHO 'IMAGE /sblogo-white.svg';?>' width='190'>														</i>
                            <span class="h4 font-w600 sidebar-mini-hide">
							</span>
                            </a>
                        </div>
                        <!-- END Side Header -->

                        <!-- Side Content -->
                        <div class="side-content">
                        	<?php //echo $_COOKIE['interface']; ?>
                        	<h3 style="color:white">Main Menu</h3>
                        	<span type="button" onclick="$('#shopstats').toggle()" style="color:white;cursor:pointer;font-weight:normal;">Statistics |</span>
                        	<span type="button" onclick="location.href='https://tech.shopbosspro.com/wip.asp?shopname=<?php echo urlencode($_COOKIE['shopname']); ?>&amp;shopid=<?php echo urlencode($_COOKIE['shopid']); ?>&amp;login=<?php echo urlencode($_COOKIE['username']); ?>&amp;mode=<?php echo urlencode($_COOKIE['mode']); ?>'" style="color:white;cursor:pointer;font-weight:normal;">Tech Mode</span>
                        	<div style="display:none;background-color:#3E4959;border-radius:5px;padding:5px;-webkit-box-shadow: 5px 5px 5px 0px rgba(181,181,181,1);
-moz-box-shadow: 5px 5px 5px 0px rgba(181,181,181,1);
box-shadow: 5px 5px 5px 0px rgba(181,181,181,1);" id="shopstats"></div>

                            <ul style="padding:5px;" class="nav-main">
                                <li>
                                    <a onclick="location.href='<?= COMPONENTS_PRIVATE ?>/wip/wip.php'" href="#"><i class="fa fa-wrench"></i><span class="sidebar-mini-hide">Work In Process</span></a>
                                </li>
                        </div>
                        <!-- END Side Content -->
                    </div>
                    <!-- Sidebar Content -->
                </div>
                <!-- END Sidebar Scroll Container -->
            </nav>
            <!-- END Sidebar -->

            <!-- Header -->
            <header id="header-navbar" class="content-mini content-mini-full">

                <!-- Header Navigation Right -->
                <ul class="nav-header pull-right">
                    <li>
                        <div style="overflow:hidden;resize:both" id="shopnotice">
                            <?php echo $_COOKIE['shopname']." #".$shopid." ".$_COOKIE['username'].'<a class="btn btn-primary btn-sm btn-logoff" href="'.COMPONENTS_PUBLIC.'/login/logoff.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Logoff</span></a>'; ?>
                        </div>
                    </li>
                </ul>
                <!-- END Header Navigation Right -->

                <!-- Header Navigation Left -->

                <ul class="nav-header pull-left">
                    <li class="hidden-md hidden-lg">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                            <i class="fa fa-navicon"></i>
                        </button>
                    </li>
                    <li class="hidden-xs hidden-sm">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                            <i class="fa fa-bars"></i>
                        </button>
                    </li>
                    <li>
                        <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                        <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                            <i class="si si-grid"></i>
                        </button>
                    </li>
                    <li class="visible-xs">
                        <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                        <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                            <i class="fa fa-search"></i>
                        </button>
                    </li>
					<li>
						<?php if(strlen($shopnotice) > 0){echo '<div contenteditable="true" id="myshopnotice" class="shopnotice">'.$shopnotice.'</div>' ;}?>
					</li>
                </ul>

                <!-- END Header Navigation Left -->
            </header>
            <!-- END Header -->

            <!-- Main Container -->
            <main id="main-container">
            	<br>
            	<h4 style="margin-left:20px;">The following cores need to be returned.  Click core to return or delete it.</h4>
				<br>
				<div class="table-responsive">
					<table style="width:90%;margin:auto" class="table table-condensed table-bordered table-striped table-hover table-header-bg">
						<thead>
							<tr>
								<td>Supplier&nbsp;</td>
								<td>RO #&nbsp;</td>
								<td>Part #&nbsp;</td>
                                <td>Date</td>
								<td>Description&nbsp;</td>
								<td class="text-right">Charge Ea.&nbsp;</td>
								<td class="text-right">Qty.&nbsp;</td>
								<td class="text-right">Total&nbsp;</td>
							</tr>
						</thead>
						<tbody>
							<?php
							$runtotal = 0;
							$stmt = "select roid,partnumber,partdesc,corecharge,id,supplier,partorderdate from cores where returnstatus = 'Not Returned' and shopid = '$shopid'";
							$result = $conn->query($stmt);
							while($row = $result->fetch_array()) {

                            $row['quantity']=0;

                            if(!empty($row['partnumber']) && $row['partnumber']!='-')
                            {
                                $partfound = false;
    
                                $stmt = "select quantity from parts where shopid=? and roid=? and partnumber=?";
                                if ($query = $conn->prepare($stmt))
                                {
                                    $query->bind_param("sis",$shopid,$row['roid'],$row['partnumber']);
                                    $query->execute();
                                    $query->store_result();
                                    $num_roid_rows = $query->num_rows;
                                    if ($num_roid_rows > 0){
                                        $partfound = true;
                                        $query->bind_result($row['quantity']);
                                        $query->fetch();
                                    }
                                    else
                                    {
                                        $stmt = "select qty from psdetail where shopid=? and psid=? and pnumber=?";
                                        if ($query = $conn->prepare($stmt))
                                        {
                                            $query->bind_param("sis",$shopid,$row['roid'],$row['partnumber']);
                                            $query->execute();
                                            $query->store_result();
                                            $num_roid_rows = $query->num_rows;
                                            if ($num_roid_rows > 0){
                                                $partfound = true;
                                                $query->bind_result($row['quantity']);
                                                $query->fetch();
                                            }
                                        }
                                    }
                                }

                                if(!$partfound)continue;
                            }

                            $runtotal += $row['corecharge']*$row['quantity'];

                            $onclickReturned = !$shopIsReadOnly 
                                ? "onclick=\"markReturned({$row['id']})\"" 
                                : "";
							?>
							<tr <?= $onclickReturned; ?>>
								<td><?php echo strtoupper($row['supplier']); ?>&nbsp;</td>
								<td><?php echo $row['roid']; ?>&nbsp;</td>
								<td><?php echo strtoupper($row['partnumber']); ?>&nbsp;</td>
                                <td><?php echo  date('m/d/Y H:i:s', strtotime($row['partorderdate'])); ?>&nbsp;</td>
								<td><?php echo strtoupper($row['partdesc']); ?>&nbsp;</td>
								<td class="text-right"><?php echo number_format($row['corecharge'],2); ?>&nbsp;</td>
								<td class="text-right"><?php echo number_format($row['quantity'],2); ?>&nbsp;</td>
								<td class="text-right"><?php echo number_format($row['corecharge']*$row['quantity'],2); ?>&nbsp;</td>
							</tr>
							<?php
							}
							?>
							<tr>
								<td colspan="7" style="text-align:right;font-weight:bold">TOTAL: <?php echo number_format($runtotal,2); ?></td>
							</tr>
						</tbody>
					</table>
				</div>

            </main>
            <!-- END Main Container -->

            <!-- Footer -->
            <!-- END Footer -->
        </div>
        <!-- END Page Container -->
        <script src="<?= SCRIPT ?>/core/jquery.min.js"></script>
        <script src="<?= SCRIPT ?>/tipped.js"></script>
        <script src="<?= SCRIPT ?>/core/bootstrap.min.js"></script>

        <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
        <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
        <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
        <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
        <script src="<?= SCRIPT ?>/plugins/easymodal/easymodal.min.js"></script>
        <script src="<?= SCRIPT ?>/app.js"></script>
        <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
        <script type="text/javascript" src="<?= SCRIPT ?>/jquery.prettyPhoto.js"></script>

        <!-- Page Plugins -->

        <!-- Page JS Code
        <script src="assets/js/pages/base_pages_dashboard.js"></script>-->
        <script>
            jQuery(function () {
                // Init page helpers (Slick Slider plugin)
                App.initHelpers('slick');
            });

            function markReturned(id){
				eModal.iframe({
					title:'Return Core',
					url: "corereturn.php?id="+id,
					size: eModal.size.xl,
					buttons: [
						{text: 'Close', style: 'warning', close:true}

			    	]

				});
            }

        </script>
        <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    </body>
    <?php if(isset($conn)){mysqli_close($conn);} ?>


</html>
