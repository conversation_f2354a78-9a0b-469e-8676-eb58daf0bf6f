<?php

require CONN;

$shopid = $_COOKIE['shopid'];
$po = filter_var($_POST['po'], FILTER_SANITIZE_STRING);
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>

<div class="col-md-12">
    <h3>Search Results</h3>

    <table id="posearchlist" class="sbdatatable w-100">
        <thead>
            <tr>
                <th>PO Number</th>
                <th>Date Issue</th>
                <th>Supplier</th>
                <th>Status</th>
                <th class="text-end">Total PO</th>
                <?php if (!$shopIsReadOnly): ?>
                    <th>Notes</th>
                    <th></th>
                <?php endif; ?>
                <th>Print</th>
            </tr>
        </thead>
        <tbody>
            <?php
            $date = date('Y-m-d', strtotime('-7 days'));
            $stmt = "select `id`,`shopid`,`ponumber`,`desc`,`issuedate`,`receiveddate`,`status`,`saletype`,`salenumber`,`issuedto`,`ordertype`,`qb`,`expectedarrival`,`deliverymethod`,`orderedby` from po where shopid = ? and ponumber = ? order by status desc";
            //echo $stmt;
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("ss", $shopid, $po);
                $query->execute();
                $result = $query->get_result();
                while ($rs = $result->fetch_array()) {
                    $ponum = $rs['ponumber'];
                    $sstmt = "select coalesce(sum(partcost*quantity),0) as pc from partsinventoryorder where shopid = '$shopid' and ponumber = $ponum";
                    if ($squery = $conn->prepare($sstmt)) {
                        $squery->execute();
                        $squery->bind_result($pc);
                        $squery->fetch();
                        $squery->close();
                    }
            ?>
                    <tr>
                        <td><?php echo $rs['ponumber']; ?></td>
                        <td><?php echo $rs['issuedate']; ?></td>
                        <td><?php echo $rs['issuedto']; ?></td>
                        <td><?php echo $rs['status']; ?></td>
                        <td class="text-end"><?php echo $pc; ?></td>
                        <?php if (!$shopIsReadOnly): ?>
                            <td>
                                <textarea class="form-control" id="notes"><?php echo $rs['desc']; ?></textarea>
                            </td>
                            <td class="text-end"><span class="btn btn-primary" onclick="saveComments(<?php echo $rs['id']; ?>)">Save Comments</span></td>
                        <?php endif; ?>
                        <td><span class="btn btn-secondary" onclick="printPO('<?php echo $rs['id']; ?>','<?php echo $rs['ordertype']; ?>','<?php echo $rs['salenumber']; ?>')">Print PO</span></td>
                    </tr>
            <?php
                }
            }
            ?>
        </tbody>
    </table>

    <script>
        $(document).ready(function() {
            $("#posearchlist").dataTable({
                responsive: true,
                retrieve: true,
                paging: false,
                searching: false,
                order: [],
            });
        });
    </script>
</div>
<?php

mysqli_close($conn);
