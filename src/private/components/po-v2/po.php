<!-- Main Container -->
<main id="main-container">
    <div class="d-flex justify-content-between mb-2">
        <div class="title">
            <h2>Receive PO</h2>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="form-outline">
                <input class="form-control" type="text" id="srch">
                <label class="form-label" for="srch">Enter PO number and click search</label>
            </div>
        </div>

        <div class="col-md-3">
            <span onclick="searchPO()" class="btn btn-secondary">Search</span>
        </div>
    </div>

    <div class="row mb-4">
        <div id="results"></div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <h3>Inventory PO's</h3>

            <table id="polist" class="sbdatatable w-100">
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>PO Number</th>
                        <th>Date Issue</th>
                        <th>Supplier</th>
                        <th>Status</th>
                        <th class="text-right">Total PO</th>
                        <th>Type</th>
                        <?php if (!$shopIsReadOnly): ?>
                            <th>Receive PO</th>
                        <?php endif; ?>
                        <th>Print</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $date = date('Y-m-d', strtotime('-7 days'));
                    $stmt = "select `id`,`shopid`,`ponumber`,`desc`,`issuedate`,`receiveddate`,`status`,`saletype`,`salenumber`,`issuedto`,`ordertype`,`qb`,`expectedarrival`,`deliverymethod`,`orderedby` from po where (receiveddate >= '$date' or receiveddate = '0000-00-00') and shopid = '$shopid' and status != 'DEAD' and status != 'closed' and ordertype = 'inv' order by status desc";
                    //echo $stmt;
                    if ($query = $conn->prepare($stmt)) {
                        $query->execute();
                        $result = $query->get_result();
                        while ($rs = $result->fetch_array()) {
                            $ponum = $rs['ponumber'];
                            $sstmt = "select coalesce(sum(partcost*quantity),0) as pc from partsinventoryorder where shopid = '$shopid' and ponumber = $ponum";
                            if ($squery = $conn->prepare($sstmt)) {
                                $squery->execute();
                                $squery->bind_result($pc);
                                $squery->fetch();
                                $squery->close();
                            }
                    ?>
                            <tr>
                                <td>INV</td>
                                <td><?php echo $rs['ponumber']; ?></td>
                                <td><?php echo $rs['issuedate']; ?></td>
                                <td><?php echo $rs['issuedto']; ?></td>
                                <td><?php echo $rs['status']; ?></td>
                                <td class="text-right"><?php echo $pc; ?></td>
                                <td>INV</td>
                                <?php if (!$shopIsReadOnly): ?>
                                    <td><span class="btn btn-primary" onclick="receivePO('inv',<?php echo $rs['id']; ?>)">Receive PO</span></td>
                                <?php endif; ?>
                                <td><span class="btn btn-secondary" onclick="printPO('<?php echo $rs['id']; ?>','<?php echo $rs['ordertype']; ?>','<?php echo $rs['salenumber']; ?>')">Print PO</span></td>
                            </tr>
                    <?php
                        }
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <h3>RO-based PO's</h3>

            <table id="porolist" class="sbdatatable w-100">
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>PO Number</th>
                        <th>Date Issue</th>
                        <th>Supplier</th>
                        <th>Status</th>
                        <th class="text-right">Total PO</th>
                        <th>Type</th>
                        <?php if (!$shopIsReadOnly): ?>
                            <th>Receive PO</th>
                        <?php endif; ?>
                        <th>Print</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $query->close();

                    $rolist = "";
                    // get a list of open RO's
                    $stmt = "select roid from repairorders r where r.shopid = '$shopid' and status != 'CLOSED' and r.ROType != 'No Approval'";
                    //echo $stmt."<BR>";
                    if ($query = $conn->prepare($stmt)) {
                        $query->execute();
                        $result = $query->get_result();
                        while ($rs = $result->fetch_array()) {
                            $rolist .= $rs['roid'] . ",";
                        }
                    }
                    if (substr($rolist, -1) == ",") {
                        $rolist = substr($rolist, 0, strlen($rolist) - 1);
                    }

                    $pc = 0;
                    $stmt = "select `id`,`shopid`,`ponumber`,`desc`,`issuedate`,`receiveddate`,`status`,`saletype`,`salenumber`,`issuedto`,`ordertype`,`qb`,`expectedarrival`,`deliverymethod`,`orderedby` from po where salenumber in ($rolist) and shopid = '$shopid' and status != 'DEAD' and status != 'closed' and ordertype = 'ro' order by salenumber asc";
                    //echo $stmt;
                    if ($query = $conn->prepare($stmt)) {
                        $query->execute();
                        $result = $query->get_result();
                        while ($rs = $result->fetch_array()) {

                            $sstmt = "select coalesce(sum(linettlcost),0) pc from parts where deleted = 'no' and roid = " . $rs['salenumber'] . " and shopid = '$shopid' and ponumber = " . $rs['ponumber'];
                            if ($squery = $conn->prepare($sstmt)) {
                                $squery->execute();
                                $squery->bind_result($pc);
                                $squery->fetch();
                                $squery->close();
                            }
                            if ($pc > 0) {
                    ?>
                                <tr>
                                    <td>RO #<?php echo $rs['salenumber']; ?></td>
                                    <td><?php echo $rs['ponumber']; ?></td>
                                    <td><?php echo date("m/d/Y", strtotime($rs['issuedate'])); ?></td>
                                    <td><?php echo strtoupper($rs['issuedto']); ?></td>
                                    <td><?php echo strtoupper($rs['status']); ?></td>
                                    <td class="text-right"><?php echo number_format($pc, 2); ?></td>
                                    <td>RO <?php echo $rs['salenumber']; ?></td>
                                    <?php if (!$shopIsReadOnly): ?>
                                        <td><span onclick="receivePO('ro',<?php echo $rs['id']; ?>)" class="btn btn-primary">Mark Received</span></td>
                                    <?php endif; ?>
                                    <td><span class="btn btn-secondary" onclick="printPO('<?php echo $rs['id']; ?>','<?php echo $rs['ordertype']; ?>','<?php echo $rs['salenumber']; ?>')">Print PO</span></td>
                                </tr>
                    <?php
                            }
                        }
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
</main>
<!-- END Main Container -->
