<?php

require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];

$today = new DateTime('now');
$today = date_format($today, 'm/d/Y');

// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";
$menuComponent = 'expenses';

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<style>
    main {
        height: 100% !important;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div >
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div class="flex-1">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/accounting.php" class="text-secondary">Accounting</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">List Recurring Bills</h2>
                    </div>
                </div>

                <?php if( !$shopIsReadOnly ): ?>
                    <div class="flex-1">
                        <button class="btn btn-primary" type="button" onclick="newExp('<?= $shopid; ?>')">Add Recurring Expense</button>
                    </div>
                <?php endif; ?>
            </div>
            <hr/>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <table class="sbdatatable w-100">
                <thead>
                <tr>
                    <th>Description</th>
                    <th>Amount</th>
                    <th>Category</th>
                    <th>Due Day</th>
                </tr>
                </thead>
                <tbody>
                <?php

                $texp = 0;

                $stmt = "SELECT id,shopid,expensename,expensecategory,amount,dueday ";
                $stmt .= " FROM `expensesrecurring` ";
                $stmt .= "WHERE shopid = ? ";
                //echo $stmt;

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $expresult = $query->get_result();
                } else {
                    echo "Expenses Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                if ($expresult->num_rows > 0) {
                    while ($exp = $expresult->fetch_array()) {
                        $texp = $texp + $exp["amount"];

                        $onclickExp = !$shopIsReadOnly
                            ? "onclick=\"editExp(
                                '{$exp["id"]}',
                                '{$exp["shopid"]}',
                                '" . str_replace("'", "\\'", $exp["expensename"]) . "',
                                '{$exp["amount"]}',
                                '{$exp["expensecategory"]}',
                                '{$exp["dueday"]}'
                            )\""
                            : "";
                        ?>
                        <tr style="cursor:pointer" <?= $onclickExp ?>>
                        <td><?php echo strtoupper($exp["expensename"]); ?>&nbsp;</td>
                        <td><?php echo asDollars($exp["amount"]); ?>&nbsp;</td>
                        <td>&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $exp["expensecategory"]; ?>&nbsp;</td>
                        <td><?php echo $exp["dueday"]; ?>&nbsp;</td>
                        </tr>
                        <?php
                    } // end of expense while
                } // end of if for expenses
                ?>
                </tbody>
                <tfoot>
                <tr>
                    <td style=" font-weight:bold"></td>
                    <td style="font-weight:bold">Total:&nbsp;&nbsp; <?php echo asDollars($texp); ?>&nbsp;</td>
                    <td style="font-weight:bold"></td>
                    <td style="font-weight:bold"></td>
                </tr>
                </tfoot>
            </table>
        </div>
    </div>
</main>
<!-- END Main Container -->

<?php if( !$shopIsReadOnly ): ?>
    <!-- Add Expense Modal -->
    <div id="addexpModal" class="modal fade" role="dialog" aria-hidden="true">
        <input id="shopid" name="shopid" value="" type="hidden">
        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Add Recurring Expense</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <table class="table table-sm">
                        <tr>
                            <td class="text-left">Name/Description:</td>
                            <td>
                                <div class="form-outline">
                                    <input class="form-control" id="expname" name="expname" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Amount:</td>
                            <td>
                                <div class="form-outline">
                                    <input class="form-control" id="amount" name="amount" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Account:</td>
                            <td>
                                <div class="form-row">
                                    <select id="account" class="select" name="account" type="text" value="">
                                        <?php
                                        $stmt = "select id,shopid,category,cattype,core ";
                                        $stmt .= "from chartofaccounts ";
                                        $stmt .= "where shopid = ?";
                                        $stmt .= "  and cattype = 'Expense'";
                                        $stmt .= " order by category";
                                        //echo $stmt;

                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("s", $shopid);
                                            $query->execute();
                                            $coaresult = $query->get_result();
                                        } else {
                                            echo "Chart of Accounts prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }

                                        if ($coaresult->num_rows > 0) {
                                            while ($coa = $coaresult->fetch_array()) {
                                                ?>
                                                <option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
                                                <?php
                                            }    // end of while
                                        } // end if

                                        ?>
                                    </select>
                                </div>
                            </td>
                        </tr>

                        <tr id="dueday">
                            <td style="width: 268px">
                                Day of Month to pay
                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Select day of month to pay"></i>
                            </td>
                            <td>
                                <div class="form-row">
                                    <select class="select" id="dayofmonth" name="dayofmonth">

                                        <?php
                                        $j = 1;
                                        while ($j >= 1 && $j <= 31) {
                                            ?>
                                            <option value="<?php echo $j; ?>"><?php echo $j; ?></option>
                                            <?php
                                            $j = $j + 1;
                                        }
                                        ?>
                                    </select>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="addexp()" type="button" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Edit Expense Modal -->
    <div id="expModal" class="modal fade" role="dialog" aria-hidden="true">
        <input id="id" name="id" value="" type="hidden">
        <input id="shopid" name="shopid" value="" type="hidden">

        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Edit Recurring Expense</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <table class="table table-sm">
                        <tr>
                            <td class="text-left">Name/Description:</td>
                            <td>
                                <div class="form-outline">
                                    <input type="expname2" class="form-control" id="expname2" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Amount:</td>
                            <td>
                                <div class="form-outline">
                                    <input class="form-control" id="amount2" name="amount2" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Due Day:</td>
                            <td>
                                <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                    <input class="form-control" id="dueday2" name="dueday2" data-mdb-toggle="datepicker" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Account:</td>
                            <td>
                                <div class="form-row">
                                    <select id="account2" class="select" name="account2" type="text" value="">
                                        <?php

                                        $stmt = "select id,shopid,category,cattype,core ";
                                        $stmt .= "from chartofaccounts ";
                                        $stmt .= "where shopid = ?";
                                        $stmt .= "  and cattype = 'Expense'";
                                        $stmt .= " order by category";
                                        //echo $stmt;

                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("s", $shopid);
                                            $query->execute();
                                            $coaresult = $query->get_result();
                                        } else {
                                            echo "Chart of Accounts prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }

                                        if ($coaresult->num_rows > 0) {
                                            while ($coa = $coaresult->fetch_array()) {
                                                ?>
                                                <option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
                                                <?php
                                            }    // end of while
                                        } // end if
                                        ?>
                                    </select>
                                </div>
                            </td>
                        </tr>

                    </table>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="saveExp()" type="button" class="btn btn-primary">Save</button>
                    <button onclick="deleteExp()" type="button" class="btn btn-secondary">Delete</button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>

<script>
    $(document).ready(function () {
        $(".sbdatatable").dataTable({
            responsive: true,
            retrieve: true,
            searching: false,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            order : []
        });
    });

    function deleteExp() {
        var id = $("#id").val();
        var shopid = $("#shopid").val();

        var ds = 'id=' + id + '&shopid=' + shopid;

        showLoader();

        $.ajax({
            type: "post",
            url: "deleterecurring.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Deletion")
            }
        });

        hideLoader()
    }

    function getFormattedDate(date) {
        var year = date.getFullYear();
        var month = (1 + date.getMonth()).toString();
        month = month.length > 1 ? month : '0' + month;
        var day = date.getDate().toString();
        day = day.length > 1 ? day : '0' + day;
        return month + '/' + day + '/' + year;
    }

    function editExp(id, shopid, name, amount, category, dueday) {

        <?php
        if ($shopid == "demo") {
            echo "$('#expModal').modal('show')";
        } else {
            echo "$('#expModal').modal('show')";
        }
        ?>

        $('#id').val(id)
        $('#shopid').val(shopid)
        $('#expname2').val(name)
        $('#amount2').val(amount)
        $('#account2').val(category).toggleClass('active')
        $('#dueday2').val(dueday)

    }

    function newExp(shopid) {

        $('#addexpModal').modal('show')

        $('#id').val(id)
        $('#shopid').val(shopid)
        var hoy = new Date(),
            mm = hoy.getMinutes(),
            d = hoy.getDate(),
            m = hoy.getMonth() + 1,
            y = hoy.getFullYear(),
            h = hoy.getHours(),
            data;

        if (d < 10) {
            d = "0" + d;
        }
        if (m < 10) {
            m = "0" + m;
        }

        d = m + "/" + d + "/" + y;

    }

    function saveExp() {

        var id = $("#id").val();
        var shopid = $("#shopid").val();
        var name = $("#expname2").val();
        var amount = $("#amount2").val();
        var dueday = $("#dueday2").val();
        var account = $("#account2").val();

        var ds = 'id=' + id + '&shopid=' + shopid + '&expensename=' + name + '&amount=' + amount + '&dueday=' + dueday + '&expensecategory=' + account;

        showLoader();

        $.ajax({
            type: "post",
            url: "editrecurring.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()
    }

    function addexp() {

        var shopid = $("#shopid").val();
        var name = $("#expname").val();
        var amount = $("#amount").val();
        var account = $("#account").val();
        var dayofmonth = $("#dayofmonth").val();

        var noworlater = "now";

        var ds = 'shopid=' + shopid + '&expensename=' + name + '&expensecategory=' + account + '&amount=' + amount + '&dayofmonth=' + dayofmonth;

        showLoader();

        $.ajax({
            type: "post",
            url: "addexpense.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()
    }

    function postPymt() {
        sbconfirm(
            'Mark Paid',
            'Are you sure you want to mark this expense as paid?',
            function () {
                var id = $("#id").val();
                var shopid = $("#shopid").val();
                var datepaid = $("#datepaid").val();

                var paidwith = $("#paidwith").val();
                var ref = $("#refnum").val();
                var accountid = $("#bankid").val();

                var expensename = $("#expname2").val();
                var amount = $("#amount2").val();
                var expensecat = $("#account2").val();

                var ds = 'id=' + id + '&shopid=' + shopid + '&datepaid=' + datepaid + '&paidwith=' + paidwith + '&ref=' + ref + '&accountid=' + accountid + '&expensename=' + expensename + '&amount=' + amount + '&expensecat=' + expensecat;

                showLoader();

                $.ajax({
                    type: "post",
                    url: "postexpense.php",
                    data: ds,
                    success: function () {
                        location.reload();
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        sbalert("Error in Save")
                    }
                });

                hideLoader()
            }
        );
    }

    function recurringPayment(v) {
        if (v == "Yes") {
            document.getElementById("dueday").style.display = ""
        }
        if (v == 'No') {
            $('#dueday').hide()
        }
    }

    function showBankAcct(v) {
        if (v == "Expense") {
            document.getElementById("bankaccount").style.display = ""
        }
        if (v == 'Bill') {
            $('#bankaccount').hide()
        }
    }
</script>
</body>
<?php
mysqli_close($conn);
?>

</html>
