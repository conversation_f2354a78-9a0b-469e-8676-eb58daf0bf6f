<?php

require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];
$today = new DateTime('now');

// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";
$menuComponent = 'expenses';

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<style>
    main {
        height: 100% !important;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div >
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div class="flex-1">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/accounting.php" class="text-secondary">Accounting</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">Expenses (A/P)</h2>
                    </div>
                </div>

                <?php if( !$shopIsReadOnly ): ?>
                    <div class="flex-1">
                        <button class="btn btn-primary" type="button" onclick="newExp('<?= $shopid; ?>')">Add New Bill/Expense</button>
                    </div>
                <?php endif; ?>
            </div>
            <hr/>
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md-12">
            <table id="exptable" class="sbdatatable">
                <thead>
                <tr>
                    <th>Description</th>
                    <th class="text-right">Amount</th class="text-right">
                    <th>Category</th>
                    <th>Due Date</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $stmt = "SELECT id,shopid,expensename,expensecategory,amount,duedate,expensepaid,paidto,paiddate,paidwith,ref,roid ";
                $stmt .= " FROM `expenses` ";
                $stmt .= "WHERE shopid = ? ";
                $stmt .= "  and expensepaid = 'no'";
                $stmt .= "  order by duedate ";
                //echo $stmt;

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $expresult = $query->get_result();
                } else {
                    echo "Expense Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                if ($expresult->num_rows > 0) {
                $texp = 0;
                while ($exp = $expresult->fetch_array()) {
                    $texp = $texp + $exp["amount"];
                    $duedate = new DateTime($exp["duedate"]);
                    ?>
                    <tr style="cursor:pointer" 
                        <?php if ( !$shopIsReadOnly ): ?>
                            onclick="editExp('<?= $exp["id"]; ?>','<?= $exp["shopid"]; ?>','<?= str_replace("'", "\'", $exp["expensename"]); ?>','<?= $exp["amount"]; ?>','<?= $exp["expensecategory"]; ?>','<?= $duedate->format('m/d/Y'); ?>','<?= $exp["roid"]; ?>')""
                        <?php endif; ?>
                    >
                    <td class=" text-left"><?= strtoupper($exp["expensename"]); ?>
                    </td>
                    <td class="text-right"><?= asDollars($exp["amount"]); ?>
                    </td>
                    <td class="text-left"><?= strtoupper($exp["expensecategory"]); ?>
                    </td>
                    <td class="text-left"><?= $duedate->format('m/d/Y'); ?>
                    </td>
                    </tr>
                    <?php
                }    // end of while

                // Start of add total and else 7/19/17
                ?>
                </tbody>
                <tfoot>
                <tr>
                    <td></td>
                    <td style="font-weight:bold">Total:&nbsp;&nbsp; <?= asdollars($texp); ?>&nbsp;</td>
                    <td></td>
                    <td></td>
                </tr>
                </tfoot>
                <?php } // end if ?>
                </tbody>
            </table>
        </div>
    </div>
</main>

<!-- Add Expense Modal -->
<div id="addexpModal" class="modal fade" role="dialog" aria-hidden="true">
    <input id="shopid" name="shopid" value="" type="hidden">
    <div class="modal-dialog modal-lg">
        <!-- Modal content-->
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">Add New Expense</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <table class="table table-sm">
                    <tr>
                        <td class="text-left">Name/Description:</td>
                        <td>
                            <div class="form-outline">
                                <input class="form-control" id="expname" name="expname" type="text" value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">Paid To:</td>
                        <td>
                            <div class="form-outline">
                                <input class="form-control" id="paidto" name="paidto" type="text" value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">Amount:</td>
                        <td>
                            <div class="form-outline">
                                <input class="form-control" id="amount" name="amount" type="text" value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">RO Number (if applicable):</td>
                        <td>
                            <div class="form-outline">
                                <input class="form-control" id="roid" name="roid" type="text" value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">Date Due:</td>
                        <td>
                            <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                <input class="form-control" id="duedate" name="duedate" data-mdb-toggle="datepicker" type="text" value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">Account:</td>
                        <td>
                            <div class="form-row">
                                <select id="account" class="select" name="account" type="text" value="">
                                    <?php

                                    $stmt = "SELECT id,shopid,category,cattype,core ";
                                    $stmt .= " FROM chartofaccounts ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  and cattype = 'Expense'";
                                    $stmt .= " order by category";
                                    //echo $stmt;

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("s", $shopid);
                                        $query->execute();
                                        $coaresult = $query->get_result();
                                    } else {
                                        echo "Chart of Accounts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    if ($coaresult->num_rows > 0) {
                                        while ($coa = $coaresult->fetch_array()) {
                                            ?>
                                            <option value="<?= $coa["category"]; ?>"><?= $coa["category"]; ?></option>
                                            <?php
                                        }    // end of while
                                    } // end if
                                    ?>
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Recurring?
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="If this is a recurring expense, select Yes and then select the day of month it is due"></i>
                        </td>
                        <td>
                            <div class="form-row">
                                <select onchange="recurringPayment(this.value)" class="select" id="recurring" name="recurring">
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr style="display:none" id="dueday">
                        <td>
                            Day of Month to pay
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Select day of month to pay."></i>
                        </td>
                        <td>
                            <div class="form-row">
                                <select class="select" id="dayofmonth" name="dayofmonth">
                                    $j =1;
                                    <?php
                                    $j = 1;
                                    while ($j >= 1 && $j <= 31) {
                                        ?>
                                        <option value="<?= $j; ?>"><?= $j; ?></option>
                                        <?php
                                        $j = $j + 1;
                                    }
                                    ?>
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Is this a bill to be paid later, or an expense you have already paid?
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="If this is an expense, please select an account to deduct the amount from."></i>
                        </td>
                        <td>
                            <div class="form-row">
                                <select onchange="showBankAcct(this.value)" class="select" id="type" name="type">
                                    <option value="Bill">Its a Bill to be Paid</option>
                                    <option value="Expense">Its an Expense That Has Been Paid</option>
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr id="bankaccount" style="display:none">
                        <td>Please select a Bank Account</td>
                        <td>
                            <?php
                            $stmt = "SELECT id, bankname ";
                            $stmt .= "FROM bankaccount ";
                            $stmt .= "WHERE shopid = ? ";
                            $stmt .= "  AND acctstatus = 'Open'";
                            //echo $stmt;

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $bankresult = $query->get_result();
                            } else {
                                echo "Bank Account Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            if ($bankresult->num_rows > 0) {
                                ?>
                                <div class="form-row">
                                    <select class="select" id="accountid" name="accountid">
                                        <?php
                                        while ($bank = $bankresult->fetch_array()) {
                                            ?>
                                            <option value="<?= $bank["id"]; ?>"><?= $bank["bankname"]; ?></option>
                                            <?php
                                        }
                                        ?>
                                    </select>
                                </div>
                                <?php
                            } else {
                                echo "You have no bank accounts added.  To automatically deduct this from a bank account, please add a new bank account first";
                            }
                            ?>
                        </td>
                        <td>&nbsp;</td>
                    </tr>
                </table>
            </div>

            <div class="modal-footer d-flex justify-content-center">
                <button onclick="addexp()" id="addexpbtn" type="button" class="btn btn-primary">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Expense Modal -->
<div id="expModal" class="modal fade" role="dialog" aria-hidden="true">
    <input id="id" name="id" value="" type="hidden">
    <input id="shopid" name="shopid" value="" type="hidden">
    <div class="modal-dialog modal-lg">
        <!-- Modal content-->
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">Edit Expense</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <table class="table table-sm">
                    <tr>
                        <td class="text-left">Name/Description:</td>
                        <td>
                            <div class="form-outline">
                                <input type="expname2" class="form-control" id="expname2" value="">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">Amount:</td>
                        <td>
                            <div class="form-outline">
                                <input class="form-control" id="amount2" name="amount2" type="text" value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">RO Number (if applicable):</td>
                        <td>
                            <div class="form-outline">
                                <input class="form-control" id="roid2" name="roid2" type="text" value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">Date Due:</td>
                        <td>
                            <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                <input class="form-control" id="duedate2" name="duedate2" data-mdb-toggle="datepicker" type="text" value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">Account:</td>
                        <td>
                            <div class="form-row">
                                <select id="account2" class="select" name="account2" type="text" value="">
                                    <?php

                                    $stmt = "SELECT id,shopid,category,cattype,core ";
                                    $stmt .= " FROM chartofaccounts ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  and cattype = 'Expense'";
                                    $stmt .= " order by category";
                                    //echo $stmt;

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("s", $shopid);
                                        $query->execute();
                                        $coaresult = $query->get_result();
                                    } else {
                                        echo "Chart of Accounts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    if ($coaresult->num_rows > 0) {
                                        while ($coa = $coaresult->fetch_array()) {
                                            ?>
                                            <option value="<?= $coa["category"]; ?>"><?= $coa["category"]; ?></option>
                                            <?php
                                        }    // end of while
                                    } // end if
                                    ?>
                                </select>
                            </div>
                        </td>
                    </tr>

                </table>
            </div>

            <div class="modal-footer d-flex justify-content-center">
                <button onclick="markPaid()" type="button" class="btn btn-secondary">Mark Paid</button>
                <button onclick="saveExp()" id="saveexp" type="button" class="btn btn-primary">Save</button>
                <button onclick="deleteExp()" type="button" class="btn btn-secondary">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Paid Expense Modal -->
<div id="payModal" class="modal fade" role="dialog" aria-hidden="true">
    <input id="id" name="id" value="" type="hidden">
    <input id="shopid" name="shopid" value="" type="hidden">
    <div class="modal-dialog modal-lg">
        <!-- Modal content-->
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">Enter Payment Information</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <table class="table table-sm">
                    <tr>
                        <td class="text-left">Paid with:</td>
                        <td>
                            <div class="form-row">
                                <select class="select" id="paidwith" name="paidwith">
                                    <option value="Cash">Cash</option>
                                    <option value="Check">Check</option>
                                    <option value="Debit Card">Debit Card</option>
                                    <option value="Credit Card">Credit Card</option>
                                    <option value="American Express">American Express</option>
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">Ref. Number:</td>
                        <td>
                            <div class="form-outline">
                                <input class="form-control" id="refnum" name="refnum" type="text" value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">Date Paid:</td>
                        <td>
                            <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                <input class="form-control" id="datepaid" name="datepaid" data-mdb-toggle="datepicker" type="text" value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">Charge to Account:</td>
                        <td>
                            <div class="form-row">
                                <select id="bankid" class="select" name="bankid" type="text" value="">
                                    <?php

                                    $stmt = "SELECT id,shopid,bankname,accounttype,accountdescription,accountnumber,routingnumber,opendate ";
                                    $stmt .= " FROM bankaccount ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  AND acctstatus != 'Closed'";
                                    //echo $stmt;

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("s", $shopid);
                                        $query->execute();
                                        $bankresult = $query->get_result();
                                    } else {
                                        echo "Chart of Accounts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    if ($bankresult->num_rows > 0) {
                                        while ($bank = $bankresult->fetch_array()) {
                                            ?>
                                            <option value="<?= $bank["id"]; ?>"><?= $bank["bankname"]; ?><?= "-" . $bank["accounttype"]; ?></option>
                                            <?php
                                        }    // end of while
                                    } // end if
                                    ?>
                                </select>
                            </div>
                        </td>
                    </tr>

                </table>
            </div>

            <div class="modal-footer d-flex justify-content-center">
                <button onclick="postPymt()" type="button" class="btn btn-primary">Post Payment</button>
            </div>
        </div>
    </div>
</div>

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>

<script>
    $(document).ready(function () {
        $(".sbdatatable").dataTable({
            responsive: true,
            retrieve: true,
            searching: false,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            order: []
        });
    });

    function deleteExp() {
        var id = $("#id").val();
        var shopid = $("#shopid").val();

        sbconfirm(
            'Are you sure?',
            'This expense will be deleted.  Are you sure?',
            function () {
                showLoader();

                var ds = 'id=' + id + '&shopid=' + shopid;
                $.ajax({
                    type: "post",
                    url: "deleteexpense.php",
                    data: ds,
                    success: function () {
                        location.reload();
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        sbalert("Error in Deletion")
                    }
                });

                hideLoader()
            }
        );
    }

    function getFormattedDate(date) {
        var year = date.getFullYear();
        var month = (1 + date.getMonth()).toString();
        month = month.length > 1 ? month : '0' + month;
        var day = date.getDate().toString();
        day = day.length > 1 ? day : '0' + day;
        return month + '/' + day + '/' + year;
    }

    function editExp(id, shopid, name, amount, category, duedate, roid) {

        <?php
        if ($shopid == "demo") {
            echo "$('#expModal').modal('show')";
        } else {
            echo "$('#expModal').modal('show')";
        }
        ?>

        $('#id').val(id)
        $('#shopid').val(shopid)
        $('#expname2').val(name)
        $('#amount2').val(amount)
        $('#roid2').val(roid)
        $('#duedate2').val(duedate)
        $('#account2').val(category).toggleClass('active')
        $('#dueday').val(dueday)

    }

    function newExp(shopid) {

        $('#addexpModal').modal('show')

        $('#id').val(id)
        $('#shopid').val(shopid)

        var hoy = new Date(),
            mm = hoy.getMinutes(),
            d = hoy.getDate(),
            m = hoy.getMonth() + 1,
            y = hoy.getFullYear(),
            h = hoy.getHours(),
            data;

        if (d < 10) {
            d = "0" + d;
        }

        if (m < 10) {
            m = "0" + m;
        }

        d = m + "/" + d + "/" + y;

        $('#duedate').val(d)
    }

    function saveExp() {

        var id = $("#id").val();
        var shopid = $("#shopid").val();
        var name = $("#expname2").val();
        var amount = $("#amount2").val();
        var roid = $("#roid2").val();
        var duedate = $("#duedate2").val();
        var account = $("#account2").val();

        var ds = 'id=' + id + '&shopid=' + shopid + '&expensename=' + name + '&amount=' + amount + '&roid=' + roid + '&duedate=' + duedate + '&expensecategory=' + account;

        showLoader();

        $.ajax({
            type: "post",
            url: "editexpense.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()

    }

    function addexp() {

        $('#addexpbtn').attr("disabled", "true")
        var shopid = $("#shopid").val();
        var name = $("#expname").val();
        var paidto = $("#paidto").val();
        var amount = $("#amount").val();
        var roid = $("#roid").val();
        var duedate = $("#duedate").val();
        var account = $("#account").val();
        var recurring = $("#recurring").val();
        var dayofmonth = $("#dayofmonth").val();
        var reqtype = $("#type").val();
        var accountid = $("#accountid").val();

        var noworlater = "now";

        var ds = 'shopid=' + shopid + '&expensename=' + name + '&expensecategory=' + account + '&paidto=' + paidto + '&amount=' + amount + '&roid=' + roid + '&duedate=' + duedate + '&recurring=' + recurring + '&dayofmonth=' + dayofmonth + '&reqtype=' + reqtype + '&accountid=' + accountid;

        showLoader();

        $.ajax({
            type: "post",
            url: "addexpense.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()

    }

    function markPaid() {

        $('#payModal').modal('show')

    }

    function postPymt() {

        var id = $("#id").val();
        var shopid = $("#shopid").val();
        var datepaid = $("#datepaid").val();
        var paidwith = $("#paidwith").val();
        var ref = $("#refnum").val();
        var accountid = $("#bankid").val();
        var expensename = $("#expname2").val();
        var amount = $("#amount2").val();
        var expensecat = $("#account2").val();

        var ds = 'id=' + id + '&shopid=' + shopid + '&datepaid=' + datepaid + '&paidwith=' + paidwith + '&ref=' + ref + '&accountid=' + accountid + '&expensename=' + expensename + '&amount=' + amount + '&expensecat=' + expensecat;

        showLoader();

        $.ajax({
            type: "post",
            url: "postpayment.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()

    }

    function recurringPayment(v) {

        if (v == "Yes") {
            document.getElementById("dueday").style.display = ""
        }
        if (v == 'No') {
            $('#dueday').hide()
        }
    }

    function showBankAcct(v) {
        if (v == "Expense") {
            document.getElementById("bankaccount").style.display = ""
        }
        if (v == 'Bill') {
            $('#bankaccount').hide()
        }
    }
</script>

</body>

</html>
