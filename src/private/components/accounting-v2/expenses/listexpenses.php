<?php
/*
Expense is ALL Expenses that have not been paid!!!
Tranasaction is ALL Expenses older than 20 days paid or not!!!
*/

require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];

$today = new DateTime('now');
$todayStr = $today->format('m/d/Y');

$today2 = new DateTime('now');
$today2 = date_format($today2, 'Y-m-d');

$highdate = "";
$lowdate = "";
$searchby = "";

$date = new DateTime();

$dateminus20 = date('Y/m/d', strtotime("-20 day"));

$highdateminus60 = "";
$lowdateplus60 = "";
$ldplus60 = "";

if (isset($_POST['searchby'])) {
    $searchby = $_POST['searchby'];
}

if (isset($_POST['searchfor'])) {
    $searchfor = $_POST['searchfor'];
}

if (isset($_GET['highdate'])) {
    $date = $_GET['highdate'];

    $highdateminus60 = date('m/d/Y', strtotime($date . "-60 day"));
    $lowdateplus60 = date('m/d/Y', strtotime($date . "60 day"));
} else {
    $highdateminus60 = date('m/d/Y', strtotime("-60 day"));
}

if (isset($_GET['lowdate'])) {
    // if low date >= todays date populate todays date

    $date = $_GET['lowdate'];
    $lowdateplus60 = date('m/d/Y', strtotime($date));
    $ldplus60 = date('Y-m-d', strtotime($date));

    if ($ldplus60 >= $today2) {
        $lowdateplus60 = $todayStr;
    } else {
        $lowdateplus60 = date('m/d/Y', strtotime($date . "60 day"));
    }
}

// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";
$menuComponent = 'expenses';

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<style>
    main {
        height: 100% !important;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div >
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div class="flex-1">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/accounting.php" class="text-secondary">Accounting</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">View All Expenses</h2>
                    </div>
                </div>
            </div>
            <hr/>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <form action="listexpenses.php" method="post">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-row">
                            <?php
                            function isSelected ($value, $searchby) {
                                return $value == $searchby ? 'selected' : '';
                            }
                            ?>

                            <select id="searchby" class="select" name="searchby">
                                <option <?= isSelected('amount', $searchby) ?> value="amount">Amount</option>
                                <option <?= isSelected('expensename', $searchby) ?> value="expensename">Name</option>
                                <option <?= isSelected('paidto', $searchby) ?> value="paidto">Paid To</option>
                                <option <?= isSelected('expensecategory', $searchby) ?> value="expensecategory">Category</option>
                                <option <?= isSelected('datedue', $searchby) ?> value="datedue">Date Due</option>
                                <option <?= isSelected('paiddate', $searchby) ?> value="paiddate">Paid Date</option>
                            </select>
                            <label class="form-label select-label" for="searchby">Search for an Expense by</label>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-outline">
                            <input id="searchfor" name="searchfor" class="form-control" type="text" value="<?= $searchfor ?>"/>
                            <label class="form-label" for="searchfor">Search for</label>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <input class="btn btn-primary" name="Button2" type="submit" value="Search"/>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <table class="table table-sm">
                <tr>
                    <td style="height: 29px;text-align:left;width:20%" colspan="3">
                        <a style="text-decoration:none;color:#3366CC" href='listexpenses.php?highdate=<?= $highdateminus60 ?>'>
                            Previous
                            <i class="fa fa-arrow-left"></i>
                        </a>
                    </td>

                    <?php if (!$shopIsReadOnly): ?>
                        <td class="style11" style="height: 29px;text-align:center;width:60%" colspan="3">
                            Click any row to edit the transaction
                        </td>
                    <?php endif; ?>
                    
                    <td style="height: 29px;text-align:right;width:20%" colspan="3">
                        <a style="text-decoration:none;color:#3366CC" href='listexpenses.php?lowdate=<?= $lowdateplus60 ?>'>
                            Next
                            <i class="fa fa-arrow-right"></i>
                        </a>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <table class="sbdatatable w-100">
                <thead>
                <tr>
                    <th>Date Due</th>
                    <th>Date Paid</th>
                    <th>Paid To</th>
                    <th>Description</th>
                    <th>Account</th>
                    <th>Amount</th>
                    <th>Paid</th>
                    <th>Balance</th>
                </tr>
                </thead>
                <tbody>
                <?php

                $balamt = 0;

                if (isset($_GET['highdate'])) {
                    $date = new DateTime($_GET['highdate']);
                    $highdateminus60 = date_format($date, 'Y-m-d');

                    $lowdateplus60 = $highdateminus60;

                    // adding in filter on sum amount to match detail
                    $startsumdate = date_format($date, 'Y-m-d');
                    $endsumdate = date('Y-m-d', strtotime($startsumdate . "60 day"));

                    $stmt = "select id,shopid,expensename,expensecategory,amount,duedate,expensepaid,paidto,paiddate,paidwith,ref ";
                    $stmt .= " from `expenses` ";
                    $stmt .= " where shopid = ? ";
                    $stmt .= "  and duedate >= ? ";
                    $stmt .= "  and duedate <= date_add('$highdateminus60', interval 60 day) ";
                    $stmt .= "  order by duedate asc ";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("ss", $shopid, $highdateminus60);
                        $query->execute();
                        $expresult = $query->get_result();
                    } else {
                        echo "Expense else Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    $lowdateplus60 = $highdateminus60;

                    if ($lowdateplus60 >= $today) {
                        $lowdateplus60 = $todayStr;
                    }

                } elseif (isset($_GET['lowdate'])) {
                    $balamt = 0;

                    $date = new DateTime($_GET['lowdate']);
                    $lowdateplus60 = $date->format('Y-m-d');

                    // adding in filter on sum amount to match detail
                    $startsumdate = date_format($date, 'Y-m-d');
                    $endsumdate = date('Y-m-d', strtotime($startsumdate . "60 day"));

                    $stmt = "select id,shopid,expensename,expensecategory,amount,duedate,expensepaid,paidto,paiddate,paidwith,ref ";
                    $stmt .= " from `expenses` ";
                    $stmt .= " where shopid = ? ";
                    $stmt .= "  and duedate >= ? ";
                    $stmt .= "  and duedate <= date_add('$lowdateplus60', interval 60 day) ";
                    $stmt .= "  order by duedate asc ";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("ss", $shopid, $lowdateplus60);
                        $query->execute();
                        $expresult = $query->get_result();
                    } else {
                        echo "Expense else Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }
                } elseif (strlen($searchby) > 0) {

                    $duedate = "";
                    $stmt = "select id,shopid,expensename,expensecategory,amount,duedate,expensepaid,paidto,paiddate,paidwith,ref ";
                    $stmt .= " from `expenses` ";
                    $stmt .= " where shopid = ?";
                    if (($searchby == 'datedue') || ($searchby == 'paiddate')) {
                        $searchfor = date('Y-m-d', strtotime($searchfor));
                        //echo "Search for is : " . $searchfor;
                        $stmt .= "  and duedate  = ? ";
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("ss", $shopid, $searchfor);
                            $query->execute();
                            $expresult = $query->get_result();
                        } else {
                            echo "Expense duedate Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }
                    } else {
                        if ($searchby == 'paidto') {
                            $stmt .= "  and paidto  = ? ";
                        } elseif ($searchby == 'expensename') {
                            $searchfor = "%" . $searchfor . "%";
                            $stmt .= "  and expensename  like ? ";
                        } elseif ($searchby == 'expensecategory') {
                            $searchfor = "%" . $searchfor . "%";
                            $stmt .= "  and expensecategory  like ? ";
                        } elseif ($searchby == 'amount') {
                            $stmt .= "  and amount = ? ";
                        } else {
                            $searchfor = 'nothing';
                            $stmt .= "  and expensecategory  like ? ";
                        }

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("ss", $shopid, $searchfor);
                            $query->execute();
                            $expresult = $query->get_result();
                        } else {
                            echo "Expense searchby Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }
                    }
                } else {
                    $date = new DateTime($dateminus20);
                    $dateminus20 = date_format($date, 'Y-m-d');

                    // adding in filter on sum amount to match detail
                    $startsumdate = date_format($date, 'Y-m-d');
                    $endsumdate = $today2;

                    // new version of listexpenses with prepare
                    $stmt = "SELECT id,shopid,expensename,expensecategory,amount,duedate,expensepaid,paidto,paiddate,paidwith,ref ";
                    $stmt .= " FROM `expenses` ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND duedate >= ? ";
                    $stmt .= "  order by duedate asc ";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("ss", $shopid, $dateminus20);
                        $query->execute();
                        $expresult = $query->get_result();
                    } else {
                        echo "Expense else Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    $lowdate = $dateminus20;
                    $lowestdate = $dateminus20;
                }

                if ($expresult->num_rows > 0) {

                $rexp = mysqli_fetch_assoc($expresult);

                $lowdate = $rexp["duedate"];
                $texp = 0;

                $stmt = "select id ";
                $stmt .= " from `expenses` ";
                $stmt .= " where shopid = ? ";
                $stmt .= "  and duedate <= ? ";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ss", $shopid, $lowdate);
                    $query->execute();
                    $texpresult = $query->get_result();

                } else {
                    echo "Database texpenses prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                if ($texpresult->num_rows > 0) {
                    $prevtrans = "no";
                } else {
                    $prevtrans = "yes";
                }

                // resetting the result set pointer to beginning
                mysqli_data_seek($expresult, 0);
                ?>
                <?php

                $cntr = 1;
                $balamt = 0;

                while ($exp = mysqli_fetch_assoc($expresult)) {
                    $showduedate = $exp["duedate"];
                    $id = $exp["id"];
                    $balamt += $exp["amount"];
                    $duedate = new DateTime($exp["duedate"]);
                    $paiddate = new DateTime($exp["paiddate"]);

                    $newDate = $exp["paiddate"] == '0000-00-00' ? $todayStr : $paiddate->format('m/d/Y');

                    $onclickExp = !$shopIsReadOnly
                        ? "onclick=\"editExp(
                            '{$exp["id"]}',
                            '{$duedate->format('m/d/Y')}',
                            '{$newDate}',
                            '" . str_replace("'", "\\'", $exp["expensename"]) . "',
                            '" . str_replace("'", "\\'", $exp["paidto"]) . "',
                            '{$exp["amount"]}',
                            '{$exp["expensecategory"]}',
                            '{$exp["expensepaid"]}'
                        )\""
                        : "";
                    ?>
                    <tr style="cursor:pointer" <?= $onclickExp ?>>                        
                        <td>
                            <?php
                            if ($exp["duedate"] == "0000-00-00") {
                                $duedate = "";
                                echo $duedate;
                            } else {
                                $duedate = new DateTime($exp["duedate"]);
                                echo $duedate->format('m/d/Y');
                            }
                            ?>
                            &nbsp;
                        </td>
                        
                        <td>
                            <?php
                            if ($exp["paiddate"] == "0000-00-00") {
                                $paiddate = "";
                                echo $paiddate;
                            } else {
                                $paiddate = new DateTime($exp["paiddate"]);
                                echo $paiddate->format('m/d/Y');
                            }
                            ?>
                        </td>

                        <td><?= $exp["paidto"] ?>&nbsp;</td>
                        <td><?= $exp["expensename"] ?>&nbsp;</td>
                        <td><?= $exp["expensecategory"] ?>&nbsp;</td>
                        <td><?= asDollars($exp["amount"]) ?>&nbsp;</td>
                        <td><?= $exp["expensepaid"] ?></td>
                        <td><?= asDollars($balamt) ?> &nbsp;</td>
                    </tr>
                <?php }    // end of while
                } // end if
                ?>
                </tbody>
            </table>
        </div>
    </div>
</main>

<?php if (!$shopIsReadOnly): ?>
    <!-- Add Expense Modal -->
    <div id="addexpModal" class="modal fade" role="dialog" aria-hidden="true">
        <input id="shopid" name="shopid" value="<?= $shopid ?>" type="hidden">

        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Add New Expense</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <table class="table table-sm">
                        <tr>
                            <td class="text-left">Name/Description:</td>
                            <td>
                                <div class="form-outline">
                                    <input class="form-control" id="expname" name="expname" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Paid To:</td>
                            <td>
                                <div class="form-outline">
                                    <input class="form-control" id="paidto" name="paidto" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Amount:</td>
                            <td>
                                <div class="form-outline">
                                    <input class="form-control" id="amount" name="amount" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">RO Number (if applicable):</td>
                            <td>
                                <div class="form-outline">
                                    <input class="form-control" id="roid" name="roid" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Date Due:</td>
                            <td>
                                <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                    <input class="form-control" id="duedate" name="duedate" data-mdb-toggle="datepicker" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Account:</td>
                            <td>
                                <div class="form-row">
                                    <select id="account" class="select" name="account" type="text" value="">
                                        <?php
                                        $stmt = "SELECT id,shopid,category,cattype,core ";
                                        $stmt .= " FROM chartofaccounts ";
                                        $stmt .= "WHERE shopid = ? ";
                                        $stmt .= "  AND cattype = 'Expense' ";
                                        $stmt .= " ORDER BY category ";

                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("s", $shopid);
                                            $query->execute();
                                            $coaresult = $query->get_result();
                                        } else {
                                            echo "Chart of accounts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }

                                        if ($coaresult->num_rows > 0) {
                                            while ($coa = $coaresult->fetch_array()) {
                                                ?>
                                                <option value="<?= $coa["category"] ?>"><?= $coa["category"] ?></option>
                                                <?php
                                            }    // end of while
                                        } // end if
                                        ?>
                                    </select>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>Recurring?</td>
                            <td>
                                <select onchange="recurringPayment(this.value)" class="data" id="recurring" name="recurring">
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td style="font-size: 8px">If this is a recurring expense, select Yes and then select the day of month it is due</td>
                        </tr>
                        <tr style="display:none" id="dueday">
                            <td style="width: 268px">Day of Month to pay</td>
                            <td>
                                <div class="form-row">
                                    <select class="select" id="dayofmonth" name="dayofmonth">
                                        <?php
                                        $j = 1;
                                        while ($j >= 1 && $j <= 31) {
                                            ?>
                                            <option value="<?= $j ?>"><?= $j ?></option>
                                            <?php
                                            $j = $j + 1;
                                        }
                                        ?>
                                    </select>
                                </div>
                            </td>
                            <td>Select day of month to pay</td>
                        </tr>
                        <tr>
                            <td>Is this a bill to be paid later, or an
                                expense you have already paid?
                            </td>
                            <td>
                                <div class="form-row">
                                    <select onchange="showBankAcct(this.value)" class="select" id="type" name="type">
                                        <option value="Bill">Its a Bill to be Paid</option>
                                        <option value="Expense">Its an Expense That Has Been Paid</option>
                                    </select>
                                </div>
                            </td>
                            <td>If this is an expense, please select an account
                                to deduct the amount from.
                            </td>
                        </tr>
                        <tr id="bankaccount" style="display:none">
                            <td>Please select a Bank Account</td>
                            <td>
                                <?php
                                $stmt = "SELECT id,bankname  ";
                                $stmt .= "from bankaccount ";
                                $stmt .= "where shopid = ? ";
                                //echo $stmt;

                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("s", $shopid);
                                    $query->execute();
                                    $bankresult = $query->get_result();
                                } else {
                                    "Database bank acct prepare failed: (" . $conn->errno . ")" . $conn->error;
                                }

                                if ($bankresult->num_rows > 0) {
                                    ?>
                                    <div class="form-row">
                                        <select class="select" id="accountid" name="accountid">
                                            <?php
                                            while ($bank = $bankresult->fetch_array()) {
                                                ?>
                                                <option value="<?= $bank["id"] ?>"><?= $bank["bankname"] ?></option>
                                                <?php
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <?php
                                } else {
                                    echo "You have no bank accounts added.  To automatically deduct this from a bank account, please add a new bank account first";
                                }
                                ?>
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                    </table>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="addexp()" type="button" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Transaction Modal -->
    <div id="edittransModal" class="modal fade" role="dialog" aria-hidden="true">
        <input id="id" name="id" value="" type="hidden">
        <input id="shopid2" name="shopid2" value="<?= $shopid ?>" type="hidden">

        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Edit Transaction</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <table class="table table-sm">
                        <tr>
                            <td class="text-left">Date Due:</td>
                            <td>
                                <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                    <input class="form-control" id="duedate2" name="duedate2" data-mdb-toggle="datepicker" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Date Paid:</td>
                            <td>
                                <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                    <input class="form-control" id="datepaid2" name="datepaid2" data-mdb-toggle="datepicker" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Paid To:</td>
                            <td>
                                <div class="form-outline">
                                    <input class="form-control" id="paidto2" name="paidto2" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Description:</td>
                            <td>
                                <div class="form-outline">
                                    <input class="form-control" id="expname2" name="expname2" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Payment Amount:</td>
                            <td>
                                <div class="form-outline">
                                    <input class="form-control" id="amount2" name="amount2" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Category:</td>
                            <td>
                                <div class="form-row">
                                    <select id="account2" class="select" name="account2" type="text" value="">
                                        <?php
                                        $stmt = "select id,shopid,category,cattype,core ";
                                        $stmt .= "from chartofaccounts ";
                                        $stmt .= "where shopid = ?";
                                        $stmt .= "  and cattype = 'Expense'";
                                        $stmt .= " order by category";
                                        //echo $stmt;

                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("s", $shopid);
                                            $query->execute();
                                            $coaresult = $query->get_result();
                                        } else {
                                            "Database chart of accounts query failed: (" . $conn->errno() . ") " . $conn->errno;
                                        }

                                        if ($coaresult->num_rows > 0) {
                                            while ($coa = $coaresult->fetch_array()) {
                                                ?>
                                                <option value="<?= $coa["category"] ?>"><?= $coa["category"] ?></option>
                                                <?php
                                            }    // end of while
                                        } // end if
                                        ?>
                                    </select>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td class="text-left">Paid:</td>
                            <td>
                                <div class="form-row">
                                    <select class="select" id="exppaid2" name="exppaid2">
                                        <option value="yes">yes</option>
                                        <option value="no">no</option>
                                    </select>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="saveExp()" type="button" class="btn btn-primary">Save</button>
                    <button onclick="deleteExp('<?= $shopid ?>')" type="button" class="btn btn-secondary">Delete</button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>

<script>
    $(document).ready(function () {
        $(".sbdatatable").dataTable({
            responsive: true,
            retrieve: true,
            searching: false,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            order : []
        });
    });

    function editExp(id, duedate, paiddate, expensename, paidto, amount, expensecategory, expensepaid) {

        $('#edittransModal').modal('show')

        $('#id').val(id)
        $('#shopid').val(shopid)
        $('#duedate2').val(duedate)
        $('#datepaid2').val(paiddate)
        $('#paidto2').val(paidto)
        $('#amount2').val(amount)
        $('#expname2').val(expensename)
        $('#account2').val(expensecategory).toggleClass('active')
        $('#exppaid2').val(expensepaid).toggleClass('active')
    }

    function saveExp() {

        var id = $("#id").val();
        var shopid = $("#shopid2").val();
        var name = $("#expname2").val();
        var amount = $("#amount2").val();
        var duedate = $("#duedate2").val();
        var account = $("#account2").val();

        var expensepaid = $('#exppaid2').val();
        var paidto = $('#paidto2').val();
        var paiddate = $('#datepaid2').val();

        var ds = 'id=' + id + '&shopid=' + shopid + '&expensename=' + name + '&amount=' + amount + '&duedate=' + duedate + '&expensecategory=' + account + '&expensepaid=' + expensepaid + '&paidto=' + paidto + '&paiddate=' + paiddate;

        $.ajax({
            type: "post",
            url: "editexpense.php",
            data: ds,
            success: function () {
                sbconfirm(
                    'Success',
                    'Save Successful',
                    function () {
                        location.reload();
                    }
                );
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });
    }

    function cancelTrans() {

        $('#duedate').val("")
        $('lowdate').val("")
        $('highdate').val("")
        $('paidto').val("")
        $('amount').val("")
        $('expensecategory').val('Cost of Goods Sold')
        $('popup').hide()
        document.getElementById("popuphider").style.display = "none"
        document.trans.transid.value = ""

    }


    function newExp(shopid) {
        $('#addexpModal').modal('show')
    }


    function addexp() {
        var shopid = $("#shopid").val();
        var name = $("#expname").val();
        var paidto = $("#paidto").val();
        var amount = $("#amount").val();
        var roid = $("#roid").val();
        var duedate = $("#duedate").val();
        var account = $("#account").val();
        var recurring = $("#recurring").val();
        var dayofmonth = $("#dayofmonth").val();
        var reqtype = $("#type").val();
        var accountid = $("#accountid").val();

        var noworlater = "now";

        var ds = 'shopid=' + shopid + '&expensename=' + name + '&expensecategory=' + account + '&paidto=' + paidto + '&amount=' + amount + '&roid=' + roid + '&duedate=' + duedate + '&recurring=' + recurring + '&dayofmonth=' + dayofmonth + '&reqtype=' + reqtype + '&accountid=' + accountid;

        $.ajax({
            type: "post",
            url: "addexpense.php",
            data: ds,
            success: function () {
                sbconfirm(
                    'Success',
                    'Save Successful',
                    function () {
                        location.reload();
                    }
                );
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });
    }

    function deleteExp(shopid) {
        var id = $("#id").val();

        sbconfirm(
            'Are you sure?',
            'This expense will be deleted.  Are you sure?',
            function () {
                var ds = 'id=' + id + '&shopid=' + shopid;
                $.ajax({
                    type: "post",
                    url: "deleteexpense.php",
                    data: ds,
                    success: function () {
                        sbconfirm(
                            'Success',
                            'Delete Successful',
                            function () {
                                location.reload();
                            }
                        );
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        sbalert("Error in Delete")
                    }
                });
            },
        );
    }

    function showBankAcct(v) {
        if (v == "Expense") {
            document.getElementById("bankaccount").style.display = ""
        }
        if (v == 'Bill') {
            $('#bankaccount').hide()
        }
    }
</script>
</body>
<?php
mysqli_close($conn);
?>
</html>
