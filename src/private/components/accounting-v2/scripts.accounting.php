<script type="text/javascript" async="false">
    function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
    }

    const readonly = getCookie("readonly");

    const shopIsReadOnly = readonly === "yes";

    function editAccount(shopid, roid) {
        !shopIsReadOnly &&
            eModal.iframe({
                title: 'Edit Account',
                url: 'ar/editaccount.php?shopid=' + shopid + '&roid=' + roid + '&hideheader=1',
                size: eModal.size.xl,
            });
    }

    function editpsAccount(shopid, psid) {
        eModal.iframe({
            title: 'Edit Part Sale',
            url: 'ar/editpsaccount.php?shopid=' + shopid + '&psid=' + psid,
            size: eModal.size.xl,
        });
    }

    function saveExp() {

        showLoader();

        var id = $("#id").val();
        var shopid = $("#shopid").val();
        var name = $("#expname").val();
        var amount = $("#amount").val();
        var roid = $("#roid").val();
        var duedate = $("#duedate").val();
        var account = $("#account").val();


        var ds = 'id=' + id + '&shopid=' + shopid + '&expensename=' + name + '&amount=' + amount + '&roid=' + roid + '&duedate=' + duedate + '&expensecategory=' + account;

        $.ajax({
            type: "post",
            url: "../accounting/expenses/editexpense.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()
    }

    function deleteExp() {
        showLoader();

        var id = $("#id").val();
        var shopid = $("#shopid").val();

        sbconfirm(
            'Are you sure?',
            'This expense will be deleted. Are you sure?',
            function () {
                var ds = 'id=' + id + '&shopid=' + shopid;
                $.ajax({
                    type: "post",
                    url: "../accounting/expenses/deleteexpense.php",
                    data: ds,
                    success: function () {
                        location.reload();
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        sbalert("Error in Deletion")
                    }
                });
            }
        );

        hideLoader()
    }

    function editExp(id, shopid, name, amount, category, duedate, roid) {

        $("#id").val(id)
        $("#shopid").val(shopid)
        $("#expname").val(name)
        $("#amount").val(amount)
        $("#roid").val(roid)
        $("#duedate").val(duedate)
        $("#account").val(category).toggleClass('active')

        $('#expModal').modal('show')
    }

    function saveBankAccount(shopid) {
        // note the balance has to be saved to the account register
        showLoader();

        var bankname = $("#bankname").val();
        var accttype = $("#accounttype").val();
        var acctdesc = $("#accountdescription").val();
        var acctnum = $("#accountnumber").val();
        var routenum = $("#routingnumber").val();
        var dateopen = $("#opendate").val();
        var begbal = $("#beginningbalance").val();


        var ds = 'bankname=' + bankname + '&accounttype=' + accttype + '&accountdescription=' + acctdesc + '&accountnumber=' + acctnum + '&routingnumber=' + routenum + '&opendate=' + dateopen + '&shopid=' + shopid + '&begbal=' + begbal;

        $.ajax({
            type: "post",
            url: "bankaccount/savebank.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Insert")
            }
        });

        hideLoader();
    }

    function resetTop() {
        $('#main-container').css('margin-top', '0')
        $.ajax({url: "hidecapital.php"});
    }
</script>
