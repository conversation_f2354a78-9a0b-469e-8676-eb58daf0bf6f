<?php

$component = "accounting-v2";
$shopid = $_COOKIE['shopid'];

require CONN;
include getHeadGlobal('');

$today = new DateTime('now');

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$disabled = $shopIsReadOnly ? "disabled" : "";
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
<main id="settings" class="min-vh-100">
    <div>
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div class="flex-1">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/accounting.php"
                           class="text-secondary">Accounting</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">Unposted Expenses
                            <i class="fa fa-circle-info" title="Please select all expenses you wish to post, select the expense category
						and the appropriate bank to post the amounts to, then click Post Expenses. NOTE: Only 100 records are shown at a time" data-mdb-toggle="tooltip"></i>
                        </h2>
                    </div>
                </div>
                <?php if( !$shopIsReadOnly ): ?>
                    <div class="flex-1">
                        <button class="btn btn-primary float-end" type="button" id="postexpbtn" onclick="postExpenses()">Post All Expenses</button>
                        <button onclick="deleteallExpenses()" class="btn btn-secondary mx-2 float-end" name="Button2" type="button">Delete</button>
                    </div>
                <?php endif; ?>
            </div>
            <hr/>
        </div>
    </div>
    <div class="container-fluid">
        <div class="row mt-4">
            <div class="col-md-12">

                <form id="mainform" name="mainform" method="post" action="unpostedexpenses.php">
                    <input name="sub" type="hidden"/>
                    <table class="sbdatatable w-100">
                        <thead>
                        <tr>
                            <?php if( !$shopIsReadOnly ): ?>
                                <th class="text-center">
                                    <input type="checkbox" class="form-check-input" onclick="checkAll()" id="master" checked/>
                                </th>
                            <?php endif; ?>

                            <th>Date</th>
                            <th>Category</th>
                            <th>Paid To</th>
                            <th>How Paid</th>
                            <th>Ref #</th>
                            <th>Memo</th>
                            <th>RO #/PS #</th>
                            <th>Amount</th>
                            <th>Post To</th>

                            <?php if( !$shopIsReadOnly ): ?>
                                <th class="text-center">Delete</th>
                            <?php endif; ?>
                        </tr>
                        </thead>
                        <tbody>
                        <?php

                        $stmt = "SELECT id,amount,`category`,memo,udate,roid,paidto ";
                        $stmt .= "FROM unpostedexpenses ";
                        $stmt .= "WHERE shopid = ? order by udate desc ";
                        $stmt .= " LIMIT 100 ";
                        //echo $stmt;

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("s", $shopid);
                            $query->execute();
                            $uperesult = $query->get_result();
                        } else {
                            echo "Undeposited Expenses Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }

                        //echo "Number of rows " . $numofrows;
                        $runningtotal = 0;
                        $idlist = "";
                        $showbtn = false;

                        if ($uperesult->num_rows) {
                        $showbtn = true;

                        while ($upe = mysqli_fetch_assoc($uperesult)) {
                            $runningtotal = $runningtotal + $upe["amount"];
                            $transdate = new DateTime($upe["udate"]);
                            ?>
                            <input type="hidden" name="transdate<?php echo $upe["id"]; ?>" value="<?php echo $upe["udate"]; ?>">
                            <tr>
                                <?php if( !$shopIsReadOnly ): ?>
                                    <td class="text-center">
                                        <input value="on" checked="checked" name="post<?php echo $upe["id"]; ?>" class="form-check-input" type="checkbox"/>
                                    </td>
                                <?php endif; ?>
                                <td id="transdate"><?php echo date_format($transdate, 'm/d/Y'); ?></td>
                                <td id="category">
                                    <select class="select data" name="category<?php echo $upe["id"]; ?>" <?php echo $disabled; ?>>
                                        <?php

                                        $stmt = "SELECT category ";
                                        $stmt .= "FROM chartofaccounts ";
                                        $stmt .= "WHERE shopid = ? ";
                                        $stmt .= "  AND cattype like 'Expense%'";
                                        //echo $stmt;

                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("s", $shopid);
                                            $query->execute();
                                            $cofaresult = $query->get_result();
                                        } else {
                                            echo "Chart of Accts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }

                                        $numofrows = mysqli_num_rows($cofaresult);

                                        if ($numofrows > 0) {
                                            while ($cofa = mysqli_fetch_assoc($cofaresult)) {
                                                if ($cofa["category"] == $upe["category"]) {
                                                    $cs = "selected='selected'";
                                                } else {
                                                    $cs = "";
                                                }
                                                ?>
                                                <option <?php echo $cs; ?>value="<?php echo $cofa["category"]; ?>"><?php echo $cofa["category"]; ?></option>
                                                <?php
                                            } //end of chart of accounts while
                                        } //end of chart of accounts if
                                        ?>
                                    </select>
                                </td>
                                <td>
                                    <input class="form-control data"
                                        id="paidto"
                                        name='paidto<?php echo $upe["id"]; ?>'
                                        size="10"
                                        value="<?php echo $upe["paidto"]; ?>"
                                        type="text"
                                        style="width: 152px"
                                        <?= $disabled; ?>
                                    />
                                </td>
                                <td id="howpaid">
                                    <select class="select data" name="howpaid<?php echo $upe["id"]; ?>" <?= $disabled; ?>>
                                        <?php
                                        $stmt = "SELECT method ";
                                        $stmt .= " FROM paymentmethods ";
                                        $stmt .= "WHERE shopid = ? ";
                                        //echo $stmt;

                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("s", $shopid);
                                            $query->execute();
                                            $pmresult = $query->get_result();
                                        } else {
                                            echo "Payment Methods Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }

                                        if ($pmresult->num_rows > 0) {
                                            while ($pm = mysqli_fetch_assoc($pmresult)) {
                                                ?>
                                                <option value="<?php echo $pm["method"]; ?>"><?php echo $pm["method"]; ?></option>
                                                <?php
                                            } //end of payment methods while
                                        } //end of payment methods if
                                        ?>
                                    </select>
                                </td>
                                <td>
                                    <input class="form-control data"
                                        name="ref<?php echo $upe["id"]; ?>"
                                        size="10"
                                        type="text"
                                        style="width: 55px"
                                        <?= $disabled; ?>
                                    />
                                </td>

                                <td><?php echo $upe["memo"]; ?></td>
                                <td class="style9"><?php echo $upe["roid"]; ?></td>
                                <td id="depositamount" class="style7"><?php echo asDollars($upe["amount"], 2); ?></td>
                                <td class="style7">

                                    <?php

                                    $stmt = "SELECT id, bankname ";
                                    $stmt .= "FROM bankaccount ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  AND acctstatus != 'Closed' order by isdefault desc";
                                    //echo $stmt;

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("s", $shopid);
                                        $query->execute();
                                        $bankresult = $query->get_result();
                                    } else {
                                        echo "Bank Account Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    if ($bankresult->num_rows > 0) {
                                        ?>
                                        <select class="select data" name="bankaccount<?php echo $upe["id"]; ?>" <?= $disabled; ?>>

                                            <?php while ($bank = mysqli_fetch_assoc($bankresult)) { ?>
                                                <option value="<?php echo $bank["id"]; ?>"><?php echo $bank["bankname"]; ?></option>
                                            <?php } ?>
                                        </select>

                                    <?php } else { ?>
                                        <a style="color:#336699" href="#" onclick="$('#bankmodal').modal('show')" data-target="#bankModal" data-toggle="modal">
                                            No bank accounts available to apply expenses. Click Here to Add</a>
                                    <?php } ?>
                                </td>

                                <?php if( !$shopIsReadOnly ): ?>
                                    <td class="text-center text-primary">
                                        <i onclick="deleteExpense('<?php echo $shopid; ?>','<?php echo $upe["id"]; ?>')" class="fas fa-trash"></i>
                                    </td>
                                <?php endif; ?>
                            </tr>
                            <?php
                            $idlist = $idlist . $upe["id"] . ",";
                        } // end of upe whileloop
                        ?>
                        </tbody>
                        <tfoot>
                        <tr>
                            <?php if( !$shopIsReadOnly ): ?>
                                <td></td>
                            <?php endif; ?>
                            
                            <td colspan="6"></td>
                            <td>Total</td>
                            <td><b><?php echo asDollars($runningtotal, 2); ?></b></td>
                            <td></td>

                            <?php if( !$shopIsReadOnly ): ?>
                                <td class="style8"></td>
                            <?php endif; ?>
                        </tr>
                        </tfoot>
                        <?php } ?>
                    </table>
                    <input name="idlist" value="<?php echo $idlist; ?>" type="hidden"/>
                </form>
            </div>
        </div>
    </div>
</main>

<?php if( !$shopIsReadOnly ): ?>
    <div id="expModal" class="modal fade" role="dialog">
        <input id="id" name="id" value="" type="hidden">
        <input id="shopid" name="shopid" value="" type="hidden">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Edit Expense </h4>
                </div>
                <div class="modal-body">

                    <table class="table table-condensed table-striped">
                        <tr>
                            <td class="text-left">Name/Description:</td>
                            <td class="style9">
                                <input class="form-control" id="expname" name="expname" type="text" value=""/></td>
                        </tr>
                        <tr>
                            <td class="text-left">Amount:</td>
                            <td class="style9"><input class="form-control" id="amount" name="amount" type="text" value=""/>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">RO Number (if applicable):</td>
                            <td class="style9"><input class="form-control" id="roid" name="roid" type="text" value=""/></td>
                        </tr>
                        <tr>
                            <td class="text-left">Date Due:</td>
                            <td><input id="duedate" type="text"/></td>
                        </tr>
                        <tr>
                            <td class="text-left">Account:</td>
                            <td class="style9">
                                <select id="account" class="select" name="account" type="text" value="">
                                    <?php
                                    $stmt = "SELECT id,shopid,category,cattype,core ";
                                    $stmt .= " FROM chartofaccounts ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  AND cattype = 'Expense' ";
                                    $stmt .= " ORDER BY category ";

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("s", $shopid);
                                        $query->execute();
                                        $coaresult = $query->get_result();
                                    } else {
                                        echo "Chart of accounts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    if ($coaresult->num_rows > 0) {
                                        while ($coa = $coaresult->fetch_array()) {
                                            ?>
                                            <option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
                                            <?php
                                        }    // end of while
                                    } // end if
                                    ?>
                                </select>
                            </td>
                        </tr>

                    </table>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="saveExp()" type="button" class="btn btn-primary">Save</button>
                    <button onclick="deleteExp()" type="button" class="btn btn-danger">Delete</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div id="bankmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <input id="customerid" type="hidden">
        <div class="modal-dialog modal-md">
            <div class="modal-content p-4">
                <div class="modal-header">
                    <h5 class="modal-title" id="spdLabel">Add New Bank Account</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-outline mb-4">
                        <input class="form-control" id="bankname" name="bankname" type="text"/>
                        <label class="form-label">Bank Name</label>
                    </div>
                    <div class="form-row mb-4">
                        <select class="select" id="accounttype" name="accounttype">
                            <option value="Checking">Checking</option>
                            <option value="Saving">Saving</option>
                            <option value="Credit Card">Credit Card</option>
                            <option value="Money Market">Money Market</option>
                            <option value="Certificate of Deposit">Certificate of Deposit
                            </option>
                        </select>
                        <label class="form-label select-label">Account Type</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" id="accountdescription"
                            name="accountdescription" type="text" value=""/>
                        <label class="form-label">Description</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" id="accountnumber" name="accountnumber"
                            type="text" value=""/>
                        <label class="form-label">Account Number</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" id="routingnumber" name="routingnumber"
                            type="text" value=""/>
                        <label class="form-label">Routing Number</label>
                    </div>
                    <div class="form-outline mb-4 datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                        <input class="form-control" id="opendate" name="opendate" type="text" data-mdb-toggle="datepicker"
                            value="<?php echo date_format($today, 'm/d/Y'); ?>"/>
                        <label class="form-label">Date Opened</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" id="beginningbalance" name="beginningbalance"
                            type="text" value=""/>
                        <label class="form-label">Begining Balance</label>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="saveBankAccount('<?php echo $shopid; ?>')" type="button"
                            class="btn btn-primary">Save
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
$component = '';
include getScriptsGlobal('');
// include getFooterComponent($component);
?>

<script>
    $(document).ready(function () {
        $(".sbdatatable").dataTable({
            responsive: true,
            fixedHeader: {
                headerOffset: 68
            },
            searching: false,
            colReorder: true,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            columnDefs: [{
                targets: 0,
                orderable: false
            }],
            order: [[1, "asc"]]
        });
    });

    function checkAll() {

        if ($("#master").is(':checked')) {
            $('input[type=checkbox]').each(function () {
                $(this).prop('checked', true);
            });
        } else {
            $('input[type=checkbox]').each(function () {
                $(this).prop('checked', false);
            });
        }
    }


    function postExpenses() {
        var s = $("#mainform").serialize();
        var sub = "yes";

        $.ajax({
            type: "post",
            url: "<?= COMPONENTS_PRIVATE ?>/accounting/expenses/postexpenses.php",
            data: s + "&sub=" + sub + "&shopid=<?php echo $shopid; ?>",
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Insert")
            }
        });
    }

    function deleteallExpenses() {
        var s = $("#mainform").serialize();

        sbconfirm("Are you sure?", "Are you sure you want to DELETE ALL EXPENSES?", function () {
            var sub = "yes";
            showLoader();
            $.ajax({
                type: "post",
                url: "<?= COMPONENTS_PRIVATE ?>/accounting/expenses/deleteallexpenses.php",
                data: s + "&sub=" + sub + "&shopid=<?php echo $shopid; ?>",
                success: function () {
                    location.reload();
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    sbalert("Error in mass delete")
                    hideLoader();
                }
            });
        });
    }

    function deleteExpense(shopid, id) {
        sbconfirm("Are you sure?", "Are you sure you want to delete this expense?", function () {
            showLoader();
            var ds = 'shopid=' + shopid + '&expenseid=' + id;
            $.ajax({
                type: "post",
                url: "deleteexpense.php",
                data: ds,
                success: function () {
                    location.reload();
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    sbconfirm("Error in Deletion")
                    hideLoader();
                }
            });
        });
    }

    function editExp(id, shopid, name, amount, category, duedate, roid) {
        $('#expModal').modal('show')

        $('#id').val(id)
        $('#shopid').val(shopid)
        $('#expname').val(name)
        $('#amount').val(amount)
        $('#roid').val(roid)
        $('#duedate').val(duedate)
        $('#account').val(category)
    }

    function saveBankAccount(shopid) {
        // note the balance has to be saved to the account register
        var bankname = $("#bankname").val();
        var accttype = $("#accounttype").val();
        var acctdesc = $("#accountdescription").val();
        var acctnum = $("#accountnumber").val();
        var routenum = $("#routingnumber").val();
        var dateopen = $("#opendate").val();
        var begbal = $("#beginningbalance").val();

        var ds = 'bankname=' + bankname + '&accounttype=' + accttype + '&accountdescription=' + acctdesc + '&accountnumber=' + acctnum + '&routingnumber=' + routenum + '&opendate=' + dateopen + '&shopid=' + shopid + '&begbal=' + begbal;
        showLoader();
        $.ajax({
            type: "post",
            url: "bankaccount/savebank.php",
            data: ds,
            success: function () {
                sbconfirm("Success", "Bank Account Created", function () {
                    location.reload();
                });
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Insert")
            }
        });
    }

    <?php
    if ($showbtn == false){
    ?>
    $(document).ready(function () {
        $('#postexpbtn').hide()
    });
    <?php
    }
    ?>
</script>
</body>
</html>
