<?php

$shopid = $_COOKIE['shopid'];

// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";
$menuComponent = 'ar';

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);

?>

<style>
    main {
        height: 100% !important;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div >
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div class="flex-1">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/ar/ar.php" class="text-secondary">Accounts Rec. (A/R)</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">
                            Manage On Hold Status
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Please select a customer below to modify their On Hold status"></i>
                        </h2>
                    </div>
                </div>
            </div>
            <hr/>
        </div>
    </div>

    <div class="row">
        <div class="col-md-10"></div>
        <div class="col-md-2">
            <input type='text' class="form-control" name="search" id="search" placeholder="Search">
        </div>
    </div>

    <div style="height:100%; width:100%" id="txtHint"></div>
</main>

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>

<script async="false">
    function changeHold(type, cid) {
        if ( !shopIsReadOnly ){
            $.ajax({
                data: "type=" + type + "&shopid=<?php echo $shopid; ?>&cid=" + cid,
                url: "customerhold.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {

                        if (type == "add") {
                            holdtdcontent = "<span onclick='changeHold(\"clear\"," + cid + ")' class='btn btn-secondary'>Remove Hold</span>"
                        } else {
                            holdtdcontent = "<span onclick='changeHold(\"add\"," + cid + ")' class='btn btn-primary'>Place on Hold</span>"
                        }

                        $('#holdtd' + cid).html(holdtdcontent)
                    }
                }
            });
        }
    }

    $(document).ready(function () {
        
        $('#search').on('keyup', function () {
           loadData(this.value);
        });

        loadData('')
    });

    function loadData(sf)
    {
       showLoader()
       ds = "sf=" + sf + "&shopid=<?php echo $shopid; ?>"
       $.ajax({
        data: ds,
        url: "customerdataonhold.php",
        success: function (r) {
            $('#txtHint').html(r)
            hideLoader()
        }
       }); 
    }
</script>
</body>
<?php
mysqli_close($conn);
?>

</html>
