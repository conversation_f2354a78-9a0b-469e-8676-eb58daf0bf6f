<?php

$shopid = $_COOKIE['shopid'];

if ($shopid == "1734") {
    redirect_to("1734ar.php");
}

$roid = "";
if (isset($_GET['roid'])) {
    $roid = $_GET['roid'];
}
if (isset($_GET['sf'])) {
    $sf = $_GET['sf'];
}

// Get value passed from the apply filter input
if (isset($_GET['n'])) {
    $searchstring = $_GET['n'];
} else {
    $searchstring = "";
}

if ((isset($_GET['sb'])) && ($_GET['sb'] == "ro")) {
    $sort = "ro";
} elseif ((isset($_GET['sb'])) && ($_GET['sb'] == "date")) {
    $sort = "date";
} elseif ((isset($_GET['sb'])) && ($_GET['sb'] == "days")) {
    $sort = "days";
} elseif ((isset($_GET['sb'])) && ($_GET['sb'] == "customer")) {
    $sort = "customer";
} elseif ((isset($_GET['sb'])) && ($_GET['sb'] == "total")) {
    $sort = "total";
} else {
    $sort = "";
}

// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";
$menuComponent = 'ar';

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);
?>
<script>
    function startStmts() {
        $("#datepromptmodal").modal('show');
    }

    function cancelStmts() {
        $('#hider').hide()
        $('#stmtdate').hide()
        $('#stmtbutton').hide()
        $('#stmtframe').hide()
        $('#btndiv').hide()
        document.getElementById("stmtframe").src = ""
    }

    function runStmts() {

        var sd = $("#sd").val();
        var ed = $("#ed").val();
        url = '<?= COMPONENTS_PRIVATE ?>/v2/accounting/ar/statementsforpdf.php?sd=' + sd + '&ed=' + ed + '&shopid=<?php echo $shopid ?>'
        /*


        document.getElementById("stmtframe").src = url
        $('#stmtbutton').show()
        $('#stmtframe').show()
        $('#btndiv').show()
        $('#stmtdate').hide()
        */
        eModal.iframe({
            title: 'A/R Statement',
            url: url,
            size: eModal.size.xl,
            buttons: [
                {
                    text: 'Print',
                    style: 'primary',
                    close:false,
                    click: function(){
                        $('#emodal-box iframe').attr("id","stmtframe")
                        var printdiv = document.getElementById("stmtframe")
                        console.log(printdiv)
                        printdiv.focus()
                        printdiv.contentWindow.print()
                      //  printStmts();
                    }
                },
                {text: 'Close', style: 'secondary', close:true}
            ]

        });

    }

    function printStmts() {
        document.getElementById("stmtframe").contentWindow.print()
    }
</script>

<style>
    main {
        height: 100% !important;
    }

    .dataTables_filter {
        display: none !important;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div>
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div class="flex-1">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/accounting.php" class="text-secondary">Accounting</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">
                            Accounts Receivable (A/R)
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="The following invoices are on account and have not been paid.
                            To print a statement for a single account, click the RO# for that customer,then click Statement."></i>
                        </h2>
                    </div>
                </div>

                <div class="flex-1">
                    <div class="float-end">
                        <div class="form-outline">
                            <input type="text" id="search" class="form-control" onkeyup="searchTables(this.value)"/>
                            <label class="form-label" for="search">Search</label>
                        </div>
                    </div>
                    <div class="float-end">
                        <button data-mdb-toggle="modal" data-mdb-target="#datepromptmodal" class="btn ms-2 me-2 btn-primary">Print</button>
                    </div>
                </div>
            </div>
            <hr/>
        </div>
    </div>

    <div id="hider"></div>
    <div id="btndiv"
         style="background-color: var(--bodybackground);text-align:center;height:65px;display:none;z-index:1500;position:absolute;width:100%;left:0px;top:0px">
        <br>
        <button type="button" class="btn btn-secondary" onclick="cancelStmts()"
                id="stmtbutton">Close
        </button>
        <button type="button" onclick="printStmts()" class="btn btn-primary"
                id="printstmtbutton">Print
        </button>
    </div>



    <div id="datepromptmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-md">
            <div class="modal-content p-4">
                <div class="modal-header">
                    <h5 class="modal-title">Select Start and End Dates</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <div class="form-outline datepicker mb-4" data-mdb-inline="true"
                         data-mdb-format="mm/dd/yyyy">
                        <input type="text" class="form-control" id="sd" name="sd"
                               data-mdb-toggle="datepicker">
                        <label class="form-label" for="sd">Select Start Date</label>
                    </div>
                    <div class="form-outline datepicker mb-4" data-mdb-inline="true"
                         data-mdb-format="mm/dd/yyyy">
                        <input type="text" class="form-control" id="ed" name="ed"
                               data-mdb-toggle="datepicker">
                        <label class="form-label" for="sd">Select End Date</label>
                    </div>
                    <div class="form-row mp-4">
                        <div class="col-md-5">
                            <div class="dropdown pb-3">
                                <button class="btn btn-clear dropdown-toggle" href="" type="button" id="dropdownMenuButton" data-mdb-toggle="dropdown" aria-expanded="false" style="">
                                    Date Range <i class="fa fa-chevron-down"></i>
                                </button>
                                <ul class="dropdown-menu" id="dateRangeDropDown" aria-labelledby="dropdownMenuButton" style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate3d(0px, 38px, 0px);" data-popper-placement="bottom-start" data-mdb-popper="null">
                                    <li><a data-range="thisWeek" class="dropdown-item" href="#">This Week</a></li>
                                    <li><a data-range="thisMonth" class="dropdown-item" href="#">This Month</a></li>
                                    <li><a data-range="thisYear" class="dropdown-item" href="#">This Year</a></li>
                                    <li>
                                        <hr class="dropdown-divider">
                                    </li>
                                    <li><a data-range="lastWeek" class="dropdown-item" href="#">Last Week</a></li>
                                    <li><a data-range="lastMonth" class="dropdown-item" href="#">Last Month</a></li>
                                    <li><a data-range="lastYear" class="dropdown-item" href="#">Last Year</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <button class="btn btn-primary" type="button" onclick="runStmts()" data-mdb-dismiss="modal">
                        Run Statements
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="table-container-ar" class="table-responsive">
        <table id="arlist-table" class="sbdatatable">
            <thead>
            <tr>
                <th>RO #</th>
                <th>Date Complete</th>
                <th>Days</th>
                <th>Customer Home/Work/Cell</th>
                <th>Total RO</th>
                <th>Balance</th>
            </tr>
            </thead>
            <tbody>
            <?php
            //********* get ro's with balances *************

            $stmt = "SELECT storagefee,onhold,repairorders.customerid,totalprts,totallbr,totalsublet,userfee1,userfee2,userfee3";
            $stmt .= ",hazardouswaste,salestax,discountamt,rotype,totalfees";
            $stmt .= ",roid,statusdate,lastfirst,customerphone,repairorders.cellphone,customerwork,totalro,balance,repairorders.shopid ";
            $stmt .= "from repairorders ";
            $stmt .= "left join customer ";
            $stmt .= "  on repairorders.shopid = customer.shopid ";
            $stmt .= "  and repairorders.customerid = customer.customerid ";
            $stmt .= "WHERE repairorders.shopid = ? ";
            $stmt .= "  AND balance > 0.01 ";
            $stmt .= "  AND status = 'CLOSED' ";
            $stmt .= "  AND rotype != 'No Problem' and rotype != 'No Approval' ";

            if (isset($_GET['n'])) {
                $searchstatus = "%" . $_GET['n'] . "%";
                $stmt .= " AND lastfirst like ? ";
            }
            if ($sort == "ro") {
                $stmt .= " order by roid desc";
            } elseif ($sort == "date") {
                $stmt .= " order by statusdate desc";
            } elseif ($sort == "days") {
                $stmt .= " order by datediff(curdate(),statusdate) desc";
            } elseif ($sort == "customer") {
                $stmt .= " order by customerlast asc";
            } elseif ($sort == "total") {
                $stmt .= " order by totalro desc";
            } else {
                $stmt .= " order by balance desc ";
            }

            if (isset($_GET['n'])) {
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ss", $shopid, $searchstatus);
                    $query->execute();
                    $result = $query->get_result();
                } else {
                    echo "Repair Orders Prepare 1 failed: (" . $conn->errno . ") " . $conn->error;
                }
            } else {
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $result = $query->get_result();
                } else {
                    echo "Repair Orders Prepare 2 failed: (" . $conn->errno . ") " . $conn->error;
                }

                //printf(str_replace("?","'"."%s"."'",$stmt),$shopid);

            }

            //echo $query;
            $bal = 0;

            $numofrows = mysqli_num_rows($result);

            $c = 0;
            $statusdate = "";
            $days = "";
            $lf = "";
            $runningbal = 0;
            $locationstring = "";

            // Check for data
            if ($numofrows > 0) {

                while ($row = mysqli_fetch_assoc($result)) {
                    $roid = $row["roid"];

                    $shopid = $row["shopid"];
                    $dstart = null;
                    //meant to be when the customer is paying on "Account" meaning they will pay later.  generally not used at all anymore
                    $stmt = "SELECT sum(amt) as a ";
                    $stmt .= "from accountpayments ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND roid = ?";
                    //$stmt .= "  AND ptype != 'Account' ";
                    //echo $stmt;

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $roid);
                        $query->execute();
                        $apresult = $query->get_result();
                    } else {
                        echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    $aprow = $apresult->fetch_array();

                    $ap_amount = $aprow['a'];

                    if ($ap_amount > 0) {
                        $ropayments = $ap_amount;
                    } else {
                        $ropayments = 0;
                    }

                    $stmt = "select coalesce(sum(feeamount),0) from rofees where shopid = '$shopid' and roid = $roid";
                    if ($query = $conn->prepare($stmt)){
                        $query->execute();
                        $query->bind_result($otherfees);
                        $query->fetch();
                        $query->close();
                    }

                    // moved ttlro calc to here

                    $ttlro = $row["totalprts"] + $row["totallbr"] + $row["totalsublet"] + $row["hazardouswaste"] + $row["userfee1"] + $row["userfee2"] + $row["userfee3"] + $row['storagefee'] + $otherfees + $row["salestax"] - $row["discountamt"];

                    $bal = $ttlro - $ropayments;
                    $displaybal = asDollars($bal);

                    $ttlro = asDollars($ttlro);

                    if ($bal > 0.01) {

                        $runningbal = $runningbal + $bal;

                        ?>
                        <tr <?php echo !$shopIsReadOnly 
                            ? "onclick=\"editAccount('{$row['shopid']}','{$row['roid']}')\"" 
                            : ""; ?>>

                            <td style="width: 5%" class="roidcell" valign="top">
                                <a class="text-primary"><?php echo $row["roid"]; ?></a>
                            </td>

                            <?php
                                if ($row["statusdate"] == "0000-00-00" || $row["statusdate"] == null) {
                                    $statusdate = "";
                                    $days = "";
                                } else {
                                    $statusdate = $row["statusdate"];
                                    $dStart = new DateTime($statusdate);
                                    $dEnd = new DateTime();
                                    $diff = $dStart->diff($dEnd);
                                    $days = $diff->format("%a");
                                }

                                // look for switch command
                                if (!$row["customerphone"]) {
                                    $customerphone = "NA";
                                } else {
                                    $customerphone = formatPhone(strtoupper($row["customerphone"]));
                                }

                                if (!$row["customerwork"]) {
                                    $customerwork = "NA";
                                } else {
                                    $customerwork = formatPhone(strtoupper($row["customerwork"]));
                                }

                                if (!$row["cellphone"]) {
                                    $cellphone = "NA";
                                } else {
                                    $cellphone = formatPhone(strtoupper($row["cellphone"]));
                                }
                            ?>

                            <!-- date complete -->
                            <td class="statdatecell" style="width: 20%" class="style6" valign="top">
                                <?php
                                    if ($row["statusdate"] == "0000-00-00" || $row["statusdate"] == null) {

                                        echo "";
                                    } else {
                                        echo date_format($dStart, 'm/d/Y');
                                    }
                                ?>
                            </td>

                            <td class="dayscell"style="width: 5%" class="style6" valign="top"><?php echo $days; ?></td>
                                <?php
                                    if (strlen($row["customerphone"]) > 0 || strlen($row["customerwork"]) > 0 || strlen($row["cellphone"]) > 0) {
                                        $lf = $row["lastfirst"] . "<br />" . $customerphone . " / " . $customerwork . " / " . $cellphone;
                                        if ((strtolower($row["onhold"])) == "yes") {
                                            $lf = $lf . " <span style='color:var(--primary);font-weight:bold'>Customer On Hold</span>";
                                        }
                                        ?>
                                        <td class="lfcell"
                                            ><?php echo $lf; ?>
                                        </td>
                                        <?php
                                    } else {
                                        $lf = $row["lastfirst"];
                                        ?>
                                        <td class="lfcell"
                                            
                                            style="width:30%" class="style7" valign="top"><?php echo $lf; ?>
                                        </td>
                                        <?php
                                    } // end of if

                                ?>
                            <td style="width: 15%" class="style5" valign="top"><?php echo sbpround($ttlro, 2); ?></td>
                            
                            <td class="totalbox" onclick="" style="width: 15%" name="balance"
                                text-align="right"><?php echo sbpround($displaybal, 2); ?>
                            </td>
                        </tr>

                        <?php
                    } // this is the end of the check for > 0.01 bal
                } // end of while
            } // end of if
            ?>
            </tbody>
        </table>
    </div>
    <!-- END ar Page Container -->

    <!-- Start of the Parts sales -->
    <?php

    $stmt = "SELECT psid,psdate,total,balance,cid,lastname, ps.shopid ";
    $stmt .= " from ps ";
    $stmt .= " left outer join customer ";
    $stmt .= " on ps.shopid = customer.shopid ";
    $stmt .= " and ps.cid = customer.customerid ";
    $stmt .= "WHERE ps.shopid = ? ";
    $stmt .= "  AND psdate > '2012-10-31' ";
    $stmt .= "  AND balance > 0.01 ";
    $stmt .= "  AND ucase(status) = 'CLOSED' ";
    $stmt .= " ORDER BY balance desc ";


    if (isset($_GET['n'])) {
        $searchstatus = "%" . $_GET['n'] . "%";
        $stmt .= " AND lastfirst like ? ";
    }

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $psresult = $query->get_result();
    } else {
        echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $psnumofrows = $psresult->num_rows;

    // Check for data

    if ($psnumofrows > 0) {
    ?>
    <hr>
    <div id="table-container-ps" class="table-responsive">
        <table id="pslist-table" class="sbdatatable">
            <thead>
            <tr>
                <th>Part Sale</th>
                <th>Date Sold</th>
                <th>Days</th>
                <th>Customer Home/Work/Cell</th>
                <th>Total Invoice</th>
                <th>Balance</th>
            </tr>
            </thead>
            <tbody>
            <?php
            $statusdate = "";
            $days = "";
            $lf = "";
            $lastname = "";
            $firstname = "";

            while ($ps = mysqli_fetch_assoc($psresult)) {
                $customerid = $ps["cid"];
                $shopid = $ps["shopid"];

                $stmt = "SELECT sum(amt) as a ";
                $stmt .= "from `accountpayments-ps` ";
                $stmt .= "WHERE shopid = ? ";
                $stmt .= "  AND psid = ?";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("si", $shopid, $ps["psid"]);
                    $query->execute();
                    $apresult = $query->get_result();
                } else {
                    echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $aprow = $apresult->fetch_array();

                $ap_amount = $aprow['a'];

                if ($ap_amount > 0) {
                    $pspayments = $ap_amount;
                } else {
                    $pspayments = 0;
                }

                $bal = $ps['total'] - $pspayments;

                if ($bal > 0.01) {
                    ?>
                    <tr class="sbphover-row">
                        <?php $dstart = ""; ?>
                        <td style="width: 5%" class="roidcell" valign="top">
                            <a 
                                <?php echo !$shopIsReadOnly 
                                    ? "onclick=\"editpsAccount('{$ps['shopid']}','{$ps['psid']}')\"" 
                                    : ""; ?> 
                                class="text-primary">
                                <?php echo $ps["psid"]; ?>
                            </a>
                        </td>
                        <?php
                        if ($ps["psdate"] == "0000-00-00") {
                            $psdate = "";
                            $days = "";
                        } else {
                            $psdate = $ps["psdate"];
                            $dStart = new DateTime($psdate);
                            $dEnd = new DateTime();
                            $diff = $dStart->diff($dEnd);
                            $days = $diff->format("%a");
                        }

                        // in the interest of performance only picking up 2 fields off customer
                        $stmt = "SELECT LastName,FirstName ";
                        $stmt .= "from customer ";
                        $stmt .= "WHERE shopid = ? ";
                        $stmt .= "  AND customerid = ?";
                        //echo $stmt;

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $customerid);
                            $query->execute();
                            $custresult = $query->get_result();
                        } else {
                            echo "Customer Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }

                        while ($cust = $custresult->fetch_array()) {

                            $custlname = $cust["LastName"];
                            $custfname = $cust["FirstName"];
                            $lf = $custlname . "," . $custfname;
                            //echo "balance:".$bal.":".$ps['balance'];
                            $runningbal = $runningbal + $ps["balance"];
                        } // end of cust loop

                        $total = $ps["total"];
                        $balance = $ps["balance"];
                        $total = asDollars($total);
                        //$bal = asDollars($bal);
                        ?>
                        <td style="width: 20%" class="style6"
                            valign="top"><?php echo date_format($dStart, 'm/d/Y'); ?></td>
                        <td style="width: 5%" class="style6" valign="top"><?php echo $days; ?></td>
                        <td style="width:30%" class="style7" valign="top"><?php echo $lf; ?></td>
                        <td style="width: 15%" class="style5" valign="top"><?php echo sbpround($total, 2); ?></td>

                        <td class="totalbox" onclick="" style="width: 15%"
                            text-align="right"><?php echo asDollars($balance); ?>
                        </td>
                    </tr>
                    <?php
                } // end of while
            } // endof check for ps data
            }
            $runningbal = asDollars($runningbal);
            ?>
            </tbody>
        </table>

        <!-- End of the Parts sales -->

        <table style="width: 100%" cellspacing="0" cellpadding="3" align="center">
            <tr>
                <td style="width: 5%"></td>
                <td style="width: 20%"></td>
                <td style="width: 5%"></td>
                <td style="width: 45%"></td>

                <td class="outstandingbox" style="text-align:left">Total
                    Outstanding: <?php echo sbpround($runningbal, 2); ?></td>
                <td style="width: 10%;text-align:right"></td>
            </tr>
        </table>
    </div>

</main>
<!-- END Main Container -->

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>

<script>
    $(document).ready(function () {
        let arTable = $("#arlist-table").DataTable({
            responsive: true,
            retrieve: true,
            searching: true,
            bFilter: false,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            order: []
        });

        let psTable = $("#pslist-table").DataTable({
            responsive: true,
            retrieve: true,
            searching: true,
            bFilter: false,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            order: []
        });
    });

    function searchTables(term) {
        let arTable = $("#arlist-table").DataTable();
        let psTable = $("#pslist-table").DataTable();

        arTable.search(term).draw();
        psTable.search(term).draw();

        let balanceElements = document.getElementsByName('balance');

        // Initialize sum variable
        let totalBalance = 0;

        // Iterate through each element
        balanceElements.forEach(element => {
            // Convert the inner HTML to a number and add it to the sum
            let balance = parseFloat(element.innerHTML.replace('$', '').replace(',', '')) || 0;
            totalBalance += balance;
        });

        let formattedTotalBalance = totalBalance.toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        document.getElementsByClassName('outstandingbox')[0].innerHTML = "Total\n                    Outstanding: " + formattedTotalBalance;
    }

    if ( !shopIsReadOnly ){
        function editAccount(shopid, roid) {
            eModal.iframe({
                title: 'Edit Account',
                url: 'editaccount.php?shopid=' + shopid + '&roid=' + roid + '&hideheader=1',
                size: eModal.size.xl,
            });
        }

        function editpsAccount(shopid, psid) {
            eModal.iframe({
                title: 'Edit Part Sale',
                url: 'editpsaccount.php?shopid=' + shopid + '&psid=' + psid,
                size: eModal.size.xl,
            });
        }
    }
</script>
<!-- Date range functions -->
<script>
    $(document).ready(function () {
        $("#sd").on("dp.change", function (e) {
            $('#ed').focus()
        });

        $("#dateRangeDropDown li a").click(function (e) {
            var range = $(this).data("range");
            console.log(range);
            setdates(range, "#sd", "#ed");
            e.preventDefault();
        });
    });

    function setdates(t, sdid, edid) {

        var lw = '01/22/2024|01/28/2024';
        var lm = '01/01/2024|01/31/2024';
        var ly = '01/01/2023|12/31/2023';
        var tw = '01/29/2024|02/04/2024';
        var tm = '02/01/2024|02/29/2024';
        var ty = '01/01/2024|12/31/2024';

        if (t == "thisWeek") {
            tar = tw.split("|")
        }
        if (t == "thisMonth") {
            tar = tm.split("|")
        }
        if (t == "thisYear") {
            tar = ty.split("|")
        }
        if (t == "lastWeek") {
            tar = lw.split("|")
        }
        if (t == "lastMonth") {
            tar = lm.split("|")
        }
        if (t == "lastYear") {
            tar = ly.split("|")
        }
        $(sdid).val(tar[0]).addClass('active');
        $(edid).val(tar[1]).addClass('active');
    }
</script>
</body>

<?php
mysqli_close($conn);
?>
</html>
