<?php

$component = "accounting-v2";
$shopid = $_COOKIE['shopid'];
$menuComponent = 'ar';

require CONN;
include getHeadGlobal('');


$title = "Payments Received Report";
$companyNamePrint = "";
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($companyname, $conamereports);
    $query->fetch();
    $query->close();
}

if ($conamereports == 'yes') {
    $companyNamePrint .= " - $companyname";
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
<main id="reports" class="min-vh-100">
    <div >
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div class="flex-1">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/accounting.php"
                           class="text-secondary">Accounting</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/ar/ar.php" class="text-secondary">Accounts Receivable</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="d-print-none"><?= $title ?></h2>
                        <h4 class="d-none d-print-inline"><?= $title . $companyNamePrint ?></h4>
                    </div>
                </div>
                <div class="flex-1">
                    <button type="button" class="btn btn-secondary btn-md d-print-none float-end" onclick="printreport()" style="">Print
                    </button>
                </div>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                echo $_GET['sd'] . ' to ' . $_GET['ed'];
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="container-fluid">
        <div class="row mt-4">
            <div class="col-md-12">
                <div >
                    <h3 class="text-center">RO Payments Received</h3>
                    <table class="sbdatatable w-100">
                        <thead>
                        <tr>
                            <th>RO#</th>
                            <th>Customer</th>
                            <th>Vehicle</th>
                            <th>Payment Date</th>
                            <th>RO Closed Date</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <?php if (!$shopIsReadOnly): ?>
                                <th class="d-print-none text-center">Post to QBO</th>
                            <?php endif; ?>
                        </tr>
                        </thead>
                        <tbody>
                        <?php
                        $sd = date("Y-m-d", strtotime($_GET['sd']));
                        $ed = date("Y-m-d", strtotime($_GET['ed']));
                        $runttl = 0;

                        $stmt = "select id,amt,pdate,ptype,customer,vehinfo,a.roid,r.statusdate from accountpayments a"
                            . " left join repairorders r on a.shopid = r.shopid and a.roid = r.roid"
                            . " where a.pdate >= '$sd' and a.pdate <= '$ed' and a.shopid = '$shopid'";
                        //echo $stmt;
                        $totalar = 0;
                        if ($query = $conn->prepare($stmt)) {
                            //$query->bind_param("s",$shopid);
                            $query->execute();
                            $result = $query->get_result();
                            while ($rs = $result->fetch_array()) {
                                // get the ro closed date and see if it is older than the payment date.
                                $rstmt = "select statusdate from repairorders where shopid = ? and roid = ?";
                                if ($rquery = $conn->prepare($rstmt)) {
                                    $rquery->bind_param("si", $shopid, $rs['roid']);
                                    $rquery->execute();
                                    $rquery->bind_result($statusdate);
                                    $rquery->fetch();
                                    $rquery->close();
                                }
                                $statdate = strtotime($statusdate);
                                $pdate = strtotime($rs['pdate']);
                                //if ($statdate < $pdate){
                                $runttl += $rs['amt'];
                                ?>
                                <tr>
                                    <td><?php echo $rs['roid']; ?></td>
                                    <td><?php echo $rs['customer']; ?></td>
                                    <td><?php echo $rs['vehinfo']; ?></td>
                                    <td><?php echo date("m/d/Y", strtotime($rs['pdate'])); ?></td>
                                    <td><?php echo date("m/d/Y", strtotime($rs['statusdate'])); ?></td>
                                    <td><?php echo $rs['ptype']; ?></td>
                                    <td class="text-end"><?php echo number_format($rs['amt'], 2); ?></td>
                                    <?php if (!$shopIsReadOnly): ?>
                                        <td class="text-end d-print-none">
                                            <button id="sendtoqbo<?php echo $rs['id']; ?>" class="btn btn-primary" onclick="posttoQBO(<?php echo $rs['id']; ?>)">Post to QBO</button>
                                        </td>
                                    <?php endif; ?>
                                </tr>
                                <?php
                                //}
                            }
                        }
                        ?>
                        </tbody>
                    </table>
                    <div class="text-end mt-3">
                        <h4>Total Payments Received: <?php echo number_format($runttl, 2); ?></h4>
                    </div>


                    <h3 class="mt-4 text-center">PS Payments Received</h3>
                    <table class="sbdatatable w-100">
                        <thead>
                        <tr>
                            <th>PS#</th>
                            <th>Customer</th>
                            <th>Payment Date</th>
                            <th>Type</th>
                            <th class="text-end">Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php
                        $runttl = 0;

                        $stmt = "select a.psid,a.ptype,a.amt,a.pdate,concat(firstname,' ',lastname) as customer from `accountpayments-ps` a"
                            . " left join ps on a.shopid = ps.shopid and a.psid = ps.psid"
                            . " left join customer c on ps.shopid = c.shopid and ps.cid = c.customerid"
                            . " where a.pdate >= '$sd' and a.pdate <= '$ed' and a.shopid = '$shopid'";

                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $result = $query->get_result();
                            while ($rs = $result->fetch_array()) {

                                $runttl += $rs['amt'];
                                ?>
                                <tr>
                                    <td><?php echo $rs['psid']; ?></td>
                                    <td><?php echo $rs['customer']; ?></td>
                                    <td><?php echo date("m/d/Y", strtotime($rs['pdate'])); ?></td>
                                    <td><?php echo $rs['ptype']; ?></td>
                                    <td class="text-end"><?php echo number_format($rs['amt'], 2); ?></td>
                                </tr>
                                <?php
                                //}
                            }
                        }
                        ?>
                        </tbody>
                    </table>
                    <div class="text-end mt-3">
                        <h4>Total Payments Received: <?php echo number_format($runttl, 2); ?></h4>
                    </div>

                    <h3 class="text-center mt-4">RO's Closed Without Payment</h3>
                    <table class="sbdatatable w-100">
                        <thead>
                        <tr>
                            <th>RO#</th>
                            <th>Customer</th>
                            <th>Vehicle</th>
                            <th>Closed Date</th>
                            <th class="text-end">Balance</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php
                        $rostmt = "select roid,customer,vehinfo,statusdate,balance,totalro from repairorders where shopid = ? and"
                            . " status = 'closed' and rotype != 'no approval' and statusdate >= ? and statusdate <= ?";
                        if ($roquery = $conn->prepare($rostmt)) {
                            $roquery->bind_param("sss", $shopid, $sd, $ed);
                            $roquery->execute();
                            $ror = $roquery->get_result();
                            while ($rors = $ror->fetch_assoc()) {
                                // get total payments made before the ro was closed
                                $stmt = "select coalesce(sum(amt),0) amt from accountpayments where shopid = ? and roid = ?"
                                    . " and pdate <= '" . $rors['statusdate'] . "'";
                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("si", $shopid, $rors['roid']);
                                    $query->execute();
                                    $query->bind_result($tpmts);
                                    $query->fetch();
                                    $query->close();
                                }
                                if (($rors['totalro'] - $tpmts) > 0) {
                                    ?>
                                    <tr>
                                        <td><?php echo $rors['roid']; ?></td>
                                        <td><?php echo $rors['customer']; ?></td>
                                        <td><?php echo $rors['vehinfo']; ?></td>
                                        <td><?php echo date('m/d/Y', strtotime($rors['statusdate'])); ?></td>
                                        <td class="text-end"><?php echo $rors['balance']; ?></td>
                                    </tr>
                                    <?php
                                }
                            }
                        }
                        ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</main>
<?php
include_once getScriptsGlobal('');
?>
<script src="<?= SCRIPT ?>/printThis.js"></script>
<script>
    function printreport() {
        //  alert("If you would like to print this report, we suggest 'Landscape' mode as it fits better on the page");
        $('#reports').printThis({
            printContainer: false,
            importStyle: true,
        });
    }
</script>
<script>

    function posttoQBO(id) {

        $('#sendtoqbo' + id).attr("disabled", true)
        $.ajax({
            data: "id=" + id,
            url: "<?= COMPONENTS_PRIVATE; ?>/accounting/apination/postpaymentoqbo.php",
            type: "post",
            success: function (r) {
                //$('#sendtoqbo'+id).attr("disabled",false)
                sbalert("Payment posted successfully")
            },
            error: function (xhr, ajaxOptions, thrownError) {
                //$('#sendtoqbo'+id).attr("disabled",false)
            }
        })
        //$('#sendtoqbo'+id).attr("disabled",false)

    }
</script>

</body>

</html>
