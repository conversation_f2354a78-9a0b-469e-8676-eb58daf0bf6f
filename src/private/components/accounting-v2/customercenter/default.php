<?php

require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];

$email = "dummy";

if (strtolower($_COOKIE["accounting"]) == "no") {
    redirect_to(COMPONENTS_PRIVATE . "/v2/wip/wip.php");
}

$rpath = realpath(COMPONENTS_PUBLIC_PATH . "/invoices/$shopid");
$rpath2 = realpath(COMPONENTS_PUBLIC_PATH . "/invoices");
$rpath2 .= "{$shopid} ";

if (file_exists($rpath)) {
    $r = $_COOKIE['shopid'] . "/printpdfro.php";
} else {
    $r = "printpdfro.php";
}

// adding ppath (part sales)
$ppath = realpath('/windows/system32');
$ppath2 = realpath($_SERVER['DOCUMENT_ROOT'] . '/sbp/psinvoices');
$ppath2 .= "{$shopid} ";

if (!file_exists($ppath)) {
    $p = "psprintpdf.php";
} else {
    $p = "psprintpdf.php";
}

// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);

$stmt = "SELECT CompanyName,CompanyPhone,CompanyEMail FROM company WHERE shopid = ? ";
if ($squery = $conn->prepare($stmt)) {
    $squery->bind_param("s", $shopid);
    $squery->execute();
    $squery->bind_result($shopname, $shopphone, $shopemail);
    $squery->fetch();
    $squery->close();
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>

<style>
    main {
        height: 100% !important;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div>
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div class="flex-1">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/accounting.php" class="text-secondary">Accounting</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">Customer Center</h2>
                    </div>
                </div>

                <?php if( !$shopIsReadOnly ): ?>
                    <div class="flex-1">
                        <button class="btn btn-primary" style="display:none" id="wo" onclick="SelectWriteOff()" type="button">Write off as Bad Debt</button>
                        <button class="btn btn-secondary" style="display:none" id="sendInvoiceButton" onclick="showSendInvoices()" type="button">Email Selected Invoices</button>
                        <button class="btn btn-secondary" style="display:none" id="PrintButton" onclick="sendInvoices('<?php echo $shopid; ?>','print')" type="button">Print Selected Invoices</button>
                    </div>
                <?php endif; ?>
            </div>
            <hr/>
        </div>
    </div>

    <div class="row mb-2">
        <input id="returnlist" name="Hidden1" type="hidden"/>
        <input id="returnlistps" name="Hidden1" type="hidden"/>
    </div>

    <!--    Page content-->
    <div class="row">
        <div class="col-md-3">
            <div class="row mb-2">
                <div class="col-md-12">
                    <h2>Customer List</h2>
                </div>

                <div class="col-md-12">
                    <div class="input-group mb-2">
                        <div class="form-outline">
                            <input class="form-control" onkeyup="filterList(this.value)" id="customersearch" style="text-transform:uppercase" type="text" placeholder="Enter Name"/>
                            <label class="form-label" for="customersearch">Search</label>
                        </div>
                        <span onclick="filterList('');document.getElementById('customersearch').value=''" class="input-group-text"><i class="fas fa-trash"></i></span>
                    </div>
                </div>
            </div>

            <div id="customerlist" style="overflow-y:auto;">
                <?php

                $_COOKIE["shopid"] = $shopid;

                if (isset($_GET['v'])) {
                    $v = ($_GET['v']);
                } else {
                    $v = "";
                }

                $stmt = "select customerid,lastname,firstname from customer  "
                    . " WHERE shopid = '{$shopid}' AND active != 'no' and (firstname like '$v%'  or lastname like '$v%'"
                    . " OR homephone like '$v%' or workphone like '$v%' or cellphone like '$v%' or address like '$v%') ORDER BY lastname,firstname ";

                if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $result = $query->get_result();
                ?>

                <table class="table table-sm table-hover">
                    <?php while ($row = $result->fetch_array()) { ?>
                        <tr>
                            <td id="c<?php echo $row["customerid"]; ?>" onclick="showCustomerDetails('<?php echo $row["customerid"]; ?>');"><?php echo strtoupper($row["lastname"] . ", " . $row["firstname"]); ?></td>
                        </tr>
                    <?php } // End of while ?>
                    <?php } else { ?>
                        <p>No customers found</p>
                    <?php } ?>
                </table>
            </div>
        </div>

        <div class="col-md-9">
            <h3>Customer Details
                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Select a customer on the left. Customer account details will be displayed here. You can also click an RO or Part Sale to view it."></i>
            </h3>

            <div id="customerdata"></div>
        </div>
    </div>
    <!--    End page content-->

    <div style="width:40%;left:30%;border:1px silver solid;z-index:1000;position:absolute;top:100px;padding:20px;display:none;" id="customeremail"></div>

    <iframe id="roframe" style="top:5%;left:2.5%;width:95%;height:90%;border:1px silver outset;display:none;position:absolute;background-color:white;z-index:9999"></iframe>

    <div style="position:absolute;top:0px;left:0px;background-color:#F0F0F0;width:100%;height:40px;z-index:9999;display:none;color:black" id="roheader">
        <table style="width: 90%;margin-left:5%">
            <tr>
                <td style="text-align:left">
                    <input id="framebutton" onclick="closeRO()" type="button" value="Close" style="width:200px;" class="btn btn-primary btn-lg"/>
                    &nbsp;
                </td>
                <td style="text-align:center">
                    <input id="gotobutton" onclick="gotoRO()" type="button" value="Go To RO" style="width:200px;" class="btn btn-secondary btn-lg"/>
                    &nbsp;
                </td>
                <td style="text-align:right">
                    <input id="gotobutton" onclick="postROPmt()" type="button" value="Post a Payment" style="width:200px;" class="btn btn-secondary btn-lg"/>
                    &nbsp;
                </td>
            </tr>
        </table>
    </div>

    <iframe id="psframe" style="top:5%;left:2.5%;width:95%;height:90%;border:1px silver outset;display:none;position:absolute;background-color:white;z-index:9999"></iframe>

    <div style="position:absolute;top:0px;left:0px;background-color:#F0F0F0;width:100%;height:40px;z-index:9999;display:none;color:black" id="psheader">
        <table style="width: 90%;margin-left:5%">
            <tr>
                <td style="text-align:left">
                    <input id="framebutton" onclick="closePS()" type="button" value="Close" style="width:200px;" class="btn btn-primary btn-lg"/>
                    &nbsp;
                </td>
                <td style="text-align:center">
                    <input id="gotobutton" onclick="gotoPS()" type="button" value="Go To Part Sale" style="width:200px;" class="btn btn-info btn-lg"/>
                    &nbsp;
                </td>
                <td style="text-align:right">
                    <input id="gotobutton" onclick="postPSPmt()" type="button" value="Post a Payment" style="width:200px;" class="btn btn-warning btn-lg"/>
                    &nbsp;
                </td>
            </tr>
        </table>
    </div>

    <div id="hider"></div>
    <div id="cover"></div>
    <input id="selectedroid" type="hidden"/>
    <input id="selectedpsid" type="hidden"/>

    <input id="selectedcustomerid" type="hidden"/>
    <input id="writeoffamount" type="hidden"/>
    <input id="bdamount" type="hidden"/>
    <input id="robdballist" type="hidden"/>
    <input id="psbdballist" type="hidden"/>


    <input id="activeroid" type="hidden"/>
</main>
<!-- END Main Container -->

<?php if (!$shopIsReadOnly): ?>
    <!-- Modals -->
    <div id="emailinvmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Email Invoice To Customer</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" id="sendingemail" name="sendingemail" value="<?php echo $email; ?>" type="text">
                                <label class="form-label" for="sendingemail">Email Address </label>
                            </div>

                            <div class="form-check mb-4">
                                <input class="form-check-input" id="update" name="update" type="checkbox" value="yes"/>
                                <label class="form-check-label" for="update">Update customer information with this email address?</label>
                            </div>

                            <div class="form-outline mb-4">
                                <textarea class="form-control" type="text" tabindex="1" id="emailmessage" name="emailmessage" placeholder="Message">The following is an invoice or group of invoices from <?= $shopname ?>.&#13;&#10;If you have any questions, please contact us.  Phone: <?= $shopphone ?> or email: <?= $shopemail ?>.&#13;&#10;&#13;&#10;Sincerely,&#13;&#10;&#13;&#10;<?= $shopname ?></textarea>
                                <label class="form-label" for="emailmessage">Message</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button class="btn btn-md btn-primary" type="button" onclick="sendInvoices('<?php echo $shopid; ?>','email')">Send</button>
                </div>
            </div>
        </div>
    </div>

    <div id="splitpmtmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Split Payment</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <div id="results" class="row"></div>
                </div>
            </div>
        </div>
    </div>

    <div id="splitpaymentmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Split Payment</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <div class="popwin"></div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- for use in getting invoice path -->
<input type="hidden" id="invpath" value="">
<input type="hidden" id="cidfortransfer" value="">

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>

<script>
    <?php
    if (isset($_GET['cid'])) {
        $cidfortransfer = $_GET['cid'];
        echo "showCustomerDetails($cidfortransfer)";
    }
    ?>

    document.getElementById("customerlist").style.maxHeight = window.innerHeight - 290 + "px"

    $('#customersearch').focus()

    function splitPmt(roid) {

        $('#activeroid').val(roid)
        ds = "t=getpayments&roid=" + roid + "&shopid=<?php echo $_COOKIE['shopid']; ?>"
        $.ajax({
            data: ds,
            url: "splitpmt.php",
            success: function (r) {
                window.scrollTo(0, 0)
                $('#results').html(r)
                $('#splitpmtmodal').modal('show');
            }
        })
    }

    function selectRO(pmtid) {
        roid = $('#activeroid').val()
        ds = "t=getpmt&pmtid=" + pmtid + "&shopid=<?php echo $shopid; ?>&roid=" + roid
        $.ajax({
            data: ds,
            url: "splitpmt.php",
            success: function (r) {
                $('.popwin').html(r)
                $('#splitpmtmodal').modal('hide');
                $('#splitpaymentmodal').modal('show');
            }
        })
    }

    function closeRO() {
        document.getElementById("roframe").src = ""
        $('#roframe').hide()
        $('#roheader').hide()
        $('#cover').hide()
        $('#hider').hide()
        $('#selectedroid').val()
        $('#psheader').hide()
        $('#hider').hide()
    }

    function closePS() {
        document.getElementById("psframe").src = ""
        $('#psframe').hide()
        $('#psheader').hide()
        $('#cover').hide()
        $('#hider').hide()
        $('#selectedpsid').val()
        $('#psheader').hide()
        $('#hider').hide()
    }

    function completeSplit(pmtid) {
        roid = $('#activeroid').val()
        totalro = $('#totalro' + roid).html()
        newroid = $('#selectro').val()

        ds = "newroid=" + newroid + "&totalro=" + totalro + "&shopid=<?php echo $_COOKIE['shopid']; ?>&t=splitpmt&pmtid=" + pmtid + "&roid=" + roid
        showLoader();

        $.ajax({
            data: ds,
            url: "splitpmt.php",
            success: function (r) {
                if (r.indexOf("success|") >= 0) {
                    rar = r.split("|")
                    cid = rar[1]
                    cancel()
                    showCustomerDetails(cid)
                }
            }
        })

        hideLoader()
    }

    function cancel() {
        $('#splitpaymentmodal').modal('hide');
    }

    // added spinner
    function filterList(v) {
        var ds = 'v=' + v;

        showLoader()

        $.ajax({
            type: "GET",
            url: "customerlistdata.php",
            data: ds,
            success: function (r) {
                $('#customerlist').html(r)
                hideLoader()
            }
        });

        hideLoader()
    }

    function showCustomerDetails(id) {

        $("#cidfortransfer").val(id)
        showLoader()

        $("#wo").css("display", 'none');
        $("#womessage").css("display", 'none')


        $("#selectedcustomerid").val(id)

        $('#c' + id).addClass("active");

        //This is the new code to replace the xmlhttp request
        var ds = 'cid=' + id;

        showLoader();

        $.ajax({
            type: "GET",
            url: "customerdata_new.php",
            data: ds,
            success: function (r) {
                $('#customerdata').html(r)

                hideLoader()

                testbal = Math.abs($('#rbal').val())

                rbal = $('#rbal').val()
                //added individual
                bdamount = $('#bdamount').val()

                $("#wo").css("display", 'inline-block')
                $("#womessage").css("display", 'block')


                // get the individual writeoff amount
                $("#writeoffamount").val(bdamount)
                $("#bdamount").val(bdamount)


                document.getElementById("balance").innerHTML = "Customer Balance: $" + rbal

                if (testbal > 0) {
                    $('#balance').css("color", "var(--primary)");
                } else if (testbal === 0) {
                    $('#balance').css("color", "var(--textColor)");
                }

                ntestbal = $('#rbal').val()

                if (ntestbal.indexOf("-") == 0 && testbal > 0.01) {
                    $('#balance').innerHTML += " (Customer has a credit)"
                }

                document.getElementById("sendingemail").value = document.getElementById("customeremailfromdata").innerText
            }
        });

        // Remove active class from other links
        x = document.getElementsByClassName("active");
        x = [].slice.call(x);
        x = x.filter(function (el) {
            return el.localName == "td";
        });

        for (i = 0; i < x.length; i++) {
            if (x[i].id != "c" + id) {
                $('#' + x[i].id).removeClass("active");
            }
        }

        $("#sendInvoiceButton").css("display", 'none');
        $("#PrintButton").css("display", 'none');
    }

    function showRO(roid, status) {
        status = status.toLowerCase()

        if (status == "closed") {
            eModal.iframe({
                title: "Closed RO",
                url: "<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?edit=no&nohead&roid=" + roid,
                size: eModal.size.xl,
                buttons: [{
                    text: "Receive Payment",
                    style: "primary",
                    close: true,
                    click: postROPmt
                }]
            });
        } else {
            sbalert("You cannot open an RO that is still on your Work in Process")
        }

        $('#selectedroid').val(roid)
    }

    function showPS(psid) {
        // Bringing in a modal and a status to evaluate if part sale is closed and editable
        eModal.iframe({
            title: "Closed Part Sale",
            url: "<?= COMPONENTS_PRIVATE ?>/v2/partsale/partsale.php?edit=no&nohead&psid=" + psid,
            size: eModal.size.xl,
            buttons: [{
                text: "Receive Payment",
                style: "primary",
                close: true,
                click: postPSPmt
            }]
        });

        $('#selectedpsid').val(psid)
    }

    function gotoRO() {
        sbconfirm(
            'GO TO RO',
            'You will now exit the Customer Center and diplay this RO.  Are you sure?',
            function () {
                roid = $('#selectedroid').val()
                location.href = '<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=' + roid
            }
        );
    }

    function gotoPS() {

        sbconfirm(
            'GO TO Part Sale',
            'You will now exit the Customer Center and diplay this Part Sale.  Are you sure?',
            function () {
                psid = $('#selectedpsid').val()
                location.href = '<?= COMPONENTS_PRIVATE ?>/partsale/partsale.php?psid=' + psid
            }
        );
    }

    function SelectWriteOff() {

        $("#womessage").css("display", 'block')


        cid = $("#selectedcustomerid").val()

        shopid = '<?php echo $shopid; ?>'


        clist = ""
        plist = ""

        d = document.getElementsByClassName("form-check-input-ro")
        p = document.getElementsByClassName("form-check-input-ps")

        bdamount - $('#bdamount').val()


        if (d.length > 0) {

            robdballist = ""
            robdballist = document.getElementById("robdballist").value

            robdballist = robdballist.substring(0, robdballist.length - 1)
            robdarray = robdballist.split(",")

            for (j = 0; j < d.length; j++) {
                c = document.getElementById(d[j].id).checked
                if (c == true) {
                    itemid = document.getElementById(d[j].id).id
                    // reinstate original code here from sendInvoice as a model
                    if (itemid.substring(0, 2) == "ro") {
                        roid = itemid.replace("ro", "")

                        roidset = roid.split("|");

                        for (i = 0; i < roidset.length; i += 3) {
                            roid = roidset[0]

                            amt = roidset[1]
                            status = roidset[2]
                        }

                        if (status != "CLOSED") {
                            sbalert("This is not a Closed Invoice. Can not write off bad debt")
                            return
                        }

                        bal = $('#robal' + roid).val()

                        clist += roid + "|" + bal + ","
                    }
                }
            }

            clist = clist.substring(0, clist.length - 1)
            carray = clist.split(",")
            if (carray != "") {
                var str = ''
                for (j = 0; j < carray.length; j++) {
                    cidset = carray[j].split("|");

                    for (k = 0; k < robdarray.length; k++) {
                        if (robdarray[k] == cidset[0]) {
                            str = str + carray[j] + ','
                        }
                    }
                }

                writeOff(str)

                $('#checkall').prop('checked', false)
            }
        }

        if (p.length > 0) {

            psbdballist = ""
            psbdballist = document.getElementById("psbdballist").value

            psbdballist = psbdballist.substring(0, psbdballist.length - 1)

            psbdarray = psbdballist.split(",")

            for (j = 0; j < p.length; j++) {
                c = document.getElementById(p[j].id).checked
                if (c == true) {
                    itemid = document.getElementById(p[j].id).id


                    if (itemid.substring(0, 2) == "PS") {
                        psid = itemid.replace("PS", "")

                        psidset = psid.split("|");

                        for (i = 0; i < psidset.length; i += 3) {
                            psid = psidset[0]
                            amt = psidset[1]
                            status = psidset[2]
                        }

                        if (status != "Closed") {
                            sbalert("This is not a Closed Invoice. Can not write off bad debt")
                            return
                        }

                        plist += psid + "|" + amt + ","
                    }
                }
            }

            plist = plist.substring(0, plist.length - 1)

            carray = plist.split(",")
            if (carray != "") {
                for (j = 0; j < carray.length; j++) {

                    for (k = 0; k < psbdarray.length; k++) {

                        psidset = carray[j].split("|");

                        for (i = 0; i < psidset.length; i += 2) {
                            psid = psidset[0]
                            amt = psidset[1]
                        }
                    }
                }

                writeOffPS(plist)

                $('#checkallps').prop('checked', false)
            }
        }
    }

    function writeOff(str) {

        cid = $("#selectedcustomerid").val()
        var ds = 'cid=' + cid + '&str=' + str;

        if (cid.length == 0 || str.length == 0) {
            sbalert("You must select a customer and roid before you can write off an amount")
            return
        }

        sbconfirm(
            'Write Off Bad Debt',
            'Write off Bad Debt for the selected RO Invoice(s). Are you sure?',
            function () {
                showLoader();
                $.ajax({
                    type: "POST",
                    url: "writeoffbaddebt.php",
                    data: ds,
                    success: function () {
                        sbalert("The amount has been written off.  You will find it listed in Expenses")
                        showCustomerDetails(cid);
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        sbalert("Error in writeoffbaddebt.php")
                    }
                });

                hideLoader();
            }
        );
    }

    function writeOffPS(str) {

        cid = $("#selectedcustomerid").val()

        //woa = $("#writeoffamount").val()

        var ds = 'cid=' + cid + '&type=PS&str=' + str;

        if (cid.length == 0 || str.length == 0) {
            sbalert("You must select a customer and psid before you can write off an amount")
            return
        }

        showLoader();

        sbconfirm(
            'Write Off Bad Debt',
            'Write off Bad Debt for the selected PS Invoice(s). Are you sure?',
            function () {
                $.ajax({
                    type: "POST",
                    url: "writeoffbaddebt.php",
                    data: ds,
                    success: function () {
                        sbalert("The amount has been written off.  You will find it listed in Expenses")
                        showCustomerDetails(cid);
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        sbalert("Error in writeoffbaddebt.php")
                    }
                });
            }
        );

        hideLoader();
    }

    function postROPmt() {
        let roid = $("#selectedroid").val()
        let url = '<?= COMPONENTS_PRIVATE ?>/v2/accounting/ar/editaccount.php?menu=yes&hideheader&roid=' + roid

        // Start timer and wait 1 sec
        setTimeout(function () {
            eModal.iframe({
                title: "Receive Payment",
                url: url,
                size: eModal.size.xl,
            });
        }, 1000);
    }

    function postPSPmt() {
        let psid = $("#selectedpsid").val()
        let url = '<?= COMPONENTS_PRIVATE ?>/v2/accounting/ar/editpsaccount.php?psid=' + psid

        // Start timer and wait 1 sec
        setTimeout(function () {
            eModal.iframe({
                title: "Receive Payment",
                url: url,
                size: eModal.size.xl,
            });
        }, 1000);
    }

    function checkBoxes() {

        x = setTimeout(function () {
            d = document.getElementsByClassName("form-check-input-ro")

            checkistrue = false
            for (j = 0; j < d.length; j++) {
                if (d[j].checked == true) {
                    $("#sendInvoiceButton").css("display", 'inline-block')
                    $("#PrintButton").css("display", 'inline-block')
                    checkistrue = true
                    return
                }
                if (checkistrue == false) {
                    $("#sendInvoiceButton").css("display", 'none')
                    $("#PrintButton").css("display", 'none')
                }
            }
        }, 100)

    }

    function checkBoxesps() {

        x = setTimeout(function () {
            p = document.getElementsByClassName("form-check-input-ps")

            checkistrue = false
            for (j = 0; j < p.length; j++) {
                if (p[j].checked == true) {
                    $("#sendInvoiceButton").css("display", 'inline-block')
                    $("#PrintButton").css("display", 'inline-block')

                    checkistrue = true
                    return
                }
                if (checkistrue == false) {
                    $("#sendInvoiceButton").css("display", 'none')
                    $("#PrintButton").css("display", 'none')

                }
            }
        }, 100)

    }

    function checkAll() {

        x = setTimeout(function () {
            d = document.getElementsByClassName("form-check-input-ro")
            c = document.getElementById("checkall").checked

            for (j = 0; j < d.length; j++) {
                if (c == true) {
                    document.getElementById(d[j].id).checked = true
                } else {
                    document.getElementById(d[j].id).checked = false
                }
            }
        }, 10)
        checkBoxes();

    }

    function checkAllps() {

        x = setTimeout(function () {
            p = document.getElementsByClassName("form-check-input-ps")
            c = document.getElementById("checkallps").checked

            for (j = 0; j < p.length; j++) {
                if (c == true) {
                    document.getElementById(p[j].id).checked = true
                } else {
                    document.getElementById(p[j].id).checked = false
                }
            }
        }, 10)
        checkBoxesps();

    }


    function sendInvoices(shopid, type) {
        //modifying significantly to determine if it is an RO or PS
        showLoader()

        cid = $("#selectedcustomerid").val()

        if (document.getElementById("sendingemail").value.length > 0 || type == 'print') {
            clist = ""
            clistps = ""

            d = document.getElementsByClassName("form-check-input-ro")
            if (d.length > 0) {

                for (j = 0; j < d.length; j++) {
                    c = document.getElementById(d[j].id).checked
                    if (c == true) {
                        itemid = document.getElementById(d[j].id).id


                        if (itemid.substring(0, 2) == "ro") {
                            roid = itemid.replace("ro", "")

                            roidset = roid.split("|");

                            for (i = 0; i < roidset.length; i += 3) {
                                roid = roidset[0]
                                amt = roidset[1]
                                status = roidset[2]

                            }

                            clist += roid + ","
                        }
                    }
                }

                clist = clist.substring(0, clist.length - 1)
                carray = clist.split(",")
                if (carray != "") {
                    for (j = 0; j < carray.length; j++) {
                        generateInvoices(carray[j])
                    }

                    $('#checkall').prop('checked', false)
                    if (type == 'email')
                        setTimeout("sendTheInvoices()", 3000)
                }
            }

            p = document.getElementsByClassName("form-check-input-ps")

            // part sale
            if (p.length > 0) {
                for (j = 0; j < p.length; j++) {
                    c = document.getElementById(p[j].id).checked

                    if (c == true) {
                        itemid = document.getElementById(p[j].id).id

                        if (itemid.substring(0, 2) == "PS") {
                            psid = itemid.replace("PS", "")

                            psidset = psid.split("|");

                            for (i = 0; i < psidset.length; i += 3) {
                                psid = psidset[0]
                                amt = psidset[1]
                                status = psidset[2]
                            }

                            clistps += psid + ","
                        }
                    }
                }

                clistps = clistps.substring(0, clistps.length - 1)

                carrayps = clistps.split(",")
                if (carrayps != "") {
                    for (j = 0; j < carrayps.length; j++) {
                        // this should launch thepsprintforemail and psprint
                        generateInvoicesPS(shopid, carrayps[j])
                    }

                    $('#checkallps').prop('checked', false)
                    if (type == 'email')
                        setTimeout("sendTheInvoicesPS()", 3000)
                }
            }

            if (type == 'print')
                PrintInvoices()
            // now create array from clist and loop it to create the invoices

        } else {
            sbalert("You must enter a valid email address to send invoices to")
        }
    }

    function generateInvoices(roid) {
        r = Math.floor((Math.random() * 100000) + 1);

        var reprint = 'yes';
        var cc = 'y';

        var ds = 'cc=' + cc + '&reprint=' + reprint + '&roid=' + roid + '&r=' + r;

        $.ajax({
            type: "GET",
            url: "<?= COMPONENTS_PUBLIC . "/invoices/" . $r; ?>",
            data: ds,
            async : false,
            success: function (r) {
                rt = r + ","
                document.getElementById("returnlist").value += rt
            }
        });
    }

    function generateInvoicesPS(shopid, psid) {

        r = Math.floor((Math.random() * 100000) + 1);

        var ds = 'shopid=' + shopid + '&psid=' + psid + "&fullpath=yes";

        $.ajax({
            type: "GET",
            url: "<?= COMPONENTS_PRIVATE ?>/partsale/psprintpdf.php",
            data: ds,
            async: false,
            success: function (r) {
                    pathfile = r
                    rt = pathfile + ","
                    document.getElementById("returnlistps").value += rt
            }
        });
    }

    function sendTheInvoices() {

        //validate the returnlist has a csv list in it
        if (document.getElementById("returnlist").value.length > 0) {
            //send the csvlist to sendinvoices.asp
            r = Math.floor((Math.random() * 100000) + 1);
            email = $('#sendingemail').val()
            if (document.getElementById("update").checked == true) {
                upd = "yes"
            } else {
                upd = "no"
            }

            cid = $('#selectedcustomerid').val()
            msg = encodeURIComponent($('#emailmessage').val())

            var ds = 'customerid=' + cid + '&msg=' + msg + '&email=' + email + '&update=' + upd + '&invoices=' + $('#returnlist').val() + "&r=" + r

            $.ajax({
                type: "POST",
                url: "sendinvoices.php",
                data: ds,
                success: function (r) {
                    if (r == "success") {
                        $('#emailinvmodal').modal('hide')
                        sbalert("Invoices sent")

                        $("#hider").hide()
                        $("#customeremail").hide()
                        hideLoader()

                        document.getElementById("returnlist").value = ""
                        $("#sendInvoiceButton").css("display", 'none')

                        d = document.getElementsByClassName("form-check-input-ro")
                        if (d.length > 0) {
                            for (j = 0; j < d.length; j++) {
                                c = document.getElementById(d[j].id).checked = false
                            }
                        }
                    } else {
                        sbalert(r)
                    }
                    hideLoader();
                }
            });

        }
    }

    function PrintInvoices() {
        setTimeout(function () {
            list = $('#returnlist').val() + $('#returnlistps').val()
            eModal.iframe({
                title: 'Print Invoices',
                url: 'printinvoices.php?list=' + list,
                size: eModal.size.xl,

            });
            hideLoader()
            $('#returnlist').val('')
            $('#returnlistps').val('')

        }, 6000)
    }

    function sendTheInvoicesPS() {

        //validate the returnlistps has a csv list in it
        if (document.getElementById("returnlistps").value.length > 0) {
            //send the csvlist to sendinvoices.asp
            r = Math.floor((Math.random() * 100000) + 1);
            //email = document.getElementById("sendingemail").value
            email = $('#sendingemail').val()
            if (document.getElementById("update").checked == true) {
                upd = "yes"
            } else {
                upd = "no"
            }

            cid = $('#selectedcustomerid').val()
            msg = encodeURIComponent($('#emailmessage').val())

            var ds1 = 'customerid=' + cid + '&msg=' + msg + '&email=' + email + '&update=' + upd + '&invoices=' + $('#returnlistps').val() + "&r=" + r

            $.ajax({
                type: "POST",
                url: "<?= COMPONENTS_PRIVATE ?>/v2/partsale/sendpsinvoices.php",
                data: ds1,
                success: function () {
                    $('#emailinvmodal').modal('hide')
                    sbconfirm(
                        'Success',
                        'Send Successful',
                        function () {
                            location.reload();
                        }
                    );

                },
                error: function (xhr, ajaxOptions, thrownError) {
                    sbalert("Error in Send Email")
                }
            });
        }
    }


    function showSendInvoices() {
        $('#hider').hide()
        $('#emailinvmodal').modal('show')
    }

    function cancelSendInvoices() {
        $('#hider').hide()
        $('#customeremail').hide()
    }
</script>

</body>
<?php
mysqli_close($conn);
?>

</html>
