<?php

$component = "accounting-v2";
$shopid = $_COOKIE['shopid'];

require CONN;
include getHeadGlobal('');

$today = new DateTime('now');
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);

// Load all data


$stmt = "SELECT id,amount,category,memo,udate,roid ";
$stmt .= "FROM undepositedfunds ";
$stmt .= "WHERE shopid = ? ";
$stmt .= " LIMIT 100 ";
//echo $stmt;

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $udpresult = $query->get_result();
} else {
    echo "Undeposited Funds Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$runningtotal = 0;
$idlist = "";
$showbtn = (bool)$udpresult->num_rows;

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$disabled = $shopIsReadOnly ? "disabled" : "";
?>
<main id="settings" class="min-vh-100">
    <div>
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div class="flex-1">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/accounting.php"
                           class="text-secondary">Accounting</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">Undeposited Funds
                            <i class="fa fa-circle-info" title="Please select all funds you wish to post, select the expense category
and the appropriate bank to post the amounts to, then click Post Funds above.  NOTE: Only 100 records are shown at a time" data-mdb-toggle="tooltip"></i>
                        </h2>
                    </div>
                </div>
                <?php if( !$shopIsReadOnly ): ?>
                    <div class="flex-1">
                        <button class="btn btn-primary float-end" type="button" id="postincbtn" onclick="postFunds()">
                            Post All Income
                        </button>
                        <?php if ($showbtn) { ?>
                            <button onclick="deleteallFunds()" class="btn btn-secondary float-end mx-2 " name="Button2"
                                    type="button">Delete
                            </button>
                        <?php } ?>
                    </div>
                <?php endif; ?>
            </div>
            <hr/>
        </div>
    </div>
    <div class="container-fluid">
        <div class="row mt-4">
            <div class="col-md-12">
                <form id="mainform" name="mainform" method="post" action="undepositedfunds.php">
                    <input name="sub" type="hidden"/>
                    <table class="sbdatatable w-100" id="undep_table">
                        <thead>
                        <tr>
                            <?php if( !$shopIsReadOnly ): ?>
                                <th class="text-center">
                                    <input type="checkbox" class="form-check-input" id="master" checked onclick="checkAll()"/>
                                </th>
                            <?php endif; ?>

                            <th>Date</th>
                            <th>Category</th>
                            <th>Memo</th>
                            <th>RO/PS #</th>
                            <th>Amount</th>
                            <th>Post To</th>

                            <?php if( !$shopIsReadOnly ): ?>
                                <th>Delete</th>
                            <?php endif; ?>
                        </tr>
                        </thead>
                        <tbody>
                        <?php
                        if ($udpresult->num_rows) {

                        while ($udp = mysqli_fetch_assoc($udpresult)) {
                            $runningtotal = $runningtotal + $udp["amount"];
                            $transdate = new DateTime($udp["udate"]);
                            ?>
                            <input type="hidden" name="transdate<?php echo $udp["id"]; ?>"
                                   value="<?php echo $udp["udate"]; ?>">
                            <tr>
                                <?php if( !$shopIsReadOnly ): ?>
                                    <td class="text-center">
                                        <input value="on" class="form-check-input" checked="checked" name="post<?php echo $udp["id"]; ?>" type="checkbox"/>
                                    </td>
                                <?php endif; ?>
                                <td id="transdate"><?php echo date_format($transdate, 'm/d/Y'); ?></td>
                                <td id="category">
                                    <select class="select data" name="category<?php echo $udp["id"]; ?>" <?php echo $disabled; ?>>
                                        <?php
                                        $stmt = "SELECT category 
                                                FROM chartofaccounts 
                                                WHERE shopid = ? 
                                                AND cattype = 'Income'";

                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("s", $shopid);
                                            $query->execute();
                                            $cofaresult = $query->get_result();
                                        } else {
                                            echo "Chart of Accts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }

                                        if ($cofaresult->num_rows > 0) {
                                            while ($cofa = mysqli_fetch_assoc($cofaresult)) {
                                                $cs = $cofa["category"] == $udp["category"] ? "selected='selected'" : "";
                                                ?>
                                                    <option
                                                        <?php echo $cs; ?>
                                                        value="<?php echo $cofa["category"]; ?>"
                                                    >
                                                        <?php echo $cofa["category"]; ?>
                                                    </option>
                                                <?php
                                            }
                                        }
                                        ?>
                                    </select>
                                </td>
                                <td id="memo"><?php echo $udp["memo"]; ?></td>
                                <td class="style9"><?php echo $udp["roid"]; ?></td>
                                <td id="depositamount" class="style7"><?php echo number_format($udp["amount"], 2); ?></td>
                                <td class="style7">
                                    <?php
                                    $stmt = "SELECT id,bankname ";
                                    $stmt .= "FROM bankaccount ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  AND acctstatus != 'Closed' order by isdefault desc";
                                    //echo $stmt;

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("s", $shopid);
                                        $query->execute();
                                        $bankresult = $query->get_result();
                                    } else {
                                        echo "Bank Account Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    if ($bankresult->num_rows > 0) {
                                        ?>
                                        <select class="select data" name="bankaccount<?php echo $udp["id"]; ?>" <?php echo $disabled; ?>>
                                            <?php while ($bank = mysqli_fetch_assoc($bankresult)) { ?>
                                                <option value="<?php echo $bank["id"]; ?>"><?php echo $bank["bankname"]; ?></option>
                                            <?php } ?>
                                        </select>

                                    <?php } else { ?>
                                        <a href="#" onclick="$('#bankmodal').modal('show')"
                                           data-target="#bankModal" data-toggle="modal">
                                            No bank accounts available for these funds. Click Here to
                                            Add
                                        </a>
                                    <?php } ?>
                                </td>

                                <?php if( !$shopIsReadOnly ): ?>
                                    <td class="text-center text-primary">
                                        <i style="color: var(--primary)" onclick="deleteFund('<?php echo $shopid; ?>','<?php echo $udp["id"]; ?>','<?php echo $udp["memo"]; ?>')" class="fas fa-trash"></i>
                                    </td>
                                <?php endif; ?>
                            </tr>
                            <?php
                            $idlist = $idlist . $udp["id"] . ",";
                        } // end of udp whileloop
                        ?>
                        </tbody>
                        <tfoot>
                        <tr>
                            <?php if( !$shopIsReadOnly ): ?>
                                <td class="style10"></td>
                            <?php endif; ?>
                            
                            <td colspan="3"></td>
                            <td>Total:</td>
                            <td><b><?php echo asDollars($runningtotal, 2); ?></b></td>
                            <td class="style10"></td>
                            
                            <?php if( !$shopIsReadOnly ): ?>
                                <td class="text-center"></td>
                            <?php endif; ?>
                        </tr>

                        <?php } ?>
                        </tfoot>
                    </table>
                    <input name="idlist" value="<?php echo $idlist; ?>" type="hidden"/>
                </form>
            </div>
        </div>
    </div>
</main>

<?php if( !$shopIsReadOnly ): ?>
    <div id="bankmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <input id="customerid" type="hidden">
        <div class="modal-dialog modal-md">
            <div class="modal-content p-4">
                <div class="modal-header">
                    <h5 class="modal-title" id="spdLabel">Add New Bank Account</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-outline mb-4">
                        <input class="form-control" id="bankname" name="bankname" type="text"/>
                        <label class="form-label">Bank Name</label>
                    </div>
                    <div class="form-row mb-4">
                        <select class="select" id="accounttype" name="accounttype">
                            <option value="Checking">Checking</option>
                            <option value="Saving">Saving</option>
                            <option value="Credit Card">Credit Card</option>
                            <option value="Money Market">Money Market</option>
                            <option value="Certificate of Deposit">Certificate of Deposit
                            </option>
                        </select>
                        <label class="form-label select-label">Account Type</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" id="accountdescription"
                            name="accountdescription" type="text" value=""/>
                        <label class="form-label">Description</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" id="accountnumber" name="accountnumber"
                            type="text" value=""/>
                        <label class="form-label">Account Number</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" id="routingnumber" name="routingnumber"
                            type="text" value=""/>
                        <label class="form-label">Routing Number</label>
                    </div>
                    <div class="form-outline mb-4 datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                        <input class="form-control" id="opendate" name="opendate" type="text" data-mdb-toggle="datepicker"
                            value="<?php echo date_format($today, 'm/d/Y'); ?>"/>
                        <label class="form-label">Date Opened</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" id="beginningbalance" name="beginningbalance"
                            type="text" value=""/>
                        <label class="form-label">Begining Balance</label>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="saveBankAccount('<?php echo $shopid; ?>')" type="button"
                            class="btn btn-primary">Save
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
$component = '';
include getScriptsGlobal('');
// include getFooterComponent($component);
?>
<script>
    $(document).ready(function () {
        $("#undep_table").dataTable({
            responsive: true,
            fixedHeader: {
                headerOffset: 68
            },
            searching: false,
            colReorder: true,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            columnDefs: [{
                targets: 0,
                orderable: false
            }],
            order: [[1, "asc"]]
        });
    });

    function checkAll() {

        if ($("#master").is(':checked')) {
            $('input[type=checkbox]').each(function () {
                $(this).prop('checked', true);
            });
        } else {
            $('input[type=checkbox]').each(function () {
                $(this).prop('checked', false);
            });
        }
    }

    function postFunds() {
        var s = $("#mainform").serialize();

        sbconfirm("Post Funds", "Post Funds to specified bank account", function () {
            var sub = "yes";
            showLoader();
            $.ajax({
                type: "post",
                url: "<?= COMPONENTS_PRIVATE ?>/accounting/bankaccount/postfunds.php",
                data: s + "&sub=" + sub + "&shopid=<?php echo $shopid; ?>",
                success: function () {
                    window.location.reload(true);
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    sbalert("Error in Insert");
                    hideLoader();
                }
            });
        })
    }

    function deleteallFunds() {
        var s = $("#mainform").serialize();

        sbconfirm("Are you sure", "Are you sure you want to DELETE ALL Undeposited Funds? ", function () {
            var sub = "yes";
            showLoader();
            $.ajax({
                type: "post",
                url: "<?= COMPONENTS_PRIVATE ?>/accounting/bankaccount/deleteallfunds.php",
                data: s + "&sub=" + sub + "&shopid=<?php echo $shopid; ?>",
                success: function () {
                    location.reload();
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    sbalert("Error in mass delete")
                    hideLoader();
                }
            });
        });
    }


    function deleteFund(shopid, id, memo) {

        sbconfirm("Are you sure?", "Are you sure you want to delete this fund?", function () {
            showLoader();
            var ds = 'shopid=' + shopid + '&fundid=' + id + '&memo=' + memo;
            $.ajax({
                type: "post",
                url: "deletefund.php",
                data: ds,
                success: function () {
                    location.reload();
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    sbalert("Error in Deletion")
                    hideLoader();
                }
            });
        });
    }

    function saveBankAccount(shopid) {
        // note the balance has to be saved to the account register
        var bankname = $("#bankname").val();
        var accttype = $("#accounttype").val();
        var acctdesc = $("#accountdescription").val();
        var acctnum = $("#accountnumber").val();
        var routenum = $("#routingnumber").val();
        var dateopen = $("#opendate").val();
        var begbal = $("#beginningbalance").val();

        var ds = 'bankname=' + bankname + '&accounttype=' + accttype + '&accountdescription=' + acctdesc + '&accountnumber=' + acctnum + '&routingnumber=' + routenum + '&opendate=' + dateopen + '&shopid=' + shopid + '&begbal=' + begbal;
        showLoader();
        $.ajax({
            type: "post",
            url: "bankaccount/savebank.php",
            data: ds,
            success: function () {
                sbconfirm("Success", "Bank Account Created", function () {
                    location.reload();
                });
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Insert")
                hideLoader();
            }
        });

    }

    <?php if (!$showbtn) { ?>
    $(document).ready(function () {
        $('#postincbtn').hide()
    });
    <?php } ?>


</script>
</body>
</html>
