<?php

require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];
// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";
$menuComponent = 'preferences';

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>

<style>
    main {
        height: 100% !important;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div >
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div class="flex-1">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/accounting.php" class="text-secondary">Accounting</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">Chart of Accounts</h2>
                    </div>
                </div>

                <?php if( !$shopIsReadOnly ): ?>
                    <div class="flex-1">
                        <button class="btn btn-primary" type="button" onclick="newCat('<?php echo $shopid; ?>')">Add New Category</button>
                    </div>
                <?php endif; ?>
            </div>
            <hr/>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <table id="cattable" class="sbdatatable">
                <thead>
                <tr>
                    <th class="text-start">Category</th>
                    <th class="text-start">Type</th>
                    <th class="text-start">Comment</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $query = "SELECT id,shopid,category,cattype,core ";
                $query .= "from chartofaccounts ";
                $query .= "where shopid = '{$shopid}'";
                $query .= " ORDER BY core, cattype, category ";
                //echo $query;

                $coaresult = mysqli_query($conn, $query);

                if (!$coaresult) {
                    die("Database chart of account query failed.");
                }

                if ($coaresult->num_rows > 0) {
                    while ($coa = $coaresult->fetch_array()) {
                        $editCat = !$shopIsReadOnly
                            ? "onclick=\"editCat('{$coa["id"]}','{$coa["shopid"]}','{$coa["category"]}','{$coa["cattype"]}','{$coa["core"]}')\""
                            : "";
                        ?>
                        <tr style="cursor:pointer" <?= $editCat ?>>
                        <td class="text-start"><?php echo $coa["category"]; ?></td>
                        <td class="text-start"><?php echo $coa["cattype"]; ?></td>
                        <td class="text-start">
                            <?php if ($coa["core"] == "yes") { ?>
                                <span>This category cannot be changed</span>
                            <?php } ?>
                        </td>
                        </tr>
                        <?php
                    }    // end of while
                } // end if
                ?>
                </tbody>
            </table>
        </div>
    </div>
</main>

<?php if( !$shopIsReadOnly ): ?>
    <!-- Add Category Modal -->
    <div id="addcatModal" class="modal fade" role="dialog" aria-hidden="true">
        <input id="shopid" name="shopid" value="" type="hidden">

        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Add New Category</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <table class="table table-sm">
                        <tr>
                            <td class="text-left">Category:</td>
                            <td class="style9">
                                <div class="form-outline">
                                    <input class="form-control" id="category" name="category" type="text"
                                        value=""/>
                                </div>
                            </td>

                        </tr>
                        <tr>
                            <td class="text-left">Type:</td>
                            <td class="style9">
                                <div class="form-row">
                                    <select id="cattype" class="select" name="cattype" type="text" value="">
                                        <?php
                                        $query = "SELECT * ";
                                        $query .= "from accounttypes ";
                                        $query .= "where shopid = '{$shopid}'";
                                        //echo $query;

                                        $accttyperesult = mysqli_query($conn, $query);

                                        if (!$accttyperesult) {
                                            die("Database account types query failed.");
                                        }

                                        if ($accttyperesult->num_rows > 0) {
                                            while ($accttype = $accttyperesult->fetch_array()) {

                                                ?>
                                                <option value="<?php echo $accttype["type"]; ?>"><?php echo $accttype["type"]; ?></option>
                                                <?php
                                            }    // end of while

                                        } // end if
                                        ?>
                                    </select>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="addCat()" type="button" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Edit Category Modal -->
    <div id="catModal" class="modal fade" role="dialog" aria-hidden="true">
        <input id="id" name="id" value="" type="hidden">
        <input id="shopid" name="shopid" value="" type="hidden">

        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Edit Category</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <table class="table table-sm">
                        <tr>
                            <td class="text-left">Category:</td>
                            <td class="style9">
                                <div class="form-outline">
                                    <input class="form-control" id="category2" name="category2" type="text"
                                        value=""/>
                                </div>
                            </td>

                        </tr>
                        <tr>
                            <td class="text-left">Type:</td>
                            <td class="style9">
                                <div class="form-row">
                                    <select id="cattype2" class="select" name="cattype2" type="text" value="">
                                        <?php
                                        $query = "SELECT * ";
                                        $query .= "from accounttypes ";
                                        $query .= "where shopid = '{$shopid}'";
                                        //echo $query;

                                        $accttyperesult = mysqli_query($conn, $query);

                                        if (!$accttyperesult) {
                                            die("Database account types query failed.");
                                        }

                                        if ($accttyperesult->num_rows > 0) {
                                            while ($accttype = $accttyperesult->fetch_array()) {

                                                ?>
                                                <option value="<?php echo $accttype["type"]; ?>"><?php echo $accttype["type"]; ?></option>
                                                <?php
                                            }    // end of while

                                        } // end if
                                        ?>
                                    </select>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="saveCat()" type="button" class="btn btn-primary">Save</button>
                    <button onclick="deleteCat()" type="button" class="btn btn-secondary">Delete</button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>

<script>
    $(document).ready(function () {
        $(".sbdatatable").dataTable({
            responsive: true,
            retrieve: true,
            searching: false,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            order : []
        });
    });

    function deleteCat() {
        var id = $("#id").val();
        var shopid = $("#shopid").val();

        showLoader();

        sbconfirm(
            'Are you sure?',
            'This category will be deleted.  Are you sure?',
            function () {
                var ds = 'id=' + id + '&shopid=' + shopid;
                $.ajax({
                    type: "post",
                    url: "deletecategory.php",
                    data: ds,
                    success: function () {
                        location.reload();
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        sbalert("Error in Deletion")
                    }
                });
            }
        );

        hideLoader()
    }

    function editCat(id, shopid, category, type, core) {
        if (core != "yes") {

            $('#catModal').modal('show')

            $('#id').val(id)
            $('#shopid').val(shopid)
            $('#category2').val(category)
            $('#cattype2').val(type).toggleClass('active')
        } else {
            sbalert("This category can not be changed")
        }
    }

    function newCat(shopid) {

        $('#addcatModal').modal('show')
        $('#shopid').val(shopid)

    }

    function saveCat() {
        var id = $("#id").val();
        var shopid = $("#shopid").val();
        var category = $("#category2").val();
        var type = $("#cattype2").val();

        var ds = 'id=' + id + '&shopid=' + shopid + '&category=' + category + '&cattype=' + type;

        showLoader();

        $.ajax({
            type: "post",
            url: "editcategory.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()
    }


    function addCat() {
        var shopid = $("#shopid").val();
        var category = $("#category").val();
        var type = $("#cattype").val();
        var core = "yes";

        var ds = 'shopid=' + shopid + '&category=' + category + '&cattype=' + type + '&core=' + core;

        showLoader();

        $.ajax({
            type: "post",
            url: "addcategory.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()
    }
</script>
</body>

</html>
