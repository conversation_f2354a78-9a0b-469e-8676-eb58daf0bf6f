<?php

require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];
// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";
$menuComponent = 'preferences';

include getHeadGlobal('');
include getHeadComponent('');
include getRulesGlobal('');
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$disabled = $shopIsReadOnly ? "disabled" : "";
?>
<style>
    main {
        height: 100% !important;
    }
</style>

<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
    <!-- Main Container -->
    <main id="main-container">
        <div >
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <div class="flex-1">
                        <div class="title col breadcrumb d-flex align-items-center mb-0">
                            <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/accounting.php" class="text-secondary">Accounting</a>
                            <span class="text-secondary ps-3 pe-3">/</span>
                            <h2 class="">Company Preferences</h2>
                        </div>
                    </div>
                </div>
                <hr/>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <input name="action" type="hidden" value="yes"/>
                <table class="table table-sm w-70 m-0">
                    <tr>
                        <td>First Month of Fiscal/Accounting Year</td>
                        <td>
                            <?php
                            // get preferences
                            $merchantaccount = "";
                            $query = "select companyform,fiscalmonth,accountingmethod ";
                            $query .= "from company ";
                            $query .= "WHERE shopid = '{$shopid}' ";
                            //echo $query;

                            $coresult = mysqli_query($conn, $query);

                            if (!$coresult) {
                                die("Database company preferences query failed.");
                            }
                            $co = mysqli_fetch_assoc($coresult);

                            $month = $co["fiscalmonth"];
                            $compform = $co["companyform"];
                            $acctmeth = $co["accountingmethod"];

                            ?>

                            <div class="form-row">
                                <select class="select" name="fiscalmonth" id="fiscalmonth" <?= $disabled; ?>>
                                    <option <?php if ($month == "January") {
                                        echo "selected='selected'";
                                    } ?> value="January">January
                                    </option>
                                    <option <?php if ($month == "February") {
                                        echo "selected='selected'";
                                    } ?> value="February">February
                                    </option>
                                    <option <?php if ($month == "March") {
                                        echo "selected='selected'";
                                    } ?> value="March">March
                                    </option>
                                    <option <?php if ($month == "April") {
                                        echo "selected='selected'";
                                    } ?> value="April">April
                                    </option>
                                    <option <?php if ($month == "May") {
                                        echo "selected='selected'";
                                    } ?> value="May">May
                                    </option>
                                    <option <?php if ($month == "June") {
                                        echo "selected='selected'";
                                    } ?> value="June">June
                                    </option>
                                    <option <?php if ($month == "July") {
                                        echo "selected='selected'";
                                    } ?> value="July">July
                                    </option>
                                    <option <?php if ($month == "August") {
                                        echo "selected='selected'";
                                    } ?> value="August">August
                                    </option>
                                    <option <?php if ($month == "September") {
                                        echo "selected='selected'";
                                    } ?> value="September">September
                                    </option>
                                    <option <?php if ($month == "October") {
                                        echo "selected='selected'";
                                    } ?> value="October">October
                                    </option>
                                    <option <?php if ($month == "November") {
                                        echo "selected='selected'";
                                    } ?> value="November">November
                                    </option>
                                    <option <?php if ($month == "December") {
                                        echo "selected='selected'";
                                    } ?> value="December">December
                                    </option>
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Company Form</td>
                        <td>
                            <div class="form-row">
                                <select class="select" name="companyform" id="companyform" <?= $disabled; ?>>
                                    <option <?php if ($compform == "Sole Proprietor") {
                                        echo "selected='selected'";
                                    } ?> value="Sole Proprietor">Sole Proprietor
                                    </option>
                                    <option <?php if ($compform == "Partnership") {
                                        echo "selected='selected'";
                                    } ?> value="Partnership">Partnership
                                    </option>
                                    <option <?php if ($compform == "LLC (Limited Liability Company)") {
                                        echo "selected='selected'";
                                    } ?> value="LLC (Limited Liability Company)">LLC (Limited Liability Company)
                                    </option>
                                    <option <?php if ($compform == "Chapter C Corporation") {
                                        echo "selected='selected'";
                                    } ?> value="Chapter C Corporation">Chapter C Corporation
                                    </option>
                                    <option <?php if ($compform == "Chapter S Corporation") {
                                        echo "selected='selected'";
                                    } ?> value="Chapter S Corporation">Chapter S Corporation
                                    </option>
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Cash or Accrual Reporting<br/>
                            <span class="style4">When you select Cash Reporting, all reports
			will be generated based <br>on payments received, not invoices
			generated.&nbsp; Accrual Reporting means that all <br>reports are based
			on invoices generated, not when payment is received.&nbsp; <br>You can
			override reports on a case-by-case basis if desired.</span></td>

                        <td>
                            <div class="form-row">
                                <select class="select" name="accountingmethod" id="accountingmethod" <?= $disabled; ?>>
                                    <option <?php if ($acctmeth == "Cash") {
                                        echo "selected='selected'";
                                    } ?> value="Cash">Cash
                                    </option>
                                    <option <?php if ($acctmeth == "Accrual") {
                                        echo "selected='selected'";
                                    } ?> value="Accrual">Accrual
                                    </option>
                                </select>
                            </div>
                        </td>

                    </tr>
                    <?php if (!$shopIsReadOnly): ?>
                        <tr>
                            <td colspan="2" class="style5">
                                <button onclick="savePref('<?php echo $shopid; ?>')" type="button" class="btn btn-primary">Save</button>
                            </td>
                        </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>
    </main>
    <!-- END Main Container -->
</div>
<!-- END Page Container -->
<!-- Modals -->

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>
<script>

    function savePref(shopid) {

        showLoader();

        var fm = $("#fiscalmonth").val();
        var cf = $("#companyform").val();
        var am = $("#accountingmethod").val();

        var ds = 'shopid=' + shopid + '&fiscalmonth=' + fm + '&companyform=' + cf + '&accountingmethod=' + am;
        $.ajax({
            type: "post",
            url: "editpreferences.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()
    }

</script>

</body>

<?php
mysqli_close($conn);
?>

</html>
