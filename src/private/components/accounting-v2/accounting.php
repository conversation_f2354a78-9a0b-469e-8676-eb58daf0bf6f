<!-- Main Container -->
<style>
    .card {
        background: none;
    }

    .card-text {
        height: 400px;
        overflow-y: scroll;
    }
</style>

<main id="main-container" class="min-vh-100">
    <?php if (!empty($capitalarr)) { ?>
        <div class="alert alert-dismissible fade show text-center" role="alert" data-mdb-color="primary">
            <a href="<?= $capitalarr['url'] ?>" target='_blank'><?= $capitalarr['message'] ?></a>
            <button type="button" class="btn-close" data-mdb-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php } ?>

    <div class="row">
        <div class="col-md-12">
            <h2 class="">Accounting</h2>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-4" id="accountsreceivable">
            <div class="card card-square w-100">
                <div class="card-body">
                    <div class="row p-2 border-bottom">
                        <a href="ar/ar.php" class="d-inline card-title">Current Accounts Receivable</a></div>
                    <div class="card-text pt-3 ms-2 lh-lg">
                        <div>
                            <!--                AR table-->
                            <div class="mb-2">
                                <table id="artable" class="sbdatatable w-100">
                                    <thead>
                                    <!-- first row within table -->
                                    <tr>
                                        <th>RO</th>
                                        <th>Date</th>
                                        <th>Days</th>
                                        <th>Name Home / Work / Cell</th>
                                        <th>Total RO</th>
                                        <th> Balance</th>
                                    </tr>
                                    </thead>

                                    <tbody>
                                    <?php
                                    $stmt = "SELECT totalprts,totallbr,totalsublet,userfee1,userfee2,userfee3,hazardouswaste,salestax,discountamt,rotype,totalfees";
                                    $stmt .= ",roid,finaldate,lastfirst,customerphone,cellphone,customerwork,totalro,balance,shopid ";
                                    $stmt .= "from repairorders ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  AND balance > 0.01 ";
                                    $stmt .= "  AND ucase(status) = 'CLOSED' ";
                                    $stmt .= "  AND (ucase(rotype) != 'NO PROBLEM' and ucase(rotype) != 'NO APPROVAL') ";
                                    $stmt .= "order by balance desc";
                                    //echo $query;

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("s", $shopid);
                                        $query->execute();
                                        $result = $query->get_result();
                                    } else {
                                        echo "Repair Orders Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    $bal = 0;
                                    $c = 0;
                                    $finaldate = "";
                                    $days = "";
                                    $lf = "";
                                    $runningbal = 0;
                                    $locationstring = "";
                                    $statusdate = "";

                                    // if accounts to be collected else send msg -->

                                    if ($result->num_rows > 0) {
                                        while ($row = $result->fetch_array()) {
                                            $roid = $row["roid"];
                                            $shopid = $row["shopid"];
                                            $dstart = null;
                                            $lf = "";

                                            $stmt = "select sum(amt) as a  ";
                                            $stmt .= "from accountpayments ";
                                            $stmt .= "WHERE shopid = ? ";
                                            $stmt .= "  AND roid = ? ";
                                            //echo $stmt;

                                            if ($query = $conn->prepare($stmt)) {
                                                $query->bind_param("si", $shopid, $roid);
                                                $query->execute();
                                                $apresult = $query->get_result();
                                            } else {
                                                echo "AccountPayments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                            }

                                            $ap = $apresult->fetch_array();

                                            $ap_amount = $ap['a'];

                                            if ($ap_amount > 0) {
                                                $ropayments = $ap_amount;
                                            } else {
                                                $ropayments = 0;
                                            }

                                            $stmt = "select coalesce(sum(feeamount),0) from rofees where shopid = '$shopid' and roid = $roid";
                                            if ($query = $conn->prepare($stmt)){
                                                $query->execute();
                                                $query->bind_result($otherfees);
                                                $query->fetch();
                                                $query->close();
                                            }

                                            // moved ttlro calc to here

                                            $ttlro = $row["totalprts"] + $row["totallbr"] + $row["totalsublet"] + $row["hazardouswaste"] + $row["userfee1"] + $row["userfee2"] + $row["userfee3"] + $otherfees + $row["salestax"] - $row["discountamt"];

                                            $bal = $ttlro - $ropayments;
                                            $runningbal = $runningbal + $bal;
                                            //$bal = "$".number_format($bal); for some reason this rounded up to the dollar amt
                                            $displaybal = asdollars($bal);

                                            if ($bal > 0.01) {
                                                ?>
                                                <!-- second row within table -->
                                                <tr class="sbphover-row">
                                                    <td style="width: 5%; height: 39px;" valign="top">
                                                        <a class="text-primary" href="#" onclick="editAccount('<?= $row["shopid"]; ?>','<?= $row["roid"]; ?>')"><?= $row["roid"]; ?></a>
                                                    </td>
                                                    <?php
                                                    if ($row["finaldate"] == "0000-00-00" || $row["finaldate"] == null) {
                                                        $finaldate = "";
                                                        $days = "";
                                                    } else {
                                                        $finaldate = $row["finaldate"];
                                                        $dStart = new DateTime($finaldate);
                                                        $dEnd = new DateTime();
                                                        $diff = $dStart->diff($dEnd);
                                                        $days = $diff->format("%a");
                                                    }
                                                    if (!$row["customerphone"]) {
                                                        $customerphone = "NA";
                                                    } else {
                                                        $customerphone = formatPhone(strtoupper($row["customerphone"]));
                                                    }
                                                    if (!$row["customerwork"]) {
                                                        $customerwork = "NA";
                                                    } else {
                                                        $customerwork = formatPhone(strtoupper($row["customerwork"]));
                                                    }
                                                    if (!$row["cellphone"]) {
                                                        $cellphone = "NA";
                                                    } else {
                                                        $cellphone = formatPhone(strtoupper($row["cellphone"]));
                                                    }
                                                    if (strlen($row["customerphone"]) > 0 || strlen($row["customerwork"]) > 0 || strlen($row["cellphone"]) > 0) {
                                                        $lf = $row["lastfirst"] . "<br />" . $customerphone . " / " . $customerwork . " / "
                                                            . $cellphone;
                                                    } else {
                                                        $lf = $row["lastfirst"];
                                                    }

                                                    // moved up 7/25/17
                                                    //$ttlro = $row["totalprts"]+$row["totallbr"]+$row["totalsublet"]+$row["hazardouswaste"]+$row["userfee1"]+$row["userfee2"]+$row["userfee3"]+$row["salestax"]-$row["discountamt"];

                                                    if ($row["finaldate"] == "0000-00-00" || $row["finaldate"] == null) {
                                                        $finaldate = "";
                                                    } else {
                                                        $finaldate = date_format($dStart, 'm/d/Y');
                                                    }
                                                    ?>
                                                    <td style="width: 20%; height: 39px;"
                                                        valign="top"><?= $finaldate; ?>&nbsp;
                                                    </td>
                                                    <td style="width: 5%; height: 39px;"
                                                        valign="top"><?= $days; ?>&nbsp;
                                                    </td>
                                                    <td style="width: 30%; height: 39px;"
                                                        valign="top"><?= $lf; ?>&nbsp;
                                                    </td>
                                                    <?php

                                                    $ttlro = asDollars($ttlro);
                                                    ?>
                                                    <td style="width: 15%; height: 39px;"
                                                        valign="top"><?= sbpround($ttlro, 2); ?>&nbsp;&nbsp;
                                                    </td>
                                                    <td style="width: 15%; height: 39px;"
                                                        valign="top"><?= sbpround($displaybal, 2); ?>&nbsp;&nbsp;
                                                    </td>
                                                </tr>
                                                <?php
                                            } // this is the end of the check for > 0.01 bal
                                        } // end of while
                                    }
                                    ?>
                                    </tbody>
                                </table>
                                <hr>
                            </div>

                            <!--                Part sale table-->
                            <div>
                                <table id="pstable" class="sbdatatable w-100">
                                    <thead>
                                    <tr>
                                        <th>Part Sale#</th>
                                        <th>Date Sold</th>
                                        <th>Days</th>
                                        <th>Customer</th>
                                        <th>Total Invoice</th>
                                        <th>Balance</th>
                                    </tr>
                                    </thead>
                                    <!-- Beginning of Parts Sales -->
                                    <?php
                                    $stmt = "select * ";
                                    $stmt .= "from ps ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  AND round(balance,2) > 0.01 ";
                                    $stmt .= "  AND psdate > '2012-10-31' ";
                                    $stmt .= "  AND ucase(status) = 'CLOSED' ";
                                    //echo $stmt;

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("s", $shopid);
                                        $query->execute();
                                        $psresult = $query->get_result();
                                    } else {
                                        echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    $numofrows = $psresult->num_rows;

                                    $finaldate = "";
                                    $days = "";
                                    $lf = "";
                                    $bal = 0;
                                    $runningbal = 0;
                                    $lastname = "";
                                    $firstname = "";

                                    while ($ps = $psresult->fetch_array()) {

                                        $stmt = "SELECT sum(amt) as a ";
                                        $stmt .= "from `accountpayments-ps` ";
                                        $stmt .= "WHERE shopid = ? ";
                                        $stmt .= "  AND psid = ?";
                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("si", $shopid, $ps["psid"]);
                                            $query->execute();
                                            $apresult = $query->get_result();
                                        } else {
                                            echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }

                                        $aprow = $apresult->fetch_array();

                                        $ap_amount = $aprow['a'];

                                        if ($ap_amount > 0) {
                                            $pspayments = $ap_amount;
                                        } else {
                                            $pspayments = 0;
                                        }

                                        $bal = $ps['total'] - $pspayments;

                                        if ($bal > 0.01) {

                                            $customerid = $ps["cid"];
                                            if ($ps["psdate"] == "0000-00-00") {
                                                $psdate = "";
                                                $days = "";
                                            } else {
                                                $psdate = $ps["psdate"];
                                                $dStart = new DateTime($psdate);
                                                $dEnd = new DateTime();
                                                $diff = $dStart->diff($dEnd);
                                                $days = $diff->format("%a");
                                            }

                                            $stmt = "SELECT LastName, FirstName FROM customer WHERE shopid = ? and customerid = ?";

                                            if ($query = $conn->prepare($stmt)) {
                                                $query->bind_param("si", $shopid, $customerid);
                                                $query->execute();
                                                $custresult = $query->get_result();
                                            } else {
                                                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                            }


                                            while ($cust = $custresult->fetch_array()) {

                                                $custlname = $cust["LastName"];
                                                $custfname = $cust["FirstName"];
                                                $lf = $custlname . "," . $custfname;
                                            } // end of cust loop

                                            $bal = $bal + $ps["balance"];
                                            $total = $ps["total"];
                                            $balance = $ps["balance"];
                                            $total = asDollars($total);
                                            //$bal = asDollars($bal);


                                            $total = $ps["total"];
                                            $balance = $ps["balance"];
                                            $total = asDollars($total);
                                            // changed code for Shop 4075 rick.shopbossdev.com
                                            //$bal = asDollars($bal);

                                            ?>
                                            <tr>
                                                <td style="width: 5%">
                                                    <a class="text-primary" href="#" onclick="editpsAccount('<?= $ps["shopid"]; ?>','<?= $ps["psid"]; ?>')"><?= $ps["psid"]; ?></a>
                                                </td>
                                                <td style="width: 20%"
                                                ><?= date_format($dStart, 'm/d/Y'); ?>
                                                    &nbsp;
                                                </td>
                                                <td style="width: 5%"><?= $days; ?>&nbsp;</td>
                                                <td style="width: 30%"><?= $lf; ?>&nbsp;</td>
                                                <td style="width: 15%;"
                                                    valign="top"><?= sbpround($total, 2); ?>&nbsp;
                                                </td>
                                                <td style="width: 15%;"
                                                    valign="top"><?= sbpround($balance, 2); ?>&nbsp;
                                                </td>
                                            </tr>
                                            <?php
                                        }
                                    } // end of while
                                    ?>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4" id="bankaccount">
            <div class="card card-square w-100">
                <div class="card-body">
                    <div class="row p-2 border-bottom">
                        <a href="../accounting/bankaccount/accountlist.php" class="d-inline card-title">Accounts and Balances</a>
                    </div>
                    <div class="card-text pt-3 ms-2 lh-lg">
                        <!--            Banks table-->
                        <table id="banktable" class="sbdatatable w-100">
                            <thead>
                            <tr>
                                <th>Bank Name</th>
                                <th>Account Description</th>
                                <th>Account Type</th>
                                <th>Balance</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php

                            $stmt = "SELECT count(*) as c";
                            $stmt .= " FROM bankaccount WHERE shopid = ? ";

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $result = $query->get_result();
                            } else {
                                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            $row = $result->fetch_array();
                            $c = $row['c'];

                            $stmt = "SELECT sum(amount) as amt";
                            $stmt .= " FROM undepositedfunds WHERE shopid = ? ";

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $result = $query->get_result();
                            } else {
                                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            $udp = $result->fetch_array();

                            $numofrows = $result->num_rows;

                            if ($numofrows > 0) {
                                //if ($udpamount->num_rows > 0) {
                                if (!empty($udp["amt"])) {
                                    $udpamt = $udp["amt"];
                                    $udpamt = asDollars($udpamt);
                                } else {
                                    $udpamt = 0.00;
                                }
                                ?>
                                <tr>

                                    <td class="text-primary">
                                        <a class="text-primary" href="<?php
                                        if ($c > 0) {
                                            echo "undepositedfunds.php";
                                        } else {
                                            echo "javascript: sbalert('You must have a bank account to post Undeposited Funds')";
                                        } ?>">
                                            Undeposited Funds
                                        </a>
                                    </td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>
                                        <a href="undepositedfunds.php"><?= $udpamt; ?></a>&nbsp;
                                    </td>
                                </tr>

                                <?php
                            } // end of i amt of rows > 0

                            $stmt = "SELECT sum(amount) as amt";
                            $stmt .= " FROM unpostedexpenses WHERE shopid = ? ";

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $upeamount = $query->get_result();
                            } else {
                                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            $upe = $upeamount->fetch_array();

                            if ($upeamount->num_rows > 0) {
                                if (!empty($upe["amt"])) {
                                    $upeamt = $upe["amt"];
                                    $upeamt = asDollars($upeamt);
                                } else {
                                    $upeamt = 0.00;
                                }
                                ?>
                                <tr>
                                    <td class="text-primary">
                                        <a class="text-primary" href="<?php
                                        if ($c > 0) {
                                            echo "unpostedexpenses.php";
                                        } else {
                                            echo "javascript: sbalert('You must have a bank account to post Unposted Expenses')";
                                        }
                                        ?>">
                                            Unposted Expenses
                                        </a>
                                    </td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>
                                        <a href="unpostedexpenses.php"><?= $upeamt; ?></a>&nbsp;
                                    </td>
                                </tr>
                                <?php
                            } // end of upe amt of rows > 0

                            $stmt = "SELECT * ";
                            $stmt .= " FROM bankaccount WHERE shopid = ? ";
                            $stmt .= "  AND acctstatus != 'Closed'";

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $bankresult = $query->get_result();
                            } else {
                                echo "Bank Account Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            if ($bankresult->num_rows > 0) {
                                while ($bank = $bankresult->fetch_array()) {
                                    $id = $bank["id"];
                                    $stmt = "SELECT sum(depositamount) as d, ";
                                    $stmt .= "sum(paymentamount) as p  ";
                                    $stmt .= "from accountregister ";
                                    $stmt .= "WHERE shopid = ? ";
                                    $stmt .= "  AND accountid = ? ";

                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("si", $shopid, $id);
                                        $query->execute();
                                        $acctregresult = $query->get_result();
                                    } else {
                                        echo "Acount Register Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    }

                                    $acctreg = mysqli_fetch_assoc($acctregresult);

                                    $d = $acctreg["d"];
                                    $p = $acctreg["p"];
                                    $b = $d - $p;
                                    // check for numeric
                                    $b = asDollars($b);
                                    ?>
                                    <tr>
                                        <td class="text-primary">
                                            <a class="text-primary" href='../accounting/bankaccount/accountlist.php'>
                                                <?= $bank["bankname"]; ?>
                                            </a>&nbsp;
                                        </td>
                                        <td><?= $bank["accountdescription"]; ?></td>
                                        <td><?= $bank["accounttype"]; ?></td>
                                        <td><?= $b; ?></td>
                                    </tr>
                                    <?php
                                } // end of do while loop
                            } else if (!$shopIsReadOnly){ // else of if

                                ?>
                                <tr>
                                    <td colspan="4">
                                        <a href="#" onclick="$('#bankmodal').modal('show')"
                                           data-target="#bankModal" data-toggle="modal" class="text-primary">
                                            No Bank Accounts Added. Click to Add&nbsp;
                                        </a>
                                    </td>
                                </tr>
                                <?php
                            } // end of if
                            ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4" id="accountspayable">
            <div class="card card-square w-100">
                <div class="card-body">
                    <div class="row p-2 border-bottom">
                        <a href="expenses/expenses.php" class="d-inline card-title">Current Accounts Payable</a></div>
                    <div class="card-text pt-3 ms-2 lh-lg">
                        <!--            Current accounts table-->
                        <table id="currentaccountstable" class="sbdatatable w-100">
                            <thead>
                            <tr>
                                <th>Payee</th>
                                <th>Amount</th>
                                <th>Due Date</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $fontcolor = "";

                            $stmt = "SELECT id,shopid,expensename,expensecategory,amount,duedate,roid ";
                            $stmt .= "from expenses ";
                            $stmt .= "WHERE shopid = ? ";
                            $stmt .= "  AND expensepaid = 'no'";
                            $stmt .= " order by duedate";
                            //echo $stmt;

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $expresult = $query->get_result();
                            } else {
                                echo "Expenses Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            if ($expresult->num_rows > 0) {
                                while ($exp = $expresult->fetch_array()) {

                                    $duedate = new DateTime($exp["duedate"]);
                                    $today = new Datetime();
                                    $expiredays = $today->diff($duedate);

                                    ?>
                                    <tr 
                                        <?php if (!$shopIsReadOnly): ?>
                                            onclick="editExp('<?= $exp["id"]; ?>','<?= $exp["shopid"]; ?>','<?= $exp["expensename"]; ?>','<?= $exp["amount"]; ?>','<?= $exp["expensecategory"]; ?>','<?= date_format($duedate, 'm/d/Y'); ?>','<?= $exp["roid"]; ?>')"
                                        <?php endif; ?>
                                    >
                                        <td class="text-primary">
                                            <?= $exp["expensename"]; ?>
                                        </td>
                                        <td><?= asDollars($exp["amount"]); ?></td>
                                        <td>
                                            <?php if ($exp["duedate"] != '0000-00-00') {
                                                echo $duedate->format('m/d/Y');
                                            } else {
                                                $duedate = "";
                                            } ?>
                                        </td>
                                    </tr>
                                    <?php
                                } //    rs.movenext

                            } else {
                                ?>
                                <tr>
                                    <td>None Due</td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <?php
                            } //end if
                            ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4" id="reminders">
            <div class="card card-square w-100">
                <div class="card-body">
                    <div class="row p-2 border-bottom">
                        <a href="reminders/reminders.php" class="d-inline card-title">Reminders</a></div>
                    <div class="card-text pt-3 ms-2 lh-lg">
                        <!--            Reminders table-->
                        <table id="reminderstable" class="sbdatatable w-100">
                            <thead>
                            <tr>
                                <th>Subject&nbsp;</th>
                                <th>Due Date</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            // Cleaning up query from * to 2 cols 12/24/19
                            $stmt = "SELECT remindersubject,reminderdate ";
                            $stmt .= "FROM accountingreminders ";
                            $stmt .= "WHERE shopid = ? ";
                            $stmt .= "  AND completed = 'no'";
                            $stmt .= " ORDER BY reminderdate";

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $remresult = $query->get_result();
                            } else {
                                echo "Reminders Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            if ($remresult->num_rows > 0) {
                                while ($rem = $remresult->fetch_array()) {
                                    $reminderdate = new DateTime($rem["reminderdate"]);

                                    ?>
                                    <tr onclick="location.href='reminders/reminders.php'">
                                        <td class="text-primary">
                                            &nbsp;&nbsp;<?= $rem["remindersubject"]; ?></td>
                                        <td
                                        ><?php
                                            if ($rem["reminderdate"] != '0000-00-00') {
                                                $reminderdate = $reminderdate->format('m/d/Y');
                                            } else {
                                                $reminderdate = "";
                                            }
                                            ?>
                                            <?= $reminderdate ?>
                                        </td>
                                    </tr>
                                    <?php
                                }

                            } else {
                                ?>
                                <tr>
                                    <td>&nbsp;&nbsp;None</td>
                                    <td></td>
                                </tr>
                                <?php
                            } //end if
                            ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<!-- END Main Container -->

