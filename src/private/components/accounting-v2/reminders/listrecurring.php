<?php

require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];

$today = new DateTime('now');
$today = date_format($today, 'm/d/Y');
// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";
$menuComponent = 'reminders';

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<!-- END Stylesheets -->
<style>
    main {
        height: 100% !important;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div class="d-flex justify-content-between mb-2">
        <div>
            <h2>List Recurring Reminders</h2>
        </div>

        <?php if( !$shopIsReadOnly ): ?>
            <button class="btn btn-primary" type="button" onclick="newRem('<?php echo $shopid; ?>')">Add Recurring
                Reminder
            </button>
        <?php endif; ?>
    </div>

    <div class="row">
        <div class="col-md-12">
            <table class="sbdatatable">
                <thead>
                <tr>
                    <th>Subject</th>
                    <th>Day Due</th>
                </tr>
                </thead>
                <?php

                $texp = 0;

                $stmt = "SELECT * ";
                $stmt .= " FROM `accountingremindersrecurring` ";
                $stmt .= "WHERE shopid = ? ";
                //echo $stmt;

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $acctresult = $query->get_result();
                } else {
                    echo "Account Reminders Recurring Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                if ($acctresult->num_rows > 0) {
                    while ($acct = $acctresult->fetch_array()) {
                        $onclickRem = !$shopIsReadOnly 
                            ? "onclick=\"editRem(
                                '{$acct["id"]}',
                                '{$acct["shopid"]}',
                                '" . str_replace("'", "\\'", $acct["remindersubject"]) . "',
                                '{$acct["dueday"]}'
                            )\""
                            : "";
                        ?>
                        <tr style="cursor:pointer" <?= $onclickRem; ?>>

                        <td><?php echo strtoupper($acct["remindersubject"]); ?>&nbsp;</td>
                        <td><?php echo $acct["dueday"]; ?>&nbsp;</td>

                        </tr>

                        <?php
                    } // end of reminder while

                } // end of if for reminders
                ?>
            </table>
        </div>
    </div>
</main>
<!-- END Main Container -->

<?php if( !$shopIsReadOnly ): ?>
    <!-- Modals -->
    <!-- Add Reminder Modal -->
    <div id="addremModal" class="modal fade" role="dialog" aria-hidden="true">
        <input id="shopid" name="shopid" value="" type="hidden">

        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Add New Reminder</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <table class="table table-sm">
                        <tr>
                            <td class="text-left">Subject:</td>
                            <td>
                                <div class="form-outline">
                                    <input class="form-control" id="subject" name="subject" type="text"
                                        value=""/>
                                </div>
                            </td>
                        </tr>

                        <tr id="dueday">
                            <td class="text-left">Day of Month to Pay</td>
                            <td>
                                <div class="form-row">
                                    <select class="select" id="dayofmonth" name="dayofmonth">
                                        $j =1;
                                        <?php
                                        $j = 1;
                                        while ($j >= 1 && $j <= 31) {
                                            ?>
                                            <option value="<?php echo $j; ?>"><?php echo $j; ?></option>
                                            <?php
                                            $j = $j + 1;
                                        }
                                        ?>
                                    </select>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="addRem()" type="button" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Recurring Reminder Modal -->
    <div id="remModal" class="modal fade" role="dialog" aria-hidden="true">
        <input id="id" name="id" value="" type="hidden">
        <input id="shopid" name="shopid" value="" type="hidden">

        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Edit Recurring Reminder</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <table class="table table-sm">
                        <tr>
                            <td class="text-left">Subject:</td>
                            <td>
                                <div class="form-outline">
                                    <input type="text" class="form-control" id="subject2" value="">
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left">Day of Month to Pay:</td>
                            <td>
                                <div class="form-row">
                                    <select class="select" id="dueday2" name="dueday2">
                                        $j =1;
                                        <?php
                                        $j = 1;
                                        while ($j >= 1 && $j <= 31) {
                                            ?>
                                            <option value="<?php echo $j; ?>"><?php echo $j; ?></option>
                                            <?php
                                            $j = $j + 1;
                                        }
                                        ?>
                                    </select>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="saveRem()" type="button" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>

<script>
    $(document).ready(function () {
        $(".sbdatatable").dataTable({
            responsive: true,
            retrieve: true,
            searching: false,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            order : []
        });
    });

    function newRem(shopid) {
        $('#addremModal').modal('show')

        $('#id').val(id)
        $('#shopid').val(shopid)
        $('#dueday').val(dueday)
    }

    function addRem(shopid) {
        var shopid = $("#shopid").val();
        var subject = $("#subject").val();
        var dayofmonth = $("#dayofmonth").val();


        var ds = 'shopid=' + shopid + '&subject=' + subject + '&recurring=Yes&dayofmonth=' + dayofmonth;

        showLoader();

        $.ajax({
            type: "post",
            url: "addreminder.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()
    }

    function editRem(id, shopid, subject, dueday) {

        <?php
        if ($shopid == "demo") {
            echo "$('#remModal').modal('show')";
        } else {
            echo "$('#remModal').modal('show')";
        }
        ?>

        $('#id').val(id)
        $('#shopid').val(shopid)
        $('#subject2').val(subject)
        $('#dueday2').val(dueday).toggleClass('active')
    }

    function saveRem() {
        var id = $("#id").val();
        var shopid = $("#shopid").val();
        var subject = $("#subject2").val();
        var dueday = $("#dueday2").val();

        var ds = 'id=' + id + '&shopid=' + shopid + '&subject=' + subject + '&dueday=' + dueday;

        showLoader();

        $.ajax({
            type: "post",
            url: "editreminder.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()
    }


    function recurringPayment(v) {

        if (v == "Yes") {
            document.getElementById("dueday").style.display = ""
        }
        if (v == 'No') {
            $('#dueday').hide()
        }
    }
</script>
</body>

<?php
mysqli_close($conn);
?>

</html>
