<?php

require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];
$today = new DateTime('now');
// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";
$menuComponent = 'reminders';

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>

<style>
    main {
        height: 100% !important;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div class="d-flex justify-content-between mb-2">
        <div>
            <h2>Acct. Reminders</h2>
        </div>

        <?php if( !$shopIsReadOnly ): ?>
            <button class="btn btn-primary" type="button" onclick="newRem('<?php echo $shopid; ?>')">Add Reminder</button>
        <?php endif; ?>
    </div>

    <div class="row">
        <div class="col-md-12">
            <table id="exptable" class="sbdatatable">
                <thead>
                <tr>
                    <th>Subject</th>
                    <th>Date</th>
                </tr>
                </thead>
                <tbody>
                <?php

                $stmt = "SELECT * ";
                $stmt .= "FROM accountingreminders ";
                $stmt .= "WHERE category = '' and shopid = ? ";
                $stmt .= "  AND completed = 'no'";
                //echo $stmt;

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $acctremresult = $query->get_result();
                } else {
                    echo "Account Reminders Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                if ($acctremresult->num_rows > 0) {
                    while ($acctrem = $acctremresult->fetch_array()) {
                        $id = $acctrem["id"];
                        $shopid = $acctrem["shopid"];
                        $remdate = new Datetime ($acctrem["reminderdate"]);

                        ?>

                        <tr style="cursor:pointer"
                            <?php if ( !$shopIsReadOnly ): ?>
                                onclick="editExp('<?php echo $acctrem["id"]; ?>','<?php echo $acctrem["shopid"]; ?>','<?php echo $acctrem["remindersubject"]; ?>','<?php echo date_format($remdate, 'm/d/Y'); ?>')""
                            <?php endif; ?>
                        >
                        <td><?php echo $acctrem["remindersubject"]; ?></td>
                        <td><?php echo date_format($remdate, 'm/d/Y'); ?></td>
                        </tr>
                        <?php
                    }    // end of while
                } // end if
                ?>
                </tbody>
            </table>
        </div>
    </div>
</main>

<!-- Add Reminder Modal -->
<div id="addremModal" class="modal fade" role="dialog" aria-hidden="true">
    <input id="shopid" name="shopid" value="" type="hidden">

    <div class="modal-dialog modal-lg">
        <!-- Modal content-->
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">Add New Reminder</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <table class="table table-sm">
                    <tr>
                        <td class="text-end">Subject:</td>
                        <td>
                            <div class="form-outline">
                                <input class="form-control" id="subject" name="subject" type="text"
                                       value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-end">Reminder Date:</td>
                        <td>
                            <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                <input class="form-control" id="reminderdate" name="reminderdate" data-mdb-toggle="datepicker" type="text"
                                       value=""/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-end">
                            Recurring?
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="If this is a recurring expense, select Yes and then select the day of the month it is due"></i>
                        </td>
                        <td>
                            <div class="form-row">
                                <select onchange="recurringPayment(this.value)" class="select" id="recurring"
                                        name="recurring"
                                        value="no">
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr style="display:none" id="dueday">
                        <td class="text-end">
                            Day of Month to pay
                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Select day of month to pay"></i>
                        </td>
                        <td>
                            <div class="form-row">
                                <select class="select" id="dayofmonth" name="dayofmonth">
                                    $j =1;
                                    <?php
                                    $j = 1;
                                    while ($j >= 1 && $j <= 31) {
                                        ?>
                                        <option value="<?php echo $j; ?>"><?php echo $j; ?></option>
                                        <?php
                                        $j = $j + 1;
                                    }
                                    ?>
                                </select>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="modal-footer d-flex justify-content-center">
                <button onclick="addrem()" type="button" class="btn btn-primary">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Reminder Modal -->
<div id="expModal" class="modal fade" role="dialog" aria-hidden="true">
    <input id="id" name="id" value="" type="hidden">
    <input id="shopid" name="shopid" value="" type="hidden">

    <div class="modal-dialog modal-lg">
        <!-- Modal content-->
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">Edit Reminder</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <table class="table table-sm">
                    <tr>
                        <td class="text-end">Subject:</td>
                        <td>
                            <div class="form-outline">
                                <input type="text" class="form-control" id="subject2" value="">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-end">Date Due:</td>
                        <td>
                            <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                <input class="form-control" id="reminderdate2" name="reminderdate2" data-mdb-toggle="datepicker"
                                       type="text" value=""/>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td class="text-end">Completed</td>
                        <td>
                            <div class="form-row">
                                <select class="select" id="completed" name="completed">
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>

            <div class="modal-footer d-flex justify-content-center">
                <button onclick="saveExp()" type="button" class="btn btn-primary">Save</button>
                <button onclick="deleteRem()" type="button" class="btn btn-secondary">Delete</button>
            </div>
        </div>
    </div>
</div>

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>

<script>
    $(document).ready(function () {
        $(".sbdatatable").dataTable({
            responsive: true,
            retrieve: true,
            searching: false,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            order : []
        });
    });

    function deleteRem() {
        var id = $("#id").val();
        var shopid = $("#shopid").val();

        showLoader();

        sbconfirm(
            'Are you sure?',
            'This reminder will be deleted.  Are you sure?',
            function () {
                var ds = 'id=' + id + '&shopid=' + shopid;
                $.ajax({
                    type: "post",
                    url: "deletereminder.php",
                    data: ds,
                    success: function () {
                        location.reload();
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        sbalert("Error in Deletion")
                    }
                });
            }
        );

        hideLoader()
    }

    function getFormattedDate(date) {
        var year = date.getFullYear();
        var month = (1 + date.getMonth()).toString();
        month = month.length > 1 ? month : '0' + month;
        var day = date.getDate().toString();
        day = day.length > 1 ? day : '0' + day;
        return month + '/' + day + '/' + year;
    }

    function editExp(id, shopid, subject, reminderdate) {
        //var formatdate = <?php echo "Embedded php "; ?>;

        <?php
        if ($shopid == "demo") {
            echo "$('#expModal').modal('show')";
        } else {
            echo "$('#expModal').modal('show')";
        }
        ?>

        $('#id').val(id)
        $('#shopid').val(shopid)
        $('#subject2').val(subject)
        $('#reminderdate2').val(reminderdate)

    }

    function newRem(shopid) {

        $('#addremModal').modal('show')

        $('#id').val(id)
        $('#shopid').val(shopid)

        var hoy = new Date(),
            mm = hoy.getMinutes(),
            d = hoy.getDate(),
            m = hoy.getMonth() + 1,
            y = hoy.getFullYear(),
            h = hoy.getHours(),
            data;

        if (d < 10) {
            d = "0" + d;
        }

        if (m < 10) {
            m = "0" + m;
        }

        d = m + "/" + d + "/" + y;

        $('#reminderdate').val(d)

        $('#dueday').val(dueday)
    }

    function saveExp() {

        var id = $("#id").val();
        var shopid = $("#shopid").val();
        var subject = $("#subject2").val();
        var reminderdate = $("#reminderdate2").val();
        // adding in completed 8/9/17 as well as additional passing param ..going to check edit reminder.php
        var completed = $("#completed").val();

        var ds = 'id=' + id + '&shopid=' + shopid + '&subject=' + subject + '&reminderdate=' + reminderdate + '&completed=' + completed;

        showLoader();

        $.ajax({
            type: "post",
            url: "editreminder.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()

    }

    function addrem() {


        var shopid = $("#shopid").val();
        var subject = $("#subject").val();
        var reminderdate = $("#reminderdate").val();
        var recurring = $("#recurring").val();
        var dayofmonth = $("#dayofmonth").val();


        var ds = 'shopid=' + shopid + '&subject=' + subject + '&reminderdate=' + reminderdate + '&recurring=' + recurring + '&dayofmonth=' + dayofmonth;

        showLoader();

        $.ajax({
            type: "post",
            url: "addreminder.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });

        hideLoader()

    }

    function recurringPayment(v) {

        if (v == "Yes") {
            document.getElementById("dueday").style.display = ""
        }
        if (v == 'No') {
            $('#dueday').hide()
        }
    }


</script>
</body>
</html>
