<?php

if (strtolower($_COOKIE['accounting']) == "no") {
    redirect_to(COMPONENTS_PRIVATE . "/v2/wip/wip.php");
}
$shopid = $_COOKIE['shopid'];

$datepickerid = "datepicker";

$today = new DateTime('now');

$stmt = "select merchantaccount,contact from company where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->bind_result($merchantaccount, $contact);
    $query->fetch();
    $query->close();
}

$stmt = "select 360popup from settings where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($popup360);
    $query->fetch();
    $query->close();
}

if ($_COOKIE['empid'] != 'Admin') {
    $stmt = "select lower(jobdesc),concat(employeefirst,' ',employeelast) from employees where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ss", $shopid, $_COOKIE['empid']);
        $query->execute();
        $query->bind_result($jobdesc, $empname);
        $query->fetch();
        $query->close();
    }
}

$capitalarr = array();

if ($popup360 == 'yes' && $merchantaccount != 'cardknox' && !isset($_COOKIE['hidecapital']) && ($jobdesc == 'owner' || strtoupper($contact) == strtoupper($empname) || $_COOKIE['empid'] == 'Admin')) {
    if ($merchantaccount != '360')
        $capitalarr = array('message' => "Get integrated payments with BOSS PAY", 'url' => 'https://360payments.com/partner-with-us/shop-boss/?tag=shopboss');
    else {
        $data = array("key" => "242dbb8a-6eab-46bf-ac9e-ec878d7aaa13", "merchant_id" => $shopid, "partner_id" => "a0Fi0000007vT9TEAU");
        $jsonEncodedData = json_encode($data);
        $curl = curl_init();
        $opts = array(
            CURLOPT_URL => 'https://us-central1-capital-prod.cloudfunctions.net/x360capital/get-offers',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $jsonEncodedData,
            CURLOPT_HTTPHEADER => array('Content-Type: application/json', 'Content-Length: ' . strlen($jsonEncodedData))
        );
        curl_setopt_array($curl, $opts);
        $result = curl_exec($curl);
        $json = json_decode($result);
        if (isset($json->signup_url) && !empty($json->signup_url) && !empty($json->message))
            $capitalarr = array('message' => $json->message, 'url' => $json->signup_url);
    }
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';