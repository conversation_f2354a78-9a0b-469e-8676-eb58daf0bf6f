<?php

require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];
$today = new DateTime('now');
// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$disabled = $shopIsReadOnly ? "disabled" : "";
?>
<style>
    main {
        height: 100% !important;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div class="d-flex justify-content-between mb-2">
        <div>
            <h2>Bank Accounts</h2>
        </div>

        <?php if( !$shopIsReadOnly ): ?>
            <button id="bankbutton" type="button" onclick="$('#bankmodal').modal('show')" data-target="#bankmodal"
                    class="btn btn-primary">Add New Account
            </button>
        <?php endif; ?>
    </div>

    <input name="dfield" id="dfield" type="hidden"/>

    <div class="row">
        <div class="col-md-12">
            <table class="sbdatatable">
                <thead>
                <tr>
                    <th>Bank Name</th>
                    <th>Account Description</th>
                    <th>Account Type</th>
                    <th class="text-end">Balance</th>
                    <th>Is Default</th>

                    <?php if( !$shopIsReadOnly ): ?>
                        <th>Edit</th>
                    <?php endif; ?>
                </tr>
                </thead>
                <?php

                $stmt = "SELECT isdefault,bankname,accountdescription,accounttype,id,acctstatus,shopid ";
                $stmt .= "FROM bankaccount ";
                $stmt .= "WHERE shopid = ? ";
                $stmt .= "  AND acctstatus != 'Closed'";
                //echo $stmt;

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $bankresult = $query->get_result();
                } else {
                    echo "Bank Account Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                if ($bankresult->num_rows > 0) {
                    while ($bank = $bankresult->fetch_array()) {
                        $shopid = $bank["shopid"];
                        $id = $bank["id"];

                        $stmt = "SELECT sum(depositamount) as d,  ";
                        $stmt .= "sum(paymentamount) as p  ";
                        $stmt .= " FROM accountregister ";
                        $stmt .= "WHERE shopid = ?";
                        $stmt .= "  AND accountid = ? ";
                        //echo $query;

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("ss", $shopid, $id);
                            $query->execute();
                            $acctregresult = $query->get_result();
                        } else {
                            echo "Account Register Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }

                        $acctreg = $acctregresult->fetch_array();

                        $d = $acctreg["d"];
                        $p = $acctreg["p"];
                        $b = $d - $p;
                        $b = asDollars($b);
                        //echo " This is the total of deposits" . $b;

                        ?>
                        <tr>
                            <td onclick="location.href='../../accounting/bankaccount/transactions.php?bankid=<?php echo $id; ?>'">
                                <?php echo $bank["bankname"]; ?>
                            </td>
                            <td onclick="location.href='../../accounting/bankaccount/transactions.php?bankid=<?php echo $id; ?>'">
                                <?php echo $bank["accountdescription"]; ?>
                            </td>
                            <td onclick="location.href='../../accounting/bankaccount/transactions.php?bankid=<?php echo $id; ?>'">
                                <?php echo $bank["accounttype"]; ?>
                            </td>
                            <td class="text-end" onclick="location.href='../../accounting/bankaccount/transactions.php?bankid=<?php echo $id; ?>'">
                                <?php echo $b; ?>
                            </td>
                            <td onclick="location.href='../../accounting/bankaccount/transactions.php?bankid=<?php echo $id; ?>'">
                                <?php echo strtoupper($bank['isdefault']); ?>
                            </td>
                            

                            <?php if( !$shopIsReadOnly ): ?>
                                <td>
                                    <span onclick="editAccount(<?php echo $bank['id']; ?>)" class="text-primary">
                                        <i class="fas fa-pencil"></i>
                                    </span>
                                </td>
                            <?php endif; ?>
                        </tr>
                        <?php
                    } // end of do while loop
                } // end of if
                ?>
            </table>
        </div>
    </div>
</main>

<?php if( !$shopIsReadOnly ): ?>
<!-- END Main Container -->
    <div id="bankmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
        <input id="customerid" type="hidden">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Add New Bank Account</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <form>
                        <table class="table table-sm">
                            <tr>
                                <td style="width: 268px">Bank Name</td>
                                <td>
                                    <div class="form-outline">
                                        <input class="form-control" id="bankname" name="bankname" type="text"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 268px">Account Type</td>
                                <td>
                                    <div class="form-row">
                                        <select class="select" id="accounttype" name="accounttype"
                                                style="width: 200px">
                                            <option value="Checking">Checking</option>
                                            <option value="Saving">Saving</option>
                                            <option value="Credit Card">Credit Card</option>
                                            <option value="Money Market">Money Market</option>
                                            <option value="Certificate of Deposit">Certificate of Deposit
                                            </option>
                                        </select>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 268px">Description</td>
                                <td>
                                    <div class="form-outline">
                                        <input class="form-control" id="accountdescription"
                                            name="accountdescription" type="text" value=""/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 268px">Account Number</td>
                                <td>
                                    <div class="form-outline">
                                        <input class="form-control" id="accountnumber" name="accountnumber"
                                            type="text" value=""/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 268px">Routing Number</td>
                                <td>
                                    <div class="form-outline">
                                        <input class="form-control" id="routingnumber" name="routingnumber"
                                            type="text" value=""/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 268px">Date Opened</td>
                                <td>
                                    <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                        <input data-mdb-toggle="datepicker" class="form-control" id="opendate" name="opendate" type="text"
                                            value="<?php echo date_format($today, 'm/d/Y'); ?>"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 268px">Beginning Balance</td>
                                <td>
                                    <div class="form-outline">
                                        <input class="form-control" id="beginningbalance" name="beginningbalance"
                                            type="text" value=""/>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </form>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="saveBankAccount('<?php echo $shopid; ?>')" type="button"
                            class="btn btn-primary">Add
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="editmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
        <input id="customerid" type="hidden">
        <div class="modal-dialog modal-md">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Edit Bank Account</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <form>
                        <table class="table table-sm">
                            <tr>
                                <td>Is Default for Deposits?</td>
                                <td>
                                    <div class="form-row">
                                        <select class="select" id="editdefault">
                                            <option value="yes">Yes</option>
                                            <option value="no">No</option>
                                        </select>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 268px">Bank Name</td>
                                <td>
                                    <div class="form-outline">
                                        <input class="form-control" id="editbankname" name="editbankname"
                                            type="text"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 268px">Account Type</td>
                                <td>
                                    <div class="form-row">
                                        <select class="select" id="editaccounttype" name="editaccounttype"
                                                style="width: 200px">
                                            <option value="Checking">Checking</option>
                                            <option value="Saving">Saving</option>
                                            <option value="Credit Card">Credit Card</option>
                                            <option value="Money Market">Money Market</option>
                                            <option value="Certificate of Deposit">Certificate of Deposit
                                            </option>
                                        </select>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 268px">Description</td>
                                <td>
                                    <div class="form-outline">
                                        <input class="form-control" id="editaccountdescription"
                                            name="editaccountdescription" type="text" value=""/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 268px">Account Number</td>
                                <td>
                                    <div class="form-outline">
                                        <input class="form-control" id="editaccountnumber"
                                            name="editaccountnumber" type="text" value=""/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 268px">Routing Number</td>
                                <td>
                                    <div class="form-outline">
                                        <input class="form-control" id="editroutingnumber"
                                            name="editroutingnumber" type="text" value=""/>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </form>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <div class="row">
                        <div class="col-md-12">
                            <button onclick="saveEditBankAccount()" type="button"
                                    class="btn btn-primary">Save
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<input type="hidden" id="accountid">

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>

<script>
    $(document).ready(function () {
        $(".sbdatatable").dataTable({
            responsive: true,
            retrieve: true,
            searching: false,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            order: []
        });
    });

    function editAccount(id) {
        showLoader();

        $.ajax({

            data: "t=getbank&shopid=<?php echo $shopid; ?>&bankid=" + id,
            url: "savebank.php",
            type: "post",
            success: function (r) {
                // echo $bankname."|".$accounttype."|".$accountdescription."|".$accountnumber."|".$routingnumber
                rar = r.split("|")
                bankname = rar[0]
                accounttype = rar[1]
                accountdescription = rar[2]
                accountnumber = rar[3]
                routingnumber = rar[4]
                isdefault = rar[5]
                $("#editbankname").val(bankname);
                $("#editaccounttype").val(accounttype).toggleClass('active');
                $("#editaccountdescription").val(accountdescription);
                $("#editaccountnumber").val(accountnumber);
                $("#editroutingnumber").val(routingnumber);

                if (isdefault == "yes") {
                    $('#editdefault').val("yes")
                } else {
                    $('#editdefault').val("no")
                }
            },

            error: function (xhr, ajaxOptions, thrownError) {
            }

        })

        hideLoader()

        $('#editmodal').modal('show')
        $('#accountid').val(id)

    }

    function recurring(v) {

        if (v == "Yes") {
            document.getElementById("dayofmonth").style.display = ""
        }
        if (v == 'No') {
            $('#dayofmonth').hide()
        }

    }

    function saveEditBankAccount() {

        var bankname = $("#editbankname").val();
        var accttype = $("#editaccounttype").val();
        var acctdesc = $("#editaccountdescription").val();
        var acctnum = $("#editaccountnumber").val();
        var routenum = $("#editroutingnumber").val();
        var shopid = "<?php echo $shopid; ?>";
        var isdefault = $('#editdefault').val()
        var id = $('#accountid').val()

        var ds = 'id=' + id + '&t=edit&isdefault=' + isdefault + '&bankname=' + bankname + '&accounttype=' + accttype + '&accountdescription=' + acctdesc + '&accountnumber=' + acctnum + '&routingnumber=' + routenum + '&shopid=' + shopid;
        showLoader();

        $.ajax({
            type: "post",
            url: "savebank.php",
            data: ds,
            success: function (r) {
                location.reload()
            },

            error: function (xhr, ajaxOptions, thrownError) {
            }
        });

        hideLoader()


    }

    function saveBankAccount(shopid) {
        // note the balance has to be saved to the account register

        var bankname = $("#bankname").val();
        var accttype = $("#accounttype").val();
        var acctdesc = $("#accountdescription").val();
        var acctnum = $("#accountnumber").val();
        var routenum = $("#routingnumber").val();
        var dateopen = $("#opendate").val();
        var begbal = $("#beginningbalance").val();
        var ds = 'bankname=' + bankname + '&accounttype=' + accttype + '&accountdescription=' + acctdesc + '&accountnumber=' + acctnum + '&routingnumber=' + routenum + '&opendate=' + dateopen + '&shopid=' + shopid + '&begbal=' + begbal;

        showLoader();

        $.ajax({
            type: "post",
            url: "savebank.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Insert")
            }
        });

        hideLoader()

    }
</script>
</body>
<?php
mysqli_close($conn);
?>
</html>
