<?php

require CONN;

$shopid = $_COOKIE['shopid'];
if (isset($_GET['bankid'])) {
    $bankid = $_GET['bankid'];
}
if (isset($_POST['bankid'])) {
    $bankid = $_POST['bankid'];
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

$start = isset($_REQUEST['start']) ? filter_var($_REQUEST['start'], FILTER_SANITIZE_STRING) : 0;
$draw = isset($_REQUEST['draw']) ? filter_var($_REQUEST['draw'], FILTER_SANITIZE_STRING) : 0;
$length = isset($_REQUEST['length']) ? filter_var($_REQUEST['length'], FILTER_SANITIZE_STRING) : 25;
$search = isset($_REQUEST['search']['value']) ? filter_var($_REQUEST['search']['value'], FILTER_SANITIZE_STRING) : "";
$order = $_REQUEST['order'];

$orderCol = intval($order[0]['column']);
$orderdir = $order[0]['dir'];

$orderSQL = "";
if (!empty($orderdir)) {
    $orderCol = $orderCol + 1;
    $orderSQL = "ORDER BY $orderCol $orderdir";
}

$today = new DateTime('now');
$today = date_format($today, 'm/d/Y');

$records_total = 0;
$stmt = "SELECT COUNT(*) FROM accountregister WHERE shopid = ? AND accountid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $bankid);
    $query->execute();
    $query->bind_result($records_total);
    $query->fetch();
    $query->close();
} else {
    echo json_encode(array('error' => 'Something went wrong. ' . $conn->error));
    return;
}
if ($length == -1) {
    $length = $records_total;
}
if (empty($search)) {
    $stmt = "SELECT transdate,transnumber,paidto,memo,paymentamount,depositamount,runningbal,ts,category,id FROM accountregister WHERE shopid = ? AND accountid = ? $orderSQL limit $start, $length";
} else {
    $searchNumber =
    $stmt = "SELECT transdate,transnumber,paidto,memo,paymentamount,depositamount,runningbal,ts,category,id FROM accountregister WHERE (id LIKE '%$search%' OR transnumber LIKE '%$search%' OR transdate LIKE '%$search%' OR paidto LIKE '%$search%' OR memo LIKE '%$search%' OR category LIKE '%$search%') AND shopid = ? AND accountid = ? $orderSQL limit $start, $length";
}
//echo $stmt;
$jsonArr = array();

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $bankid);
    $query->execute();
    $acctregresult = $query->get_result();
} else {
    echo json_encode(array('error' => 'Something went wrong. ' . $conn->error));
    return;
}

if ($acctregresult->num_rows > 0) {
    while ($acctreg = $acctregresult->fetch_array()) {
        // evaluate for default value
        if ($acctreg["transdate"] == 0) {
            $tdate = "";
        } else {
            $transdate = new DateTime($acctreg["transdate"]);
            $tdate = $transdate->format('m/d/Y');
        }

        // So you can sort by running balance date
        $dateentered = new DateTime($acctreg["ts"]);
        //echo $dateentered->format('m/d/Y H:m:s');
        //$acctreg["id"]
        $jsonArr[] = array($tdate, $acctreg["transnumber"], $acctreg["paidto"], $acctreg["memo"], asDollars($acctreg["paymentamount"], 2), asDollars($acctreg["depositamount"], 2), asDollars($acctreg["runningbal"], 2), date_format($dateentered, 'm/d/Y H:i:s'),
            'DT_RowId' => $acctreg["id"],
            'DT_RowData' => array(
                'id' => $acctreg["id"],
                'date' => $tdate,
                'trans_num' => $acctreg["transnumber"],
                'paid_to' => str_replace("'", "\'", $acctreg["paidto"]),
                'amount' => $acctreg["paymentamount"],
                'deposit' => $acctreg["depositamount"],
                'memo' => addslashes($acctreg["memo"]),
                'category' => $acctreg["category"],
                'shopid' => $shopid
            )
        );

        if (!$shopIsReadOnly) {
            $jsonArr['DT_RowAttr'] = array(
                'onClick' => "editTrans('" . $acctreg["id"] . "','" . $tdate . "','" . $acctreg["transnumber"] . "','" . str_replace("'", "\'", $acctreg["paidto"]) . "','" . $acctreg["paymentamount"] . "','" . $acctreg["depositamount"] . "','" . addslashes($acctreg["memo"]) . "','" . $acctreg["category"] . "','" . $shopid . "')"
            );
        }
    }

    echo json_encode(array(
        'draw' => $draw,
        'recordsTotal' => $records_total,
        'recordsFiltered' => $records_total,
        'data' => $jsonArr
    ));
    return;
}

echo json_encode(array('error' => 'No records found'));
?>
