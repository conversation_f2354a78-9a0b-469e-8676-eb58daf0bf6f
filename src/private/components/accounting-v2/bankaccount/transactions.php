<?php

require_once(COMPONENTS_PRIVATE_PATH . "/accounting/functions.php");

$shopid = $_COOKIE['shopid'];
if (isset($_GET['bankid'])) {
    $bankid = $_GET['bankid'];
}
if (isset($_POST['bankid'])) {
    $bankid = $_POST['bankid'];
}
if (isset($_GET['transid'])) {
    $transid = $_GET['transid'];
}
if (isset($_POST['transid'])) {
    $transid = $_POST['transid'];
}

// Global Variables
$date = new DateTime('now');
$component = "accounting-v2";
// Page Variables
$title = 'Accounting';
$subtitle = "";

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal('');
include getMenuGlobal($component);

$today = new DateTime('now');
$today = date_format($today, 'm/d/Y');

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$disabled = $shopIsReadOnly ? "disabled" : "";
?>

<style>
    main {
        height: 100% !important;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div>
        <div class="col-12">
            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/accounting/bankaccount/accountlist.php"
                           class="text-secondary">Bank Accounts</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">Transactions</h2>
                    </div>
                    <hr/>
                </div>
            </div>
        </div>
    </div>

    <?php if( !$shopIsReadOnly ): ?>
        <div class="row pb-4">
            <div class="col-md-12 justify-content-between d-flex">
                <div>
                    <button class="btn btn-primary" type="button" onclick="addTrans('<?php echo $shopid; ?>')">
                        Add New Transaction
                    </button>
                    <button id="bankbutton" type="button" onclick="closeAccount(bankid)" class="btn btn-secondary ">
                        Close Bank Account
                    </button>
                    <button id="runbalbutton" type="button"
                            onclick="runbalRecalc('<?php echo $shopid; ?>',<?php echo $bankid; ?>)"
                            class="btn btn-secondary ">
                        Running Balance Recalc
                    </button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12" id="tableresults">
            <div class="table-responsive">
                <table id="banktable" class="sbdatatable w-100">
                    <thead>
                    <tr>
                        <td class="text-start">Trans Date</td>
                        <td class="text-start">Number</td>
                        <td class="text-start">Paid To</td>
                        <td class="text-start">Memo</td>
                        <td class="text-end">Payment</td>
                        <td class="text-end">Deposit</td>
                        <td class="text-end">Running Balance</td>
                        <td class="text-end">Date Posted</td>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</main>

<?php if( !$shopIsReadOnly ): ?>
    <!-- Modals -->

    <!-- Add Transaction Modal -->
    <div id="addTransModal" class="modal fade" role="dialog" aria-hidden="true">
        <input id="shopid" name="shopid" value="" type="hidden">
        <input id="transid" name="transid" type="hidden"/>

        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Add New Transaction</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <table class="table table-sm">
                        <tr>
                            <td class="text-start">Transaction #:</td>
                            <?php

                            $maxtransnumber = 0;
                            $stmt = "select max(transnumber) c from accountregister where shopid = '$shopid' and accountid = '$bankid'";
                            if ($query = $conn->prepare($stmt)) {
                                $query->execute();
                                $query->bind_result($maxtransnumber);
                                $query->fetch();
                                $query->close();
                            }

                            $stmt = "SELECT runningbal ";
                            $stmt .= " FROM accountregister ";
                            $stmt .= " WHERE shopid = ? ";
                            $stmt .= "   AND accountid = ? ";
                            $stmt .= "  ORDER BY ts desc ";
                            $stmt .= "  LIMIT 1 ";

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("ss", $shopid, $bankid);
                                if ($query->execute()) {
                                    $conn->commit();
                                    //echo "success";
                                } else {
                                    echo $conn->errno;
                                }
                            } else {
                                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            $result = $query->get_result();

                            $row = $result->fetch_assoc();

                            $runningbal = $row["runningbal"];

                            $newtransnum = $maxtransnumber + 1;
                            ?>

                            <td class="style9">
                                <div class="form-outline">
                                    <input class="form-control" id="transnumber" name="transnumber"
                                        value="<?= $newtransnum ?>">
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-start">Date</td>
                            <td class="style9">
                                <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                    <input class="form-control" id="transdate" name="transdate" data-mdb-toggle="datepicker"
                                        type="text" value="<?= $today ?>"/>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td class="text-start">Paid To:</td>
                            <td class="style9">
                                <div class="form-outline">
                                    <input class="form-control" id="paidto" name="paidto" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-start">Payment Amount:</td>
                            <td class="style9">
                                <div class="form-outline">
                                    <input class="form-control" id="pymtamount" name="pymtamount" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-start">Deposit Amount:</td>
                            <td class="style9">
                                <div class="form-outline">
                                    <input class="form-control" id="depamount" name="depamount" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-start">Account:</td>
                            <td class="style9">
                                <div class="form-row">
                                    <select id="account" class="select" name="account" type="text" value="">
                                        <?php

                                        $stmt = "SELECT id,shopid,category,cattype,core ";
                                        $stmt .= " FROM chartofaccounts ";
                                        $stmt .= "WHERE shopid = ? ";
                                        $stmt .= " order by category";
                                        //echo $stmt;

                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("s", $shopid);
                                            $query->execute();
                                            $coaresult = $query->get_result();
                                        } else {
                                            echo "Chart of Accounts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }

                                        if ($coaresult->num_rows > 0) {
                                            while ($coa = $coaresult->fetch_array()) {
                                                ?>
                                                <option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
                                                <?php
                                            }    // end of while
                                        } // end if
                                        ?>
                                    </select>
                                </div>
                            </td>

                        <tr>
                            <td class="style9" style="text-align:left">Memo</td>
                            <td>
                                <div class="form-outline">
                                    <input name="memo" id="memo" class="form-control" type="text"/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-start">Runningbal</td>
                            <td class="style9">
                                <div class="form-outline">
                                    <input class="form-control" id="runningbal" name="runningbal" type="text" readonly
                                        value="<?php echo asDollars($runningbal); ?>"/>
                                </div>
                            </td>
                        </tr>

                    </table>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="addtransaction('<?php echo $shopid; ?>',<?php echo $bankid; ?>,<?php echo $runningbal; ?>)"
                            id="addtransbtn" type="button" class="btn btn-primary">Save
                    </button>
                </div>
            </div>
        </div>
    </div>


    <!-- Edit Transaction Modal -->
    <div id="editTransModal" class="modal fade" role="dialog" aria-hidden="true">
        <input id="transid" name="transid" type="hidden"/>
        <input id="bankid" name="bankid" type="hidden" value="<?php echo $bankid; ?>"/>
        <input name="sub" type="hidden" value="yes"/>

        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Edit Transaction</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <table class="table table-sm">
                        <tr>
                            <td class="text-start">Transaction #</td>
                            <td class="style9">
                                <div class="form-outline">
                                    <input class="form-control" id="transnumber2" name="transnumber2" type="text"/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-start">Date</td>
                            <td class="style9">
                                <div class="form-outline datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                                    <input class="form-control" id="transdate2" name="transdate2"
                                        data-mdb-toggle="datepicker" type="text" value=""/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-start">Paid To</td>
                            <td class="style9">
                                <div class="form-outline">
                                    <input id="paidto2" name="paidto2" class="form-control" type="text"/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-start">Payment Amount</td>
                            <td class="style9">
                                <div class="form-outline">
                                    <input id="paymentamount2" name="paymentamount2" class="form-control" type="text"/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-start">Deposit Amount</td>
                            <td class="style9">
                                <div class="form-outline">
                                    <input id="depositamount2" name="depositamount2" class="form-control" type="text"/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-start">Account</td>
                            <td class="style9">
                                <div class="form-row">
                                    <select id="account2" name="account2" class="select">
                                        <?php
                                        $stmt = "SELECT category ";
                                        $stmt .= "FROM chartofaccounts ";
                                        $stmt .= "WHERE shopid = ? ";
                                        //echo $stmt;

                                        if ($query = $conn->prepare($stmt)) {
                                            $query->bind_param("s", $shopid);
                                            $query->execute();
                                            $coaresult = $query->get_result();
                                        } else {
                                            echo "Chart of Accts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }

                                        if ($coaresult->num_rows > 0) {
                                            while ($coa = $coaresult->fetch_array()) {
                                                ?>
                                                <option value="<?php echo $coa["category"]; ?>"><?php echo $coa["category"]; ?></option>
                                                <?php
                                            }    // end of while

                                        } // end if
                                        ?>
                                    </select>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td class="style9" style="text-align:left">Memo</td>
                            <td>
                                <div class="form-outline">
                                    <input name="memo2" id="memo2" class="form-control" type="text"/>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-start">Runningbal</td>
                            <td class="style9">
                                <div class="form-outline">
                                    <input class="form-control" id="runningbal2" name="runningbal2" type="text" readonly
                                        value="<?php echo asDollars($runningbal); ?>"/>
                                </div>
                            </td>
                        </tr>

                    </table>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="saveTrans('<?php echo $shopid; ?>',<?php echo $runningbal; ?>)" type="button"
                            class="btn btn-primary">Save
                    </button>
                    <button onclick="deleteTrans('<?php echo $shopid; ?>')" type="button" class="btn btn-secondary">
                        Delete
                    </button>
                </div>
            </div>
        </div>
    </div>


    <!-- Delete Transaction Modal -->
    <div id="delModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <!--input id="customerid" type="hidden"-->
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Close Bank Account</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <?php
                    $stmt = "SELECT id,bankname,accounttype,accountdescription ";
                    $stmt .= "FROM bankaccount ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND id = ?";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $bankid);
                        $query->execute();
                        $bankresult = $query->get_result();
                    } else {
                        echo "Bank Account Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    if ($bankresult->num_rows > 0) {

                        $bank = mysqli_fetch_assoc($bankresult);

                        $bankname = $bank['bankname'];
                        $accttype = $bank['accounttype'];
                        $acctdesc = $bank['accountdescription'];
                        $id = $bank['id'];

                    }
                    // added on 9/8/17
                    $stmt = "SELECT sum(depositamount) as deposits,  ";
                    $stmt .= "sum(paymentamount) as payments  ";
                    $stmt .= " FROM accountregister ";
                    $stmt .= "WHERE shopid = ?";
                    $stmt .= "  AND accountid = ? ";
                    //echo $stmt;

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("ss", $shopid, $id);
                        $query->execute();
                        $acctreg2result = $query->get_result();
                    } else {
                        echo "Account Register 2 Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    //printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$id);

                    $acctreg2 = $acctreg2result->fetch_array();

                    $deposits = $acctreg2["deposits"];
                    $payments = $acctreg2["payments"];
                    $balance = $deposits - $payments;
                    $balance = asDollars($balance);
                    ?>
                    <table class="table table-sm">
                        <tr>
                            <td style="width: 268px">Bank Name</td>
                            <td style="padding-left:10px;"><?php echo $bankname; ?></td>
                        </tr>
                        <tr>
                            <td style="width: 268px">Description</td>
                            <td style="padding-left:10px;"><?php echo $acctdesc; ?></td>
                        </tr>
                        <tr>
                            <td style="width: 268px">Account Type</td>
                            <td style="padding-left:10px;"><?php echo $accttype; ?></td>
                        </tr>
                        <tr>
                            <td style="width: 268px">Balance</td>
                            <td style="padding-left:10px;"><?php echo $balance; ?></td>
                        </tr>
                    </table>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button onclick="deleteAccount('<?php echo $shopid; ?>',<?php echo $bankid; ?>)"
                            type="button" class="btn btn-primary">Close Bank Account
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
include getScriptsGlobal('');
include getFooterComponent($component);
?>

<script>
    $(document).ready(function () {
        $.fn.dataTable.ext.errMode = 'none';
        let qs = "?bankid=<?= $bankid ?>";

        let myTable = $("#banktable").dataTable({
            responsive: true,
            retrieve: true,
            fixedHeader: true,
            select: true,
            stateSave: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: true,
            searching: true,
            searchDelay: 450,
            order: [],
            lengthMenu: [[50, 100, 500, 1000, -1], [50, 100, 500, "1,000", "All"]],
            serverSide: true,
            ajax: {
                url: 'transactions_data.php' + qs,
                error: function (jqXHR, textStatus, errorThrown) {
                    // Do something here
                    console.log("Error", jqXHR, textStatus, errorThrown);
                }
            },
        });

        $("#banktable").on('processing.dt', function (e, settings, processing) {
            if (processing) {
                showLoader()
            } else {
                hideLoader()
            }
        }).dataTable();

        $("#banktable_wrapper").on('click', '.paginate_button', function () {
            width = $(this).width();
            height = $(this).width();
            img = $('<img>');
            img.attr('src', '<?= IMAGE ?>/loaderbig.gif');
            $(img).width(width)
            $(img).height(height)
            $(this).html(img);
        });

        $('[name="banktable_length"]').addClass('form-control form-select');

    });


    function checkForm(shopid) {

        $('#depositamount2').prop('disabled', false)
        $('#paymentamount2').prop('disabled', false)

        myid = $('#transid').val()

        <?php
        echo "$('#editTransModal').modal('show')";
        ?>
    }

    function saveTrans(shopid, runningbal) {


        var myid = $("#transid").val();
        var acctid = $("#bankid").val();
        var transnumber = $("#transnumber2").val();
        var transdate = $("#transdate2").val();
        var paidto = $("#paidto2").val();
        var depositamount = $("#depositamount2").val();
        var paymentamount = $("#paymentamount2").val();
        var memo = encodeURIComponent($("#memo2").val());
        var account = $("#account2").val();

        var ds = 'id=' + myid + '&shopid=' + shopid + '&transnumber=' + transnumber + '&transdate=' + transdate + '&paidto=' + paidto + '&memo=' + memo + '&depositamount=' + depositamount + '&paymentamount=' + paymentamount + '&category=' + account + '&runningbal=' + runningbal;

        $.ajax({
            type: "post",
            url: "edittransaction.php",
            data: ds,
            success: function () {
                runbalRecalc(shopid, acctid)
                //location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Save")
            }
        });


    }

    function editTrans(id, transdate, transnumber, paidto, paymentamount, depositamount, memo, mycat, shopid) {

        paidto = paidto.replace("`", "'")
        $('#transdate2').val(transdate)
        $('#account2').val(mycat).toggleClass('active')
        $('#transid').val(id)
        $('#transnumber2').val(transnumber)
        $('#paidto2').val(paidto)
        $('#paymentamount2').val(paymentamount)
        $('#depositamount2').val(depositamount)
        $('#memo2').val(memo)
        $('#popup').show()

        if (paymentamount == 0) {
            $('#paymentamount2').prop('disabled', true)
            $('#depositamount2').prop('disabled', false)
            $('#paidto2').prop('disabled', true)
        }
        if (depositamount == 0) {
            $('#depositamount2').prop('disabled', true)
            $('#paymentamount2').prop('disabled', false)
            $('#paidto2').prop('disabled', false)
        }

        checkForm(shopid);
    }

    function deleteTrans(shopid) {

        myid = $('#transid').val()
        acctid = $('#bankid').val()

        sbconfirm(
            'Are you sure?',
            'This transaction will be deleted.  Are you sure?',
            function () {
                var ds = 'id=' + myid + '&acctid=' + acctid + '&shopid=' + shopid;
                $.ajax({
                    type: "post",
                    url: "deletetrans.php",
                    data: ds,
                    success: function () {
                        runbalRecalc(shopid, acctid)
                        //location.reload();
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        sbalert("Error in Deletion")
                    }
                });
            }
        );
    }

    function cancelTrans() {
        document.getElementById("transdate").value = ""
        $('#transnumber').val("")
        document.getElementById("paidto").value = ''
        document.getElementById("paymentamount").value = ''
        document.getElementById("depositamount").value = ''
        document.getElementById("memo").value = ''
        $('#popup').hide()
        $('#transid').val("")
        $('#depositamount').prop('disabled', false)
        $('#paymentamount').prop('disabled', false)
    }

    function addTrans() {
        $('#addtransModal').show()
    }

    function cancelAdd() {
        $('#addtransModal').hide()
    }

    function addBank(shopid) {
        $('#bankModal').modal('show')
    }

    function saveBankAccount(shopid) {
        // note the balance has to be saved to the account register

        var bankname = $("#bankname").val();
        var accttype = $("#accounttype").val();
        var acctdesc = $("#accountdescription").val();
        var acctnum = $("#accountnumber").val();
        var routenum = $("#routingnumber").val();
        var dateopen = $("#opendate").val();
        var begbal = $("#beginningbalance").val();

        var ds = 'bankname=' + bankname + '&accounttype=' + accttype + '&accountdescription=' + acctdesc + '&accountnumber=' + acctnum + '&routingnumber=' + routenum + '&opendate=' + dateopen + '&shopid=' + shopid + '&begbal=' + begbal;

        $.ajax({
            type: "post",
            url: "savebank.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Insert")
            }
        });
    }

    function closeAccount(bankid) {
        <?php
        echo "$('#delModal').modal('show')";
        ?>
    }

    function addTrans(shopid) {

        myid = $('#transid').val()

        $('#addTransModal').modal('show')
    }

    var Old_Val1;
    var Input_Field1 = $('#depamount');

    Input_Field1.focus(function () {
        Old_Val1 = Input_Field1.val();
    });

    Input_Field1.blur(function () {
        var new_input_val1 = Input_Field1.val();
        if (new_input_val1 != Old_Val1) {
            $('#pymtamount').prop('disabled', true)
        }
    });

    var Old_Val2;
    var Input_Field2 = $('#pymtamount');

    Input_Field2.focus(function () {
        Old_Val2 = Input_Field2.val();
    });

    Input_Field2.blur(function () {
        var new_input_val2 = Input_Field2.val();
        if (new_input_val2 != Old_Val2) {
            $('#depamount').prop('disabled', true)
        }
    });


    function addtransaction(shopid, id, runningbal) {
        $('#addtransbtn').attr("disabled", "true")

        //var myid = $('#transid').val(id)

        var transnumber = $("#transnumber").val();
        var transdate = $("#transdate").val();
        var paidto = $("#paidto").val();
        var pymtamount = $("#pymtamount").val();
        var depamount = $("#depamount").val();
        var account = $("#account").val();
        var memo = $("#memo").val();
        //var displayrunningbal = $("#runningbal").val();

        //var runningbal = parseFloat("displayrunningbal");


        var ds = 'shopid=' + shopid + '&accountid=' + id + '&transnumber=' + transnumber + '&transdate=' + transdate + '&paidto=' + paidto + '&memo= ' + memo + '&depamount=' + depamount + '&pymtamount=' + pymtamount + '&account=' + account + '&runningbal=' + runningbal;
        $.ajax({
            type: "post",
            url: "addtransaction.php",
            data: ds,
            success: function () {
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Insert")
            }

        })
    }

    function runbalRecalc(shopid, bankid) {

        showLoader()
        var ds = 'shopid=' + shopid + '&accountid=' + bankid;

        $.ajax({
            type: "post",
            url: "updateAcctRegistryRunBal_ByAcct.php",
            data: ds,
            success: function () {
                hideLoader()
                location.reload();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                sbalert("Error in Insert")
            }

        })
    }

    function deleteAccount(shopid, id) {

        sbconfirm(
            'Are you sure?',
            'Are you sure you want to close this Bank Account?',
            function () {
                var ds = 'shopid=' + shopid + '&id=' + id;
                $.ajax({
                    type: "post",
                    url: "closeaccount.php",
                    data: ds,
                    success: function () {
                        location.href = "accountlist.php";
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        sbalert("Error in Delete")
                    }
                });

            }
        );
    }
</script>

</body>
</html>
