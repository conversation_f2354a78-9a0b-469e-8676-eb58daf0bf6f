<!DOCTYPE html>
<?php
require(CONN);
$shopid = $_COOKIE['shopid'];
$date = date('m/d/Y');
$stmt = "select shopnotice from company where shopid = ?";

if ($query = $conn->prepare($stmt)){

	$query->bind_param("s",$shopid);
    $query->execute();
    //echo $query->fullQuery."<BR>";
    $query->bind_result($shopnotice);
	//echo $query->fullQuery;
    $query->fetch();
    $query->close();

}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!--> <html class="no-focus"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">

         <title><?= getPageTitle() ?></title>

        <meta name="robots" content="noindex, nofollow">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
		<link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon'/ >
        <!-- Icons -->
        <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

        <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
        <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
        <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
        <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
        <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
        <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
        <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
        <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
        <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
        <!-- END Icons -->

        <!-- Stylesheets -->
        <!-- Web fonts -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

        <!-- Page JS Plugins CSS -->
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">

        <!-- Bootstrap and OneUI CSS framework -->
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
        <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
        <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">

        <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
        <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
        <!-- END Stylesheets -->
        <style>
    	.rocell, .stcell, .dacell, .cucell, .phcell, .vecell, .tocell, .licell, .tycell{
			padding:1px;
		}
		.table-medium{
			font-size:14px
		}
		.table-small{
			font-size:10px
		}
		.table-large{
			font-size:18px
		}

        </style>
    </head>
    <body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    <img src="<?= IMAGE ?>/loaderbig.gif" style="display:block" id="spinner">
        <!-- Page Container -->
        <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
        <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
            <!-- Side Overlay-->
            <aside id="side-overlay">
                <!-- Side Overlay Scroll Container -->
                <div id="side-overlay-scroll">
                    <!-- Side Header -->
                    <div class="side-header side-content">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default pull-right" type="button" data-toggle="layout" data-action="side_overlay_close">
                            <i class="fa fa-times"></i>
                        </button>
                        <span>
                            <img class="img-avatar img-avatar32" src="<?= IMAGE ?>/avatars/avatar10.jpg" alt="">
                            <span class="font-w600 push-10-l">Walter Fox</span>
                        </span>
                    </div>
                    <!-- END Side Header -->

                </div>
                <!-- END Side Overlay Scroll Container -->
            </aside>
            <!-- END Side Overlay -->

            <!-- Sidebar -->
            <nav id="sidebar">
                <!-- Sidebar Scroll Container -->
                <div id="sidebar-scroll">
                    <!-- Sidebar Content -->
                    <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                    <div class="sidebar-content">
                        <!-- Side Header -->
                        <div class="side-header side-content bg-white-op">
                            <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                            <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                                <i class="fa fa-times"></i>
                            </button>
                            <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                            <i class="text-primary">
															<?php getLogo() ?></i>
                            <span class="h4 font-w600 sidebar-mini-hide">
							</span>
                            </a>
                        </div>
                        <!-- END Side Header -->

                        <!-- Side Content -->
                        <div class="side-content">
                        	<h3 style="color:white">Main Menu</h3>
                        	<?php
                        	require getMenuContent();
                        	?>

                        </div>
                        <!-- END Side Content -->
                    </div>
                    <!-- Sidebar Content -->
                </div>
                <!-- END Sidebar Scroll Container -->
            </nav>
            <!-- END Sidebar -->

            <!-- Header -->
            <header id="header-navbar" class="content-mini content-mini-full">

                <!-- Header Navigation Right -->
                <ul class="nav-header pull-right">
                    <li>
                        <div id="shopnotice">
                            <?php echo $_COOKIE['shopname']." #".$shopid."<br>".$_COOKIE['username'].'<a class="btn btn-primary btn-sm btn-logoff" href="'.COMPONENTS_PUBLIC.'/login/logoff.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Logoff</span></a>'; ?>
                        </div>
                    </li>
                </ul>
                <!-- END Header Navigation Right -->

                <!-- Header Navigation Left -->

                <ul class="nav-header pull-left">
                    <li class="hidden-md hidden-lg">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                            <i class="fa fa-navicon"></i>
                        </button>
                    </li>
                    <li class="hidden-xs hidden-sm">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                            <i class="fa fa-bars"></i>
                        </button>
                    </li>
                    <li>
                        <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                        <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                            <i class="si si-grid"></i>
                        </button>
                    </li>
                    <li class="visible-xs">
                        <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                        <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                            <i class="fa fa-search"></i>
                        </button>
                    </li>
					<li>
						<?php if(strlen($shopnotice) > 0){echo '<div class="shopnotice">'.$shopnotice.'</div>' ;}?>
					</li>
                </ul>

                <!-- END Header Navigation Left -->
            </header>
            <!-- END Header -->

            <!-- Main Container -->
            <main id="main-container" style="padding-left:5px;"><br>
            	<div class="col-md-3">
            	<input class="form-control" placeholder="Enter PO number and click search"  type="text" id="srch" >
            	</div>
            	<div class="col-md-3">
            	<span onclick="searchPO()" class="btn btn-danger">Search</span>
            	</div><br><br>
            	<div id="results"></div>
            	<h3>Inventory PO's</h3>
				<table id="polist" class="table table-condensed table-striped table-header-bg">
					<thead>
						<tr>
							<td><strong>Type</strong></td>
							<td onclick="sortTable(1,'polist')" style="cursor: pointer;"><strong>PO Number</strong></td>
							<td><strong>Date Issue</strong></td>
							<td><strong>Supplier</strong></td>
							<td><strong>Status</strong></td>
							<td class="text-right"><strong>Total PO</strong></td>
							<td><strong>Type</strong></td>
							<?php if (!$shopIsReadOnly): ?>
								<td><strong>Receive PO</strong></td>
							<?php endif; ?>
							<td><strong>Print</strong></td>
						</tr>
					</thead>
					<tbody>
						<?php
						$date = date('Y-m-d', strtotime('-7 days'));
						$stmt = "select `id`,`shopid`,`ponumber`,`desc`,`issuedate`,`receiveddate`,`status`,`saletype`,`salenumber`,`issuedto`,`ordertype`,`qb`,`expectedarrival`,`deliverymethod`,`orderedby` from po where (receiveddate >= '$date' or receiveddate = '0000-00-00') and shopid = '$shopid' and status != 'DEAD' and status != 'closed' and ordertype = 'inv' order by status desc";
						//echo $stmt;
						if ($query = $conn->prepare($stmt)){
							$query->execute();
							$result = $query->get_result();
							while ($rs = $result->fetch_array()){
								$ponum = $rs['ponumber'];
								$sstmt = "select coalesce(sum(partcost*quantity),0) as pc from partsinventoryorder where shopid = '$shopid' and ponumber = $ponum";
								if ($squery = $conn->prepare($sstmt)){
									$squery->execute();
									$squery->bind_result($pc);
									$squery->fetch();
									$squery->close();
								}
						?>
						<tr>
							<td>INV</td>
							<td><?php echo $rs['ponumber']; ?></td>
							<td><?php echo $rs['issuedate']; ?></td>
							<td><?php echo $rs['issuedto']; ?></td>
							<td><?php echo $rs['status']; ?></td>
							<td class="text-right"><?php echo $pc; ?></td>
							<td>INV</td>
							<?php if (!$shopIsReadOnly): ?>
								<td><span class="btn btn-primary btn-sm" onclick="receivePO('inv',<?php echo $rs['id']; ?>)">Receive PO</span></td>
							<?php endif; ?>
							<td><span class="btn btn-info btn-sm" onclick="printPO('<?php echo $rs['id']; ?>','<?php echo $rs['ordertype']; ?>','<?php echo $rs['salenumber']; ?>')">Print PO</span></td>
						</tr>
						<?php
							}
						}
						?>
					</tbody>
				</table>
				<br><br>
				<h3>RO-based PO's</h3>
				<table id="porolist" class="table table-condensed table-striped table-header-bg">
					<thead>
						<tr>
							<td onclick="sortTable(0,'porolist')" style="cursor: pointer;"><strong>Type</strong></td>
                            <td onclick="sortTable(1,'porolist')" style="cursor: pointer;"><strong>PO Number</strong></td>
							<td><strong>Date Issue</strong></td>
							<td><strong>Supplier</strong></td>
							<td><strong>Status</strong></td>
							<td class="text-right"><strong>Total PO</strong></td>
							<td><strong>Type</strong></td>
							<?php if (!$shopIsReadOnly): ?>
								<td><strong>Receive PO</strong></td>
							<?php endif; ?>
							<td><strong>Print</strong></td>
						</tr>
					</thead>
					<tbody>
						<?php
						$query->close();

						$rolist = "";
						// get a list of open RO's
						$stmt = "select roid from repairorders r where r.shopid = '$shopid' and status != 'CLOSED' and r.ROType != 'No Approval'";
						//echo $stmt."<BR>";
						if ($query = $conn->prepare($stmt)){
							$query->execute();
							$result = $query->get_result();
							while ($rs = $result->fetch_array()){
								$rolist .= $rs['roid'].",";
							}
						}
						if (substr($rolist,-1) == ","){
							$rolist = substr($rolist,0,strlen($rolist)-1);
						}

						$pc = 0;
						$stmt = "select `id`,`shopid`,`ponumber`,`desc`,`issuedate`,`receiveddate`,`status`,`saletype`,`salenumber`,`issuedto`,`ordertype`,`qb`,`expectedarrival`,`deliverymethod`,`orderedby` from po where salenumber in ($rolist) and shopid = '$shopid' and status != 'DEAD' and status != 'closed' and ordertype = 'ro' order by salenumber asc";
						//echo $stmt;
						if ($query = $conn->prepare($stmt)){
							$query->execute();
							$result = $query->get_result();
							while ($rs = $result->fetch_array()){

								$sstmt = "select coalesce(sum(linettlcost),0) pc from parts where deleted = 'no' and roid = " . $rs['salenumber'] . " and shopid = '$shopid' and ponumber = " . $rs['ponumber'];
								if ($squery = $conn->prepare($sstmt)){
									$squery->execute();
									$squery->bind_result($pc);
									$squery->fetch();
									$squery->close();
								}
								if ($pc > 0){
						?>
						<tr>
							<td>RO #<?php echo $rs['salenumber']; ?></td>
							<td><?php echo $rs['ponumber']; ?></td>
							<td><?php echo date("m/d/Y",strtotime($rs['issuedate'])); ?></td>
							<td><?php echo strtoupper($rs['issuedto']); ?></td>
							<td><?php echo strtoupper($rs['status']); ?></td>
							<td class="text-right"><?php echo number_format($pc,2); ?></td>
							<td>RO <?php echo $rs['salenumber']; ?></td>
							<?php if (!$shopIsReadOnly): ?>
								<td><span onclick="receivePO('ro',<?php echo $rs['id']; ?>)" class="btn btn-primary btn-sm">Mark Received</span></td>
							<?php endif; ?>
							<td><span class="btn btn-info btn-sm" onclick="printPO('<?php echo $rs['id']; ?>','<?php echo $rs['ordertype']; ?>','<?php echo $rs['salenumber']; ?>')">Print PO</span></td>
						</tr>
						<?php
								}
							}
						}
						?>
					</tbody>
				</table>
            </main>
            <!-- END Main Container -->

            <!-- Footer -->
            <!-- END Footer -->
        </div>
        <!-- END Page Container -->

		<?php if (!$shopIsReadOnly): ?>
			<!-- Receive PO Modal -->
			<div id="receivepomodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
				<div class="modal-dialog modal-md">
					<div class="modal-content">
						<div class="block block-themed block-transparent remove-margin-b">
							<div class="block-header bg-primary-dark">
								<ul class="block-options">
									<li>
										<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
									</li>
								</ul>
								<h3 id="tctitle" class="block-title">Receive PO</h3>
							</div>
							<div class="block-content">
								<div class="row">
									<div class="col-md-12">

									</div>
								</div>
							</div>
						</div>
						<div style="margin-top:20px;" class="modal-footer">
						<button class="btn btn-md btn-primary" type="button" onclick="myCarfaxSignup()">Register Customer</button>
						<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
						</div>
					</div>
				</div>
			</div>
		<?php endif; ?>

		<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">
        <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
        <script src="<?= SCRIPT ?>/tipped.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

        <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
        <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
        <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
        <script src="<?= SCRIPT ?>/app.js"></script>
        <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
        <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
        <script src="<?= SCRIPT ?>/emodal.js?v=6.1"></script>
        <!-- Page Plugins -->

        <!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
        <script>
            jQuery(function () {
                // Init page helpers (Slick Slider plugin)
                App.initHelpers('slick');
            });


            function searchPO(){

            	po = encodeURIComponent($('#srch').val())
				$.ajax({
					data: "po="+po,
					url: "posearch.php",
					type: "post",
					success: function(r){
						$('#results').html(r)
					},
					error: function (xhr, ajaxOptions, thrownError) {
						console.log(xhr.status);
						console.log(xhr.responseText);
						console.log(thrownError);
					}
				});
            }


            function saveComments(id){

            	notes = encodeURIComponent($('#notes').val())
				$.ajax({
					data: "notes="+notes+"&id="+id,
					url: "poupdatenotes.php",
					type: "post",
					success: function(r){
						//console.log(r)
						//searchPO()
					},
					error: function (xhr, ajaxOptions, thrownError) {
						console.log(xhr.status);
						console.log(xhr.responseText);
						console.log(thrownError);
					}
				});

            }

			function printPO(id,type,roid){

				eModal.iframe({
					title:'Print PO',
					url: '<?= COMPONENTS_PRIVATE ?>/po/inventoryorderprintpdf.php?roid=' + roid + '&typ=' + type + '&shopid=<?php echo $shopid; ?>&id='+id,
					size: eModal.size.xl,
					buttons: [
						{
							text: 'Print',
							style: 'info',
							close:false,
							click: function(){
								$('#emodal-box iframe').attr("id","ropdfframe")
								pdf = document.getElementById("ropdfframe")
								pdf.focus()
								pdf.contentWindow.print()
							}
						},
						{text: 'Close', style: 'warning', close:true}

			    	]

				});


			}

			function receivePO(typ,id){

				if (typ == "ro"){

					swal({
						title: "Are you sure?",
						text: "Mark this PO as Received?",
						type: "warning",
						showCancelButton: true,
						confirmButtonClass: "btn-danger",
						confirmButtonText: "Yes, Mark it!",
						closeOnConfirm: false
					},
					function(){
						//console.log(type+":"+id)
						$.ajax({
							data: "t=ro&shopid=<?php echo $shopid; ?>&id="+id,
							url: "roporeceive.php",
							type: "post",
							success: function(r){
								if (r == "success"){
									location.reload()
								}
							},
							error: function (xhr, ajaxOptions, thrownError) {
								console.log(xhr.status);
								console.log(xhr.responseText);
								console.log(thrownError);
							}
						});
					});
				}else{
					eModal.iframe({
						title:'Receive PO',
						url: 'receivepo.php?id='+id,
						size: eModal.size.xl,
						buttons: [
							{
								text: 'Print',
								style: 'info',
								close:false,
								click: function(){
									$('#emodal-box iframe').attr("id","ropdfframe")
									pdf = document.getElementById("ropdfframe")
									pdf.focus()
									pdf.contentWindow.print()
								}
							},
							{text: 'Close', style: 'warning', close:true}

				    	]

					});
				}
			}


function sortTable(n,tbid) {
  var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
  table = document.getElementById(tbid);
  switching = true;
  //Set the sorting direction to ascending:
  dir = "asc";
  /*Make a loop that will continue until
  no switching has been done:*/
  while (switching) {
    //start by saying: no switching is done:
    switching = false;
    rows = table.rows;
    /*Loop through all table rows (except the
    first, which contains table headers):*/
    for (i = 1; i < (rows.length - 1); i++) {
      //start by saying there should be no switching:
      shouldSwitch = false;
      /*Get the two elements you want to compare,
      one from current row and one from the next:*/
      x = rows[i].getElementsByTagName("TD")[n];
      y = rows[i + 1].getElementsByTagName("TD")[n];
      /*check if the two rows should switch place,
      based on the direction, asc or desc:*/
      if (dir == "asc") {
        if (x.innerHTML.toLowerCase() > y.innerHTML.toLowerCase()) {
          //if so, mark as a switch and break the loop:
          shouldSwitch= true;
          break;
        }
      } else if (dir == "desc") {
        if (x.innerHTML.toLowerCase() < y.innerHTML.toLowerCase()) {
          //if so, mark as a switch and break the loop:
          shouldSwitch = true;
          break;
        }
      }
    }
    if (shouldSwitch) {
      /*If a switch has been marked, make the switch
      and mark that a switch has been done:*/
      rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
      switching = true;
      //Each time a switch is done, increase this count by 1:
      switchcount ++;
    } else {
      /*If no switching has been done AND the direction is "asc",
      set the direction to "desc" and run the while loop again.*/
      if (switchcount == 0 && dir == "asc") {
        dir = "desc";
        switching = true;
      }
    }
  }
}




        </script>
        <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    </body>
    <?php if(isset($conn)){mysqli_close($conn);} ?>
</html>
