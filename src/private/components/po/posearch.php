<?php
require CONN;
$shopid = $_COOKIE['shopid'];
$po = filter_var($_POST['po'],FILTER_SANITIZE_STRING);
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
				<h4>Search Results</h4>
				<table id="polist" class="table table-condensed table-striped table-header-bg">
					<thead>
						<tr>
							<td><strong>PO Number</strong></td>
							<td><strong>Date Issue</strong></td>
							<td><strong>Supplier</strong></td>
							<td><strong>Status</strong></td>
							<td class="text-right"><strong>Total PO</strong></td>
							<?php if (!$shopIsReadOnly): ?>
								<td><strong>Notes</strong></td>
								<td></td>
							<?php endif; ?>
							<td>Print</td>
						</tr>
					</thead>
					<tbody>
						<?php
						$date = date('Y-m-d', strtotime('-7 days'));
						$stmt = "select `id`,`shopid`,`ponumber`,`desc`,`issuedate`,`receiveddate`,`status`,`saletype`,`salenumber`,`issuedto`,`ordertype`,`qb`,`expectedarrival`,`deliverymethod`,`orderedby` from po where shopid = ? and ponumber = ? order by status desc";
						//echo $stmt;
						if ($query = $conn->prepare($stmt)){
							$query->bind_param("ss",$shopid,$po);
							$query->execute();
							$result = $query->get_result();
							while ($rs = $result->fetch_array()){
								$ponum = $rs['ponumber'];
								$sstmt = "select coalesce(sum(partcost*quantity),0) as pc from partsinventoryorder where shopid = '$shopid' and ponumber = $ponum";
								if ($squery = $conn->prepare($sstmt)){
									$squery->execute();
									$squery->bind_result($pc);
									$squery->fetch();
									$squery->close();
								}
						?>
						<tr>
							<td><?php echo $rs['ponumber']; ?></td>
							<td><?php echo $rs['issuedate']; ?></td>
							<td><?php echo $rs['issuedto']; ?></td>
							<td><?php echo $rs['status']; ?></td>
							<td class="text-right"><?php echo $pc; ?></td>
							<?php if (!$shopIsReadOnly): ?>
								<td><textarea class="form-control" id="notes"><?php echo $rs['desc']; ?></textarea></td>
								<td><span class="btn btn-primary btn-sm" onclick="saveComments(<?php echo $rs['id']; ?>)">Save Comments</span></td>
							<?php endif; ?>
							<td><span class="btn btn-info btn-sm" onclick="printPO('<?php echo $rs['id']; ?>','<?php echo $rs['ordertype']; ?>','<?php echo $rs['salenumber']; ?>')">Print PO</span></td>
						</tr>
						<?php
							}
						}
						?>
					</tbody>
				</table>
				
				
<?php

mysqli_close($conn);
