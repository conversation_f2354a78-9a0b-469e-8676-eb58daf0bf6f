<?php
// Initialize variables that might be undefined
if (!isset($companyzip)) $companyzip = '';
if (!isset($chargehst)) $chargehst = 'no';
if (!isset($chargepst)) $chargepst = 'no';
if (!isset($chargegst)) $chargegst = 'no';
if (!isset($chargeqst)) $chargeqst = 'no';
if (!isset($worldpac)) $worldpac = 'no';
if (!isset($email)) $email = '';
if (!isset($phone)) $phone = '';
if (!isset($notes)) $notes = '';
if (!isset($cid)) $cid = '';
if (!isset($vid)) $vid = '';
if (!isset($customer)) $customer = '';
if (!isset($year)) $year = '';
if (!isset($make)) $make = '';
if (!isset($model)) $model = '';
if (!isset($color)) $color = '';
if (!isset($vin)) $vin = '';
?>

<div id="feesmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="feesmodalLabel">Fees</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="feesform">

                    <div class="form-outline mb-4">
                        <input class="form-control"  value="<?php echo $hazardouswaste; ?>" type="text"
                               id="hazardouswaste" name="hazardouswaste">
                        <label class="form-label" for="hazardouswaste">Hazardous Waste</label>
                    </div>

                    <?php
                    if (!empty($fee1label)) {
                        ?>
                        <div class="row mb-4">
                            <?php if ($fee1percent > 0) {
                                ?>
                                <div class="col-sm-3"><?php echo $fee1label; ?>:</div>
                                <div class="col-sm-3">
                                    <div class="input-group">
                                        <div class="form-outline">
                                            <input tabindex="1" value="<?php echo $fee1percent; ?>" class="feepercent form-control" data-id="1" type="text" id="fee1percent" name="fee1percent">
                                        </div>
                                        <div class="input-group-append">
                                            <span class="input-group-text">%</span>
                                        </div>

                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>

                                        <div class="form-outline">
                                            <input tabindex="1" value="<?php echo sbpround($fee1amount, 2); ?>" type="text" id="fee1amount" name="fee1amount" class="form-control">
                                        </div>

                                    </div>
                                </div>
                                <div class="col-sm-3"></div>
                                <?php
                            } else {
                                ?>
                                <div class="col-sm-3"><?php echo $fee1label; ?>:</div>
                                <div class="col-sm-3">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>

                                        <div class="form-outline">
                                            <input tabindex="1" value="<?php echo sbpround($fee1amount, 2); ?>" type="text" id="fee1amount" name="fee1amount" class="form-control">
                                        </div>

                                    </div>
                                </div>
                                <div class="col-sm-6"></div>
                                <?php
                            } ?>
                        </div>
                        <?php
                    }

                    if (!empty($fee2label)) {
                        ?>
                        <div class="row mb-4">
                            <?php if ($fee2percent > 0) {
                                ?>
                                <div class="col-sm-3"><?php echo $fee2label; ?>:</div>
                                <div class="col-sm-3">
                                    <div class="input-group">
                                        <div class="form-outline">
                                            <input tabindex="1" value="<?php echo $fee2percent; ?>" class="feepercent form-control" data-id="2" type="text" id="fee2percent" name="fee2percent">
                                        </div>
                                        <div class="input-group-append">
                                            <span class="input-group-text">%</span>
                                        </div>

                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>

                                        <div class="form-outline">
                                            <input tabindex="1" value="<?php echo sbpround($fee2amount, 2); ?>" type="text" id="fee2amount" name="fee2amount" class="form-control">
                                        </div>

                                    </div>
                                </div>
                                <div class="col-sm-3"></div>
                                <?php
                            } else {
                                ?>
                                <div class="col-sm-3"><?php echo $fee2label; ?>:</div>
                                <div class="col-sm-3">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>

                                        <div class="form-outline">
                                            <input tabindex="1" value="<?php echo sbpround($fee2amount, 2); ?>" type="text" id="fee2amount" name="fee2amount" class="form-control">
                                        </div>

                                    </div>
                                </div>
                                <div class="col-sm-6"></div>
                                <?php
                            } ?>
                        </div>
                        <?php
                    }

                    if (!empty($fee3label)) {
                        ?>
                        <div class="row mb-4">
                            <?php if ($fee3percent > 0) {
                                ?>
                                <div class="col-sm-3"><?php echo $fee3label; ?>:</div>
                                <div class="col-sm-3">
                                    <div class="input-group">
                                        <div class="form-outline">
                                            <input tabindex="1" value="<?php echo $fee3percent; ?>" class="feepercent form-control" data-id="3" type="text" id="fee3percent" name="fee3percent">
                                        </div>
                                        <div class="input-group-append">
                                            <span class="input-group-text">%</span>
                                        </div>

                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>

                                        <div class="form-outline">
                                            <input tabindex="1" value="<?php echo sbpround($fee3amount, 2); ?>" type="text" id="fee3amount" name="fee3amount" class="form-control">
                                        </div>

                                    </div>
                                </div>
                                <div class="col-sm-3"></div>
                                <?php
                            } else {
                                ?>
                                <div class="col-sm-3"><?php echo $fee3label; ?>:</div>
                                <div class="col-sm-3">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>

                                        <div class="form-outline">
                                            <input tabindex="1" value="<?php echo sbpround($fee3amount, 2); ?>" type="text" id="fee3amount" name="fee3amount" class="form-control">
                                        </div>

                                    </div>
                                </div>
                                <div class="col-sm-6"></div>
                                <?php
                            } ?>
                        </div>
                        <?php
                    } ?>

                     <?php if (!is_numeric($companyzip)) { ?>
                        <?php if ($chargehst == 'yes') { ?>
                            <div class="form-outline mb-4">
                                <input class="form-control"  value="<?php echo $hst; ?>" type="text"
                                       id="hst" name="hst">
                                <label class="form-label" for="hst">Canadian HST</label>
                            </div>
                        <?php }
                        if ($chargepst == 'yes') { ?>
                            <div class="form-outline mb-4">
                                <input class="form-control"  value="<?php echo $pst; ?>" type="text"
                                       id="pst" name="pst">
                                <label class="form-label" for="pst">Canadian PST</label>
                            </div>
                        <?php }
                        if ($chargegst == 'yes') { ?>
                            <div class="form-outline mb-4">
                                <input class="form-control"  value="<?php echo $gst; ?>" type="text"
                                       id="gst" name="gst">
                                <label class="form-label" for="gst">Canadian GST</label>
                            </div>
                        <?php }
                        if ($chargeqst == 'yes') { ?>
                            <div class="form-outline mb-4">
                                <input class="form-control"  value="<?php echo $qst; ?>" type="text"
                                       id="qst" name="qst">
                                <label class="form-label" for="qst">Canadian QST</label>
                            </div>
                        <?php }
                    } else { ?>
                        <div class="form-outline mb-4">
                            <input class="form-control"  value="<?php echo $partstaxrate; ?>" type="text"
                                   id="partstaxrate" name="partstaxrate">
                            <label class="form-label" for="partstaxrate">Parts Tax Rate</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control"  type="text"
                                   value="<?php echo $labortaxrate; ?>" id="labortaxrate"
                                   name="labortaxrate">
                            <label class="form-label" for="labortaxrate">Labor Tax Rate</label>
                        </div>

                        <div class="form-outline mb-4">
                            <input class="form-control"  type="text"
                                   value="<?php echo $sublettaxrate; ?>" id="sublettaxrate"
                                   name="sublettaxrate">
                            <label class="form-label" for="sublettaxrate">Sublet Tax Rate</label>
                        </div>
                    <?php } ?>
                </form>
            </div>

            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-md btn-primary" type="button" onclick="saveFees()">Save</button>
            </div>
        </div>
    </div>
</div>


<div id="issuemodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="issuemodalLabel">Edit Issue</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="issuecomid">
                <input type="text" id="issue" class="form-control">
            </div>

            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-md btn-primary" type="button" onclick="saveIssue()">Save</button>
            </div>
        </div>
    </div>
</div>

<div id="partsordering" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="partsorderingLabel">Select Parts Ordering System</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mt-2">
                    <div class="col-md-12 text-center">
                        <?php
                        $ptapikey = "";
                        $stmt = "select apikey from partstechshops where shopid = '$shopid'";
                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $query->bind_result($ptapikey);
                            $query->fetch();
                            $query->close();
                        }


                        if (!empty($ptapikey)) {
                            ?>
                            <button id="partstechbutton" class="btn btn-secondary me-3 pobutton" onclick="launchPartsTech('partstech')" type="button">PartsTech</button>
                            <?php
                        }
                        $epicorsupp = "";
                        $stmt = "select shopid FROM epicor WHERE shopid = '$shopid'";
                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $query->bind_result($epicorsupp);
                            $query->fetch();
                            $query->close();
                        }

                        if (!empty($epicorsupp)) {

                            ?>
                            <button id="epicorbutton" class="btn btn-secondary me-3 pobutton" onclick="showOrdering('epicor')" type="button">Epicor</button>
                            <?php
                        }
                        ?>
                        <div id="epicorselect" class="row" style="display:none">
                            <div class="form-row mb-4">
                                <select id="epicorsupplier" class="select">
                                    <?php
                                    $stmt = "select suppliername from epicor where shopid = ?";
                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("s", $shopid);
                                        $query->execute();
                                        $result = $query->get_result();
                                        $query->store_result();
                                        while ($row = $result->fetch_assoc()) {
                                            ?>
                                            <option
                                                    value="<?php echo $row['suppliername']; ?>"><?php echo $row['suppliername']; ?></option>
                                            <?php
                                        }
                                    }
                                    ?>
                                </select>
                                <label class="form-label select-label" for="epicorsupplier">Select Supplier</label>
                            </div>
                            <div class="form-row mb-4">
                                <select id="epicortype" class="select">
                                    <option value="Transfer">Build Estimate</option>
                                    <option value="Order">Order Parts</option>
                                    <option value="TransferAndOrder">Both</option>
                                </select>
                                <label class="form-label select-label" for="epicortype">Select Mode</label>
                            </div>

                            <div class="d-flex justify-content-center">

                                <button type="button" onclick="launchEpicor()" class="btn btn-primary me-3">Launch Epicor
                                </button>
                                <button type="button" onclick="cancelOrdering('epicor')" class="btn btn-secondary">Cancel
                                    Epicor
                                </button>
                            </div>
                        </div>
                        <?php
                        $nexpartsupp = "";
                        $stmt = "select `desc` FROM nexpart WHERE shopid = '$shopid'";
                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $query->bind_result($nexpartsupp);
                            $query->fetch();
                            $query->close();
                        }

                        if (!empty($nexpartsupp)) {

                            ?>
                            <button id="nexpartbutton" class="btn btn-secondary me-3 pobutton" onclick="showOrdering('nexpart')" type="button">Nexpart</button>
                            <?php
                        }
                        ?>
                        <div id="nexpartselect" class="row" style="display:none">
                            <div class="form-row mb-3">
                                <select id="nexpartcreds" class="select">
                                    <?php
                                    $stmt = "select `desc`,username,password from nexpart where shopid = '$shopid'";
                                    if ($query = $conn->prepare($stmt)) {
                                        $query->execute();
                                        $result = $query->get_result();
                                        $query->store_result();
                                        while ($row = $result->fetch_assoc()) {
                                            ?>
                                            <option
                                                    value="<?php echo $row['username'] . "|" . $row['password']; ?>"><?php echo $row['desc']; ?></option>
                                            <?php
                                        }
                                    }
                                    ?>
                                </select>
                                <label class="form-label select-label" for="nexpartcreds">Select Supplier</label>
                            </div>
                            <div class="d-flex justify-content-center">
                                <button type="button" onclick="launchNexpart()" class="btn btn-primary me-3">Launch Nexpart
                                </button>
                                <button type="button" onclick="cancelOrdering('nexpart')" class="btn btn-secondary">Cancel
                                    Nexpart
                                </button>
                            </div>
                        </div>
                        <?php
                        if ($worldpac == 'yes') {
                            ?>
                            <button id="worldpacbutton" onclick="launchWorldpac()" class="btn btn-secondary me-3 pobutton" type="button">WorldPac</button>
                            <?php
                        }
                        $rlstmt = "SELECT uid FROM repairlinkshops where shopid = ?";
                        if ($rlquery = $conn->prepare($rlstmt)) {
                            $rlquery->bind_param("s", $shopid);
                            $rlquery->execute();
                            $rlquery->bind_result($rluid);
                            $rlquery->fetch();
                            $rlquery->close();
                        }

                        if (!empty($rluid)) {
                            ?>
                            <button type="button" id="repairlinkbutton" onclick="launchRepairLink('<?= $rluid ?>')" class="btn btn-secondary pobutton">RepairLink</button>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div id="sendquotemodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="sendquotemodalLabel">Send Quote</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">

                        <div class="form-outline mb-4">
                            <input class="form-control" tabindex="1" id="emailto" name="emailto" value="<?php echo strtoupper($email); ?>" type="email">
                            <label class="form-label" for="emailto">Email Address</label>
                        </div>

                        <div class="form-outline">
                            <input class="form-control" tabindex="1" id="textto" name="textto" value="<?php echo str_ireplace(array('-', 'cell', 'work', 'home', ',', ' ', ',', '(', ')', ':'), '', $phone); ?>" type="text">
                            <label class="form-label" for="textto">Cell Phone</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-md btn-secondary" type="button" onclick="sendQuote('email')">Send Email</button>
                <button class="btn btn-md btn-secondary" type="button" onclick="sendQuote('text')">Send Text</button>
                <button class="btn btn-md btn-secondary" type="button" onclick="sendQuote('both')">Send Both</button>
            </div>
        </div>
    </div>
</div>


<div id="commentmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="commentmodalLabel">Comments</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-outline mb-4">
                            <textarea class="form-control" <?php if (empty($quoteroid)){ ?>onblur="saveComments()"<?php } ?> style="padding:20px;" tabindex="1" id="comments"><?php echo $notes; ?></textarea>
                            <label class="form-label" for="comments">Comments<?php if (empty($quoteroid)) { ?> (auto-saved)<?php } ?></label>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>


<div id="dupcusmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="dupcusmodalLabel">Duplicate Customers</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="max-height: 800px;overflow: auto;">
                <div class="row">
                    <div class="col-md-12">

                        <p class="text-primary">Duplicate customer exists with similar name or phone. Please select below the existing customer OR click create new customer</p>

                        <center>
                            <button class="btn btn-md btn-secondary" type="button" onclick="createnewCustomer()">Create New Customer <?= !empty($customer) ? '"' . $customer . '"' : '' ?></button>
                            <br><br><b>OR</b><br><br>
                        </center>

                        <table class="sbdatatable w-100">
                            <thead>
                            <tr>
                                <th>Customer Name</th>
                                <th>Address</th>
                                <th>Phone</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody id="dupcusrows">

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div id="vehmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="vehmodalLabel">Customer Vehicles</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="max-height: 800px;overflow: auto;">
                <div class="row">
                    <div class="col-md-12">
                        <p class="text-primary">Please select the vehicles of selected customer OR click create new vehicle</p>

                        <center>
                            <button class="btn btn-md btn-secondary" type="button" onclick="createnewVehicle()">Create New
                                Vehicle <?= !empty($year) ? '"' . $year . ' ' . $make . ' ' . $model . '"' : '' ?></button>
                            <br><br><b>OR</b><br><br></center>

                        <table class="sbdatatable w-100">
                            <thead>
                            <tr>
                                <th>Year</th>
                                <th>Make</th>
                                <th>Model</th>
                                <th>VIN</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody id="vehrows">

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div id="quicklubemodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="quicklubemodalLabel">Quick Lube Data</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div id="quicklubedata" class="mb-4">
                        </div>
                    </div>
                </div>
            </div>
            <?php if (!$shopIsReadOnly): ?>
                <div class="modal-footer d-flex justify-content-center">
                    <button class="btn btn-md btn-secondary" type="button" onclick="clearQuickLube()">Clear Lube Info</button>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<div id="editquotemodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="editquotemodalLabel">Edit Customer / Vehicle</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="quoteform" name="quoteform">
                    <input id="cid" name="cid" value="<?= $cid ?>" type="hidden"/>
                    <input id="vid" name="vid" value="<?= $vid ?>" type="hidden"/>

                    <div class="row">
                        <div class="col-md-6">

                            <div class="form-outline mb-4 autocomplete" id="async">
                                <input autocomplete="off" class="form-control" type="text" value="<?= $customer ?>" id="customer" name="customer" tabindex="1"/>
                                <label class="form-label" id="customerlabel" for="customer">Customer (L,F)</label>
                            </div>

                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" value="<?= $address ?>" id="address" name="address">
                                <label class="form-label" id="addresslabel" for="address">Address</label>
                            </div>

                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" value="<?= $zip ?>" id="zip" name="zip">
                                <label class="form-label" id="ziplabel" for="zip">Zip</label>
                            </div>

                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" value="<?= $city ?>" id="city" name="city">
                                <label class="form-label" id="citylabel" for="city">City</label>
                            </div>

                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" value="<?= $state ?>" id="state" name="state">
                                <label class="form-label" id="statelabel" for="state">State</label>
                            </div>

                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" value="<?= $phone ?>" id="phone" name="phone">
                                <label class="form-label" id="phonelabel" for="phone">Phone</label>
                            </div>

                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" value="<?= $email ?>" id="email" name="email">
                                <label class="form-label" id="emaillabel" for="email">Email</label>
                            </div>

                        </div>

                        <div class="col-md-6">

                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" value="<?= $year ?>" id="year" name="year">
                                <label class="form-label" id="yearlabel" for="year">Year</label>
                            </div>

                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" value="<?= $make ?>" id="make" name="make">
                                <label class="form-label" id="makelabel" for="make">Make</label>
                            </div>

                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" value="<?= $model ?>" id="model" name="model">
                                <label class="form-label" id="modellabel" for="model">Model</label>
                            </div>

                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" type="text" value="<?= $color ?>" id="color" name="color">
                                <label class="form-label" id="colorlabel" for="color">Color</label>
                            </div>

                            <div class="input-group">
                                <div class="form-outline">
                                    <input class="form-control" tabindex="1" type="text" value="<?= $vin ?>" id="vin" name="vin">
                                    <label class="form-label" id="vinlabel" for="vin">VIN</label>
                                </div>
                                <a href="javascript:void(null)" data-mdb-toggle="tooltip" title="Decode VIN" class="btn btn-secondary" onclick="decodeVIN()"><i class="fas fa-key"></i></a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-md btn-secondary btn-md" style="display:none" id="btn-convert" type="button" onclick="convertRO()">Convert to RO</button>
                <button class="btn btn-md btn-primary btn-md" type="button" onclick="check_customer()">Save</button>
            </div>
        </div>
    </div>
</div>

<div id="motorModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">

            <div class="modal-header">
                <h4 class="modal-title">Motor</h4>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <iframe id="motor-iframe" class="embed-responsive-item" frameborder=0 src="" style="width:100%;height:75vh;display:block;"></iframe>
            </div>

        </div>
    </div>
</div>


<div id="custconfirm" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="z-index:10001 !important;">
    <div class="modal-dialog modal-md">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h4 class="modal-title">Edit Customer</h4>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Customer <span id="ecust"></span> information has been changed, Do you want to update the Customer information or update the quote only?
            </div>
            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-md btn-primary" type="button" onclick="update_customer()">Update Customer</button>
                <button class="btn btn-md btn-secondary" type="button" onclick="check_vehicle()" data-dismiss="modal">Update Quote</button>
            </div>
        </div>
    </div>
</div>
<div id="vehconfirm" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="z-index:10002 !important;">
    <div class="modal-dialog modal-md">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h4 class="modal-title">Edit Vehicle</h4>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Vehicle information has been changed, Do you want to create a new vehicle with new information or update the current vehicle.
            </div>
            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-md btn-primary" type="button" onclick="create_vehicle('')">Create New Vehicle</button>
                <button class="btn btn-md btn-secondary" type="button" onclick="update_vehicle('')" data-dismiss="modal">Update Vehicle</button>
            </div>
        </div>
    </div>
</div>