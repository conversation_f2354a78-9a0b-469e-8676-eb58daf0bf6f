﻿<?php
require_once CONN;

$shopid = isset($_COOKIE['shopid']) ? filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING) : "";
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

include getHeadGlobal('');
echo("<body>");
include getHeaderGlobal($component);
include  "menu.canned.php";
?>
 <main id="main-container">

    <h2>Create Quote from Canned Job</h2>
            
        <table class="sbdatatable w-100">
            <thead>
                <tr>
                    <th>Job Name</th>
                    <th>Price</th>
                </tr>
            </thead>
            <tbody>
                <?php

                $stmt = "select hourlyrate from company where shopid = ?";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $query->store_result();
                    $query->bind_result($hrate);
                    $query->fetch();
                    $query->close();
                } else {
                    echo "company prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
                
                $cstmt = "select id,jobname from cannedjobs where shopid = ? order by trim(jobname)";
                if ($cquery = $conn->prepare($cstmt)) {
                    $cquery->bind_param("s", $shopid);
                    $cquery->execute();
                    $cresults = $cquery->get_result();
                    if ($cresults->num_rows > 0) {
                        while ($rs = $cresults->fetch_assoc()) {
                            $partsprice = $subletprice = $labortotal = 0;
                            $laborlist = $partlist = $subletlist = "";
                            $cid = $rs['id'];

                            $clstmt = "select labor,laborhours,flatprice,nocalc,rateforcalc from cannedlabor where cannedjobsid = ? and shopid = ?";
                            if ($clquery = $conn->prepare($clstmt)) {
                                $clquery->bind_param("is", $cid, $shopid);
                                $clquery->execute();
                                $clresults = $clquery->get_result();
                                while ($trs = $clresults->fetch_assoc()) {
                                    $laborlist .= strtoupper($trs["labor"]) . ", ";
                                    if ($trs['flatprice'] > 0 || $trs['nocalc'] == '1')
                                        $labortotal += $trs['flatprice'];
                                    else
                                        $labortotal += $trs['rateforcalc'] * $trs['laborhours'];
                                }
                            } else {
                                echo "cand lbr failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            $cpstmt = "select partprice*qty as partprice, partdescription from cannedparts where cannedjobsid = ? and shopid = ?";
                            if ($cpquery = $conn->prepare($cpstmt)) {
                                $cpquery->bind_param("is", $cid, $shopid);
                                $cpquery->execute();
                                $cpresults = $cpquery->get_result();
                                while ($trs = $cpresults->fetch_assoc()) {
                                    $partlist .= strtoupper($trs["partdescription"]) . ", ";
                                    $partsprice += $trs["partprice"];
                                }
                            } else {
                                echo "cnnd prts failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            $cpstmt = "select subletdesc,subletprice from cannedsublet where cannedjobsid = ? and shopid = ?";
                            if ($cpquery = $conn->prepare($cpstmt)) {
                                $cpquery->bind_param("is", $cid, $shopid);
                                $cpquery->execute();
                                $cpresults = $cpquery->get_result();
                                while ($trs = $cpresults->fetch_assoc()) {
                                    $subletlist .= strtoupper($trs["subletdesc"]) . ", ";
                                    $subletprice += $trs["subletprice"];
                                }
                            } else {
                                echo "cnnd prts failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            $laborlist = rtrim($laborlist,', ');
                            $partlist = rtrim($partlist,', ');
                            $subletlist = rtrim($subletlist,', ');

                            if (!empty($laborlist)) {
                                $laborlist = "<br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Labor: " . $laborlist;
                            }
                            if (!empty($partlist)) {
                                $partlist = "<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Parts: " . $partlist;
                            }
                            if (!empty($subletlist)) {
                                $subletlist = "<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sublet: " . $subletlist;
                            }

                            $onclickQuote = !$shopIsReadOnly 
                                ? "onclick=\"location.href='cannedcreatequote.php?id={$cid}'\"" 
                                : "";

                            ?>
                            <tr <?= $onclickQuote; ?>>
                                <td>&nbsp;<strong><?= strtoupper($rs['jobname'])?></strong><?= $laborlist . $partlist . $subletlist; ?></td>
                                <td><?php echo asDollars(($labortotal + $partsprice + $subletprice), 2); ?>&nbsp;&nbsp;&nbsp;&nbsp;</td>
                            </tr>
                        <?php
                        }
                    } else {
                        ?>
                        <tr>
                            <td>&nbsp;You have no canned jobs</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                <?php
                    }
                    $cquery->close();
                } else {
                    echo "cannd jbs prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
                ?>
              </tbody>
            </table>
    </main>

    <?php include getScriptsGlobal($component);?>

    <script type="text/javascript">
        $(document).ready(function () {

            $(".sbdatatable").dataTable({
                responsive: true,
                paging: false,
                info: false,
                order: []
            });
        })

    </script>

</body>

</html>