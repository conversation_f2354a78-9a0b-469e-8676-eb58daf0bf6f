<?php

$component = "quotes-v2";
include getHeadGlobal($component);
include getRulesGlobal($component);

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$stmt = "select carfaxlocation,companystate from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($cfl,$state);
    $query->fetch();
    $query->close();
}
echo "<body>";
include getHeaderGlobal($component);
include getMenuGlobal($component);

?>
<style type="text/css">
.autocomplete-dropdown-container
{
    width: 700px !important;
}
.autocomplete-items-list {
  max-height: 400px !important; overflow-y: scroll;
}

.autocomplete-item {font-size: 14px;}

.mdb-autocomplete .mdb-autocomplete-wrap {
  left: 0 !important;
  right: auto !important;
}
</style>

<main id="main-container" class="min-vh-100">

    <div class="row mb-2">

        <div class="col-md-6">
            <h2>Quotes List</h2>
        </div>
        <div class="col-md-6 d-flex justify-content-end">
            <div class="form-check form-switch me-3">
             <input type="checkbox" class="form-check-input" id="showconverted" onchange="toggleConverted()" checked>
             <label class="form-check-label">Show Converted Quotes</label>
            </div>
            <?php if( !$shopIsReadOnly ): ?>
                <a id="deleteQuotesButton" class="text-primary" href="javascript:void(null)" onclick="deleteSelected()"><i class="fas fa-trash"></i> Delete Selected Open Quotes</a>
            <?php endif; ?>
        </div>
    </div>

    <div class="row">

        <div class="col-md-12">

            <form id="mainform" name="mainform" method="post" action="quotes.php">
            <input name="sub" type="hidden"/>

            <table class="w-100 sbdatatable hidden" id="quotes-table">
                <thead>
                <tr>
                    <?php if( !$shopIsReadOnly ): ?>
                        <th><input onclick="checkAll()" id="checkall" name="checkall" type="checkbox" class="form-check-input"></th>
                    <?php endif; ?>
                    <th>Quote #</th>
                    <th>Date</th>
                    <th>Customer</th>
                    <th>Vehicle</th>
                    <th>Total</th>
                </tr>
                </thead>
                <tbody>
                <?php

                $stmt = "select id, customer, year, make, model, totalquote, salestax, quotedate, roid, city, state, address from quotes where shopid = ? and customer!='' order by id desc";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $results = $query->get_result();
                    $query->store_result();
                    if ($results->num_rows > 0) {
                            while ($rs = $results->fetch_assoc()) {

                                $address = str_replace("'", "''", $rs['address']);
                                $city = $rs['city'];
                                $zip = $rs['zip'];
                                $total_quote = $rs['totalquote'];

                                ?>
                                    <input type="hidden" name="deletecheckbox<?php echo $rs["id"]; ?>" value="<?php echo $rs["id"]; ?>">
                                
                                    <tr style="background-color: <?= !empty($rs['roid'])?'rgb(160, 28, 28,0.1)':''?>;" class="<?= !empty($rs['roid'])?'converted':''?>">
                                        <?php if( !$shopIsReadOnly ): ?>    
                                            <td>
                                                <?php if(empty($rs['roid'])){?>
                                                    <input class="deletecheckbox form-check-input" id="<?php echo $rs["id"]; ?>" name="checkbox<?php echo $rs["id"]; ?>" type="checkbox">
                                                <?php }?>
                                            </td>
                                        <?php endif; ?>
                                        <td onclick="location.href='quote.php?quoteid=<?php echo $rs["id"]; ?>'"><?php echo $rs["id"]; ?>
                                        </td>
                                        <td onclick="location.href='quote.php?quoteid=<?php echo $rs["id"]; ?>'"><?php
                                            $quotedateTs = strtotime($rs["quotedate"]);
                                            $quoteDate = date('n/j/Y', $quotedateTs);
                                            echo $quoteDate;
                                            ?>
                                        </td>
                                        <td onclick="location.href='quote.php?quoteid=<?php echo $rs["id"]; ?>'"><?php echo $rs["customer"]; ?>
                                        </td>
                                        <td><span onclick="location.href='quote.php?quoteid=<?php echo $rs["id"]; ?>'"><?php echo $rs["year"] . " " . $rs["make"] . " " . $rs["model"]; ?></span><?= !empty($rs['roid'])?"&nbsp;&nbsp;&nbsp;<a target='_blank' href='".COMPONENTS_PRIVATE."/ro/ro.php?roid=".$rs['roid']."'><i><small>RO #".$rs['roid'].'</small></i></a>':'' ?>
                                        </td>
                                        <td onclick="location.href='quote.php?quoteid=<?php echo $rs["id"]; ?>'"><?php echo asDollars($total_quote); ?>
                                        </td>
                                    </tr>

                                <?php

                            }
                    } 
                    $query->close();
                } else {
                    echo "quots prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
                ?>
                </tbody>
            </table>
        </form>
        </div>
    </div>
</main>

<div id="plateModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="z-index: 1111;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="plateModalLabel">Plate Lookup</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">

                        <div class="form-outline mb-4">
                            <input autocomplete="off" autofocus class="form-control sbp-form-control" type="text" id="platelicense" name="platelicense" tabindex="1"/>
                            <label class="form-label" id="platelicenselabel">License #</label>
                        </div>

                        <div class="form-outline mb-4">
                           <input autocomplete="off" class="form-control sbp-form-control" type="text" value="<?= $state?>" id="platestate" name="platestate" tabindex="1"/>
                           <label class="form-label" for="platestate">License State</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer d-flex justify-content-center">
             <button class="btn btn-md btn-primary" type="button" onclick="plateLookup()">Lookup</button>
            </div>
        </div>
    </div>
</div>

<div id="newquotemodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="newquotemodalLabel">Create New Quote</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="newquoteform" name="newquoteform">
                <input id="cid" name="cid" type="hidden"/>
                <input id="vid" name="vid" type="hidden"/>

                <div class="row">
                    <div class="col-md-6">

                        <div id="async" class="form-outline mb-4 autocomplete">
                            <input class="form-control sbp-form-control" type="text" id="customer" name="customer" tabindex="1"/>
                            <label class="form-label" id="customerlabel" for="customer">Customer (L,F)</label>
                        </div>

                        <div class="form-outline mb-4">
                            <input class="form-control sbp-form-control" tabindex="1" type="text" id="address" name="address">
                            <label class="form-label" id="addresslabel" for="address">Address</label>
                        </div>

                        <div class="form-outline mb-4">
                            <input class="form-control sbp-form-control" tabindex="1" type="text" id="zip" name="zip">
                            <label class="form-label" id="ziplabel" for="zip">Zip</label>
                        </div>

                        <div class="form-outline mb-4">
                            <input class="form-control sbp-form-control" tabindex="1" type="text" id="city" name="city">
                            <label class="form-label" id="citylabel" for="city">City</label>
                        </div>

                        <div class="form-outline mb-4">
                            <input class="form-control sbp-form-control" tabindex="1" type="text" id="state" name="state">
                            <label class="form-label" id="statelabel" for="state">State</label>
                        </div>

                        <div class="form-outline mb-4">
                            <input class="form-control sbp-form-control" tabindex="1" type="text" id="phone" name="phone">
                            <label class="form-label" id="phonelabel" for="phone">Phone</label>
                        </div>

                        <div class="form-outline mb-4">
                            <input class="form-control sbp-form-control" tabindex="1" type="text" id="email" name="email">
                            <label class="form-label" id="emaillabel" for="email">Email</label>
                        </div>

                    </div>

                    <div class="col-md-6">

                        <div class="form-outline mb-4">
                           <input class="form-control sbp-form-control" tabindex="1" type="text" id="year" name="year">
                           <label class="form-label" id="yearlabel" for="year">Year</label>
                        </div>

                        <div class="form-outline mb-4">
                           <input class="form-control sbp-form-control" tabindex="1" type="text" id="make" name="make">
                           <label class="form-label" id="makelabel" for="make">Make</label>
                        </div>

                        <div class="form-outline mb-4">
                           <input class="form-control sbp-form-control" tabindex="1" type="text" id="model" name="model">
                           <label class="form-label" id="modellabel" for="model">Model</label>
                        </div>

                        <div class="form-outline mb-4">
                            <input class="form-control sbp-form-control" tabindex="1" type="text" id="color" name="color">
                            <label class="form-label" id="colorlabel" for="color">Color</label>
                        </div>

                        <div class="input-group mb-4">
                            <div class="form-outline">
                               <input class="form-control" tabindex="1" type="text" id="vin" name="vin">
                               <label class="form-label" id="vinlabel" for="vin">VIN</label>
                            </div>
                             <a href="javascript:void(null)" data-mdb-toggle="tooltip" title="Decode VIN" class="btn btn-secondary" onclick="decodeVIN()"><i class="fas fa-key"></i></a>
                        </div>

                        <div class="form-outline mb-4">
                           <input class="form-control sbp-form-control" tabindex="1" type="text" id="comments" name="comments">
                           <label class="form-label" id="commentslabel" for="comments">Comments</label>
                        </div>

                        <div class="form-row">
                           <select id="writer" name="writer" class="select sbp-form-control" tabindex="14">
                            <option value="none">Select</option>
                            <?php
                            $stmt = "select employeelast, employeefirst from employees where shopid = ? and active = 'yes'";
                            if ($equery = $conn->prepare($stmt)) {
                                $equery->bind_param("s", $shopid);
                                $equery->execute();
                                $equery->store_result();
                                $equery->bind_result($employeelast, $employeefirst);
                                while ($equery->fetch()) {
                                    $name = $employeelast . ", " . $employeefirst;
                                    echo "<option value='" . $name . "'>" . $name . "</option>";
                                }
                                $equery->close();
                            } else {
                                echo "employees prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }
                            ?>
                        </select>
                            <label class="form-label select-label" id="writerlabel" for="writer">Writer</label>
                        </div>

                    </div>

                </div>

            </form>
        </div>

        <div class="modal-footer d-flex justify-content-center">
        <?php if ($cfl != 'no'){?><button class="btn btn-sm btn-secondary btn-md pull-left" type="button" onclick="$('#plateModal').modal('show')">Plate Lookup</button><?php }?>
        <button class="btn btn-md btn-primary" type="button" onclick="check_customer()">Create</button>

    </div>
</div>
</div>
</div>


<?php
include getModalsComponent ($component);
include getScriptsGlobal($component);
?>
<script>


    $('.deletecheckbox').each(function () {
        $(this).prop('checked', true);
    })


    $('.deletecheckbox').each(function () {
        $(this).prop('checked', false);
    })

    function checkAll() {

        x = setTimeout(function () {
            d = document.getElementsByClassName("deletecheckbox")
            c = document.getElementById("checkall").checked

            for (j = 0; j < d.length; j++) {
                if (c == true) {
                    document.getElementById(d[j].id).checked = true

                } else {
                    document.getElementById(d[j].id).checked = false

                }
            }
        }, 10)

    }

    function deleteSelected() {

        shopid = '<?php echo $shopid; ?>';
        var checks = []

        $.each($(".deletecheckbox:checked"), function(){
                checks.push($(this).attr('id'));
        });

        if(checks.length === 0)
        {
            sbalert("Please click on the checkbox next to the Quote you want to delete.")
            return;
        }

        sbconfirm("Delete Quotes","You will not be able to recover these quotes once deleted",

            function () {

                showLoader()

                var checkboxids=checks.join(",");
                $.ajax({
                    type: "post",
                    url: "deleteselectedquotes.php",
                    data: "checkboxids=" + checkboxids + "&shopid=" + shopid,
                    success: function () {
                        location.reload();
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        sbalert("Error in mass delete")
                        hideLoader()
                    }
                });

            });

    }

    function decodeVIN() {

        vin = $('#vin').val()
        p = false

        if (vin.length == 17) {
            p = true
        }

        if (p == true) {

            showLoader()
            $.ajax({
                data: "vin=" + vin + "&carfaxloc=<?= $cfl?>",
                url: "<?= COMPONENTS_PRIVATE ?>/v2/customer/vehicle-vin-decode.php",
                dataType: "json",
                success: function (r) {

                    if (r.status=='success'){
                        rvin = r.vin
                        yr = r.yr
                        mk = r.mk
                        md = r.md
                        tr = r.tr
                        bd = r.bd
                        en = r.en

                        $('#year').val(yr).focus().blur()
                        $('#make').val(mk).focus().blur()
                        $('#model').val(md + " " + tr + " " + bd).focus().blur()
                        hideLoader()
                    } else {
                        hideLoader()
                        sbalert("The VIN was not able to be decoded.  Please verify the VIN number")
                    }
                }
            });
        } else {
            sbalert("You must enter a 17 digit VIN to decode it")
        }


    }

    function check_customer() {
        if (($("#vid").val() != "" && $("#vid").val() != 0) && ($("#cid").val() != "" && $("#cid").val() != 0)) {
            var ds = $('#newquoteform').serialize() + "&t=check_customer";
            $.ajax({
                data: ds,
                url: "vehicle_actions.php",
                type: "post",
                dataType: "json",
                success: function (r) {
                    console.log("check cust response "+r);
                    if (r.hasOwnProperty('response') && r.response == "success") {
                        //customer information hasn't changed, continue with saving.
                        check_vehicle();
                    } else {
                        // customer information has been changed
                        var customer = "";
                        if (r.hasOwnProperty('data')){
                            customer = r.data.customer;
                            console.log(r);
                        }
                        console.log(customer);
                        $("#ecust").html(customer)
                        $("#custconfirm").modal("show");
                    }
                }
            });
        } else {
            createQuote();
        }
    }

    function update_customer() {
        var ds = $('#newquoteform').serialize() + "&t=update_customer";
        $.ajax({
            data: ds,
            url: "vehicle_actions.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    //vehicle information hasn't changed, continue with saving.
                    check_vehicle();
                    console.log("customer inforamtion updated");
                }
            }
        });
    }

    function check_vehicle() {
        if (($("#vid").val() != "" && $("#vid").val() != 0) && ($("#cid").val() != "" && $("#cid").val() != 0)) {

            var ds = $('#newquoteform').serialize() + "&t=check_vehicle";
            $.ajax({
                data: ds,
                url: "vehicle_actions.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        //vehicle information hasn't changed, continue with saving.
                        createQuote();
                    } else {
                        // vehicle information has been changed
                        $("#vehconfirm").modal("show");
                    }
                }
            });
        } else {
            createQuote();
        }
    }

    function update_vehicle() {
        var ds = $('#newquoteform').serialize() + "&t=update_vehicle";
        $.ajax({
            data: ds,
            url: "vehicle_actions.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    //vehicle information hasn't changed, continue with saving.
                    createQuote();
                    console.log("vehicle inforamtion updated");
                }
            }
        });
    }

    function create_vehicle() {
        var ds = $('#newquoteform').serialize() + "&t=create_vehicle";
        $.ajax({
            data: ds,
            url: "vehicle_actions.php",
            type: "post",
            success: function (r) {
                if (r.length > 0) {
                    console.log("vehicle id : " + r + " new vehicle created");
                    $("#vid").val(r);
                    createQuote();
                } else {
                    console.log("ERROR : " + r);
                }
            }
        });
    }

    function createQuote()
    {
        $('.btn-md').attr('disabled','disabled')
        showLoader()

        formdata = $('#newquoteform').serialize()

        $.ajax({
            data: formdata + "&t=createquote",
            url: "quoteactions.php",
            type: "post",
            success: function(r) {

                if($.isNumeric(r))
                document.location = 'quote.php?quoteid='+r+'&recalc=y'
                else
                {
                    sbalert(r)
                    $('.btn-md').attr('disabled',false)
                    hideLoader()
                }

            }
        })
    }

  function plateLookup()
  {
    $('.btn-md').attr('disabled','disabled')
    showLoader()

    var license = $('#platelicense').val();
    var state = $('#platestate').val();
    ds = "vin=&lic="+license+"&lst="+state+"&shopid=<?php echo $shopid; ?>"
    $.ajax({
    data: ds,
    url: "<?= INTEGRATIONS ?>/carfax/carfax-quickvin.php",
    success: function(r){
     if (r.indexOf("success") >= 0)
     {
      rar = r.split("|")
      $('#year').val(rar[1])
      $('#make').val(rar[2])
      $('#model').val(rar[3])
      $('#vin').val(rar[8])
      $('#plateModal').modal('hide')
      $('.btn-md').attr('disabled',false)
      hideLoader()
      $('#platelicense').val('')
      $('#platestate').val('<?= $state?>')
     }
     else
     {
      sbalert("No Results found");
      $('.btn-md').attr('disabled',false)
      hideLoader()
     }
    }
   })

  };

    $(document).ready(function() {
        var isChecked = localStorage.getItem('toggleConvertedChecked');
        if (isChecked === null) {
            isChecked = true;
            localStorage.setItem('toggleConvertedChecked', isChecked);
        } else {
            isChecked = isChecked === 'true';
        }
        $('#showconverted').prop('checked', isChecked);
        $('.converted').toggle(isChecked);
    });

    function toggleConverted() {
        var isChecked = $('#showconverted').is(':checked');
        $('.converted').toggle(isChecked);
        localStorage.setItem('toggleConvertedChecked', isChecked);
    }


    $(document).ready(function () {

    $("#quotes-table").dataTable({
        responsive: true,
        paging: false,
        info: false,
        columnDefs: [
            {orderable: false, targets: 0}
        ],
        order: [],
        language: {
         search: '',
         searchPlaceholder: "Search..."
        },
        initComplete : function(){
            $("#quotes-table").removeClass("hidden")
        }
    });

    $('#newquotemodal').on('hide.bs.modal', function () {

     $('.sbp-form-control').each(function(){
        $(this).val('')
     });
     $('#cid').val('')
     $('#vid').val('')
     $('#writer').val('none')
    })

    const asyncAutocomplete = document.querySelector('#async');
    const asyncFilter = async (query) => {

      const url = `customerdata.php?term=${encodeURI(query)}`;
      const response = await fetch(url);
      const data = await response.json();
      const results = Object.values(data).map(obj => ({
        display: obj.value,
        value: obj.orival
      }));
      return results;

    };

    const autocomplete = new mdb.Autocomplete(asyncAutocomplete, {
      filter: asyncFilter,
      displayValue: (value) => value.display,
      setValue: (value) => value.value
    });

    asyncAutocomplete.addEventListener('itemSelect.mdb.autocomplete', (e) => {

    e.preventDefault()

    tar = e.value.value.split("~")

    $('#customer').val($.trim(tar[0]) + ", " + $.trim(tar[1])).blur()
    $('#address').val($.trim(tar[2])).focus().blur()
    $('#city').val($.trim(tar[3])).focus().blur()
    $('#state').val($.trim(tar[4])).focus().blur()
    $('#zip').val($.trim(tar[5])).focus().blur()
    $('#phone').val($.trim(tar[6])).focus().blur()
    $('#email').val($.trim(tar[7])).focus().blur()
    $('#year').val($.trim(tar[8])).focus().blur()
    $('#make').val($.trim(tar[9])).focus().blur()
    $('#model').val($.trim(tar[10])).focus().blur()
    $('#vin').val($.trim(tar[11])).focus().blur()
    $('#color').val($.trim(tar[12])).focus().blur()
    $('#cid').val($.trim(tar[13]))
    $('#vid').val($.trim(tar[14]))
    autocomplete.close();

   })

})

</script>

</body>
</html>
