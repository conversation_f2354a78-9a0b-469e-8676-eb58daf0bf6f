<?php
require CONN;
$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$quoteid = isset($_REQUEST['quoteid']) ? filter_var($_REQUEST['quoteid'], FILTER_SANITIZE_STRING) : 0;
$empid = (isset($_COOKIE['empid'])?$_COOKIE['empid']:'');
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

$stmt = "select companyzip,readonly,feesonquote,alldatausername,newpackagetype,carfaxlocation,datestarted, defaulttaxrate, defaultlabortaxrate, defaultsublettaxrate,userfee1max,userfee2max,userfee3max,hst,pst,gst,qst,chargehst,chargepst,chargegst,chargeqst,userfee1taxable,userfee2taxable,userfee3taxable,userfee1applyon,userfee2applyon,userfee3applyon,updateinvonadd,lineitemcomplete,sortvi,estguide,showpartscostonro,showtechoverhours,hazwastetaxable from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $query->bind_result($companyzip, $readonly, $feesonquote, $alldatausername, $newpackagetype,$cfl,$datestarted, $defaulttaxrate, $defaultlabortaxrate, $defaultsublettaxrate, $userfee1max, $userfee2max, $userfee3max,$hst,$pst,$gst,$qst,$chargehst,$chargepst,$chargegst,$chargeqst,$userfee1taxable,$userfee2taxable,$userfee3taxable,$userfee1applyon,$userfee2applyon,$userfee3applyon,$updateinvonadd,$lineitemcomplete,$sortvi,$estguide,$showpartscostonro,$showtechoverhours,$hazwastetaxable);
    $query->fetch();
    $feesonquote = strtolower($feesonquote);
    $userfee1max = doubleval($userfee1max);
    $userfee2max = doubleval($userfee2max);
    $userfee3max = doubleval($userfee3max);
    $query->close();
}

$feesonquote = strtolower($feesonquote);
$plan = strtolower($newpackagetype);
$motor = $alldatausername;

if ($empid == "Admin")
{
    $showgpinro = "yes";
    $partsordering = "yes";
}
else
{
    $stmt = "select showgpinro,partsordering from employees where shopid = '$shopid' and id = $empid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $query->bind_result($showgpinro,$partsordering);
        $query->fetch();
        $query->close();
    }
}

$stmt = "select pph,showgponro,motor from settings where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($haspph,$showgponro,$motor);
    $query->fetch();
    $query->close();
}

if ($plan == "none") {
    if ($motor == "motorfull") {
        $pid = "39";
    } else {
        if ($motor == "motorest") {
            $pid = "40";
        }
    }
}
if ($plan == "gold") {
    $pid = "40";
} else {
    if ($plan == "platinum" || $plan == "premier" || $plan == "premier plus") {
        $pid = "39";
    }
}

$worldpac = "no";
$wpstmt = "select 'yes' as wp from companyadds where shopid = ? and name = 'WorldPac Integrated Parts Ordering'";
if ($query = $conn->prepare($wpstmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $result = $query->get_result();
    $query->store_result();
    $num_rows = $result->num_rows;
    if ($num_rows > 0) {
        $worldpac = "yes";
    }
    $query->close();
} 

if ($plan == "gold" || $plan == "platinum" || $plan == "premier" || $plan == "premier plus") {
    $worldpac = "yes";
}

$pdusername = $pdtype = '';

$stmt = "select username,apikey from apilogin where shopid = '$shopid' and companyname='prodemand'";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($pdusername,$pdtype);
    $query->fetch();
    $query->close();
}

$stmt = "select quotedate,customer,address,city,state,zip,phone,email,year,make,model,color,vin,notes,writer,fee1label,fee2label,fee3label,fee1amount,fee2amount,fee3amount,fee1percent,fee2percent,fee3percent,notes,cid,vid,roid,totalfees,salestax,totalparts,totallabor,totalsublet,partstaxrate,labortaxrate,sublettaxrate,canadiantax,hazardouswaste from quotes where shopid = ? and id = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $quoteid);
    $query->execute();
    $query->bind_result($quotedate,$customer,$address,$city,$state,$zip,$phone,$email,$year,$make,$model,$color,$vin,$notes,$writer,$fee1label,$fee2label,$fee3label,$fee1amount,$fee2amount,$fee3amount,$fee1percent,$fee2percent,$fee3percent,$notes,$cid,$vid,$quoteroid,$totalfees,$salestax,$totalparts,$totallabor,$totalsublet,$partstaxrate,$labortaxrate,$sublettaxrate,$canadiantax,$hazardouswaste);
    $query->fetch();
    $query->close();
}

if($partstaxrate == '')
{
    $partstaxrate = $defaulttaxrate;
    $labortaxrate = $defaultlabortaxrate;
    $sublettaxrate = $defaultsublettaxrate;
}

if(!is_numeric($companyzip) && !empty($canadiantax))
{
    $ctaxarr = explode(',',$canadiantax);
    $hst = $ctaxarr[0]??0;
    $pst = $ctaxarr[1]??0;
    $gst = $ctaxarr[2]??0;
    $qst = $ctaxarr[3]??0;
}

        
if(!empty($cid))
{
$cstmt = "select taxexempt from customer where shopid = ? and customerid = ?";
//else
//$cstmt = "select taxexempt from customer where shopid = ? and address = ? and city = ? and zip = ?";
 if ($cquery = $conn->prepare($cstmt)) 
 {
    $cquery->bind_param("si", $shopid,$cid);
    $cquery->execute();
    $cquery->store_result();
    $cquery->bind_result($taxexempt);
    $cquery->fetch();
    if ($cquery->num_rows < 1) {
        $taxable = "yes";
    } else {
        if (strtolower($taxexempt) == "yes") {
            $taxable = "no";
        } else {
            $taxable = "yes";
        }
    }
    $cquery->close();
 }
}
else
$taxable = "yes";

$taxablefees = 0;

if(isset($_GET['recalc']) && $_GET['recalc']=='y')
{
    $salestax = 0;

    $laborstmt = "select coalesce(round(sum(extlabor),2),0) tlabor, COALESCE(SUM(if(upper(taxable)='YES',extlabor,0)),0) from quotelabor where shopid = '$shopid' and quoteid = $quoteid";
    if ($lquery = $conn->prepare($laborstmt)){
        $lquery->execute();
        $lquery->store_result();
        $lquery->bind_result($totallabor,$taxablelabor);
        $lquery->fetch();
    }

    $partstmt = "select coalesce(round(sum(extprice),2),0) tprts ,COALESCE(SUM(if(taxable='yes',extprice,0)),0) from quoteparts where shopid = '$shopid' and quoteid = $quoteid";
    if ($pquery = $conn->prepare($partstmt)){
        $pquery->execute();
        $pquery->store_result();
        $pquery->bind_result($totalparts,$taxableparts);
        $pquery->fetch();
    }


    $substmt = "select coalesce(round(sum(price),2),0) from quotesublet where shopid = '$shopid' and quoteid = $quoteid";
    if ($squery = $conn->prepare($substmt)){
        $squery->execute();
        $squery->store_result();
        $squery->bind_result($totalsublet);
        $squery->fetch();
    }

    if ($feesonquote == "yes") {
        if ($fee1percent > 0) {
            if ($userfee1applyon == "all") {
                $fee1amount = ($totalparts + $totallabor + $totalsublet) * ($fee1percent / 100);
            } else {
                $fee1amount = 0;
                if (stripos($userfee1applyon,"labor") !== false)
                    $fee1amount += $totallabor * ($fee1percent / 100);
                if (stripos($userfee1applyon,"parts") !== false)
                    $fee1amount += $totalparts * ($fee1percent / 100);
                if (stripos($userfee1applyon,"sublet") !== false)
                    $fee1amount += $totalsublet * ($fee1percent / 100);
            }

            $fee1max = $userfee1max;
            if ($fee1max > 0 && $fee1max < $fee1amount) {
                $fee1amount = $fee1max;
            }
            if (($totalparts + $totallabor + $totalsublet) == 0) {
                $fee1amount = 0;
            }
        }

        if ($fee2percent > 0) {
            if ($userfee2applyon == "all") {
                $fee2amount = ($totalparts + $totallabor + $totalsublet) * ($fee2percent / 100);
            } else {
                $fee2amount = 0;
                if (stripos($userfee2applyon, "labor") !== false)
                    $fee2amount += $totallabor * ($fee2percent / 100);
                if (stripos($userfee2applyon,"parts") !== false)
                    $fee2amount += $totalparts * ($fee2percent / 100);
                if (stripos($userfee2applyon,"sublet") !== false)
                    $fee2amount += $totalsublet * ($fee2percent / 100);
            }

            $fee2max = $userfee2max;
            if ($fee2max > 0 && $fee2max < $fee2amount) {
                $fee2amount = $fee2max;
            }
            if (($totalparts + $totallabor + $totalsublet) == 0) {
                $fee2amount = 0;
            }
        }

        if ($fee3percent > 0) {
            if ($userfee3applyon == "all") {
                $fee3amount = ($totalparts + $totallabor + $totalsublet) * ($fee3percent / 100);
            } else {
                $fee3amount = 0;
                if (stripos($userfee3applyon, "labor") !== false)
                    $fee3amount += $totallabor * ($fee3percent / 100);
                if (stripos($userfee3applyon, "parts") !== false)
                    $fee3amount += $totalparts * ($fee3percent / 100);
                if (stripos($userfee3applyon,"sublet") !== false)
                    $fee3amount += $totalsublet * ($fee3percent / 100);
            }

            $fee3max = $userfee3max;
            if ($fee3max > 0 && $fee3max < $fee3amount) {
                $fee3amount = $fee3max;
            }
            if (($totalparts + $totallabor + $totalsublet) == 0) {
                $fee3amount = 0;
            }
        }

        $totalfees = $fee1amount + $fee2amount + $fee3amount + $hazardouswaste;

    } else {
        $fee1amount = $fee2amount = $fee3amount = 0;
        $fee1percent = $fee2percent = $fee3percent = 0;
        $totalfees = 0;
    }

    if($taxable=='yes')
    {
    	if($userfee1taxable=='Taxable') $taxablefees += $fee1amount;
    	if($userfee2taxable=='Taxable') $taxablefees += $fee2amount;
    	if($userfee3taxable=='Taxable') $taxablefees += $fee3amount;

    	$ptax = $ltax = $stax = $cantax = 0;


        if($chargehst == "yes" && $hst > 0 ) $cantax += $hst;
    	if($chargegst == "yes" && $gst > 0 ) $cantax += $gst;
    	if($chargepst == "yes" && $pst > 0 ) $cantax += $pst;
    	if($chargeqst == "yes" && $qst > 0 ) $cantax += $qst;

    	if($cantax>0 && $partstaxrate==0 && $labortaxrate==0 && $sublettaxrate==0)
    	{
    	    $partstaxrate = $labortaxrate = $sublettaxrate = $cantax;
    	}

    	if ($partstaxrate > 0) {
    	    $ptax = round(($taxableparts + $taxablefees) * ($partstaxrate / 100), 2);
    	}
    	if ($labortaxrate > 0) {
    	    $ltax = round($taxablelabor * ($labortaxrate / 100), 2);
    	}
    	if ($sublettaxrate > 0) {
    	    $stax = round($totalsublet * ($sublettaxrate / 100), 2);
    	}

        if (strtolower($hazwastetaxable) == "yes"){
            $hazwastetax = ($partstaxrate/100) * $hazardouswaste;
        }else{
            $hazwastetax = 0.00;
        }


    	$salestax = round($ptax + $ltax + $stax + $hazwastetax,2);
    }
}

$fee1amount = round($fee1amount,2);
$fee2amount = round($fee2amount,2);
$fee3amount = round($fee3amount,2);
    
$subtotal = sbpround($totallabor+$totalparts+$totalsublet+$totalfees,2);
$subtotalwofees = $subtotal - $totalfees;
$totalquote = round($subtotal + $salestax,2);

if(empty($quoteroid) && isset($_GET['recalc']) && $_GET['recalc']=='y')
{
    $stmt = "update quotes set totalparts = ?,totallabor = ?,totalsublet = ?,totalfees = ?,salestax = ?,totalquote = ?,fee1amount = ?,fee2amount = ?,fee3amount = ? where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)){
    	$query->bind_param("dddddddddsi", $totalparts, $totallabor, $totalsublet, $totalfees, $salestax, $totalquote, $fee1amount, $fee2amount, $fee3amount, $shopid, $quoteid);
    	$query->execute();
    	$conn->commit();
    	$query->close();
    }
    header('location:quote.php?quoteid='.$quoteid);
    exit;
}


$tparts = 0;
$tpartscost = 0;
$tlabor = 0;
$tlaborhrs = 0;
$gtlaborhrs = 0;
$gtpcost = 0;
$tsublet = 0;
$totaljob = 0;
$egbutton = "";

if ($shopid == "demo"){
    $plan = "platinum";
}
if ($estguide == "yes"){
    $egbutton = "yes";
}else{
    $egbutton = "no";
}
if ($alldatausername == "motorfull"){
    $egbutton = "motorfull";
}elseif ($alldatausername == "motorest"){
    $egbutton = "motorest";
}else{
    $egbutton = "no";
}


if($haspph == "yes")
{
$stmt = "SELECT count(DISTINCT complaintid) FROM quotelabor where shopid = ? AND quoteid = ? AND complaintid>0";

if ($query = $conn->prepare($stmt))
{
    $query->bind_param("si",$shopid,$quoteid);
    $query->execute();
    $query->bind_result($distinctcom);
    $query->fetch();
    $query->close();
}

$pphfees = ($distinctcom>0?$totalfees/$distinctcom:0);
//$pphdisc = ($distinctcom>0?round($discount/$distinctcom,2):0);
    $pphdisc = 0;

$stmt = "select target from profitboost where shopid = ?";
if ($query = $conn->prepare($stmt))
{
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($target);
    $query->fetch();
    $query->close();
}
}

$stmt = "select complaintid from quotecomplaints where shopid = ? and quoteid = ? limit 1";
if ($query = $conn->prepare($stmt))
{
$query->bind_param("si",$shopid,$quoteid);
$query->execute();
$query->store_result();
$numrows = $query->num_rows();
 if ($numrows < 1)
 {
    $stmt = "select coalesce(complaintid,0) from quotecomplaints where shopid = ? order by complaintid desc,quoteid desc limit 1";
    if ($query = $conn->prepare($stmt)){

        $query->bind_param("s",$shopid);
        $query->execute();
        $query->store_result();
        $num_rows = $query->num_rows;
        if ($num_rows > 0){
            $query->bind_result($complaintid);
            $query->fetch();
        }else{
            $complaintid = 1000;
        }
        $query->close();
    }

    // now get existing complaints to get the correct display order
    $stmt = "select displayorder from quotecomplaints where shopid = ? and quoteid = ? order by displayorder desc limit 1";
    if ($query = $conn->prepare($stmt)){

        $query->bind_param("si",$shopid,$quoteid);
        $query->execute();
        $query->store_result();
        $num_rows = $query->num_rows;
        if ($num_rows > 0){
            $query->bind_result($lastdisplayorder);
            $query->fetch();
        }else{
            $lastdisplayorder = 0;
        }
        $query->close();

    }

    $newcomid = $complaintid + 1;
    $lastdisplayorder += 1;

    $stmt = "insert into quotecomplaints (shopid,quoteid,complaint,acceptdecline,complaintid,issue,displayorder) values ('$shopid',$quoteid,'QUOTE','Pending',$newcomid,'None',$lastdisplayorder)";
    if ($query = $conn->prepare($stmt))
    {
        $query->execute();
        $conn->commit();
        $query->close();
    }

    $stmt = "update quoteparts set complaintid='$newcomid' where shopid = ? and quoteid = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("si",$shopid,$quoteid);
        $query->execute();
        $conn->commit();
        $query->close();
    }

    $stmt = "update quotelabor set complaintid='$newcomid' where shopid = ? and quoteid = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("si",$shopid,$quoteid);
        $query->execute();
        $conn->commit();
        $query->close();
    }

    $stmt = "update quotesublet set complaintid='$newcomid' where shopid = ? and quoteid = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("si",$shopid,$quoteid);
        $query->execute();
        $conn->commit();
        $query->close();
    }

 }
}

require(COMPONENTS_PRIVATE_PATH."/gp-v2/gp_quote.php");
require(COMPONENTS_PRIVATE_PATH."/pif-v2/pifquotecalc.php");