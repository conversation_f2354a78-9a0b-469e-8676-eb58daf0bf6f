<?php
$component = "quotes-v2";
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$disabled = $shopIsReadOnly ? "disabled" : "";

include "rules.quote.php";
include getHeadGlobal('');
include getHeaderGlobal('');
include "postheader.quote.php";
include "menu.quote.php";
?>
<style type="text/css">
.autocomplete-dropdown-container
{
    width: 700px !important;
}
.autocomplete-items-list {
  max-height: 400px !important; overflow-y: scroll;
}

.autocomplete-item {font-size: 14px;}

.mdb-autocomplete .mdb-autocomplete-wrap {
  left: 0 !important;
  right: auto !important;
}
.pobutton{width: 125px;}
</style>
<body>
<!-- Content-->
<main id="RO" class="mt-0 min-vh-100">
    <div class="row">
        <div class="col-xxl-9 col-xl-12">
            <div class="row mb-3">
                <!-- Customer Information -->
                <div class="col-lg-4 mb-3 d-flex align-items-stretch">
                    <div class="card card-square w-100">
                        <div class="card-body d-flex flex-column pb-0">
                            <div class="row align-items-center p-2 pb-2 border-bottom">
                                <div class="col-9">
                                    <h5 class="d-inline card-title">Customer</h5>
                                </div>
                                <div class="col-3 text-end">
                                <?php if(empty($quoteroid) && !$shopIsReadOnly){?>
                                    <a href="javascript:void(null)" data-mdb-toggle="tooltip" onclick="$('#btn-convert').hide();editQuote()" data-mdb-placement="left" title="Edit"><i class="fas fa-edit"></i></a>
                                <?php }?>
                                </div>
                            </div>
                            <div class="card-text m-0 pt-3">
                                <table class="table table-borderless table-sm ro-card-text m-0">
                                    <tbody>
                                        <tr>
                                          <th width="30%">Name</th>
                                          <td><?= substr($customer,0,20);?></td>
                                        </tr>
                                        <tr>
                                          <th>Address</th>
                                          <td><?= $address?></td>
                                        </tr>
                                        <tr>
                                          <th>CSZ</th>
                                          <td><?= $city.', '.$state.' '.$zip?></td>
                                        </tr>
                                        <tr>
                                          <th>Phone</th>
                                          <td><?= $phone ?></td>
                                        </tr>
                                        <tr>
                                          <th>Email</th>
                                          <td><?= $email?></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vehicle Information -->
                <div class="col-lg-4 mb-3 d-flex align-items-stretch">
                    <div class="card card-square w-100">
                        <div class="card-body d-flex flex-column pb-0">
                            <div class="row align-items-center p-2 pb-2 border-bottom">
                                <div class="col-9">
                                    <h5 class="d-inline card-title">Vehicle</h5>
                                </div>
                                <div class="col-3 text-end">
                                <?php if(empty($quoteroid) && !$shopIsReadOnly){?>
                                    <a href="javascript:void(null)" class="d-inline" data-mdb-toggle="tooltip" data-mdb-placement="left" title="Edit" onclick="$('#btn-convert').hide();editQuote()"><i class="fas fa-edit"></i></a>
                                <?php }?>
                                </div>
                            </div>
                            <div class="card-text m-0 pt-3">
                                <table class="table table-borderless table-sm  ro-card-text m-0">
                                    <tbody>
                                        <tr>
                                          <th width="30%">Year</th>
                                          <td><?= $year?></td>
                                        </tr>
                                        <tr>
                                          <th>Make</th>
                                          <td><?= $make?></td>
                                        </tr>
                                        <tr>
                                          <th>Model</th>
                                          <td><?= $model?></td>
                                        </tr>
                                        <tr>
                                          <th>VIN</th>
                                          <td>
                                            <?php if(!empty($vin)){?>

                                            <a id="copybtn" href="javascript:void(null)" data-clipboard-action="copy" data-clipboard-text="<?= $vin?>" data-mdb-toggle="tooltip" data-mdb-placement="left" title="Copy"><?php if(!empty($vin)){?><i class="fas fa-copy"></i><?php }?>
                                            <?php
                                            $vinlen = strlen( $vin );
                                            if ($vinlen = 17){
                                                for( $i = 0; $i <= $vinlen; $i++ ) {
                                                    $char = substr( $vin, $i, 1 );
                                                    if ($i == 9){
                                                        echo "<span style='font-weight:bold;color:var(--primary)'>";
                                                    }
                                                    echo $char;
                                                }

                                                echo "</span>";
                                            }
                                        ?></a>
                                        <?php }?>
                                         </td>
                                        </tr>
                                        <tr>
                                          <th>Color</th>
                                          <td><?= $color?></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- QUOTE Information -->
                <div class="col-lg-4 mb-3 d-flex align-items-stretch">
                    <div class="card card-square w-100">
                        <div class="card-body d-flex flex-column pb-0">
                            <div class="row align-items-center p-2 pb-2 border-bottom">
                                <div class="col-11">
                                    <h5 class="d-inline card-title ">Quote</h5>
                                </div>
                            </div>
                            <div class="card-text m-0 pt-3">
                                <table class="table table-borderless table-sm ro-card-text m-0">
                                    <tbody>
                                        <tr>
                                          <th width="30%">Date In</th>
                                          <td><?php $DateIn = date_create($quotedate); echo date_format($DateIn,'m/d/Y') ?></td>
                                        </tr>
                                        <?php if (!$shopIsReadOnly): ?>
                                            <tr>
                                                <th>Comments</th>
                                                <td><a href="javascript:void(null)" class="text-primary" onclick="$('#commentmodal').modal('show')">View<?php if(empty($quoteroid)){?> / Edit<?php }?></a></td>
                                            </tr>
                                        <?php endif; ?>
                                        <tr>
                                            <th>Writer</th>
                                            <td>
                                                <?php if(empty($quoteroid)){?>
                                                <select class="select" onchange="setWriter(this.value)" <?= $disabled; ?>>
                                                  <option><?= ucwords(strtolower($writer)) ?></option>
                                                  <?php
                                                $stmt = "select employeelast, employeefirst from employees where shopid = ? and active = 'yes' and `mode` = 'full'";
                                                if ($query = $conn->prepare($stmt)) {
                                                        $query->bind_param("s", $shopid);
                                                        $query->execute();
                                                        $r = $query->get_result();
                                                        
                                                        while ($rs = $r->fetch_array()) {
                                                                $employeeFirst = ucwords(strtolower($rs['employeefirst']));
                                                                $employeeLast = ucwords(strtolower($rs['employeelast']));
                                                                $selected = ($Writer == $employeeFirst) ? 'selected' : '';

                                                                echo "<option " . $selected . ">" . $employeeFirst . ' ' . $employeeLast . "</option>";
                                                        }
                                                }
                                                ?>
                                                </select>
                                                <?php }else echo(ucwords(strtolower($writer)));?>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


<?php
$numrows = 0;
$lents = "";
$comsfornewcanned = "";
$query = "select * from quotecomplaints where shopid = ? and quoteid = ? and cstatus = 'no' order by displayorder asc";
if ($stmt = $conn->prepare($query)){

    $stmt->bind_param("si",$shopid,$quoteid);
    $stmt->execute();
    $result = $stmt->get_result();
    $issuecount = $result->num_rows;
    $stmt->store_result();
    $icntr = 1;
    $runicntr = "1,";?>


        <div class="row mb-3">
            <div class="col">
                <div class="card card-square w-100">
                    <div class="card-body d-flex flex-column">
                        <div class="row align-items-center p-2">
                            <div class="col-lg-7 justify-content-lg-start justify-content-center">
                                <h5 class="d-inline card-title">Vehicle Issues and Customer Concerns</h5>
                            </div>
                            <div class="col-lg-5 d-flex justify-content-lg-end justify-content-center">
                                <span class="text-secondary me-2">Links to Issues:</span> 
                                <?php for($i=1;$i<=$issuecount;$i++)echo("<a href='javascript:void(null)' onclick='scrollToIssue(\"issuelink_".$i."\")' class='me-2'>".$i."</a>");?>
                            </div>
                        </div>
                        <div class="card-text pt-3">

             <?php
             while($row = $result->fetch_assoc()) {
                ?>
                <div class="accordion <?= $icntr!=1?'pt-3':''?>" id="accordion-vi-<?= $row['complaintid']?>">
                    <a id="issuelink_<?php echo $icntr; ?>" name="<?php echo $icntr; ?>"></a>
                    <div class="accordion-item ro-status-pending" id="accordion-bg-<?= $row['complaintid']?>">
                        <h2 class="accordion-header" id="flush-vi-<?= $row['complaintid']?>">
                        <button class="accordion-button" type="button" data-mdb-toggle="collapse" data-mdb-target="#vi-<?= $row['complaintid']?>" aria-expanded="false" aria-controls="vi-<?= $row['complaintid']?>">
                            <div class="row w-100">
                                <div class="col-md-8">
                                    #<?= $icntr?> <span id="com-<?= $row['complaintid'] ?>"><?php echo trim(strtoupper(iconv('UTF-8', 'ASCII//TRANSLIT',$row['complaint'])));?></span>
                                    <?php if(empty($quoteroid) && !$shopIsReadOnly): ?>
                                        <a href="#" onclick="editIssue(event,'<?= $row['complaintid']?>');return false;" title="Edit" class="ms-2"><i class="fas fa-edit"></i></a>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-4 d-flex justify-content-end pe-3">
                                <?php
                                if (strlen($row['issue']) > 0){
                                ?>
                                 <span class="text-secondary">Category:&nbsp;</span> <?php echo strtoupper($row['issue']); ?>
                                <?php }?>
                                </div>
                            </div>
                        </button>
                        </h2>
                        <div id="vi-<?= $row['complaintid']?>" class="accordion-collapse collapse show" aria-labelledby="flush-headingOne" data-mdb-parent="#vi-<?= $row['complaintid']?>">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col">
                                        <?php if(!$shopIsReadOnly):?>
                                        <div class="row">
                                            <!-- Left Elements-->
                                            <div class="col-xl-9 col-lg-12 col-sm-2 p-0 m-0">
                                                <div class="d-flex justify-content-start ms-0 me-0">
                                                    <!-- Navbar -->
                                                    <nav class="navbar navbar-expand-lg text-start">
                                                        <!-- Container wrapper -->
                                                        <div class="container-fluid pe-2">
                                                            <!-- Toggle button -->
                                                            <button class="navbar-toggler text-primary" type="button" data-mdb-toggle="collapse" data-mdb-target="#navbarSupportedContent<?= $row['complaintid']?>" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                                                            <i class="fas fa-bars"></i>
                                                            </button>

                                                            <!-- Collapsible wrapper -->
                                                            <div class="collapse navbar-collapse" id="navbarSupportedContent<?= $row['complaintid']?>">

                                                                <?php if(empty($quoteroid)){?>
                                                                <!-- Navbar brand -->
                                                                <a class="navbar-brand" href="javascript:void(null)">
                                                                </a>
                                                                <!-- Left links -->
                                                                <ul class="navbar-nav">
                                                                    <?php

                                                                    $motorecho = "";

                                                                    if ($readonly == "yes")
                                                                    $motorecho = "";
                                                                    elseif ($newpackagetype == "platinum" || $newpackagetype == "platinum trial" || $newpackagetype == "premier" || $newpackagetype == "premier plus" || ($newpackagetype == "none" && $egbutton == "motorfull"))
                                                                    {
                                                                        $motorfunction = chr(34) . $row['complaintid'] . chr(34) . chr(44) . chr(34) . "39" . chr(34);
                                                                        $motorecho.="<a class='nav-link' data-mdb-toggle='tooltip' data-mdb-placement='bottom' title='Parts and Labor Guide' onclick='loadMotor(" . $motorfunction . ")' href='javascript:void(null)'>Motor</a>";
                                                                    }
                                                                    elseif($newpackagetype == "gold" || ($newpackagetype == "none" && $egbutton == "motorest"))
                                                                    {
                                                                        $motorfunction = chr(34) . $row['complaintid'] . chr(34) . chr(44) . chr(34) . "40" . chr(34);
                                                                        $motorecho.="<a class='nav-link' data-mdb-toggle='tooltip' data-mdb-placement='bottom' title='Parts and Labor Guide' onclick='loadMotor(" . $motorfunction . ")' href='javascript:void(null)'>Motor</a>";
                                                                    }
                                                                    else
                                                                    $motorecho = "";

                                                                    echo '<li class="nav-item d-flex text-center ps-2 pe-2">'.$motorecho.'</li>';
                                                                    
                                                                    ?>

                                                                    <li class="nav-item d-flex text-center ps-2 pe-2">
                                                                      <a class="nav-link" href="javascript:void(null)" data-mdb-toggle="tooltip" data-mdb-placement="bottom" title='Add Parts to this Issue' onclick="addPart(<?php echo $row['complaintid'];?>)">Parts</a>
                                                                    </li>
                                                                    <li class="nav-item d-flex text-center ps-2 pe-2">
                                                                      <a class="nav-link" href="javascript:void(null)" data-mdb-toggle="tooltip" data-mdb-placement="bottom" title='Add Labor to this Issue' onclick="addLabor(<?php echo $row['complaintid'];?>)">Labor</a>
                                                                    </li>
                                                                    <li class="nav-item d-flex text-center ps-2 pe-2">
                                                                      <a class="nav-link" href="javascript:void(null)" data-mdb-toggle="tooltip" data-mdb-placement="bottom" title='Add a Sublet item to this issue' onclick="addSublet(<?php echo $row['complaintid'];?>)">Sublet</a>
                                                                    </li>
                                                                    <li class="nav-item d-flex text-center ps-2 pe-2">
                                                                      <a class="nav-link" href="javascript:void(null)" data-mdb-toggle="tooltip" data-mdb-placement="bottom" onclick="addCannedJob(<?php echo $row['complaintid'];?>)" title='Add a canned job (canned jobs are multiple parts and labor in one job)'>Canned Job</a>
                                                                    </li>
                                                                  </ul>
                                                                  <?php }?>
                                                            </div>
                                                        </div>
                                                    </nav>
                                                </div>
                                            </div>

                                            
                                        </div>
                                    <?php endif; ?>
                        <div class="d-flex">
                            <div class="row overflow-auto content-container p-0 mt-0 card-square">
                                <table class="display dt-responsive nowrap" style="width:100%;">
                                
                                    <tbody>
        <?php
            $currcommid = $row['complaintid'];
            $pquery = "select * from quoteparts where shopid = ? and quoteid = ? and complaintid = ? order by id asc";
            $partsgp = 0;
            $laborgp = 0;
            $subgp = 0;

            if ($pstmt = $conn->prepare($pquery)){
                $pstmt->bind_param("sii",$shopid,$quoteid,$currcommid);
                $pstmt->execute();

                $presult = $pstmt->get_result();
                $pstmt->store_result();
                $num_parts_rows = $presult->num_rows;

                while ($prow = $presult->fetch_assoc()){

                    if (strtolower($prow['taxable']) == 'yes'){
                        $taxstr = " (TAX)";
                    }else{
                        $taxstr = " (NON-TAX)";
                    }
                    $extcost = round($prow['partcost']*$prow['qty'],2);
                    $tparts += round($prow['extprice'],2);
                    $tpartscost += round($extcost,2);
                    $gtpcost+= round($extcost,2);
                    $statuscolor = "white";

                    $partsgp += ($prow['extprice'] - $extcost);

                    $onclickPart = !$shopIsReadOnly 
                        ? "onclick=\"editPart('{$prow['id']}')\"" 
                        : "";
                    ?>

                    <tr>
                        <td <?= $onclickPart; ?>><strong>(P)</strong> <?php echo strtoupper($prow['partnumber']);?></td>
                        <td <?= $onclickPart; ?>>
                            <?php
                                    echo strtoupper($prow['part']) . "<span> (Cost Each:" . number_format(round($prow['partcost'],2),2) . "/ Cost Total: " .  number_format($extcost,2) . ")</span>" ;
                            ?>
                        </td>
                        <td <?= $onclickPart; ?>><?php echo $prow['qty']." @ ".number_format(round($prow['price'],2),2).$taxstr;?></td>
                        <td <?= $onclickPart; ?>><?php if ($prow['discount'] > 0){echo $prow['discount']."% Disc";} ?></td>
                        <td <?= $onclickPart; ?>>$<?php echo number_format(round($prow['extprice'],2),2);?></td>
                        <td <?= $onclickPart; ?>></td>
                        
                        <?php if( !$shopIsReadOnly ): ?>
                            <td data-mdb-toggle="tooltip" data-mdb-placement="right" title="Click to delete this part permanently from the quote">
                                <?php if(empty($quoteroid)){?><i onclick="deleteItem(<?php echo $prow['id'];?>,'PART')" class="fas fa-times-circle"></i><?php }?>
                            </td>
                        <?php endif; ?>
                    </tr>
                    <?php 
                     }
                    }


            $lquery = "select extlabor,hours,taxable,complaintid,id,rate,labor,tech from quotelabor where shopid = ? and quoteid = ? and complaintid = ? order by id asc";
            if ($lstmt = $conn->prepare($lquery)){
                $lstmt->bind_param("sii",$shopid,$quoteid,$currcommid);
                $lstmt->execute();

                $lresult = $lstmt->get_result();
                $lstmt->store_result();
                $num_labor_rows = $lresult->num_rows;
                while ($lrow = $lresult->fetch_assoc()){
                    $tlabor += round($lrow['extlabor'],2);
                    $tlaborhrs += $lrow['hours'];
                    $gtlaborhrs =  $gtlaborhrs + $lrow['hours'];
                    $labtaxline = strtolower($lrow["taxable"]);

                    if ((empty($labtaxline) || $labtaxline == 'yes')){
                        $ltax = " (TAX)";
                    }else{
                        $ltax = " (NON-TAX)";
                    }
                    
                    $onclickLabor = !$shopIsReadOnly 
                        ? "onclick=\"editLabor('{$lrow['id']}')\"" 
                        : "";
                ?>
                <tr>
                    <td <?= $onclickLabor; ?>><strong>(L)</strong> <?= substr(html_entity_decode(strtoupper($lrow['tech']),ENT_QUOTES | ENT_HTML5),0,15) ?></td>
                    <td <?= $onclickLabor; ?>><span><?php echo strtoupper($lrow['labor']);?></span></td>
                    <td <?= $onclickLabor; ?>><?php echo $lrow['hours']." @ ".number_format(round($lrow['rate'],2),2).$ltax;?></td>
                    <td <?= $onclickLabor; ?>></td>
                    <td <?= $onclickLabor; ?>>$<?php echo number_format(round($lrow['extlabor'],2),2);?></td>
                    <td></td>

                    <?php if( !$shopIsReadOnly ): ?>
                        <td data-mdb-toggle="tooltip" data-mdb-placement="right" title="Click to delete this labor permanently from the quote">
                            <?php if(empty($quoteroid)){?><i onclick="deleteItem(<?php echo $lrow['id'];?>,'LABOR')" class="fas fa-times-circle"></i><?php }?>
                        </td>
                    <?php endif; ?>
                </tr>
                <?php
                }
            }

            $squery = "select description,cost,price,cost,complaintid,id,invnum from quotesublet where shopid = ? and quoteid = ? and complaintid = ? order by id asc";
            if ($sstmt = $conn->prepare($squery)){
                $sstmt->bind_param("sii",$shopid,$quoteid,$currcommid);
                $sstmt->execute();

                $sresult = $sstmt->get_result();
                $sstmt->store_result();
                $num_sublet_rows = $sresult->num_rows;
                while ($srow = $sresult->fetch_assoc()){
                    $tsublet += round($srow['price'],2);
                    $subgp += ($srow['price'] - $srow['cost']);

                    if ($sublettaxrate > 0){
                        $stax = " (TAX)";
                    }else{
                        $stax = " (NON-TAX)";
                    }

                $onclickSublet = !$shopIsReadOnly 
                    ? "onclick=\"editSublet('{$srow['id']}')\"" 
                    : "";
                ?>
                <tr>
                    <td <?= $onclickSublet; ?>><strong>(S)</strong> Sublet #<?php echo strtoupper($srow['invnum']);?></td>
                    <td <?= $onclickSublet; ?>><?php echo strtoupper($srow['description']);?></td>
                    <td <?= $onclickSublet; ?>><?php echo "1 @ ".number_format(round($srow['price'],2),2).$stax;?></td>
                    <td <?= $onclickSublet; ?>>&nbsp;</td>
                    <td <?= $onclickSublet; ?>>$<?php echo number_format(round($srow['price'],2),2);?></td>
                    <td <?= $onclickSublet; ?>></td>
                    
                    <?php if( !$shopIsReadOnly ): ?>
                        <td data-mdb-toggle="tooltip" data-mdb-placement="right" title="Click to delete this sublet item permanently from the quote">
                            <?php if(empty($quoteroid)){?><i onclick="deleteItem(<?php echo $srow['id'];?>,'SUBLET')" class="fas fa-times-circle"></i><?php }?>
                        </td>
                    <?php endif; ?>
                </tr>
                <?php
                }
            }
            
            $totaljob = $tsublet+$tlabor+$tparts;
            $comsfornewcanned .= $currcommid."|";

            $pif = '';

            if($haspph == "yes" && $num_labor_rows > 0)
            {
                $pif1 = $partsgp+$subgp+$tlabor+$pphfees-$pphdisc;
                $pif = $pif1/$tlaborhrs;
            }
            

            ?>

                                                    </tbody>
                                                </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                                <div class="row pe-2 ps-2 mt-3">
                                                    <div class="col-12 col-lg-2 text-center">Total this Concern: <br><?php if($pif!=''){?> <a href="javascript:void(null)" class="<?= $pif>=$target?'text-success':'text-danger' ?>" onclick="showPPFIssue(<?php echo $row['complaintid'];?>)">PPH: <?= asdollars($pif)?></a><?php }?></div>
                                                    <div class="col-6 col-lg text-center"><?php if (strtolower($showpartscostonro) == "yes") {?>Parts Cost: <?php echo "$".number_format($tpartscost,2); ?><?php }else{?>Parts: <?php echo "$".number_format($tparts,2); ?><?php }?></div>
                                                    <?php if (strtolower($showpartscostonro) == "yes") {?><div class="col-6 col-lg text-center">Parts Price: <?php echo "$".number_format($tparts,2); ?></div><?php }?>
                                                    <div class="col-6 col-lg text-center">Labor Hrs: <?php echo $tlaborhrs; ?></div>
                                                    <div class="col-6 col-lg text-center">Labor: <?php echo "$".number_format($tlabor,2); ?></div>
                                                    <div class="col-6 col-lg text-center">Sublet: <?php echo "$".number_format($tsublet,2); ?></div>
                                                    <div class="col-6 col-lg text-center">Total: <?php echo "$".number_format($totaljob,2); ?></div>
                                                </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>  
                                        
                                        
                    <?php 

        $tlabor = 0;
        $tlaborhrs = 0;
        $tparts = 0;
        $tpartscost = 0;
        $tsublet = 0;
        $icntr++;
        $runicntr .= $icntr.",";
                     }
                    }

if (($icntr-1) == 0){
    echo "<br><br><div class='text center text-primary'>You have no vehicle issues or customer concerns.  Please add them first, then add your parts and labor.  Click the Vehicle Issues button on the left</div><br><br><br><br>";

}


if (strpos($lents,"0") === false){
    echo "<input type='hidden' id='lents' value='no'>";
}else{
    echo "<input type='hidden' id='lents' value='yes'>";
}
echo '<input type="hidden" id="comsfornewcanned" value="'.$comsfornewcanned.'">';
echo "<input type='hidden' id='icntrs' value='". substr($runicntr,0,strlen($runicntr)-1) . "'>";

?>
                                                <div class="d-flex border-top ps-0 pe-0 pt-4 justify-content-center">
                                                    <?php if (strtolower($showpartscostonro) == "yes"){?>
                                                    <div class="ps-4 pe-4 text-center"><h4>Total Parts Cost: <?= asdollars($gtpcost)?></h4></div>
                                                    <?php }?>
                                                    <div class="ps-4 pe-4 text-center"><h4>Total Labor Hrs: <?= $gtlaborhrs?></h4></div>
                                                </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                     
                     
				    <div class="col-xxl-3 col-xl-12">
                        <div class="row mb-3 fees">
                            <!-- Fees -->
                            <div class="col">
                                <div class="d-flex flex-column">
                                    <div id="fees" class="extras">
                                        <div class="card-body">
                                        <div class="d-flex justify-content-between flex-row align-items-center text-center">
                                            <div class="card-title w-100">Fees</div>
                                            <?php if(empty($quoteroid) && !$shopIsReadOnly){?><div><a href="javascript:void(null)" data-mdb-toggle="modal" data-mdb-target="#feesmodal" title="Edit"><i class="fas fa-edit"></i></a></div><?php }?>
                                        </div>
                                        <div class="d-flex justify-content-start"></div>
                                        <div class="d-flex justify-content-end"></div>
                                    </div>
                                    <div class="card-text">
                                        <table class="table table-borderless table-sm">
                                            <tbody>
                                                <tr>
                                                    <th>Hazardous Waste</th>
                                                    <td class="text-end"><?php echo '$'.sbpround($hazardouswaste,2); ?></td>
                                                </tr>
                                                <?php
                                                if (strlen($fee1label) > 0){
                                                ?>
                                                <tr>
                                                    <th><?php echo ucwords(strtolower($fee1label));?></th>
                                                    <td class="text-end">
                                                        <?php
                                                            if ($fee1percent > 0)
                                                            echo sbpround($fee1percent,2).'%';
                                                            else
                                                            echo '$'.sbpround($fee1amount,2);
                                                        ?>
                                                    </td>
                                                </tr>
                                                <?php
                                                }
                                                if (strlen($fee2label) > 0){
                                                ?>
                                                <tr>
                                                    <th><?php echo ucwords(strtolower($fee2label));?></th>
                                                    <td class="text-end">
                                                        <?php
                                                            if ($fee2percent > 0)
                                                            echo sbpround($fee2percent,2).'%';
                                                            else
                                                            echo '$'.sbpround($fee2amount,2);
                                                        ?>
                                                    </td>
                                                </tr>
                                                <?php
                                                }
                                                if (strlen($fee3label) > 0){
                                                ?>
                                                <tr>
                                                    <th><?php echo ucwords(strtolower($fee3label));?></th>
                                                    <td class="text-end">
                                                        <?php
                                                            if ($fee3percent > 0)
                                                            echo sbpround($fee3percent,2).'%';
                                                            else
                                                            echo '$'.sbpround($fee3amount,2);
                                                        ?>
                                                    </td>
                                                </tr>
                                                <?php
                                                }
                                                ?>
                                                <tr>
                                                    <th>Parts Tax Rate</th>
                                                    <td class="text-end"><?php echo $partstaxrate; ?>%</td>
                                                </tr>
                                                <tr>
                                                    <th>Labor Tax Rate</th>
                                                    <td class="text-end"><?php echo $labortaxrate; ?>%</td>
                                                </tr>
                                                <tr>
                                                    <th>Sublet Tax Rate</th>
                                                    <td class="text-end"><?php echo $sublettaxrate; ?>%</td>
                                                </tr>
                                            </tbody>
                                      </table>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between flex-row align-items-center text-center">
                                    <div class="card-title w-100">Totals</div>
                                </div>
                                <div class="card-text">
                                    <table class="table table-borderless table-sm">
                                        <tbody>
                                            <tr>
                                              <th>Parts</th>
                                              <td class="text-end">$<?php echo sbpround($totalparts,2);?></td>
                                            </tr>
                                            <tr>
                                                <th>Labor</th>
                                                <td class="text-end">$<?php echo sbpround($totallabor,2);?></td>
                                            </tr>
                                            <tr>
                                                <th>Sublet</th>
                                                <td class="text-end">$<?php echo sbpround($totalsublet,2);?></td>
                                            </tr>
                                            <tr>
                                                <th>Fees</th>
                                                <td class="text-end">$<?php echo sbpround($totalfees,2);?></td>
                                            </tr>
                                            <tr>
                                                <th>Subtotal</th>
                                                <td class="text-end">$<?php echo sbpround(($subtotal),2);?></td>
                                            </tr>
                                            <tr>
                                                <th>Sales Tax</th>
                                                <td class="text-end">$<?php echo sbpround($salestax,2);?></td>
                                            </tr>
                                            <tr class="text-primary">
                                                <th><strong>Total Quote</strong></th>
                                                <td class="text-end"><strong>$<?php echo number_format($totalquote,2);?></strong></td>
                                            </tr>
                                        </tbody>
                                  </table>
                                </div>
                            </div>
                        </div>          
                    </div>
                </div>
            </div>
        </main>

<input type="hidden" id="createcus">
<input type="hidden" id="createveh">
			
														
<?php 
include getModalsGlobal('');
include getScriptsGlobal($component);
?>
 
</body>
    
</html>
