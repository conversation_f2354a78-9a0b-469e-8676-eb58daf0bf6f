<nav id="main-sidenav" class="sidenav sidenav-sm d-print-none" data-mdb-hidden="false" data-mdb-accordion="true">

	<ul class="sidenav-menu pb-5 ps-0 mt-2">
	
		<li class="sidenav-item">
            <a class="sidenav-link ripple-surface-primary" href="quotes.php"><i class="fas fa-sign-out"></i><span class="ms-2">Quote List</span></a>
        </li>
        <li class="sidenav-item">
            <a class="sidenav-link ripple-surface-primary" title='Print the Quote' onclick="printQuote()" href="javascript:void(null)"> <i class="fas fa-print"></i><span class="ms-2">Print</span></a>
        </li>

        <?php if( !$shopIsReadOnly ): ?>
            <li class="sidenav-item">
                <a class="sidenav-link ripple-surface-primary" onclick="startSendQuote()" href="javascript:void(null)"><i class="fas fa-paper-plane"></i><span class="ms-2">Send Quote</span></a>
            </li>
        <?php endif; ?>

        <?php if(empty($quoteroid)){?>
            <?php if(!empty($pdusername) && ($plan=='gold' || $plan=='platinum' || $plan=='premier' || $plan=='premier plus')){
            if($pdtype!='3'){?>
                <li class="sidenav-item">
                    <a class="sidenav-link ripple-surface-primary" title='ProDemand' onclick="loadProDemand('<?= $pdtype?>')" href="javascript:void(null)"><i class="fas fa-wrench"></i><span class="ms-2">ProDemand</span></a>
                </li>
            <?php }else{?>
                <li class="sidenav-item"><a class="sidenav-link ripple-surface-primary" href="javascript:void(null)"><i class="fas fa-suitcase"></i><span class="ms-2">ProDemand</span></a>
                    <ul class="sidenav-collapse">
                    <li class="sidenav-item">
                        <a class="sidenav-link" title='ProDemand for Light Duty Vehicles' onclick="loadProDemand('1')" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i>Light Duty</a>
                    </li>
                    <li class="sidenav-item">
                        <a class="sidenav-link" title='ProDemand for Heavy Duty Vehicles' onclick="loadProDemand('2')" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i>Heavy Duty</a>
                    </li>
                    </ul>
                </li>
            <?php }}?>

            <?php if( !$shopIsReadOnly ): ?>
                <li class="sidenav-item">
                    <a class="sidenav-link ripple-surface-primary" onclick="sendCalendar()" href="javascript:void(null)"><i class="fas fa-calendar"></i><span class="ms-2">Send to Calendar</span></a>
                </li>

                <li class="sidenav-item">
                    <a class="sidenav-link ripple-surface-primary" title='Add / Edit / ReOrder the Vehicle Issues' onclick="showIssues()" href="javascript:void(null)"><i class="fas fa-car-crash"></i><span class="ms-2">Vehicle Issues</span></a>
                </li>
            <?php
            if ($readonly == "no" && strtolower($partsordering) == 'yes' && ($plan != 'silver' || ($plan == 'silver' && strtotime($datestarted) < strtotime('2022-06-09')))){
                ?>
                    <li class="sidenav-item">
                        <a class="sidenav-link ripple-surface-primary" title='Place an electronic order for parts with Epicor / Nexpart / Worldpac' onclick="$('#partsordering').modal('show')" href="javascript:void(null)"><i class="fas fa-cart-plus"></i><span class="ms-2">Parts Ordering</span></a>
                    </li>
                <?php
            }
            endif;
        }
        ?>
        <li class="sidenav-item">
            <a class="sidenav-link ripple-surface-primary" title='Retrieve Quick Lube Info' onclick="getQuickLube()" href="javascript:void(null)"><i class="fas fa-oil-can"></i><span class="ms-2">Lube Info</span></a>
        </li>

        <li class="sidenav-item">
            <a class="sidenav-link ripple-surface-primary" onclick="showSuppliers()" href="javascript:void(null)">
              <i class="fas fa-truck"></i>
              <span class="ms-2">Supplier List</span>
            </a>
        </li>

        <?php if( !$shopIsReadOnly ): ?>
            <li class="sidenav-item">
                <?php if(!empty($quoteroid)){?>
                    <a class="sidenav-link ripple-surface-primary text-primary" href="<?= COMPONENTS_PRIVATE?>/v2/ro/ro.php?roid=<?= $quoteroid?>"><i class="fas fa-check"></i><span class="ms-2">RO #<?= $quoteroid?></span></a>
                <?php }else{?>
                    <a class="sidenav-link ripple-surface-primary" title='Convert to RO' onclick="$('#btn-convert').show();convertRO()" href="javascript:void(null)"><i class="fas fa-check"></i><span class="ms-2">Convert to RO</span></a>
                <?php }?>
            </li>
        <?php endif; ?>
	</ul>	
</nav>
