<!-- Main Container -->
<main id="main-container" class="min-vh-100">
    <div class="row pb-4">
        <div class="form-group">
            <div class="row pb-4">
                <h2>Customers</h2>
            </div>
            <div class="row justify-content-start">
                <div class="col-md-5 col-12">
                    <div class="form-outline mb-2">
                        <input class="form-control" type="text" id="sf" value="<?php if (strlen($ln) > 0) {
                            echo $ln . ',' . $fn;
                        } ?>" name="sf">
                        <label class="form-label" for="sf">Search Customer by last name, first name, address, phone </label>
                    </div>

                </div>
                <div class="col-md-7 col-12">
                    <div class="d-flex justify-content-between">
                        <div class="d-flex">
                            <div class="me-4">
                                <label for="vincheck" class="form-check-label">Partial VIN: </label>
                                <input type="checkbox" class="form-check-input me-2" id="vincheck" onclick="showByVin()">
                            </div>

                            <button style="display:none" onclick="hideInactive('hide')" id="hidebutton" class="btn btn-secondary btn-md">Hide Inactive</button>
                            <button onclick="hideInactive('show')" id="showbutton" class="btn btn-secondary btn-md">Show Inactive</button>
                        </div>
                        
                        <?php if (!$shopIsReadOnly) : ?>
                            <button onclick="location.href='customer-add.php'" id="newcustomerbutton" class="btn btn-primary btn-md">Add New Customer</button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12" id="table-container">
            <table id="customer-table" class="w-100 nowrap display sbdatatable">
                <thead>
                <tr class="header">
                    <th>Name</th>
                    <th>Address</th>
                    <th>Home</th>
                    <th>Work</th>
                    <th>Cell</th>
                    <th>Active</th>
                </tr>
                </thead>
            </table>
    </div>


</main>

<input type="hidden" id="showinactive" value="no">
