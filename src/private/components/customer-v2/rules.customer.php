<?php

$shopid = $_COOKIE['shopid'];
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$empid = (isset($_COOKIE['empid']) ? $_COOKIE['empid'] : '');
$date = date('m/d/Y');
$fn = "";
$ln = "";
if (isset($_GET['ln'])) {
    $ln = $_GET['ln'];
}
if (isset($_GET['ln'])) {
    $fn = $_GET['fn'];
}


$stmt = "select readonly,shopnotice from company where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($readonly, $shopnotice);
    $query->fetch();
    $query->close();
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

if ($empid == "Admin") {
    $reportsaccess = $settingsaccess = $dashboardaccess = $EditInventory = $accountingaccess = "YES";
} else {
    $stmt = "select upper(ReportAccess),upper(CompanyAccess), UCASE(dashboardaccess), UCASE(editinventory), UCASE(accounting) from employees where id = ? and shopid = ?";

    if ($query = $conn->prepare($stmt)) {

        $query->bind_param("is", $empid, $shopid);
        $query->execute();
        $query->bind_result($reportsaccess, $settingsaccess, $dashboardaccess, $EditInventory, $accountingaccess);
        $query->fetch();
        $query->close();
    } else {
        die("Prepare failed: (" . $conn->errno . ") " . $conn->error);
    }
}

$profitboost = "";

$stmt = "select shopnotice,merchantaccount,profitboost, matco, readonly from company where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    //echo $query->fullQuery."<BR>";
    $query->bind_result($shopnotice, $merchantaccount, $profitboost, $matco, $readonly);
    //echo $query->fullQuery;
    $query->fetch();
    $query->close();
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$date = date('m/d/Y');

$rocount = 0;
$stmt = "select count(*) c from repairorders where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->bind_result($rocount);
    $query->fetch();
    $query->close();
}
