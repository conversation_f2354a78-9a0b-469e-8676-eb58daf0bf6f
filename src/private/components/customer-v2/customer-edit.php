<?php

$time_start = microtime(true);

// Global Variables
$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$disabled = $shopIsReadOnly ? "disabled" : "";
$date = new DateTime('now');
$component = "customer-v2";
// Page Variables
$title = 'Customer';
$subtitle = "";

include getHeadGlobal($component);
//include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal($component);
include getMenuGlobal($component);

$empid = (isset($_COOKIE['empid']) ? $_COOKIE['empid'] : '');

//$cid = $_GET['cid'];
$cid = filter_var($_GET['cid'], FILTER_SANITIZE_STRING);


//check for create part sale button
if ($empid == "Admin") {
    $createCT = "yes";
    $deletecustomer = "YES";
    $mergecustomers = "YES";
} else {
    $stmt = "select createCT,upper(deletecustomer),upper(mergecustomers) from employees where shopid = '$shopid' and id = '$empid'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($createCT, $deletecustomer, $mergecustomers);
        $query->fetch();
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
}

$vid = 0;
if (isset($_GET['vid'])) {
    $vid = filter_var($_GET['vid'], FILTER_SANITIZE_STRING);
    $stmt = "update vehicles set customerid = $cid where shopid = '$shopid' and vehid = $vid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
    } else {
        echo $conn->error;
    }
}

$yr = '';
$mk = '';
$md = '';
$cr = '';
$qvin = '';

if (isset($_GET['schid'])) {
    $quotestr = "&schid=" . filter_var($_GET['schid'], FILTER_SANITIZE_STRING);
} elseif (isset($_GET['quoteid'])) {
    $quotestr = "&quoteid=" . filter_var($_GET['quoteid'], FILTER_SANITIZE_STRING);
    $stmt = "select year,make,model,color,vin from quotes where shopid = '{$shopid}' and id = " . $_GET['quoteid'];
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($yr, $mk, $md, $cr, $qvin);
        $query->fetch();
        $query->close();
    }
} else {
    $yr = '';
    $mk = '';
    $md = '';
    $cr = '';
    $qvin = '';

    $quotestr = "";
}

$stmt = "select smsnum from smsnumbers where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->store_result();
    $rc = $query->num_rows();
    if ($rc > 0) {
        $query->bind_result($smsnum);
        $query->fetch();
    } else {
        $smsnum = "";
    }
    $query->close();
}


$stmt = "select readonly,shopnotice,customuserfield1,customuserfield2,customuserfield3,vehiclefield1label,vehiclefield2label,vehiclefield3label,vehiclefield4label,vehiclefield5label,vehiclefield6label,vehiclefield7label,vehiclefield8label,carfaxlocation,companystate,profitboost,cfpid from company where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($readonly, $shopnotice, $cu1, $cu2, $cu3, $cv1, $cv2, $cv3, $cv4, $cv5, $cv6, $cv7, $cv8, $cfl, $companystate, $isprofitboost, $cfpid);
    $query->fetch();
    $query->close();
} else {
    echo "1 Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select showcfp,notesalert from settings where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($showcfp, $notesalert);
    $query->fetch();
    $query->close();
}

$stmt = "select yearlabel,makelabel,modellabel,enginelabel,cylinderlabel,translabel,licenselabel,statelabel,fleetlabel,currmileagelabel,colorlabel,drivelabel,vinlabel from vehiclelabels where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($yearlabel, $makelabel, $modellabel, $enginelabel, $cylinderlabel, $translabel, $licenselabel, $statelabel, $fleetlabel, $currmileagelabel, $colorlabel, $drivelabel, $vinlabel);
    $query->fetch();
    $query->close();
} else {
    echo "2 Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

if (empty($yearlabel))
    $yearlabel = "Year";
if (empty($makelabel))
    $makelabel = "Make";
if (empty($modellabel))
    $modellabel = "Model";
if (empty($enginelabel))
    $enginelabel = "Engine";
if (empty($cylinderlabel))
    $cylinderlabel = "Cylinders";
if (empty($translabel))
    $translabel = "Transmission";
if (empty($licenselabel))
    $licenselabel = "License";
if (empty($statelabel))
    $statelabel = "License State";
if (empty($fleetlabel))
    $fleetlabel = "Fleet Number";
if (empty($currmileagelabel))
    $currmileagelabel = "Current Mileage";
if (empty($colorlabel))
    $colorlabel = "Color";
if (empty($drivelabel))
    $drivelabel = "Drive Type";
if (empty($vinlabel))
    $vinlabel = "VIN";


$stmt = "select `column 27`,preferredcontactmethod,CustomerID,LastName,FirstName,Address,City,State,Zip,HomePhone,WorkPhone,CellPhone,Pager,Fax,EMail,Wholesale,UserDefined1,UserDefined2,UserDefined3,Comments,ServiceReminder,Specials,Follow,Discount,contact,cellprovider,active,taxexempt,spousename,spousecell,spousework,shippingto,shippingaddress,shippingcity,shippingstate,shippingzip,shippingphone,billto,billtoaddress,billtocity,billtostate,billtozip,billtophone,creditlimit,extension,customertype,onhold,accounttype,notes from customer where shopid = '$shopid' and customerid = $cid";

if ($query = $conn->prepare($stmt)) {

    $query->execute();
    $query->store_result();
    $num_stmt_rows = $query->num_rows;
    if ($num_stmt_rows > 0) {
        $query->bind_result($discountpercent, $preferredcontactmethod, $CustomerID, $LastName, $FirstName, $Address, $City, $State, $Zip, $HomePhone, $WorkPhone, $CellPhone, $Pager, $Fax, $EMail, $Wholesale, $UserDefined1, $UserDefined2, $UserDefined3, $Comments, $ServiceReminder, $Specials, $Follow, $Discount, $contact, $cellprovider, $active, $taxexempt, $spousename, $spousecell, $spousework, $shippingto, $shippingaddress, $shippingcity, $shippingstate, $shippingzip, $shippingphone, $billto, $billtoaddress, $billtocity, $billtostate, $billtozip, $billtophone, $creditlimit, $extension, $customertype, $onhold, $accounttype, $notes);
        $query->fetch();
    } else {
        $discountpercent = "";
        $preferredcontactmethod = "";
        $CustomerID = "";
        $LastName = "";
        $FirstName = "";
        $Address = "";
        $City = "";
        $State = "";
        $Zip = "";
        $HomePhone = "";
        $WorkPhone = "";
        $CellPhone = "";
        $Pager = "";
        $Fax = "";
        $EMail = "";
        $Wholesale = "";
        $UserDefined1 = "";
        $UserDefined2 = "";
        $UserDefined3 = "";
        $Comments = "";
        $ServiceReminder = "";
        $Specials = "";
        $Follow = "";
        $Discount = "";
        $contact = "";
        $cellprovider = "";
        $active = "";
        $taxexempt = "";
        $spousename = "";
        $spousecell = "";
        $spousework = "";
        $shippingto = "";
        $shippingaddress = "";
        $shippingcity = "";
        $shippingstate = "";
        $shippingzip = "";
        $shippingphone = "";
        $billto = "";
        $billtoaddress = "";
        $billtocity = "";
        $billtostate = "";
        $billtozip = "";
        $billtophone = "";
        $creditlimit = "";
        $extension = "";
        $customertype = "";
        $onhold = "";
        $accounttype = "";
    }
    $query->close();
} else {
    echo "Customer failed: (" . $conn->errno . ") " . $conn->error;
}

if ($qvin != '') {
    $decodevin = $qvin;
} elseif (isset($_GET['vin'])) {
    $decodevin = trim(filter_var($_GET['vin'], FILTER_SANITIZE_STRING));
} else {
    $decodevin = "";
}

// Get outstanding balance set for > 10000 for now

$highbal = 0;

$stmt = "SELECT sum(balance) as balance  ";
$stmt .= "FROM repairorders ";
$stmt .= "WHERE shopid = ? ";
$stmt .= "  AND customerid = ? ";
$stmt .= " AND rotype != 'NO APPROVAL'";
$stmt .= "  AND round(balance,2) > 0 ";
//echo $stmt;

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $cid);
    $query->execute();
    $roresult = $query->get_result();
} else {
    echo "Repair Order credit limit query failed (" . $conn->errno . ") " . $conn->error;
}

$ro = mysqli_fetch_assoc($roresult);

$highbal = $ro["balance"];

$currdate = date('Y-m-d');
$appts = array();
$stmt = "select schdate,schtime,year,make,model from schedule where shopid = ? and customerid = ? and schdate > ? AND deleted = 'no'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $cid, $currdate);
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array())
        $appts[] = $rs;
}

$cfpphone = $cfpaltphone = $cfplink = '';

if ($showcfp == 'yes' && !empty($cfpid)) {
    $phones = array($CellPhone, $HomePhone, $WorkPhone, $spousecell, $spousework);
    foreach ($phones as $phone) {
        if (!empty($phone)) {
            if (empty($cfpphone)) {
                $cfpphone = $phone;
            } elseif ($phone != $cfpphone && empty($cfpaltphone))
                $cfpaltphone = $phone;
        }
    }

    $data = array("merchantId" => $cfpid, "phone" => $cfpphone, "altphone" => $cfpaltphone, "zip" => $Zip, "firstName" => $FirstName, "lastName" => $LastName, "street" => $Address, "state" => $State, "city" => $City, "email" => $EMail, "middle" => '', 'amount' => '');

    if (in_array($shopid, array('13846', '6062'))) {
        $data["partnerKey"] = "SBPS9teaU";
        $url = "https://p2-6182--partial.sandbox.my.site.com/360payments/services/apexrest/remotelend?" . http_build_query($data); //sandbox
    } else {
        $data["partnerKey"] = "Sb24F110";
        $url = "https://360-partners.force.com/360payments/services/apexrest/remotelend?" . http_build_query($data);
    }

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_URL, $url);
    $result = curl_exec($ch);
    curl_close($ch);

    $cfpresult = json_decode($result);

    if (isset($cfpresult->applyUrl) && !empty($cfpresult->applyUrl)) {
        $href = $cfpresult->applyUrl;
        $cfplink = '<a href="' . $href . '" target="_blank">' . $cfpresult->result . ' Click to Apply</a>';
    }
}

?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet"/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui/dist/fancybox.css"/>
<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/dropzonejs/dropzone.min.css">
<style>
    .modal-lg-history {
        width: 80% !important;
        /* respsonsive width */
        /*margin-left:-40%;  width/2) */
    }

    .table-borderless tbody tr td,
    .table-borderless tbody tr th,
    .table-borderless thead tr th {
        border: none;

    }

    .modal-big {
        max-height: 75%;
        overflow-y: auto;
    }

    .sbp-form-control-sm {
        text-transform: uppercase;
        color: #999999;
        font-weight: bold;
        padding: 3px 20px 3px 20px !important;
        border-radius: 3px;
        border: 1px silver solid;
        width: 200px;
    }

    main {
        margin-top: 143px;
        min-height: 100vh;
    }

    .dropzone {
        background: var(--bodybackground);
        border: 2px dashed var(--textColor);
    }

    .loader {
        position: absolute;
        width: 90%;
        padding: 50px;
    }

    .rounded_corner {
        border-radius: 15px 0 0 0;
    }
</style>

<!-- Main Container -->
<main id="main-container">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/customer/customer.php"
                           class="text-secondary">Customers</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">
                            <?php if ($shopIsReadOnly) : ?>
                                View
                            <?php else : ?>
                                Edit
                            <?php endif; ?>
                            Customer and Vehicle Information
                        </h2>
                    </div>
                    <hr/>
                </div>
            </div>
        </div>
    </div>

    <form id="mainform" class="form-horizontal push-10-t" name="mainform">
        <div class="row mb-4">
            <?php if (!empty($cfplink))
                echo("<center>$cfplink</center><br>"); ?>
            <?php
            if ($creditlimit > 0 && $highbal > $creditlimit) {
                ?>
                <div style="text-align:center;padding:20px;margin-bottom:10px;font-weight:bold;font-size:x-large;text-transform:uppercase"
                     class="alert-danger">This customer is over their credit
                    limit!
                </div>
                <?php
            }
            ?>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" value="<?php echo $LastName; ?>" type="text" id="lastname" name="lastname" <?= $disabled; ?>>
                        <label class="form-label" for="lastname">Last Name / Company</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" value="<?php echo $FirstName; ?>" type="text" id="firstname" name="firstname" <?= $disabled; ?>>
                        <label class="form-label" for="firstname">First Name</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" value="<?php echo $Address; ?>" type="text" id="address" name="address" <?= $disabled; ?>>
                        <label class="form-label" for="address">Address</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" onblur="getCS(this.value)" value="<?php echo $Zip; ?>" type="text" id="zip" name="zip" <?= $disabled; ?>>
                        <label class="form-label" for="zip">Zip/Postal Code</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="city" value="<?php echo $City; ?>" name="city" <?= $disabled; ?>>
                        <label class="form-label" for="city" id="cityfloatinglabel">City</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="state" value="<?php echo $State; ?>" name="state" <?= $disabled; ?>>
                        <label class="form-label" for="state" id="statefloatinglabel">State</label>
                    </div>
                </div>

                <div class="col-md-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="email" id="email" value="<?php echo $EMail; ?>" name="email" <?= $disabled; ?>>
                        <label class="form-label" for="">Email</label>
                    </div>
                </div>

                <div class="col-md-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="contact" name="contact" value="<?php echo strtoupper($contact); ?>" <?= $disabled; ?>>
                        <label class="form-label" for="contact" for="">Business Contact</label>
                    </div>
                </div>
                <?php if ($isprofitboost == 'yes') { ?>
                    <div class="col-md-9">
                        <div class="form-row mb-4">
                            <select class="select" size="1" id="accounttype" name="accounttype" <?= $disabled; ?>>
                                <option value="">Select</option>
                                <?php
                                $stmt = "select type from pbaccounttypes where shopid = '" . $shopid . "' order by type";
                                $result = $conn->query($stmt);
                                while ($row = $result->fetch_array()) { ?>
                                    <option value="<?= $row['type'] ?>" <?= $accounttype == $row['type'] ? 'selected' : '' ?>><?= $row['type'] ?></option>
                                <?php } ?>
                            </select>
                            <label class="form-label select-label" for="accounttype"
                                   id="accounttypelabel">AccountType</label>
                        </div>
                    </div>
                <?php } ?>
                <div class="col-md-9">
                    <div id="notecreditlimit" class="mb-4 text-secondary">
                        <?php
                        $tcnt = 0;
                        $tro = 0;
                        $sdate = "";
                        // now we fetch customer history
                        $stmt = "SELECT count(*) c, sum(totalro) tro from repairorders where shopid = ? and customerid = ? and status = 'closed' and rotype!= 'no approval'";
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $CustomerID);
                            $query->execute();
                            $query->bind_result($tcnt, $tro);
                            $query->fetch();
                            $query->close();
                        }
                        //get last visit
                        $stmt = "select statusdate from repairorders where shopid = ? and customerid = ? and status = 'closed' and rotype != 'no approval' order by roid desc limit 1";
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $CustomerID);
                            $query->execute();
                            $query->bind_result($sdate);
                            $query->fetch();
                            $query->close();
                        }

                        if ($tcnt > 0) {
                            $tavg = round($tro / $tcnt, 2);
                            echo "Customer Sales Data:<br>";
                            echo "RO Count: " . $tcnt . " @ " . $tavg . " = " . round($tro, 2);
                            echo "<br>Last RO: " . date("m/d/Y", strtotime($sdate));
                        }

                        if (!empty($appts)) {
                            echo("<br><span style='color:#000'>Future Appointments:<br><ul style='padding-left:20px'>");
                            foreach ($appts as $appt) {
                                echo("<li>" . date('m/d/Y - g:iA', strtotime($appt['schdate'] . ' ' . $appt['schtime'])) . " - " . $appt['year'] . ' ' . $appt['make'] . ' ' . $appt['model'] . "</li>");
                            }
                            echo("</ul></span>");
                        }
                        ?>
                    </div>
                </div>

            </div>

            <div class="col-md-4">
                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="homephone" value="<?php echo $HomePhone; ?>"
                               name="homephone" onKeyUp="javascript:return mask(this.value,this,'3,7','-');"
                               onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled; ?>>
                        <label class="form-label" for="homephone">Home Phone</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="workphone" value="<?php echo $WorkPhone; ?>"
                               name="workphone" onKeyUp="javascript:return mask(this.value,this,'3,7','-');"
                               onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled; ?>>
                        <label class="form-label" for="workphone">Work Phone</label>
                    </div>
                </div>


                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="extension" value="<?php echo $extension; ?>" name="extension" <?= $disabled; ?>>
                        <label class="form-label" for="extension">Ext.</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="input-group mb-4">
                        <div class="form-outline">
                            <input class="form-control" type="text" id="cellphone" value="<?php echo $CellPhone; ?>"
                                   name="cellphone" onKeyUp="javascript:return mask(this.value,this,'3,7','-');"
                                   onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled; ?>>
                            <label class="form-label" for="cellphone">Cell Phone</label>
                        </div>

                        <?php if (strlen($CellPhone) > 0 && $smsnum != "") {
                            echo "<span class='input-group-text text-primary'><i class='fas fa-message' data-mdb-toggle='tooltip' onclick='sendSMS($CellPhone)' aria-label='Send Text Message' data-mdb-original-title='Send Text Message'></i></span>";
                        } ?>
                    </div>

                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="fax" value="<?php echo $Fax; ?>" name="fax"
                               onKeyUp="javascript:return mask(this.value,this,'3,7','-');"
                               onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled; ?>>
                        <label class="form-label" for="fax">Fax</label>
                    </div>
                </div>


                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="spousename" value="<?php echo $spousename; ?>" name="spousename" <?= $disabled; ?>>
                        <label class="form-label" for="spousename">Other Contact Name</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="spousework" value="<?php echo $spousework; ?>"
                               name="spousework" onKeyUp="javascript:return mask(this.value,this,'3,7','-');"
                               onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled; ?>>
                        <label class="form-label" for="spousework">Other Contact Work Phone</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="spousecell" value="<?php echo $spousecell; ?>"
                               name="spousecell" onKeyUp="javascript:return mask(this.value,this,'3,7','-');"
                               onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled; ?>>
                        <label class="form-label" for="spousecell">Other Contact Cell Phone</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <textarea class="form-control" type="text" id="notes" name="notes" <?= $disabled; ?>>
                            <?php echo $notes; ?>
                        </textarea>
                        <label class="form-label" for="comments">Alerts</label>
                    </div>
                </div>

            </div>

            <div class="col-md-4">
                <div class="col-sm-9">
                    <div class="form-row mb-4"><?php //echo $customertype;
                        ?>
                        <select class="select" id="customertype" name="customertype" size="1" <?= $disabled; ?>
                                onchange="saveField('customertype',this.value)">
                            <option <?php if (trim(strtolower($customertype)) == 'cash') {
                                echo 'selected';
                            } ?> value="cash">CASH
                            </option>
                            <option <?php if (strtolower($customertype) == 'net 10') {
                                echo 'selected';
                            } ?> value="net 10">NET 10
                            </option>
                            <option <?php if (strtolower($customertype) == 'net 15') {
                                echo 'selected';
                            } ?> value="net 15">NET 15
                            </option>
                            <option <?php if (strtolower($customertype) == 'net 30') {
                                echo 'selected';
                            } ?> value="net 30">NET 30
                            </option>
                            <option <?php if (strtolower($customertype) == 'net 60') {
                                echo 'selected';
                            } ?> value="net 60">NET 60
                            </option>
                            <option <?php if (strtolower($customertype) == 'net 90') {
                                echo 'selected';
                            } ?> value="net 90">NET 90
                            </option>
                        </select>
                        <label class="form-label select-label" for="customertype">Customer Pay Type</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-row mb-4"><?php //echo $customertype;
                        ?>
                        <select class="select" id="preferredcontactmethod" name="preferredcontactmethod" size="1" <?= $disabled; ?>
                                onchange="saveField('preferredcontactmethod',this.value)">

                            <option <?php if (strtolower($preferredcontactmethod) == "text") {
                                echo "selected";
                            } ?> value="text">Text
                            </option>
                            <option <?php if (strtolower($preferredcontactmethod) == "phone") {
                                echo "selected";
                            } ?> value="phone">Phone
                            </option>
                            <option <?php if (strtolower($preferredcontactmethod) == "email") {
                                echo "selected";
                            } ?> value="email">Email
                            </option>
                        </select>
                        <label class="form-label select-label" for="customertype">Preferred Contact Method</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input me-2" id="follow" <?= $disabled; ?>
                               onchange="save(this.id)" <?php if (strtolower($Follow) == 'yes') {
                            echo 'checked';
                        } ?>>
                        <label for="follow" class="form-check-label">Follow Up</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input me-2" id="taxexempt" <?= $disabled; ?>
                               onchange="save(this.id)" <?php if (strtolower($taxexempt) == 'yes') {
                            echo 'checked';
                        } ?>>
                        <label for="taxexempt" class="form-check-label">Tax Exempt</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-check mb-4">
                        <input type="checkbox" class="form-check-input me-2" id="active" <?= $disabled; ?>
                               onchange="<?= (strtolower($active) == 'yes' && $deletecustomer != 'YES') ? '' : 'save(this.id)' ?>" <?= (strtolower($active) == 'yes' && $deletecustomer != 'YES') ? 'disabled="disabled"' : '' ?> <?php if (strtolower($active) == 'yes') {
                            echo 'checked';
                        } ?>>
                        <label for="active" class="form-check-label">Active</label>
                    </div>
                </div>

                <div class="col-sm-9" style="<?php if (strlen($cu1) == 0) {
                    echo 'display:none;';
                } ?>">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="userdefined1" value="<?php echo $UserDefined1; ?>" name="customuserfield1" <?= $disabled; ?>>
                        <label class="form-label" for="userdefined1"><?php echo strtoupper($cu1); ?></label>
                    </div>
                </div>

                <div class="col-sm-9" style="<?php if (strlen($cu2) == 0) {
                    echo 'display:none;';
                } ?>">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="userdefined2" value="<?php echo $UserDefined2; ?>" name="customuserfield2" <?= $disabled; ?>>
                        <label class="form-label" for="userdefined2"><?php echo strtoupper($cu2); ?></label>
                    </div>
                </div>

                <div class="col-sm-9" style="<?php if (strlen($cu3) == 0) {
                    echo 'display:none;';
                } ?>">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="userdefined3" value="<?php echo $UserDefined3; ?>" name="customuserfield" <?= $disabled; ?>>
                        <label class="form-label" for="userdefined3"><?php echo strtoupper($cu3); ?></label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <input class="form-control" type="text" id="discountpercent" value="<?php echo $discountpercent; ?>" name="discountpercent" <?= $disabled; ?>>
                        <label class="form-label" for="discountpercent">Discount Percent (Number ONLY (15 for
                            15%)</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-row mb-4">
                        <select class="select" id="Discount" onchange="saveField('discount',this.value)" <?= $disabled; ?>>
                            <?php
                            $pd = "";
                            $ld = "";
                            $bd = "";
                            if ($Discount == "") {
                                echo '<option value="none">Select</option>';
                            } elseif (strtolower($Discount) == "parts") {
                                $pd = "selected";
                            } elseif (strtolower($Discount) == "labor") {
                                $ld = "selected";
                            } elseif (strtolower($Discount) == "partsandlabor") {
                                $bd = "selected";
                            }
                            ?>
                            <option <?php echo $pd; ?> value="parts">Parts</option>
                            <option <?php echo $ld; ?> value="labor">Labor</option>
                            <option <?php echo $bd; ?> value="partsandlabor">Parts and Labor</option>
                        </select>
                        <label class="form-label select-label" for="discountpercent">Apply Discount To:</label>
                    </div>
                </div>

                <div class="col-sm-9">
                    <div class="form-outline mb-4">
                        <textarea class="form-control" type="text" id="comments" name="comments" <?= $disabled; ?>>
                            <?php echo $Comments; ?>
                        </textarea>
                        <label class="form-label" for="comments">Comments</label>
                    </div>
                </div>

            </div>
        </div>


        <div class="row mt-4" id="vehiclelist">
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-12" style="left: 0px; top: 0px">
                        <?php
                        if ($creditlimit > 0 && $highbal > $creditlimit) {
                            ?>
                            <button id="bscr" type="button" onclick="$('#shipmodal').modal('show')"
                                    class="btn btn-secondary me-2">Billing / Shipping / Credit Limit
                            </button>

                            <?php
                        } else {
                            ?>
                            <button type="button" onclick="$('#shipmodal').modal('show')"
                                    class="btn btn-secondary me-2">
                                Billing / Shipping / Credit Limit
                            </button>
                            <?php
                        } // end if of credit limit chech
                        ?>
                        
                        <?php if ( !$shopIsReadOnly ) : ?>
                            <button type="button" onclick="$('#addvehmodal').modal('show')" class="btn btn-secondary me-2">
                                Add Vehicle
                            </button>
                            
                            <?php if (strtolower($createCT) == "yes") : ?>
                                <button type="button" onclick="location.href='<?= COMPONENTS_PRIVATE ?>/v2/partsale/createps.php?id=<?php echo $cid; ?>'" class="btn btn-secondary me-2">
                                    Create Part Sale
                                </button>
                            <?php endif; ?>

                            <?php if ($deletecustomer == 'YES') : ?>
                                <button type="button" onclick="delCustomer()" class="btn btn-secondary me-2">Mark Customer
                                    Inactive
                                </button>
                            <?php endif; ?>

                            <button type="button" onclick="updateAllROs()" class="btn btn-secondary me-2">
                                Apply Current Info to All Repair Orders
                            </button>
                        <?php endif; ?>

                        <button type="button" onclick="showCustomerHistory()" class="btn btn-secondary me-2">Customer
                            History
                        </button>

                        <?php if ($mergecustomers == 'YES' && !$shopIsReadOnly):  ?>
                            <button type="button" onclick="openMerge()" class="btn btn-secondary me-2">
                                Merge Customers
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <h3 class="mt-4">Vehicles</h3>

                <table id="vehtable" class="sbdatatable w-100">
                    <thead>
                    <tr>
                        <th style="height: 34px">Image</th>
                        <th style="height: 34px">Year/Make/Model</th>
                        <th style="height: 34px">Transmission</th>
                        <th style="height: 34px">VIN</th>
                        <th style="height: 34px">Fleet #</th>
                        <th style="height: 34px">License</th>
                        <?php if ( !$shopIsReadOnly ) : ?>
                            <th style="height: 34px;text-align: center">Delete</th>
                        <?php endif; ?>
                    </tr>
                    </thead>
                    <tbody id="table-body">
                    <?php
                    if (isset($_GET['cid'])) {
                        $cid = $_GET['cid'];
                        $stmt = "select year,make,model,vin,fleetno,licnumber,vehid,transmission,photo from vehicles where shopid = ? and customerid = ?";

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $cid);
                            $query->execute();
                            $result = $query->get_result();
                            while ($row = $result->fetch_array()) {
                                if (!empty($row['photo'])) {
                                    $vehicle_thumb = "https://customers-ss.s3.us-east-1.amazonaws.com/" . preg_replace('/(\.[^.\s]{2,5})$/', '_thumb$1', $row['photo']);
                                    $vehicle_photo = "https://customers-ss.s3.us-east-1.amazonaws.com/" . $row['photo'];
                                }
                                ?>
                                <tr>
                                    <td id="<?= $row['vehid'] ?>_imgcell">
                                        <?php if (!empty($row['photo'])) {
                                            $vehicle_thumb = "https://customers-ss.s3.us-east-1.amazonaws.com/" . preg_replace('/(\.[^.\s]{2,5})$/', '_thumb$1', $row['photo']);
                                            $vehicle_photo = "https://customers-ss.s3.us-east-1.amazonaws.com/" . $row['photo'];
                                            ?>
                                            <a id="<?= $row['vehid'] ?>_img" class="vimg_link" data-fancybox="gallery"
                                               href="<?= $vehicle_photo ?>">
                                                <img src="<?= $vehicle_thumb ?>" class="img-thumbnail vimg"
                                                     alt="vehicle photo"
                                                />
                                            </a>
                                        <?php } else {
                                            ?>
                                            <img src="<?= IMAGE ?>/placeholderCar.png" class="img-thumbnail vimg"
                                                 alt="Upload vehicle photo"
                                            />
                                            <?php
                                        } ?>
                                    </td>
                                    <td onclick="editVehicle(<?php echo $row['vehid']; ?>)">
                                        <?php echo strtoupper($row['year'] . ' ' . $row['make'] . ' ' . $row['model']); ?>
                                    </td>
                                    <td onclick="editVehicle(<?php echo $row['vehid']; ?>)">
                                        <?php echo strtoupper($row['transmission']); ?>
                                    </td>
                                    <td onclick="editVehicle(<?php echo $row['vehid']; ?>)">
                                        <?php echo strtoupper($row['vin']); ?>
                                    </td>
                                    <td onclick="editVehicle(<?php echo $row['vehid']; ?>)">
                                        <?php echo strtoupper($row['fleetno']); ?>
                                    </td>
                                    <td onclick="editVehicle(<?php echo $row['vehid']; ?>)">
                                        <?php echo strtoupper($row['licnumber']); ?>
                                    </td>
                                    <?php if ( !$shopIsReadOnly ) : ?>
                                        <td class="text-primary text-center">
                                            <i class="fas fa-trash" onclick="$('#vehid').val('<?= $row['vehid'] ?>');delVehicle()"></i>
                                        </td>
                                    <?php endif; ?>
                                </tr>
                                <?php
                            }
                        }
                    }
                    ?>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
</main>
<!-- END Main Container -->

<!-- Apps Modal -->
<div id="vehmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden" value="<?php echo $cid; ?>">
    <input id="vehid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">
                    <?php if ($shopIsReadOnly) : ?>
                        View
                    <?php else : ?>
                        Edit
                    <?php endif; ?>
                    Vehicle Information
                </h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div id="vehiclemaininfo" class="col-md-6 mt-2">
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="year" name="year" <?= $disabled; ?>>
                                <label class="form-label" for="year" id="yearlabel"><?php echo strtoupper($yearlabel); ?></label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="make" name="make" <?= $disabled; ?>>
                                <label class="form-label" for="make" id="makelabel"><?php echo strtoupper($makelabel); ?></label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="model" name="model" <?= $disabled; ?>>
                                <label class="form-label" for="model" id="modellabel"><?php echo strtoupper($modellabel); ?></label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="vin" name="vin" <?= $disabled; ?>>
                                <label class="form-label" for="vin" id="vinlabel"><?php echo strtoupper($vinlabel); ?></label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="licnumber" name="licnumber" <?= $disabled; ?>>
                                <label class="form-label" for="licnumber" id="licenselabel"><?php echo strtoupper($licenselabel); ?></label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="transmission" name="transmission" <?= $disabled; ?>>
                                <label class="form-label" for="transmission" id="transmissionlabel"><?php echo strtoupper($translabel); ?></label>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="engine" name="engine" <?= $disabled; ?>>
                                <label class="form-label" for="engine" id="enginelabel"><?php echo strtoupper($enginelabel); ?></label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="cyl" name="cyl" <?= $disabled; ?>>
                                <label class="form-label" for="cyl" id="cyllabel"><?php echo strtoupper($cylinderlabel); ?></label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="drivetype" name="drivetype" <?= $disabled; ?>>
                                <label class="form-label" for="drivetype" id="drivetypelabel"><?php echo strtoupper($drivelabel); ?></label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="color" name="color" <?= $disabled; ?>>
                                <label class="form-label" for="color" id="colorlabel"><?php echo strtoupper($colorlabel); ?></label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="fleetno" name="fleetno" <?= $disabled; ?>>
                                <label class="form-label" for="fleetno" id="fleetnolabel"><?php echo strtoupper($fleetlabel); ?></label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="licstate" name="licstate" <?= $disabled; ?>>
                                <label class="form-label" for="licstate" id="statelabel"><?php echo strtoupper($statelabel); ?></label>
                            </div>
                        </div>

                    </div>

                    <div class="col-md-6 mt-2">
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="currentmileage" name="currentmileage" <?= $disabled; ?>>
                                <label class="form-label" for="currentmileage" id="currentmileagelabel"><?php echo strtoupper($currmileagelabel); ?></label>
                            </div>
                        </div>
                        <div id="custom1group" class="col-sm-9" style="<?php if (strlen($cv1) == 0) {
                            echo 'display:none;';
                        } ?>">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="custom1" name="custom1" <?= $disabled; ?>>
                                <label class="form-label" for="custom1" id="custom1label"><?php echo strtoupper($cv1); ?></label>
                            </div>
                        </div>
                        <div id="custom2group" class="col-sm-9" style="<?php if (strlen($cv2) == 0) {
                            echo 'display:none;';
                        } ?>">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="custom2" name="custom2" <?= $disabled; ?>>
                                <label class="form-label" for="custom2" id="custom2label"><?php echo strtoupper($cv2); ?></label>
                            </div>
                        </div>
                        <div id="custom3group" class="col-sm-9" style="<?php if (strlen($cv3) == 0) {
                            echo 'display:none;';
                        } ?>">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="custom3" name="custom3" <?= $disabled; ?>>
                                <label class="form-label" for="custom3" id="custom3label"><?php echo strtoupper($cv3); ?></label>
                            </div>
                        </div>
                        <div id="custom4group" class="col-sm-9" style="<?php if (strlen($cv4) == 0) {
                            echo 'display:none;';
                        } ?>">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="custom4" name="custom4" <?= $disabled; ?>>
                                <label class="form-label" for="custom4" id="custom4label"><?php echo strtoupper($cv4); ?></label>
                            </div>
                        </div>
                        <div id="custom5group" class="col-sm-9" style="<?php if (strlen($cv5) == 0) {
                            echo 'display:none;';
                        } ?>">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="custom5" name="custom5" <?= $disabled; ?>>
                                <label class="form-label" for="custom5" id="custom5label"><?php echo strtoupper($cv5); ?></label>
                            </div>
                        </div>
                        <div id="custom6group" class="col-sm-9" style="<?php if (strlen($cv6) == 0) {
                            echo 'display:none;';
                        } ?>">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="custom6" name="custom6" <?= $disabled; ?>>
                                <label class="form-label" for="custom6" id="custom6label"><?php echo strtoupper($cv6); ?></label>
                            </div>
                        </div>
                        <div id="custom7group" class="col-sm-9" style="<?php if (strlen($cv7) == 0) {
                            echo 'display:none;';
                        } ?>">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="custom7" name="custom7" <?= $disabled; ?>>
                                <label class="form-label" for="custom7" id="custom7label"><?php echo strtoupper($cv7); ?></label>
                            </div>
                        </div>
                        <div id="custom8group" class="col-sm-9" style="<?php if (strlen($cv8) == 0) {
                            echo 'display:none;';
                        } ?>">
                            <div class="form-outline mb-4">
                                <input class="form-control" type="text" id="custom8" name="custom8" <?= $disabled; ?>>
                                <label class="form-label" for="custom8" id="custom8label"><?php echo strtoupper($cv8); ?></label>
                            </div>
                        </div>

                        <div id="notesdiv" class="form-group">
                            <div class="col-sm-9" style="max-height:200px;overflow-y:scroll">
                                <div class="mb-4" id="vehiclenotes"></div>
                            </div>
                        </div>

                        <?php if (!$shopIsReadOnly) : ?>
                            <div class="col-sm-9">
                                <div class="mb-4">
                                    <span class="btn btn-secondary" onclick="addNotes()">Add/Edit Notes</span>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (!$shopIsReadOnly) : ?>
                            <div class="col-sm-9" style="">
                                <div class="clearfix"></div>
                                <div id="image-preview-container">
                                    <div class="delete_photo">
                                        <i class="fa-solid fa-trash" onclick="delete_image(this)" style="visibility: hidden"></i>
                                    </div>
                                    <img id="image-preview" class="img-fluid" src="">
                                </div>
                                <div style="">
                                    <div id="file-upload-wrapper-edit">
                                        <form action="update_vehicle_image.php" class="dropzone" id="vehicle-edit-upload">
                                            <div class="dz-default dz-message"><span>Click the box or Drag and Drop your files here to upload</span>
                                            </div>
                                            <input type="hidden" name="cid" value="<?= $cid ?>">
                                            <input type="hidden" name="vid" value="" id="imguploadVid">
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php else : ?>
                                <div class="col-sm-9" style="">
                                    <div id="image-preview-container">
                                        <img id="image-preview" class="img-fluid" src="">
                                    </div>
                                </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="col-sm-9 text-primary">
                    <div id="vehtotals"></div>
                </div>
            </div>

            <div class="modal-footer d-flex justify-content-center pt-2">
                <button class="btn btn-md btn-secondary" onclick="recommendRepairs()" type="button">
                    Recommended Repairs
                </button>

                <?php if (!$shopIsReadOnly) : ?>
                    <button class="btn btn-md btn-secondary" onclick="transferVehicle()" type="button">
                        Transfer to New Owner
                    </button>

                    <button class="btn btn-md btn-secondary" onclick="createappointment()" type="button">
                        Create Appointment
                    </button>
                <?php endif; ?>

                <button class="btn btn-md btn-secondary" onclick="showHistory()" type="button">History</button>

                <?php if (!$shopIsReadOnly && $_COOKIE['createro'] == 'yes') : ?>
                    <button class="btn btn-md btn-secondary" type="button" onclick="createNewRO('<?php echo $onhold; ?>')">
                        Create RO
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div id="shipmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">
                    <?php if ($shopIsReadOnly) : ?>
                        View
                    <?php else : ?>
                        Edit
                    <?php endif; ?>
                    Billing / Shipping / Credit Limit
                </h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div id="vehiclemaininfo" class="col-md-6">
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $shippingto; ?>" type="text" id="shippingto" name="shippingto" <?= $disabled ?>>
                                <label class="form-label" for="shippingto" id="shippingtolabel">Shipping To:</label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $shippingaddress; ?>" type="text" id="shippingaddress" name="shippingaddress" <?= $disabled ?>>
                                <label class="form-label" for="shippingaddress" id="shippingaddresslabel">Shipping Address:</label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $shippingcity; ?>" type="text" id="shippingcity" name="shippingcity" <?= $disabled ?>>
                                <label class="form-label" for="shippingcity" id="shippingcitylabel">Shipping City</label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $shippingstate; ?>" type="text" id="shippingstate" name="shippingstate" <?= $disabled ?>>
                                <label class="form-label" for="shippingstate" id="shippingstatelabel">Shipping State</label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $shippingzip; ?>" type="text" id="shippingzip" name="shippingzip" <?= $disabled ?>>
                                <label class="form-label" for="shippingzip" id="shippingziplabel">Shipping Zip</label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $shippingphone; ?>" type="text" id="shippingphone" name="shippingphone" <?= $disabled ?>>
                                <label class="form-label" for="shippingphone" id="shippingphonelabel">Shipping Phone</label>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $creditlimit; ?>" type="text" id="creditlimit" name="creditlimit" <?= $disabled ?>>
                                <label class="form-label" for="creditlimit" id="creditlimitlabel">Credit Limit</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $billto; ?>" type="text" id="billto" name="billto" <?= $disabled ?>>
                                <label class="form-label" for="billto" id="billtolabel">Bill To:</label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $billtoaddress; ?>" type="text" id="billtoaddress" name="billtoaddress" <?= $disabled ?>>
                                <label class="form-label" for="billtoaddress" id="billtoaddresslabel">Bill To Address</label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $billtocity; ?>" type="text" id="billtocity" name="billtocity" <?= $disabled ?>>
                                <label class="form-label" for="billtocity" id="billtocitylabel">Bill To City</label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $billtostate; ?>" type="text" id="billtostate" name="billtostate" <?= $disabled ?>>
                                <label class="form-label" for="billtostate" id="billtostatelabel">Bill To State</label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $billtozip; ?>" type="text" id="billtozip" name="billtozip" <?= $disabled ?>>
                                <label class="form-label" for="billtozip" id="billtoziplabel">Bill To Zip</label>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-outline mb-4">
                                <input class="form-control" value="<?php echo $billtophone; ?>" type="text" id="billtophone" name="billtophone" <?= $disabled ?>>
                                <label class="form-label" for="billtophone" id="billtophonelabel">Bill To Phone</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="historymodal" class="modal fade modal-big" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-lg-history">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">Vehicle History</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12"></div>
                    <div id="historycontent" class="modal-body"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="notesmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">Add/Edit Vehicle Notes</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="col-sm-12">
                            <div class="form-outline mb-4">
                                <textarea class="form-control" style="padding:20px;height:250px;" id="vehnotes"
                                          name="vehnotes"></textarea>
                                <label class="form-label" for="vehnotes">Notes</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer d-flex justify-content-center pt-2">
                <button class="btn btn-md btn-primary" type="button" onclick="saveNotes()">Save Notes</button>
            </div>
        </div>
    </div>
</div>

<form id="vehaddform" enctype="multipart/form-data">
    <input id="customerid" type="hidden">
    <div id="addvehmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title">Add Vehicle</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div id="vehiclemaininfo" class="col-md-6 mt-2">
                            <div class="col-sm-9 mb-4">
                                <div class="input-group mb-4">
                                    <div class="form-outline">
                                        <input class="form-control rounded" onblur="" type="text"
                                               value="<?php echo $decodevin; ?>" id="addvin" name="addvin">
                                        <label class="form-label" for="addvin"
                                               id="addvinlabel"><?php echo strtoupper($vinlabel); ?></label>
                                    </div>
                                    <a href="javascript:void(null)" tabindex="-1" data-mdb-toggle="tooltip"
                                       title="Decode VIN" class="btn btn-secondary" onclick="decodeVIN()"><i
                                                class="fas fa-key"></i></a>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addyear" name="addyear"
                                           value="<?php echo $yr; ?>">
                                    <label class="form-label" for="addyear"
                                           id="addyearlabel"><?php echo strtoupper($yearlabel); ?></label>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-outline mb-4">
                                    <input class="form-control" list="makelist" onblur="getModelList()" type="text"
                                           autocomplete="off" id="addmake" name="addmake" value="<?php echo $mk; ?>">
                                    <datalist id="makelist">
                                        <?php
                                        $data = array();
                                        $stmt = "select make from makes";
                                        if ($query = $conn->prepare($stmt)) {
                                            $query->execute();
                                            $r = $query->get_result();
                                            $c = 0;
                                            while ($rs = $r->fetch_array()) {
                                                echo "<option value='" . $rs['make'] . "'>";
                                            }
                                        }
                                        ?>
                                    </datalist>
                                    <label class="form-label" for="addmake"
                                           id="addmakelabel"><?php echo strtoupper($makelabel); ?></label>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-outline mb-4">
                                    <input class="form-control" list="modellist" type="text" id="addmodel"
                                           name="addmodel" value="<?php echo $md; ?>">
                                    <datalist id="modellist"></datalist>
                                    <label class="form-label" for="addmodel"
                                           id="addmodellabel"><?php echo strtoupper($modellabel); ?></label>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addengine" name="addengine">
                                    <label class="form-label" for="addengine"
                                           id="addenginelabel"><?php echo strtoupper($enginelabel); ?></label>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addcyl" name="addcyl">
                                    <label class="form-label" for="addcyl"
                                           id="addcyllabel"><?php echo strtoupper($cylinderlabel); ?></label>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="adddrivetype" name="adddrivetype">
                                    <label class="form-label" for="adddrivetype"
                                           id="adddrivetypelabel"><?php echo strtoupper($drivelabel); ?></label>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addcolor" name="addcolor"
                                           value="<?php echo $cr; ?>">
                                    <label class="form-label" for="addcolor"
                                           id="addcolorlabel"><?php echo strtoupper($colorlabel); ?></label>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addfleetno" name="addfleetno">
                                    <label class="form-label" for="addfleetno"
                                           id="fleetnolabel"><?php echo strtoupper($fleetlabel); ?></label>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addlicnumber" name="addlicnumber">
                                    <label class="form-label" for="addlicnumber"
                                           id="addlicnumberlabel"><?php echo strtoupper($licenselabel); ?></label>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-row mb-4" id="modelDialog1">
                                    <div class="form-control border-primary text-primary"
                                         style="border-color: black !important;">
                                        <label id="select2floatinglabel" class="form-label select-label active"
                                               for="addtransmission"><?php echo strtoupper($translabel); ?></label>
                                        <select onkeyup="" class="" tabindex="12" name="addtransmission"
                                                id="addtransmission">
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addvehstate" name="addvehstate"
                                           value="<?= $companystate ?>">
                                    <label class="form-label" for="addvehstate"
                                           id="addstatelabel"><?php echo strtoupper($statelabel); ?></label>
                                </div>
                            </div>

                        </div>

                        <div class="col-md-6 mt-2">
                            <div class="col-sm-9">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addcurrmiles" name="addcurrmiles">
                                    <label class="form-label" for="addcurrmiles"
                                           id="addcurrmileslabel"><?php echo strtoupper($currmileagelabel); ?></label>
                                </div>
                            </div>
                            <div id="custom1group" class="col-sm-9" style="<?php if (strlen($cv1) == 0) {
                                echo 'display:none;';
                            } ?>">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addcustom1" name="addcustom1">
                                    <label class="form-label" for="addcustom1"
                                           id="custom1label"><?php echo strtoupper($cv1); ?></label>
                                </div>
                            </div>
                            <div id="custom2group" class="col-sm-9" style="<?php if (strlen($cv2) == 0) {
                                echo 'display:none;';
                            } ?>">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addcustom2" name="addcustom2">
                                    <label class="form-label" for="addcustom2"
                                           id="custom2label"><?php echo strtoupper($cv2); ?></label>
                                </div>
                            </div>
                            <div id="custom3group" class="col-sm-9" style="<?php if (strlen($cv3) == 0) {
                                echo 'display:none;';
                            } ?>">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addcustom3" name="addcustom3">
                                    <label class="form-label" for="addcustom3"
                                           id="custom31label"><?php echo strtoupper($cv3); ?></label>
                                </div>
                            </div>
                            <div id="custom4group" class="col-sm-9" style="<?php if (strlen($cv4) == 0) {
                                echo 'display:none;';
                            } ?>">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addcustom4" name="addcustom4">
                                    <label class="form-label" for="addcustom4"
                                           id="custom4label"><?php echo strtoupper($cv4); ?></label>
                                </div>
                            </div>
                            <div id="custom5group" class="col-sm-9" style="<?php if (strlen($cv5) == 0) {
                                echo 'display:none;';
                            } ?>">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addcustom5" name="addcustom5">
                                    <label class="form-label" for="addcustom5"
                                           id="custom5label"><?php echo strtoupper($cv5); ?></label>
                                </div>
                            </div>
                            <div id="custom6group" class="col-sm-9" style="<?php if (strlen($cv6) == 0) {
                                echo 'display:none;';
                            } ?>">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addcustom6" name="addcustom6">
                                    <label class="form-label" for="addcustom6"
                                           id="custom6label"><?php echo strtoupper($cv6); ?></label>
                                </div>
                            </div>
                            <div id="custom7group" class="col-sm-9" style="<?php if (strlen($cv7) == 0) {
                                echo 'display:none;';
                            } ?>">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addcustom7" name="addcustom7">
                                    <label class="form-label" for="addcustom7"
                                           id="custom7label"><?php echo strtoupper($cv7); ?></label>
                                </div>
                            </div>
                            <div id="custom8group" class="col-sm-9" style="<?php if (strlen($cv8) == 0) {
                                echo 'display:none;';
                            } ?>">
                                <div class="form-outline mb-4">
                                    <input class="form-control" type="text" id="addcustom8" name="addcustom8">
                                    <label class="form-label" for="addcustom8"
                                           id="custom8label"><?php echo strtoupper($cv8); ?></label>
                                </div>
                            </div>
                            <div class="col-sm-9" style="">
                                <div id="file-upload-wrapper">
                                    <input
                                            id="file-upload"
                                            type="file"
                                            name="vehicle_photo"
                                            class="file-upload-input"
                                            data-mdb-main-error="Ooops, error here"
                                            data-mdb-format-error="Bad file format (correct format ~~~)"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer d-flex justify-content-center pt-2">
                    <button class="btn btn-md btn-secondary" onclick="launchNewScanner()" type="button">BOSS Vin
                    </button>
                    <?php
                    if ($cfl != 'no') {
                        ?>
                        <button class="btn btn-md btn-secondary" onclick="openCarFax()" type="button">Open Carfax
                        </button>
                        <?php
                    }
                    ?>
                    <!-- if read only dont allow saving of vehicle -->
                    <?php
                    if ($readonly == "no") {
                        ?>
                        <button class="btn btn-md btn-secondary" onclick="saveVehicle('')" type="button">Save Vehicle
                        </button>
                        <?php
                    }
                    ?>

                    <button class="btn btn-md btn-secondary" onclick="saveAndCreateRO()" type="button">Save and Create
                        RO
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<div id="smsmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">Send Text Message</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" value="" id="smscell" name="smscell">
                            <label class="form-label" id="smscelllabel" for="material-text2">Cell Number</label>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <div class="form-outline mb-4">
                            <textarea class="form-control" id="smsmsg" name="smsmsg"></textarea>
                            <label class="form-label" class="form-label" for="material-text2">Message</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer d-flex justify-content-center pt-2">
                <button class="btn btn-md btn-primary" type="button" onclick="sendSMSMessage()">Send Message</button>
            </div>
        </div>
    </div>
</div>

<div id="emailRRModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
     style="z-index: 99999 !important;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">Email Recommended Repairs To Customer</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-outline mb-4">
                            <input class="form-control" id="RRemailto" name="RRemailto" value="<?php echo $EMail; ?>"
                                   type="text">
                            <label class="form-label" for="RRemailto">Email Address</label>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text"
                                   value="Your Recommended Repairs List from <?php echo $_COOKIE['shopname']; ?>"
                                   id="RRemailsubject" name="RRemailsubject" placeholder="Subject">
                            <label class="form-label" for="RRemailsubject">Subject</label>
                        </div>
                    </div>
                    <br><br>
                    <div class="col-md-12">
                        <div class="form-outline mb-4">
                            <textarea class="form-control" type="text" style="padding:20px;height:100px;"
                                      id="RRemailmessage" name="RRemailmessage" placeholder="Message">Your Recommended Repairs List from  <?php echo str_replace("#", "", str_replace("&", " and ", $_COOKIE['shopname'])) ?> </textarea>
                            <label class="form-label" for="RRemailmessage">Message</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer d-flex justify-content-center pt-2">
                <button class="btn btn-md btn-primary" type="button" onclick="sendRREmail()">Send Email</button>
            </div>
        </div>
    </div>
</div>

<div id="scanmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">Scan VIN</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div id="vehiclemaininfo" style="text-align:center" class="col-md-12">
                        <p>If this is your first time scanning a VIN, please install the app on your phone or tablet by
                            clicking the appropriate button below. If you have already installed
                            the app, click the Scan VIN button. After scanning, you can retrieve your scanned VIN by
                            clicking Retrieve Scanned VIN button</p>
                        <p id="scannedvins"></p>
                        <button style="width:250px;" class="btn btn-primary btn-lg" type="button"
                                onclick="launchScanner()">SCAN VIN
                        </button>
                        <button style="width:250px;" class="btn btn-success btn-lg" type="button"
                                onclick="getScannedVins()">
                            Retrieve Scanned VIN
                        </button>
                        <br><br>
                    </div>
                </div>

            </div>
        </div>

        <div class="modal-footer d-flex justify-content-center pt-2">
            <button class="btn btn-primary btn-sm" type="button" onclick="installAndroid()">Install Android App</button>
            <button class="btn btn-secondary btn-sm" type="button" onclick="installApple()">Install Apple App</button>
        </div>
    </div>

</div>

<div id="newvinscanmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">New VIN Scan</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div style="text-align:center" class="col-md-12">
                        <div id="pictureslot" style="height: 200px;display: none;margin-bottom: 20px;"></div>
                        <div class="btn-group" style="text-align: center;">
                            <button type="button" onclick="$('#ocrimage').click()" class="btn btn-secondary">
                                Upload Picture
                            </button>
                        </div>
                        <form name="ocrform" enctype="multipart/form-data" style="display: none;">
                            <input type="file" class="form-control" name="ocrimage" id="ocrimage"
                                   accept="image/png,image/jpeg,image/png,image/gif,image/bmp,android/force-camera-workaround">
                        </form>
                        <br><br>
                    </div>
                </div>

            </div>

            <div class="modal-footer d-flex justify-content-center pt-2">
                <button class="btn btn-primary" id="btn-ocrscan" type="button">Scan</button>
            </div>
        </div>
    </div>
</div>

<div id="carfaxmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title">Carfax Vehicle Lookup</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-outline mb-4">
                            <input class="form-control" onblur="$('#cflic').focus()" type="text" value="" id="cfvin"
                                   name="cfvin">
                            <label class="form-label" for="material-text2">VIN</label>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <div class="form-outline mb-4">
                            <input class="form-control" onblur="$('#cfst').focus()" type="text" id="cflic" name="cflic">
                            <label class="form-label" for="material-text2">License</label>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" id="cfst" name="cfst">
                            <label class="form-label" for="material-text2">License State</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer d-flex justify-content-center pt-2">
                <button class="btn btn-md btn-primary" type="button" onclick="quickVIN()">Lookup</button>
            </div>
        </div>
    </div>
</div>

<?php
include_once getModalsGlobal($component);
include getScriptsGlobal($component);
//include getScriptsComponent($component);
//include getFooterComponent($component);
?>
<script src="<?= SCRIPT ?>/plugins/masked-inputs/jquery.maskedinput.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/typeahead/typeahead.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui/dist/fancybox.umd.js"></script>

<script src="<?= SCRIPT ?>/plugins/dropzonejs/dropzone.min.js"></script>


<style>
    .select2-results__message {
        display: none !important;
    }

    .mdb-select2 {
        width: 100%;
    }

    .bmd-form-group .form-control, .bmd-form-group label, .bmd-form-group input::placeholder {
        /*  line-height: 1 */
    }

    span.select2.select2-container.select2-container--default, .select2-selection__rendered {
        color: var(--primary) !important;
    }

    .select2-container--default .select2-selection--single {
        border-top: 0;
        border-left: 0;
        border-right: 0;
        border-bottom: none;
        background-color: var(--bodybackground);
        border-radius: 0;
    }

    .select2-dropdown, .select2-dropdown--above {
        border-radius: 0;
        padding: 0.35rem;
        border: 0;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16), 0 2px 10px 0 rgba(0, 0, 0, .12);
    }

    .select2-container--default .select2-selection--multiple {
        border-top: 0;
        border-left: 0;
        border-right: 0;
        border-bottom: none;
        background-color: var(--bodybackground);
        border-radius: 0;
        height: 40px;
    }

    .select2-container *:focus {
        outline: none;
    }

    .select2-container--default.select2-container--focus .select2-selection--multiple {
        border: 0;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {

    }

    .select2-results__option:hover {
        color: var(--primary) !important;
        background-color: var(--mdb-form-outline-select-option-hover-not-disabled-bg) !important;
    }


    .select2-results__option--highlighted, .select2-results__option[aria-selected="true"] {
        color: var(--primary) !important;
        background-color: var(--mdb-form-outline-select-option-hover-not-disabled-bg) !important;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {

    }

    .select2-container--default .select2-results__option[aria-selected="true"] {
        background-color: var(--mdb-form-outline-select-option-selected-active-bg);
        color: var(--primary);
    }

    .select2-selection__arrow {
        color: var(--mdb-form-outline-select-arrow-color);
        text-align: center;
        font-size: 0.8rem !important;
        position: absolute;
        top: 5px !important;
    }

    .select2-selection__arrow::before {
        content: "▼";
    }

    .select2-selection__arrow b {
        display: none;
    }

    #select2floatinglabel {
        position: absolute;
        /* top: 0; */
        max-width: 90%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        /* left: 0.75rem; */
        /* padding-top: 0.37rem; */
        pointer-events: none;
        transform-origin: 0 0;
        transition: all .2s ease-out;
        color: rgba(0, 0, 0, .6);
        margin-bottom: 0;
        margin-top: -12px;
        font-size: 0.7rem !important;
        margin-left: 0.5rem !important;
        background: var(--bodybackground);
        padding: 0px 5px 0px 3px;
    }

</style>
<script>
    var shopIsReadOnly = <?= $shopIsReadOnly ? 'true' : 'false'; ?>;

    function updateAllROs() {
        sbconfirm('Are you sure?', 'Are you sure you want to UPDATE ALL repair orders for this customer to this information', function () {
            $.ajax({

                data: "shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>",
                url: "customer-update-all-ros.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        sbalert("All RO's successfully updated to this information")
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                }
            });
        });

    }

    function sendSMS(cell) {

        $('#smsmodal').modal('show')
        $('#smscell').val(cell)
        setTimeout(function () {
            $('#smsmsg').focus()
        }, 500)

    }

    function sendSMSMessage() {

        to = $('#smscell').val();
        msg = encodeURIComponent($('#smsmsg').val())

        $.ajax({
            data: "sendsms=save&from=<?php echo $smsnum; ?>&to=" + to + "&msg=" + msg,
            type: "post",
            url: "<?php echo INTEGRATIONS; ?>/bandwidth/sendsmsv2.php",
            success: function (r) {
                if (r == "success") {
                    $('#smsmodal').modal('hide')
                    sbalert("Message sent successfully")
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
            }
        })

    }

    // blink billing shipping credit limit button if over credit limit
    setInterval(function () {
        $("#bscr").toggleClass("btn-secondary btn-primary");
    }, 1000); // put timeout here, e.g. 1000 milliseconds is 1 second

    function getModelList() {

        make = $('#addmake').val()
        $.ajax({
            data: "make=" + make,
            url: "customer-model-list.php",
            success: function (r) {
                $('#modellist').append(r)
            }
        })

    }

    function mask(str, textbox, loc, delim) {
        var x = event.which || event.keyCode;
        if (x != 8) {
            var locs = loc.split(',');
            for (var i = 0; i <= locs.length; i++) {
                for (var k = 0; k <= str.length; k++) {
                    if (k == locs[i]) {
                        if (str.substring(k, k + 1) != delim) {
                            str = str.substring(0, k) + delim + str.substring(k, str.length)
                        }

                    }

                }

            }
            textbox.value = str
        }
    }

    function showHistory() {
        // close the vehicle modal, open large modal with history
        $('#vehmodal').modal('hide')
        eModal.iframe({
            title: 'Repair History',
            url: "/v2/customer/customer-vehicle-history.php?shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&vid=" + $('#vehid').val(),
            size: eModal.size.xl,
        });

    }

    function showCustomerHistory() {

        eModal.iframe({
            title: 'Customer History',
            url: "/v2/customer/customer-history.php?shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>",
            size: eModal.size.xl,
        });

    }

    function recommendRepairs() {
        let buttons = [
            {
                text: 'Print',
                style: 'secondary',
                click: function () {
                    const contents = $("#emodal-box").find("iframe");
                    contents[0].contentWindow.focus();
                    contents[0].contentWindow.print();
                }
            }
        ];

        if (!shopIsReadOnly) {
            buttons.push({
                text: 'Email',
                style: 'primary',
                click: function () {
                    $('#emodal-box iframe').attr("id", "rr_iframe")
                    rr = document.getElementById("rr_iframe")
                    $("#emailRRModal").modal('show')
                }
            });
        }

        // close the vehicle modal, open large modal with recommended repairs
        $('#vehmodal').modal('hide')
        let url = "customer-recommend-repair.php?shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&vid=" + $('#vehid').val();
        eModal.iframe({
            title: 'Recommended Repairs',
            url: url,
            size: eModal.size.xl,
            buttons: buttons
        });
    }

    function sendRREmail() {
        showLoader();
        sendto = encodeURIComponent($('#RRemailto').val())
        emailsubject = encodeURIComponent($('#RRemailsubject').val())
        emailmessage = encodeURIComponent($('#RRemailmessage').val())
        if (sendto != "" && emailsubject != "" && emailmessage != "") {
            ds = "shopid=<?php echo $shopid; ?>&cid=<?php echo $CustomerID; ?>&sendfrom=<?php echo urlencode($_COOKIE['shopname']); ?>&sendto=" + sendto + "&subject=" + emailsubject + "&message=" + emailmessage + "&vid=" + $('#vehid').val()
            $.ajax({
                data: ds,
                url: "<?= COMPONENTS_PRIVATE ?>/customer/recrepair_email.php",
                method: "POST",
                success: function (r) {
                    r = r.trim()
                    if (r == "success") {
                        sbalert("Recommended Repairs Sent")
                        $('#emailRRModal').modal('hide')
                    } else {
                        sbalert(r)
                    }
                    hideLoader();
                }
            });

        }
    }

    function transferVehicle() {
        eModal.iframe({
            title: 'Select Customer',
            url: "customer-select-transfer.php?shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&vid=" + $('#vehid').val(),
            size: eModal.size.xl,
        });
    }

    function openMerge() {
        eModal.iframe({
            title: 'Merge Customers',
            url: "customer-select-merge.php?shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>",
            size: eModal.size.xl,
        });
    }

    function createappointment() {

        var cvid = "<?php echo $cid . '-' ?>" + $('#vehid').val()

        localStorage.setItem("calendarcvid", cvid)
        location.href = '<?= COMPONENTS_PRIVATE ?>/v2/calendar/calendar.php'

    }

    function delCustomer() {

        sbconfirm('Are you sure?', 'Are you sure you want to mark this customer inactive?', function () {
            $.ajax({
                data: "t=customer&shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>",
                url: "customer-delete.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        location.href = 'customer.php'
                    }
                },
            });
        });
    }

    function delVehicle() {

        vid = $('#vehid').val()
        sbconfirm('Are you sure?', 'Are you sure you want to delete this Vehicle?', function () {
            $.ajax({

                data: "t=vehicle&shopid=<?php echo $shopid; ?>&vid=" + vid,
                url: "customer-delete.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        location.reload()
                    }
                },

            });
        });

    }

    function decodeVIN() {

        vin = $('#addvin').val()
        vin = vin.trim()
        p = false

        if (vin.length == 17) {
            p = true
        }

        if (p == true) {


            showLoader()
            $.ajax({
                data: "vin=" + vin + "&carfaxloc=<?= $cfl ?>",
                url: "vehicle-vin-decode.php",
                dataType: "json",
                error: function (xhr, ajaxOptions, thrownError) {
                },
                success: function (r) {

                    if (r.status == 'success') {
                        rvin = r.vin
                        yr = r.yr
                        mk = r.mk
                        md = r.md
                        tr = r.tr
                        bd = r.bd
                        en = r.en
                        tra = r.tra.split("|")

                        dr = r.dr

                        $('#addyear').val(yr).focus()
                        $('#addmake').val(mk).focus()
                        $('#addmodel').val(md + " " + tr + " " + bd).focus()
                        $('#addengine').val(en).focus()
                        $('#addtransmission').html('')
                        for (var t of tra) {
                            $('#addtransmission').append("<option>" + t.trim() + "</option>")
                        }
                        $('#adddrivetype').val(dr).focus().blur()
                    }
                    else if(r.status == 'failed' && r.message != '')
                    {
                        sbalert(r.message)
                    }
                    else {
                        sbalert("The VIN was not able to be decoded.  Please verify the VIN number")
                    }

                    hideLoader()
                }
            });
        } else {
            sbalert("You must enter a 17 digit VIN to decode it")
        }


    }

    function createNewRO(onhold) {
        if (onhold == "yes") {
            sbalert("This customer is on a credit hold. Please go to Accounting - Accounts Receivable to release this hold.")
        } else {
            // check for a current RO for this customer and vehicle
            $.post({
                data: 'shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&vid=' + $('#vehid').val(),
                url: "searchopenro.php",
                error: function (xhr, ajaxOptions, thrownError) {
                },
                success: function (r) {
                    if (r == "0") {
                        sbconfirm('Are you sure?', 'Create RO for this Vehicle?', function () {
                            location.href = '<?= COMPONENTS_PRIVATE ?>/v2/createro/addconcernswithcat.php?cid=<?php echo $cid . $quotestr; ?>&vid=' + $('#vehid').val()
                        });
                    } else {
                        sbconfirm('WARNING - WARNING', 'There is an existing RO for this customer and vehicle. Do you want to create a possible duplicate?', function () {
                            location.href = '<?= COMPONENTS_PRIVATE ?>/v2/createro/addconcernswithcat.php?cid=<?php echo $cid . $quotestr; ?>&vid=' + $('#vehid').val()
                        });
                    }
                }
            });
        }
    }

    function cannotcreateNewRO() {
        sbalert("Can not create RO. Customer is on hold")
    }

    function openCarFax() {
        $('#addvehmodal').modal('hide')
        $('#carfaxmodal').modal('show')
    }

    function saveVehicle(createRO = false) {

        if ($('#addmake').val() == "") {
            sbalert("Please add make of the vehicle");
            return;
        }

        ds = $('#vehaddform').serialize()
        var formData = new FormData($('#vehaddform')[0]);
        formData.append('shopid', '<?php echo $shopid; ?>');
        formData.append('cid', '<?php echo $cid; ?>');
        formData.append('dupcheck', 'yes');
        $.ajax({
            data: formData,
            type: "post",
            url: "customer-vehicle-add.php",
            processData: false,
            contentType: false,
            success: function (r) {
                if (r.indexOf("success") >= 0) {
                    if (createRO) {
                        location.href = '<?= COMPONENTS_PRIVATE ?>/v2/createro/addconcernswithcat.php?cid=<?php echo $cid . $quotestr; ?>&vid=' + vid
                    } else {
                        location.href = 'customer-edit.php?reload=no&cid=<?php echo $cid; ?>'
                    }
                } else if (r.indexOf("duplicate") >= 0) {
                    var cusarr = r.split('duplicate|');
                    formData.delete('dupcheck');
                    sbconfirm('Are you sure?', 'Vehicle with this VIN already belongs to ' + cusarr[1] + '. Do you still want to continue?', function () {
                        $.ajax({
                            data: formData,
                            type: "post",
                            url: "customer-vehicle-add.php",
                            processData: false,
                            contentType: false,
                            success: function (r) {
                                if (r.indexOf("success") >= 0) {
                                    if (createRO) {
                                        location.href = '<?= COMPONENTS_PRIVATE ?>/v2/createro/addconcernswithcat.php?cid=<?php echo $cid . $quotestr; ?>&vid=' + vid
                                    } else {
                                        location.href = 'customer-edit.php?reload=no&cid=<?php echo $cid; ?>'
                                    }
                                }
                            }
                        });
                    });
                }
            }

        });

    }

    function saveAndCreateRO() {
        if ($('#addmake').val() == "") {
            sbalert("Please add make of the vehicle");
            return;
        }

        // save the vehicle, get the vehid, the redirect to the location.href='../createro/addconcerns.php?cid='+$('#customerid').val()+'&vid='+vid
        ds = $('#vehaddform').serialize()
        var formData = new FormData($('#vehaddform')[0]);
        formData.append('shopid', '<?php echo $shopid; ?>');
        formData.append('cid', '<?php echo $cid; ?>');
        formData.append('dupcheck', 'yes');

        $.ajax({
            data: formData,
            type: "post",
            url: "customer-vehicle-add.php",
            processData: false,
            contentType: false,
            success: function (r) {
                if (r.indexOf("success") >= 0) {
                    // now redirect to create RO
                    rar = r.split("|")
                    vid = rar[1]
                    location.href = '<?= COMPONENTS_PRIVATE ?>/v2/createro/addconcernswithcat.php?cid=<?php echo $cid . $quotestr; ?>&vid=' + vid
                } else if (r.indexOf("duplicate") >= 0) {
                    var cusarr = r.split('duplicate|');
                    formData.delete('dupcheck');
                    sbconfirm('Are you sure?', 'Vehicle with this VIN already belongs to ' + cusarr[1] + '. Do you still want to continue?', function () {
                        $.ajax({
                            data: formData,
                            type: "post",
                            url: "customer-vehicle-add.php",
                            processData: false,
                            contentType: false,
                            success: function (r) {
                                if (r.indexOf("success") >= 0) {
                                    // now redirect to create RO
                                    rar = r.split("|")
                                    vid = rar[1]
                                    location.href = '<?= COMPONENTS_PRIVATE ?>/v2/createro/addconcernswithcat.php?cid=<?php echo $cid . $quotestr; ?>&vid=' + vid
                                }
                            }
                        });
                    });
                }
            }
        });
    }

    function save(id) {

        if ($('#' + id).is(':checked')) {
            ds = "t=checkbox&shopid=<?php echo $shopid; ?>&i=" + id + "&v=yes&cid=<?php echo $cid; ?>"
        } else {
            ds = "t=checkbox&shopid=<?php echo $shopid; ?>&i=" + id + "&v=no&cid=<?php echo $cid; ?>"
        }

        $.ajax({
            type: "get",
            data: ds,
            url: "customer-update.php",
            success: function (r) {
                $('#saved').show()
                setTimeout(function () {
                    $('#saved').fadeOut(1500)
                }, 1000);

            }
        });

    }

    function quickVIN() {
        vin = $('#cfvin').val()
        lic = $('#cflic').val()
        st = $('#cfst').val()
        proceed = false

        if (vin.length == 17) {
            proceed = true
        }
        if (lic.length > 0 && st.length > 0) {
            proceed = true
        }

        if (proceed == false) {
            sbalert("VIN or License and State are required")
            return
        }

        if (proceed == true) {
            // get the veh info from quickvin
            ds = "vin=" + vin + "&lic=" + lic + "&lst=" + st + "&shopid=<?php echo $shopid; ?>"
            $.ajax({
                data: ds,
                url: "<?= INTEGRATIONS ?>/carfax/carfax-quickvin.php",
                success: function (r) {
                    if (r.indexOf("success") >= 0) {
                        rar = r.split("|")
                        //"success|".$yr."|".$mk."|".$md."|".$cyl."|".$en."|".$tr."|".$dr."|".$vin;
                        yr = rar[1]
                        mk = rar[2]
                        md = rar[3]
                        cyl = rar[4]
                        en = rar[5]
                        tr = rar[6]
                        dr = rar[7]
                        rvin = rar[8]
                        if (rvin.length == 17) {
                            vin = rvin
                        }

                        $('#carfaxmodal').modal('hide').on('hidden.bs.modal', function () {
                            $('#addvehmodal').modal('show')
                        })

                        // close the carfax modal, open the addvehmodal and enter the values
                        $('#addvin').val(rvin)
                        $('#addyear').val(yr)
                        $('#addmake').val(mk)
                        $('#addmodel').val(md)
                        $('#addengine').val(en)
                        if (st != '')
                            $('#addvehstate').val(st)
                        if (tr != '')
                            $('#addtransmission').html("<option>" + tr.trim() + "</option>")
                        $('#adddrivetype').val(dr)
                        $('#addlicnumber').val(lic)

                    } else {
                        $('#carfaxmodal').modal('hide')
                        $('#addvehmodal').modal('show')
                        sbalert("No result found for this license or VIN")
                    }

                }
            });
        }

    }


    // original values
    lastname = "<?php echo str_replace('"', '\"', $LastName); ?>"
    firstname = "<?php echo str_replace('"', '\"', $FirstName); ?>"
    address = "<?php echo str_replace('"', '\"', $Address); ?>"
    city = "<?php echo $City; ?>"
    state = "<?php echo $State; ?>"
    zip = "<?php echo $Zip; ?>"
    homephone = "<?php echo $HomePhone; ?>"
    workphone = "<?php echo $WorkPhone; ?>"
    cellphone = "<?php echo $CellPhone; ?>"
    fax = "<?php echo $Fax; ?>"
    email = "<?php echo $EMail; ?>"
    userdefined1 = "<?php echo $UserDefined1; ?>"
    userdefined2 = "<?php echo $UserDefined2; ?>"
    userdefined3 = "<?php echo $UserDefined3; ?>"
    comments = "<?php echo addslashes(str_replace(chr(13), ' ', str_replace(chr(10), ' ', $Comments))); ?>"
    follow = "<?php echo $Follow; ?>"
    contact = "<?php echo $contact; ?>"
    active = "<?php echo $active; ?>"
    taxexempt = "<?php echo $taxexempt; ?>"
    spousename = "<?php echo str_replace('"', '', $spousename); ?>"
    spousecell = "<?php echo $spousecell; ?>"
    spousework = "<?php echo $spousework; ?>"
    shippingto = "<?php echo $shippingto; ?>"
    shippingaddress = "<?php echo $shippingaddress; ?>"
    shippingcity = "<?php echo $shippingcity; ?>"
    shippingstate = "<?php echo $shippingstate; ?>"
    shippingzip = "<?php echo $shippingzip; ?>"
    shippingphone = "<?php echo $shippingphone; ?>"
    billto = "<?php echo $billto; ?>"
    billtoaddress = "<?php echo $billtoaddress; ?>"
    billtocity = "<?php echo $billtocity; ?>"
    billtostate = "<?php echo $billtostate; ?>"
    billtozip = "<?php echo $billtozip; ?>"
    billtophone = "<?php echo $billtophone; ?>"
    creditlimit = "<?php echo $creditlimit; ?>"
    customertype = "<?php echo $customertype; ?>"

    vehid = ''
    year = ''
    make = ''
    model = ''
    miles = ''
    license = ''
    vin = ''
    engine = ''
    drive = ''
    trans = ''
    cyl = ''
    fleetno = ''
    state = ''
    color = ''
    custom1 = ''
    custom2 = ''
    custom3 = ''
    custom4 = ''
    custom5 = ''
    custom6 = ''
    custom7 = ''
    custom8 = ''

    function getScannedVins() {
        $.ajax({
            url: "retrievescannedvin-addveh.php",
            success: function (r) {
                $('#scannedvins').html(r)
            }
        });

    }

    function createROVINScan(customerid, shopid, vehid) {
        sbconfirm('Are you sure?', 'VIN will be removed', function () {
            location.href = '<?= COMPONENTS_PRIVATE ?>/v2/createro/addconcernswithcat.php?cid=' + customerid + '&vid=' + vehid
        });
    }

    function saveField(id, v) {
        ds = "shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&i=" + id + "&v=" + v + "&vid=" + vehid
        $.ajax({
            data: ds,
            url: "customer-update.php",
            success: function (r) {
                $('#saved').show()
                setTimeout(function () {
                    $('#saved').fadeOut(1500)
                }, 1000);
                window[id] = v
            },
            error: function (xhr, ajaxOptions, thrownError) {
            }
        });
    }


    $('.form-control:not(.select-input)').blur(function () {

        id = $(this).attr("id")

        if (id != "smsmsg" && id != "smscell" && id != "ocrimage") {
            if ((id.substring(0, 3) != 'add' && id.substring(0, 2) != 'cf' && id != "vehnotes") || id == "address") {
                v = $(this).val()

                if (id != "preferredcontactmethod" && id != 'Discount' && id != "accounttype") {
                    previousval = eval(id)
                    if (previousval.length > 0) {
                        previousval = previousval.toUpperCase()
                    }
                } else {
                    previousval = "x"
                }
                if (id == "homephone" || id == "workphone" || id == "cellphone" || id == "fax" || id == "spousecell" || id == "spousework") {
                    v = v.replace(/\D/g, '')
                }
                if (id == "discountpercent") {
                    id = "`column 27`"
                }
                v = encodeURIComponent(v)
                if (previousval != v) {
                    saveField(id, v)
                }
            }
        }
    });

    Dropzone.options.vehicleEditUpload = {
        maxFilesize: 80, // MB
        maxFiles: 1,
        success: function (file, response) {
            var json = JSON.parse(response)
            if (json.hasOwnProperty('success') && json.success == true) {
                if (json.hasOwnProperty('img') && json.hasOwnProperty('thumb')) {
                    console.log("img uploaded",json)
                    let img = json.img;
                    let thumb = json.thumb;
                    var ts = Date.now();
                    $("#image-preview").attr("src","");
                    let fb_html = '<a id="' + vehid + '_img" data-fancybox="gallery" href="https://customers-ss.s3.us-east-1.amazonaws.com/' + img + '?cache='+ts+'"><img src = "https://customers-ss.s3.us-east-1.amazonaws.com/' + thumb + '?cache='+ts+'" class="img-thumbnail vimg" alt = "vehicle photo"></a>';
                    $("#" + vehid + "_imgcell").html(fb_html);
                    $("#image-preview").attr("src", "https://customers-ss.s3.us-east-1.amazonaws.com/" + img+'?cache='+ts);
                    $(".delete_photo i").css("visibility", "visible");
                    this.removeAllFiles(true);
                }
            }
        }
    };

    function delete_image(elem){
        $.ajax({
            url: 'delete_image.php',
            method: 'POST',
            data: {
                shopid: '<?php echo $shopid; ?>',
                cid: '<?php echo $cid; ?>',
                vid: vehid
            },
            success: function (r) {
                if (r == 'success') {
                    var img_html = '<img src="<?= IMAGE ?>/placeholderCar.png" class="img-thumbnail vimg" alt="Upload vehicle photo">';
                    $("#"+vehid+"_imgcell").html(img_html);
                    $("#image-preview").attr("src", "<?= IMAGE ?>/placeholderCar.png");
                    $(elem).css("visibility", "hidden");
                }
            }
        })
    }

    $(document).ready(function () {

        $('#file-upload').fileUpload({
            maxFileQuantity: 1,
            removeBtn: '',
            height: 120,
            width: 200
        });

        $('a[data-fancybox]').each(function () {
            const fullImg = $(this).attr('href');
            const img = new Image();
            img.src = fullImg;
        });

        $(".sbdatatable").dataTable({
            responsive: true,
            retrieve: true,
            fixedHeader: true,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            searching: true,
            "order": [],
            language: {
                search: '',
                searchPlaceholder: "Search..."
            },
        });

        $('#close-sidebar').on('click', function () {
            $('#logomain').toggle()
        });

        $('#lastname').focus()

        <?php
        if (isset($_GET['addveh']) && $vid == 0) {
            echo "$('#addvehmodal').modal('show')\r\n";
            echo "$('#addyear').val(localStorage.getItem('year'))\r\n";
            echo "$('#addmake').val(localStorage.getItem('make'))\r\n";
            echo "$('#addmodel').val(localStorage.getItem('model'))\r\n";
        }


        if (isset($_GET['vin']) && $vid == 0) {
            echo "$('#addvehmodal').modal('show')\r\n";
            echo "setTimeout(function(){\$('#addvehmodal').focus},500)\r\n";
        }

        if ($vid != 0) {
            echo "sbalert('Vehicle was successfully transferred')\r\n";
        }

        if ($cfl == $shopid && !isset($_GET['reload'])) {
            //echo '$(\'#carfaxmodal\').modal(\'show\');';
            //echo 'setTimeout(function(){$(\'#cfvin\').focus()},300);';
        }

        if (strlen($decodevin) == 17) {
            echo "setTimeout(function(){decodeVIN()},1000);";
        }
        ?>
        $('#createro').click();

        <?php
        if (isset($_GET['transfer'])) {
            echo "sbalert('Vehicle successfully transferred to this customer')\r\n";
        }
        ?>

        localStorage.removeItem("fn");
        localStorage.removeItem("ln");
        localStorage.removeItem("year");
        localStorage.removeItem("make");
        localStorage.removeItem("model");
        localStorage.removeItem("email");
        localStorage.removeItem("cellphone");
        localStorage.removeItem("cid");
        localStorage.removeItem("a2ro");

        $('#ocrimage').on('change', function () {
            if ($(this).val() != '') {
                $('#pictureslot').html("<img src='" + URL.createObjectURL(event.target.files[0]) + "' width='200' height='200' border='1'>").show();
            }

        })

        $('#btn-ocrscan').on('click', function () {
            if ($('#ocrimage').val() == '') {
                sbalert("Please select VIN image to upload")
                return
            }

            var $this = $(this);
            $this.attr('disabled', 'disabled');
            $this.html('Please Wait...');

            formData = new FormData(document.forms.namedItem("ocrform"));
            $.ajax({
                url: '<?= COMPONENTS_PRIVATE ?>/vinscanner/html/dist/ocrscan.php',
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function (data) {

                    if (data.status == 'success') {
                        $('#addvin').val(data.vin)
                        $('#addyear').val(data.yr)
                        $('#addmake').val(data.mk)
                        $('#addmodel').val(data.md + " " + data.tr + " " + data.bd)
                        $('#addengine').val(data.en)
                        tra = data.tra.split("|")
                        dr = data.dr

                        $('#addtransmission').html('')
                        for (var t of tra) {
                            $('#addtransmission').append("<option>" + t.trim() + "</option>")
                        }
                        $('#adddrivetype').val(dr)
                        $('#addcyl').focus()
                        $('#addvehmodal').modal('show');
                    } else
                        sbalert(data.status != '' ? data.status : "No Results Found")

                    $('#ocrimage').val('');
                    $('#newvinscanmodal').modal('hide');
                    $('#pictureslot').html('').hide();
                    $this.attr('disabled', false);
                    $this.html('Scan');
                }
            });

        });

        <?php if ($notesalert == 'yes' && !empty($notes)) { ?>
        sbalert("<?= preg_replace("/(\r|\n)/", " ", $notes) ?>")
        <?php } ?>

        $("#addtransmission").select2({
            tags: true,
            width: '100%',
            dropdownParent: $("#modelDialog1")
        });
    });

    function getCS(z) {

        $.ajax({
            data: "z=" + z,
            url: "getcsfromzip.php",
            success: function (r) {
                if (r.indexOf("success|") >= 0) {
                    rar = r.split("|")
                    $('#city').val(rar[1])
                    $('#city').focus()
                    $('#city').focus().blur()
                    $('#state').val(rar[2])
                    $('#state').focus()
                    $('#state').focus().blur()
                    $('#email').focus()
                }
            }
        });

    }


        $("#image-preview").attr('src', '<?= IMAGE ?>/placeholderCar.png');
        $(".delete_photo i").css("visibility", "hidden");

    $('#vehmodal').on('shown.bs.modal', function () {
        $('#year').focus()

    })
    

    $('#addvehmodal').on('shown.bs.modal', function () {
         $('#addvin').focus()
    })


    function launchScanner() {

        location.href = 'sbpvinscan://?returnURL=https://<?php echo $_SERVER['SERVER_NAME']; ?>/sbp/api/vinscan/?roid=XXXX,shopid=<?php echo $shopid; ?>'
        $('#addvehmodal').modal('hide')
        $('#scanmodal').modal('show')

    }

    function launchNewScanner() {

        $('#addvehmodal').modal('hide')
        $('#newvinscanmodal').modal('show')

    }

    function getThumbnailFilename(originalFilename) {
        const lastDotIndex = originalFilename.lastIndexOf('.');
        if (lastDotIndex === -1) return originalFilename + '_thumb';
        const baseName = originalFilename.substring(0, lastDotIndex);
        const extension = originalFilename.substring(lastDotIndex);
        return `${baseName}_thumb${extension}`;
    }

    function editVehicle(vid) {

        $('#vehmodal').modal('show')
        $("#mainform input").prop("disabled", true);

        $.ajax({

            data: "shopid=<?php echo $shopid; ?>&vid=" + vid,
            url: "customer-vehicle-details.php",
            success: function (r) {
                if (r.indexOf("||") >= 0) {
                    rar = r.split("||")
                    vehid = rar[1];
                    $('#vehid').val(vehid)
                    year = rar[3];
                    $('#year').val(rar[3]);
                    $('#year').focus().blur();
                    make = rar[4];
                    $('#make').val(rar[4]);
                    $('#make').focus().blur();
                    model = rar[5];
                    $('#model').val(rar[5]);
                    $('#model').focus().blur();
                    miles = rar[6];
                    $('#miles').val(rar[6]);
                    $('#miles').focus().blur();
                    license = rar[7];
                    $('#licnumber').val(license);
                    $('#licnumber').focus().blur();
                    state = rar[8];
                    $('#licstate').val(state);
                    $('#licstate').focus().blur();
                    vin = rar[9];
                    $('#vin').val(rar[9]);
                    $('#vin').focus().blur();
                    engine = rar[10];
                    $('#engine').val(rar[10]);
                    $('#engine').focus().blur();
                    drivetype = rar[11];
                    $('#drivetype').val(rar[11]);
                    $('#drivetype').focus().blur();
                    transmission = rar[12];
                    $('#transmission').val(rar[12]);
                    $('#transmission').focus().blur();
                    cyl = rar[13];
                    $('#cyl').val(rar[13]);
                    $('#cyl').focus().blur();
                    fleetno = rar[14];
                    $('#fleetno').val(rar[14]);
                    $('#fleetno').focus().blur();
                    color = rar[15];
                    $('#color').val(rar[15]);
                    $('#color').focus().blur();
                    custom1 = rar[16];
                    $('#custom1').val(rar[16]);
                    $('#custom1').focus().blur();
                    custom2 = rar[17];
                    $('#custom2').val(rar[17]);
                    $('#custom2').focus().blur();
                    custom3 = rar[18];
                    $('#custom3').val(rar[18]);
                    $('#custom3').focus().blur();
                    custom4 = rar[19];
                    $('#custom4').val(rar[19]);
                    $('#custom4').focus().blur();
                    custom5 = rar[20];
                    $('#custom5').val(rar[20]);
                    $('#custom5').focus().blur();
                    custom6 = rar[21];
                    $('#custom6').val(rar[21]);
                    $('#custom6').focus().blur();
                    custom7 = rar[22];
                    $('#custom7').val(rar[22]);
                    $('#custom7').focus().blur();
                    custom8 = rar[23];
                    $('#custom8').val(rar[23]);
                    $('#custom8').focus().blur();
                    currmileage = rar[24];
                    $('#currentmileage').val(rar[24]);
                    $('#currentmileage').focus().blur();

                    tro = parseFloat(rar[25] == "" ? 0 : rar[25]).toFixed(2)
                    cnt = rar[26]

                    if (cnt > 0) {
                        avg = (tro / cnt).toFixed(2)
                    } else {
                        avg = 0.00
                    }

                    tmsg = "Ro Count: " + cnt + " @ " + avg + " = " + tro
                    appts = rar[27]

                    if (appts != '') tmsg = tmsg + "<br>" + appts

                    $('#vehtotals').html(tmsg)

                    setTimeout(function () {
                        $('#year').focus()
                    }, 500)

                    vehicle_image = rar[28];
                    vehicle_image = vehicle_image.trim()
                    $("#imguploadVid").val(vehid);
                    if (vehicle_image != '') {
                        //show vehicle image
                        $("#image-preview").attr('src', 'https://customers-ss.s3.amazonaws.com/' + vehicle_image+'?cache='+Date.now());
                        $(".delete_photo i").css("visibility", "visible");
                    } else {
                        $("#image-preview").attr('src', '<?= IMAGE ?>/placeholderCar.png');
                        $(".delete_photo i").css("visibility", "hidden");
                    }

                }
            }

        });

        // now get the notes
        $.ajax({

            data: "vehid=" + vid + "&shopid=<?php echo $shopid; ?>",
            url: "customer-vehicle-get-notes.php",
            type: "post",
            success: function (r) {
                rar = r.split("|")
                //$('#vehnotes').val(rar[0])
                $('#vehiclenotes').html(rar[1])
            },
            error: function (xhr, ajaxOptions, thrownError) {
            },

        })
    }


    function addNotes() {

        $('#notesmodal').modal('show')
        vid = $('#vehid').val()
        // now get the notes
        setTimeout(function () {
            $('#vehnotes').focus()
            $.ajax({
                data: "vehid=" + vid + "&shopid=<?php echo $shopid; ?>",
                url: "customer-vehicle-get-notes.php",
                type: "post",
                success: function (r) {
                    rar = r.split("|")
                    $('#vehnotes').val(rar[0])
                    //$('#vehiclenotes').html(rar[1])
                },
                error: function (xhr, ajaxOptions, thrownError) {
                },

            })
        }, 500)
    }

    function saveNotes() {

        notes = encodeURIComponent($('#vehnotes').val())
        vid = $('#vehid').val()
        ds = "notes=" + notes + "&shopid=<?php echo $shopid; ?>&vehid=" + vid
        $.ajax({
            data: ds,
            url: "customer-vehicle-add-notes.php",
            type: "post",
            success: function (r) {
                // now get the notes and update the vehiclenotes div
                $.ajax({

                    data: "vehid=" + vid + "&shopid=<?php echo $shopid; ?>",
                    url: "customer-vehicle-get-notes.php",
                    type: "post",
                    success: function (r) {
                        rar = r.split("|")
                        $('#vehnotes').val(rar[0])
                        $('#vehiclenotes').html(rar[1])
                        $('#notesmodal').modal('hide')
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                    },

                })
            },
            error: function (xhr, ajaxOptions, thrownError) {
            }
        });

    }

    function closeModal(id) {
        $('.modal-backdrop').remove();
        $('#' + id).modal('hide')
    }

    function decodeVINScan(vin) {

        $('#addvehmodal').modal('show')
        $('#scanmodal').modal('hide')
        p = false

        if (vin.length == 17) {
            p = true
        }

        if (p == true) {


            $('#saved').show()
            $.ajax({
                data: "vin=" + vin,
                url: "<?= COMPONENTS_PRIVATE ?>/vehicle/vehicle-vin-decode.php",
                success: function (r) {
                    if (r.indexOf("success|") >= 0) {
                        rar = r.split("|")
                        rvin = rar[1]
                        yr = rar[2]
                        mk = rar[3]
                        md = rar[4]
                        tr = rar[5]
                        bd = rar[6]
                        en = rar[7]
                        $('#addvin').val(rvin)
                        $('#addyear').val(yr)
                        $('#addmake').val(mk)
                        $('#addmodel').val(md + " " + tr + " " + bd)
                        $('#addengine').val(en)
                        $('#addcyl').focus()
                        $('#saved').fadeOut('slow')
                        $('#scanmodal').modal('hide')
                        //$('.modal-backdrop').remove();
                    } else {
                        $('#saved').fadeOut('slow')
                        $('#scanmodal').modal('hide')
                        //$('.modal-backdrop').remove();
                        sbalert("The VIN was not able to be decoded.  Please verify the VIN number")
                    }
                }
            });
        } else {
            sbalert("You must enter a 17 digit VIN to decode it")
        }


    }

    function checkDup() {
        ln = $('#lastname').val()
        fn = $('#firstname').val()
        ln = encodeURIComponent(ln.trim())
        fn = encodeURIComponent(fn.trim())
        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&ln=" + ln + "&fn=" + fn,
            type: "post",
            url: "checkdupcustomer.php",
            success: function (r) {
                if (r.indexOf("found") >= 0) {
                    sbconfirm('Are you sure?', 'This may be a duplicate customer.  Click Continue Entering or Cancel to search for the customer', function () {
                        $('#address').focus()
                    });
                }
            }
        });

    }

    function removeVIN(vin, shopid) {
        sbconfirm('Are you sure?', 'VIN will be removed', function () {
            $.ajax({
                data: "vin=" + vin + "&shopid=<?php echo $shopid; ?>",
                url: "removevin.php",
                error: function (xhr, ajaxOptions, thrownError) {
                },
                success: function () {
                    getScannedVins()
                }
            });
        });
    }

    function saveCustomer() {
        formdata = $('#mainform').serialize()
        $.ajax({
            data: formdata + "&shopid=<?php echo $shopid; ?>",
            url: "customer-add-action.php",
            type: "post",
            success: function (r) {
                if (r.indexOf("success|") >= 0) {
                    rar = r.split("|")
                    location.href = '<?= COMPONENTS_PRIVATE ?>/vehicle/vehicle-add.php?cid=' + rar[1]
                }
            }
        })
    }
</script>
</body>
<?php if (isset($conn)) {
    mysqli_close($conn);
}
$time_end = microtime(true);
$execution_time = ($time_end - $time_start) / 60;
?>

</html>
