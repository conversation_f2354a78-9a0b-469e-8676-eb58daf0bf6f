<?php
$date = new DateTime('now');
$component = "customers-v2";
function isInteger($input)
{
    return (ctype_digit(strval($input)));
}

include getRulesGlobal($component);
// Page Variables
$title = 'Customer';
$subtitle = "";

$shopid = filter_var($_GET['shopid'], FILTER_SANITIZE_STRING);
$cid = filter_var($_GET['cid'], FILTER_SANITIZE_STRING);
$vid = filter_var($_GET['vid'], FILTER_SANITIZE_STRING);

$shoplist = "shopid = '$shopid'";
$stmt = "select shopid sid,joinedshopid jid from joinedshops where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $result = $query->get_result();
    while ($row = $result->fetch_array()) {
        $shoplist .= " or shopid = '" . $row['jid'] . "'";
    }
}

$stmt = "select showonlyclosedonhistory soch from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $result = $query->get_result();
    while ($row = $result->fetch_array()) {
        $showonlyclosed = $row['soch'];
    }
}

include getHeadGlobal('');
echo "<body>";
?>

<div id="Recommended_Repairs" class="p-3">
    <h4 class="d-none d-print-block">Recommended Repairs</h4>

    <table id="history-table" class="sbdatatable w-100">
        <thead>
            <tr>
                <th style="width: 8%">ROID</th>
                <th style="width: 8%">Date In</th>
                <th style="width: 8%">Status</th>
                <th style="width: 15%">Customer</th>
                <th style="width: 25%; word-wrap: break-word">Vehicle</th>
                <th style="width: 10%">VIN/Fleet/License</th>
                <th style="text-align:right; width: 10%">Total Rec</th>
            </tr>
        </thead>
        <tbody>

            <?php
            $stmt = "SELECT ro.datein,ro.status,ro.customer,ro.vehinfo,ro.vin,ro.fleetno,ro.vehlicense,ro.totalro,ro.roid,rec.comid,rec.totalrec ";
            $stmt .= " FROM repairorders ro  ";
            $stmt .= " JOIN recommend rec ";
            $stmt .= "   ON ro.shopid = rec.shopid and ro.roid = rec.roid ";
            $stmt .= " WHERE ro.rotype != 'No Approval' and ro.shopid = ? and ro.customerid = ? and ro.vehid = ? order by datein desc";

            //printf(str_replace("?","%s",$stmt),$shopid,$cid,$vid);
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sii", $shopid, $cid, $vid);
                $query->execute();
                $result = $query->get_result();
                $query->store_result();
                while ($row = $result->fetch_array()) {
                    $status = $row['status'];
                    $checkdigit = substr($status, 0, 1);
                    if (isInteger($checkdigit)) {
                        $status = substr($status, 1);
                    }
                    $currroid = $row['roid'];
                    $newdatein = $row['datein'];
                    $currdatein = new DateTime($newdatein);
                    $datein = $currdatein->format('m/d/Y');
                    $complaintid = $row['comid'];
            ?>
                    <tr style="cursor: pointer" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?roid=<?php echo $row['roid']; ?>'">
                        <td><?php echo $row['roid']; ?></td>
                        <td><?php echo $datein; ?></td>
                        <td><?php echo $status; ?></td>
                        <td><?php echo $row['customer']; ?></td>
                        <td><?php echo $row['vehinfo']; ?></td>
                        <td><?php echo $row['vin'] . " / " . $row['fleetno'] . " / " . $row['vehlicense']; ?></td>
                        <td style="text-align:right"><?php echo number_format($row['totalrec'], 2); ?></td>
                    </tr>

                    <tr>
                        <td style="padding-left:20px;">&nbsp;</td>
                        <td colspan="4"><b>RECOMMENDED REPAIR ISSUE: </b>
                            <?php
                            $rstmt = "select `desc` d,totalrec from recommend where shopid = ? and roid = ? and comid = ?";

                            if ($rquery = $conn->prepare($rstmt)) {
                                $rquery->bind_param("sii", $shopid, $currroid, $complaintid);
                                $rquery->execute();
                                $rresult = $rquery->get_result();
                                $rquery->store_result();
                                while ($rrow = $rresult->fetch_array()) {
                                    echo $rrow['d'];
                                }
                            } else {
                                echo "Recommend Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }

                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td colspan="4"><b></b>
                            <?php
                            // now get the complaints
                            $sstmt = "select complaintid,complaint from complaints where shopid = ? and roid = ? and complaintid = ? and acceptdecline = 'declined'";
                            if ($squery = $conn->prepare($sstmt)) {
                                $squery->bind_param("sii", $shopid, $currroid, $complaintid);
                                $squery->execute();
                                $sresult = $squery->get_result();
                                $squery->store_result();
                                while ($srow = $sresult->fetch_array()) {
                                    //echo $srow['complaint'];
                                    //$complaintid = $srow['complaintid'];
                            ?>
                                    <table id="parts-table" class="sbdatatable w-100">
                                        <thead>
                                            <tr>
                                                <th>Part Number</th>
                                                <th>Part Description</th>
                                            </tr>
                                        </thead>

                                        <?php
                                        // now get the parts
                                        $pstmt = "select PartNumber,PartDesc from recommendparts where shopid = ? and roid = ? and complaintid = ?";
                                        if ($pquery = $conn->prepare($pstmt)) {
                                            $pquery->bind_param("sii", $shopid, $currroid, $complaintid);
                                            $pquery->execute();
                                            $presult = $pquery->get_result();
                                            $pquery->store_result();
                                            while ($prow = $presult->fetch_array()) {
                                        ?>
                                                <tr>
                                                    <td><?php echo $prow['PartNumber']; ?></td>
                                                    <td><?php echo $prow['PartDesc']; ?></td>
                                                </tr>
                                            <?php
                                            } // end of parts while
                                            ?>
                                    </table>
                                <?php
                                        } else {
                                            echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        } // end of parts if

                                        // start of labor
                                ?>
                                <table id="labor-table" class="sb-datatable w-100">
                                    <thead>
                                        <tr style="">
                                            <th>Labor</th>
                                        </tr>
                                    </thead>

                                    <?php
                                    // now get the labor
                                    $lstmt = "select `desc` from recommendlabor where shopid = ? and roid = ? and comid = ?";
                                    if ($lquery = $conn->prepare($lstmt)) {
                                        $lquery->bind_param("sii", $shopid, $currroid, $complaintid);
                                        $lquery->execute();
                                        $lresult = $lquery->get_result();
                                        $lquery->store_result();
                                        while ($lrow = $lresult->fetch_array()) {
                                    ?>
                                            <tr>
                                                <td><?php echo $lrow['desc']; ?></td>
                                            </tr>
                                        <?php
                                        } // end of labor while
                                        ?>
                                </table>
                    <?php
                                    } else {
                                        echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                    } // end of labor if
                                } // end of complaints while
                            } else {
                                echo "Complaint Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            } // end of complaints if
                    ?>
                        </td>
                    </tr>
            <?php
                } // ro rec while
            } else {
                echo "Ro Rec Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            ?>
        </tbody>
    </table>
</div>
<?php
include getScriptsGlobal('');
?>
<script src="<?= SCRIPT ?>/plugins/sticky-table-headers/sticky-table-headers.min.js"></script>
<script>
    $(document).ready(function() {
        $('#history-table').stickyTableHeaders();
    });
</script>

<?php
mysqli_close($conn);
?>
