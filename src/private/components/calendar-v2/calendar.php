<?php
$component = "calendar-v2";
include getHeadGlobal($component);
include getRulesGlobal($component);

$shopid = $_COOKIE['shopid'];
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$empid = $_COOKIE['empid'] ?? '';
$editschedule = 'yes';

if (isset($_GET['roid'])) {
    $roid = $_GET['roid'];
} else {
    $roid = 0;
}

$key = "shop:$shopid:company"; // Custom key for this data block

if ($redis && $redis->exists($key)) {
    $data = $redis->hGetAll($key);

    $shopname = $data['companyname'];
    $shopphone = $data['companyphone'];
    $shopemail = $data['companyemail'];
    $schedulesendreminderdefault = $data['schedulesendreminderdefault'];
    $calendardefault = $data['calendardefault'];
    $cfl = $data['carfaxlocation'];
    $companyaddress = $data['companyaddress'];
    $companycity = $data['companycity'];
    $showwaiting = $data['showwaiting'];

} else {
    $stmt = "select companyname,companyphone,companyemail,schedulesendreminderdefault,calendardefault,carfaxlocation,companyaddress,companycity,showwaiting from company where shopid = '$shopid'";

    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $num_roid_rows = $query->num_rows;
        if ($num_roid_rows > 0) {
            $query->bind_result($shopname, $shopphone, $shopemail, $schedulesendreminderdefault, $calendardefault, $cfl, $companyaddress, $companycity, $showwaiting);
            $query->fetch();
        } else {
            echo "error";
        }
        $query->close();
    } else {
        die("Prepare failed: (" . $conn->errno . ") " . $conn->error);
    }
}

$stmt = "select coalesce(MIN(START),'06:00:00'),coalesce(MAX(end),'20:00:00') FROM shophours WHERE shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->bind_result($minstart, $maxend);
    $query->fetch();
    $query->close();
}

if($_COOKIE['mode'] == 'tech2' && $empid!='Admin')
{
    $stmt = "select editschedule from techpermissions where shopid = '$shopid' and empid = $empid";
    if ($query = $conn->prepare($stmt))
    {
       $query->execute();
       $query->bind_result($editschedule);
       $query->fetch();
       $query->close();
    }
}

$stmt = "select yearlabel,makelabel,modellabel,enginelabel,cylinderlabel,translabel,licenselabel,statelabel,fleetlabel,currmileagelabel,colorlabel,drivelabel,vinlabel from vehiclelabels where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($yearlabel, $makelabel, $modellabel, $enginelabel, $cylinderlabel, $translabel, $licenselabel, $statelabel, $fleetlabel, $currmileagelabel, $colorlabel, $drivelabel, $vinlabel);
    $query->fetch();
    $query->close();
}

if (empty($yearlabel)) $yearlabel = "Year";
if (empty($makelabel)) $makelabel = "Make";
if (empty($modellabel)) $modellabel = "Model";
if (empty($enginelabel)) $enginelabel = "Engine";
if (empty($cylinderlabel)) $cylinderlabel = "Cylinders";
if (empty($translabel)) $translabel = "Transmission";
if (empty($licenselabel)) $licenselabel = "License";
if (empty($statelabel)) $statelabel = "License State";
if (empty($fleetlabel)) $fleetlabel = "Fleet Number";
if (empty($currmileagelabel)) $currmileagelabel = "Current Mileage";
if (empty($colorlabel)) $colorlabel = "Color";
if (empty($drivelabel)) $drivelabel = "Drive Type";
if (empty($vinlabel)) $vinlabel = "VIN";

$remindermsg = "This is an appointment reminder from " . $shopname . "." . "\r\n" . "\r\n";
$remindermsg .= "You have an appointment on" . "\r\n" . "\r\n";
$remindermsg .= "\r\n" . "Additional Comments: " . "\r\n";

echo "<body>";
include getHeaderGlobal($component);
include getMenuGlobal($component);


?>
<main id="main-container">

    <div class="container-fluid">

        <input type="hidden" id="viewtype">
        <input type="hidden" id="laborid">
        <input type="hidden" id="currentview">
        <input type="hidden" id="hsd">
        <input type="hidden" id="hed">


        <div class="row mb-4">

            <div class="col-md-9">
                <h2>Appointment Calendar <i class="fas fa-circle-info" data-mdb-toggle="tooltip"
                                            title="To add a calendar item, click a time slot. You can then search for a customer by last name, first name or by phone number."></i>
                </h2>
                <strong>STATUSES: </strong>
                <span class="me-3"><i id='customerArrived' class='fas fa-hourglass text-primary'></i> = Waiting for Customer</span>
                <span class="me-3"><i id='customerArrived' class='fas fa-thumbs-up text-primary'></i> = Customer Arrived</span>
                <span class="me-3"><i id='customerArrived' class='fas fa-check-square text-primary'></i> = Work Completed</span>
                <span class="me-3"><i id='customernoshow' class='fas fa-eye-slash text-primary'></i> = Customer No Show</span>
            </div>

            <div class="col-md-3">
                <?php if($_COOKIE['mode'] == 'full'){?>
                <div class="input-group">
                    <div class="form-outline">
                        <input type="text" id="search-term" class="form-control">
                        <label class="form-label" for="search-term">Search appointments by name or vehicle info</label>
                    </div>
                    <a href="javascript:void(null)" class="input-group-text" onclick="searchAppt()"><i
                                class="fa fa-search"></i></a>
                </div>
                <?php }?>
            </div>


        </div>

        <div class="row">

            <div class="col-md-12">

                <div id='calendar'></div>

            </div>
        </div>
    </div>

</main>

<?php include getModalsGlobal($component); ?>

<input type="hidden" id="customerstatus" value="">
<input type="hidden" id="statusid" value="">
<input type="hidden" id="passedroid" value="<?php echo $roid; ?>">

<script>
    var jq = $.noConflict();
    jq(document).ready(function () {

        jq('.dateTimePickerBS').datetimepickerbs({
            format: "MM/DD/YYYY hh:mm a",
            sideBySide: true,
        });
    });
</script>
<?php include getScriptsGlobal($component); ?>


</body>

</html>
