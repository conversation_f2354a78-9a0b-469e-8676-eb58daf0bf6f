<link href='<?= ASSETS ?>/calendar/lib/fullcalendar.min.css' rel='stylesheet'/>
<link href='<?= ASSETS ?>/calendar/lib/fullcalendar.print.min.css' rel='stylesheet' media='print'/>
<link href='<?= ASSETS ?>/calendar/scheduler.min.css' rel='stylesheet'/>

<style>
    .fc-nonbusiness {
        opacity: 1;
    }

    .tooltip-inner {
        text-align: left;
    }

    .fc-license-message {
        display: none
    }

    [data-resource-id=a] {
        background-color: #006699 !important;
        opacity: .05
    }

    [data-resource-id=b] {
        background-color: #b36b00 !important;
        opacity: .05
    }

   [data-resource-id=c] {
        background-color: #006600 !important;
        opacity: .05
    }

    [data-resource-id=d] {
        background-color: #800000 !important;
        opacity: .05
    }

    [data-resource-id=e] {
        background-color: #337ab7 !important;
        opacity: .05
    }

    [data-resource-id=f] {
        background-color: #4d004d !important;
        opacity: .05
    }

    [data-resource-id=g] {
        background-color: #b38600 !important;
        opacity: .05
    }

    [data-resource-id=h] {
        background-color: #666633 !important;
        opacity: .05
    }

    [data-resource-id=i] {
        background-color: #5c5c8a !important;
        opacity: .05
    }

    [data-resource-id=j] {
        background-color: #5c8a8a !important;
        opacity: .05
    }
    
    [data-resource-id=k] {
        background-color: #808080 !important;
        opacity: .05
    }

    [data-resource-id=l] {
        background-color: #660033 !important;
        opacity: .05
    }

    [data-resource-id=m] {
        background-color: #c6538c !important;
        opacity: .05
    }

    [data-resource-id=n] {
        background-color: #e68a00 !important;
        opacity: .05;
    }

    [data-resource-id=o] {
        background-color: #ff66cc !important;
        opacity: .05;
    }

    .fc-day-header {
        padding: 5px;
        color: var(--primary);
        font-weight: bold;
        vertical-align: middle !important;
    }
    th.fc-day-header {
        background: transparent !important;
    }
    thead{
        background-color: transparent !important;
    }
    .fc-day-top{
        color: var(--tableheadertext) !important;
        background: var(--tableheaderbackground) !important;
    }

    th.fc-resource-cell {
        padding: 5px;
        opacity:.95;
        vertical-align: middle;
        color:white;
    }

     td,tr,th {
        height: auto ;
    }

    .fc-content {
        font-size: 9pt;
        color: white
    }

    .fc-time > span {
        font-size: 9pt;
    }

    [data-time].fc-minor {
        border-bottom: 1px gray solid
    }

    [data-time]:hover {
        background-color: #FFFFCC
    }

    #calendar {
        max-height: 100vh !important;
        overflow-y: auto !important;
    }

    .autocomplete-dropdown-container {
        width: 700px !important;
    }

    .autocomplete-items-list {
        max-height: 400px !important;
        overflow-y: scroll;
    }

    .autocomplete-item {
        font-size: 14px;
    }

    .mdb-autocomplete .mdb-autocomplete-wrap {
        left: 0 !important;
        right: auto !important;
    }

    .fc-button {
        text-transform: capitalize !important;
    }

    #calendar::-webkit-scrollbar {
        display: none;
    }

    #calendar {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }

    .cstatus{
    	border:1px white solid;
    	background-color:white;
    }

    #calendar tr,#calendar th {
        height: auto !important
    }
    
    .fc-time-grid .fc-slats td {
        height: 1.5em !important;
    }
</style>

<script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
<script src='<?= SCRIPT ?>/plugins/moment/moment.js'></script>
<script src='<?= ASSETS ?>/calendar/lib/fullcalendar.min.js'></script>
<script src='<?= ASSETS ?>/calendar/scheduler.min.js'></script>
<script src="<?= SCRIPT ?>/emodal_mdb.js"></script>


<script>
    var shopIsReadOnly = <?= $shopIsReadOnly ? 'true' : 'false'; ?>;

    Object.defineProperty(String.prototype, 'capitalize', {
        value: function() {
            return this.charAt(0).toUpperCase() + this.slice(1);
        },
        enumerable: false
    });

    function preventDefault() {
        $('.fc-time-grid-event, .fc-v-event, .fc-event, .fc-start, .fc-end, .fc-draggable, .fc-resizable').click(function (e) {
            e.stopPropagation();
        });
    }

    $(function () { // document ready

        resid = ''

        resourcedata = [
            <?php
            $rstr = "";
            $stmt = "select colorhex,title from colorcoding where shopid = '$shopid'";
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $query->store_result();
                $c = 0;
                $a = array('a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z');
                $num_roid_rows = $query->num_rows;
                if ($num_roid_rows > 0) {
                    $query->bind_result($colorhex, $title);
                    while ($query->fetch()) {
                        $currltr = $a[$c];
                        $title = addslashes($title);
                        $rstr .= "{ id: '$currltr', title: '$title' }," . chr(10) . chr(9) . chr(9) . chr(9) . chr(9);
                        $c = $c + 1;

                    }
                    echo $rstr;
                }
                $query->close();
            }
            ?>

        ]

        var isMobile = window.innerWidth < 768;
        
        $('#calendar').fullCalendar({

            defaultView: '<?= $calendardefault?>',
            <?php
            if (isset($_GET['d'])){
            ?>
            defaultDate: '<?php echo $_GET['d']; ?>',
            <?php
            }else{
            ?>
            defaultDate: '<?php echo date("Y-m-d"); ?>',
            <?php
            }
            ?>
            minTime: "<?= $minstart?>",
            maxTime: "<?= $maxend?>",
            slotMinutes: 15,
            slotDuration: '00:15:00',
            slotLabelFormat: 'hh:mm',
            aspectRatio: 0.80,
            droppable: true,
            selectHelper: true,
            editable: <?= ($_COOKIE['mode']=='full' && !$shopIsReadOnly) ? 'true' : 'false' ?>,
            selectable: !shopIsReadOnly,
            allDaySlot: false,
            columnFormat: isMobile ? 'ddd\nM/D' : 'ddd M/D/Y',
            eventLimit: true, // allow "more" link when too many events
            customButtons: {
                printbtn: {
                    text: 'Print View',
                    click: function () {
                        printView()
                    }
                }
            },
            header: {
                left: 'prev,next today printbtn',
                center: 'title',
                right: 'agendaDay,agendaTwoDay,agendaWeek,month'
            },

            views: {
                agendaTwoDay: {
                    type: 'agenda',
                    duration: {days: 3},
                    groupByDateAndResource: true
                },
                agenda: {
                    titleFormat: 'dddd M/D/Y'
                }
            },

            resources: resourcedata,
            events: {
                url: 'calendardata.php',
                type: 'POST',
                data: {
                    t: "getlist"
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },

            },


            dayClick: function (date, jsEvent, view, resource) {

                <?php if($_COOKIE['mode'] == 'full' && !$shopIsReadOnly){?>

                if (view.name != "month" && view.name != "agendaWeek") {

                    if (localStorage.getItem("calendarroid") !== null) {

                        roid = localStorage.getItem("calendarroid")
                        $.ajax({
                            data: "t=getroid&shopid=<?php echo $shopid; ?>&roid=" + roid,
                            url: "calendardata.php",
                            type: "post",
                            success: function (r) {
                                if (r.indexOf("|") !== -1) {
                                    rar = r.split("|")
                                    ln = rar[0]
                                    fn = rar[1]
                                    cl = rar[2]
                                    yr = rar[3]
                                    mk = rar[4]
                                    md = rar[5]
                                    em = rar[6]
                                    cid = rar[7]
                                    vid = rar[8]
                                    svc = rar[9]
                                    hrs = rar[10]
                                    wri = rar[11]
                                    $('#addlastname').val(ln)
                                    $('#addfirstname').val(fn)
                                    $('#addcellphone').val(cl)
                                    $('#addyear').val(yr)
                                    $('#addmake').val(mk)
                                    $('#addmodel').val(md)
                                    $('#addemail').val(em)
                                    $('#addhours').val(hrs)
                                    $('#addservice').val(svc)
                                    $('#customerid').val(cid)
                                    $('#vehid').val(vid)
                                    if (wri != '') $("#sales option:contains(" + wri + ")").attr('selected', 'selected')
                                    temproid = $('#passedroid').val()
                                    $('#schroid').val(temproid)
                                    $('#passedroid').val('')
                                    $('#quoteid').val(0)

                                    localStorage.removeItem("calendarroid")
                                }
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                            }
                        });

                    } else if (localStorage.getItem("calendarcvid") !== null) {
                        var cvarr = localStorage.getItem("calendarcvid").split('-')
                        var cid = cvarr[0]
                        var vid = cvarr[1]

                        $.ajax({
                            data: "t=getcvdetails&shopid=<?php echo $shopid; ?>&cid=" + cid + "&vid=" + vid,
                            url: "calendardata.php",
                            type: "post",
                            success: function (r) {
                                if (r.indexOf("|") > 0) {
                                    rar = r.split("|")
                                    ln = rar[0]
                                    fn = rar[1]
                                    cl = rar[2]
                                    yr = rar[3]
                                    mk = rar[4]
                                    md = rar[5]
                                    em = rar[6]
                                    $('#addlastname').val(ln)
                                    $('#addfirstname').val(fn)
                                    $('#addcellphone').val(cl)
                                    $('#addyear').val(yr)
                                    $('#addmake').val(mk)
                                    $('#addmodel').val(md)
                                    $('#addemail').val(em)
                                    $('#addhours').val('')
                                    $('#addservice').val('')
                                    $('#customerid').val(cid)
                                    $('#vehid').val(vid)
                                    $('#quoteid').val(0)

                                    localStorage.removeItem("calendarcvid")
                                }
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                            }
                        });

                    } else if (localStorage.getItem("calendarquoteid") !== null) {
                        quoteid = localStorage.getItem("calendarquoteid")
                        $.ajax({
                            data: "t=getquoteid&shopid=<?php echo $shopid; ?>&quoteid=" + quoteid,
                            url: "calendardata.php",
                            type: "post",
                            success: function (r) {
                                if (r.indexOf("|") !== -1) {
                                    rar = r.split("|")
                                    ln = rar[0]
                                    fn = rar[1]
                                    cl = rar[2]
                                    yr = rar[3]
                                    mk = rar[4]
                                    md = rar[5]
                                    em = rar[6]
                                    cid = rar[7]
                                    vid = rar[8]
                                    svc = rar[9]
                                    hrs = rar[10]
                                    wri = rar[11]
                                    $('#addlastname').val(ln)
                                    $('#addfirstname').val(fn)
                                    $('#addcellphone').val(cl)
                                    $('#addyear').val(yr)
                                    $('#addmake').val(mk)
                                    $('#addmodel').val(md)
                                    $('#addemail').val(em)
                                    $('#addhours').val(hrs)
                                    $('#addservice').val(svc)
                                    $('#customerid').val(cid)
                                    $('#vehid').val(vid)
                                    $('#quoteid').val(quoteid)

                                    localStorage.removeItem("calendarquoteid")
                                }
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                            }
                        });

                    } else {

                        $('#addlastname').val('')
                        $('#addfirstname').val('')
                        $('#addcellphone').val('')
                        $('#addyear').val('')
                        $('#addmake').val('')
                        $('#addmodel').val('')
                        $('#addemail').val('')
                        $('#addhours').val('')
                        $('#addservice').val('')
                        $('#customerid').val(0)
                        $('#vehid').val(0)
                        $('#btn-addveh').hide()
                        $('#quoteid').val(0)
                    }

                    showLoader()

                    sd = date.format("MM/DD/YYYY HH:mm")

                    $.ajax({
                        data: "t=checkdt&shopid=<?php echo $shopid; ?>&dt=" + sd,
                        url: "calendardata.php",
                        type: "post",
                        success: function (r) {
                            if (r == 'yes') {
                                $('#addmodal').modal('show');
                            } else sbalert("Appointment time is outside the shop hours")
                            hideLoader();
                        }
                    })


                    $('#schroid').val('')
                    $('#addsd').val(sd)

                    $("#addcolumn option:contains(" + resource.title + ")").attr('selected', 'selected');
                    setTimeout(function () {
                        $('#addlastname').focus()
                    }, 500)

                    const asyncAutocomplete = document.querySelector('#async');
                    const asyncFilter = async (query) => {


                        const url = `customersearch.php?term=${encodeURI(query)}`;
                        const response = await fetch(url);
                        const data = await response.json();
                        const results = Object.values(data).map(obj => ({
                            display: obj.value,
                            value: obj.orival
                        }));
                        return results;

                    };

                    const autocomplete = new mdb.Autocomplete(asyncAutocomplete, {
                        filter: asyncFilter,
                        threshold : 3,
                        displayValue: (value) => value.display,
                        setValue: (value) => value.value
                    });

                    asyncAutocomplete.addEventListener('itemSelect.mdb.autocomplete', (e) => {

                        e.preventDefault()

                        tar = e.value.value.split("|")

                        c = $.trim(tar[0])

                        ctar = c.split("~")
                        fleetno = ctar[0]
                        cid = ctar[1]
                        vid = ctar[2]
                        onhold = ctar[3]

                        n = $.trim(tar[1])
                        ntar = n.split(",")
                        ln = $.trim(ntar[0])
                        fn = $.trim(ntar[1])
                        em = $.trim(tar[3])
                        ph = $.trim(tar[4])
                        yr = $.trim(tar[5])
                        mk = $.trim(tar[6])
                        md = $.trim(tar[7])

                        $('#addlastname').val(ln).blur()
                        $('#addfirstname').val(fn).focus()
                        $('#addcellphone').val(ph).focus()
                        $('#addyear').val(yr).focus()
                        $('#addmake').val(mk).focus()
                        $('#addmodel').val(md).focus()
                        $('#addemail').val(em).focus()
                        $('#customerid').val(cid)
                        $('#vehid').val(vid)
                        $('#addservice').focus()
                        if (cid != '' && cid > 0)
                            $('#btn-addveh').show()

                        autocomplete.close();

                    });

                } else {
                    if (view.name == "agendaWeek") {
                        // _i: (7) [2018, 0, 30, 6, 45, 0, 0]
                        tar = date._i
                        day = tar[1]
                        day = day + 1
                        newdateformat = moment(tar[0] + "-" + day + "-" + tar[2])
                        //console.log(newdateformat)
                        $('#calendar').fullCalendar('changeView', 'agendaDay');
                        //$('#calendar').fullCalendar('gotoDate',newdateformat)
                        $('#calendar').fullCalendar('gotoDate', date.format("MM/DD/YYYY 00:00:00"))
                        //sbalert("Please switch to Day View or 3 Day View to add a new appointment")
                    }
                    if (view.name == "month") {
                        //console.log(date.format("MM/DD/YYYY"))
                        $('#calendar').fullCalendar('changeView', 'agendaDay');
                        $('#calendar').fullCalendar('gotoDate', date.format("MM/DD/YYYY 00:00:00"))
                        //sbalert("Please switch to Day View or 3 Day View to add a new appointment")
                    }


                }

               <?php }?>
            },


            eventClick: function (calEvent, jsEvent, view) {

                if (!shopIsReadOnly && '<?= strtolower($editschedule)?>' == "yes") {

                $('#statusid').val(calEvent.id)
                if ($('#customerstatus').val() == "") {

                    showLoader()

                    $('#editmodalheader').css("backgroundColor", calEvent.backColor)
                    $('#editservice').val(calEvent.service)
                    $('#editfirstname').val(calEvent.firstname)
                    $('#editlastname').val(calEvent.lastname)
                    $('#edithours').val(calEvent.hrs)
                    $('#edityear').val(calEvent.year)
                    $('#editmake').val(calEvent.make)
                    $('#editmodel').val(calEvent.model)
                    $('#editid').val(calEvent.id)
                    $('#editcustomerid').val(calEvent.customerid)
                    $('#editvehid').val(calEvent.vehid)
                    $('#editquoteid').val(calEvent.quoteid)
                    $('#editemail').val(calEvent.email)
                    $('#editcellphone').val(calEvent.cellphone)
                    $('#editsd').val(calEvent.start.format("MM/DD/YYYY HH:mm"))
                    $('#editschroid').val('')

                    console.log(calEvent.customer_address)
                    $('#editaddress').val(calEvent.customer_address)
                    $('#editcity').val(calEvent.customer_city)
                    $('#editstate').val(calEvent.customer_state)
                    $('#editzip').val(calEvent.customer_zip)

                    schroid = calEvent.schroid
                    $('#editschroid').val(schroid)
                    $('#schroid').val(schroid)

                    if($('#editwaiter').length)
                    $('#editwaiter').removeClass('active').val(calEvent.waiter).addClass('active')

                    $('.btn-mark').hide()
                    if(calEvent.schflag=='complete')$('#btn-undone').show();
                    else $('#btn-done').show();

                    if (typeof calEvent.empname !== 'undefined' && calEvent.empname != '')
                        $('#salesperson').html("<b>Sales Person:</b> " + calEvent.empname)
                    else
                        $('#salesperson').html("")

                    if (calEvent.schsource == 'O.A.S.' && calEvent.linkedori == '') {
                        $('#editrollspan').show()
                        $('#editrollcheck').prop('checked', false)
                    } else {
                        $('#editrollspan').hide()
                        $('#editrollcheck').prop('checked', false)
                    }
                    if (calEvent.linkedori != '') {
                        $('#linkedori').html(calEvent.linkedori)
                        $('#linkedori').show()
                    } else
                        $('#linkedori').hide()
                    determiner = parseFloat(schroid) >= 1000
                    if (determiner === true) {
                        $('#converttorobutton').hide();
                        $('#gotorobutton').show();
                    } else {
                        $('#converttorobutton').show();
                        $('#gotorobutton').hide();
                    }
                    $('#historybutton').hide()
                    hsd = $.fullCalendar.formatDate(calEvent.start, "YYYY-MM-DD HH:mm:ss")
                    // geting non 24hr
                    $('#editsd2').val(calEvent.start.format("MM/DD/YYYY hh:mm A"))
                    hsd2 = $.fullCalendar.formatDate(calEvent.start, "YYYY-MM-DD hh:mm:ss A")

                    $.ajax({
                        data: "t=getcidvid&shopid=<?php echo $shopid; ?>&id=" + calEvent.id,
                        url: "calendardata.php",
                        type: "post",
                        success: function (r) {
                            rar = r.split("|")
                            cid = rar[0]
                            vid = rar[1]
                            onhold = rar[2]
                            if ($.isNumeric(cid) && $.isNumeric(vid)) {
                                if (cid != 0 && vid != 0) {
                                    $('#historycid').val(cid)
                                    $('#historyvid').val(vid)
                                    $('#onhold2').val(onhold)
                                    $('#historybutton').show()
                                }
                            }

                            hideLoader()

                            $('#editmodal').modal('show')
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        }
                    })

                    $('#calid').val(calEvent.id)
                    $('#customerstatus').val('')

                } else {
                    $('#statusid').val(calEvent.id)

                    if ($('#customerstatus').val() == "arrived") {
                        finishedCustomer()
                    } else if ($('#customerstatus').val() == "waiting") {
                        waitingCustomer()
                    } else if ($('#customerstatus').val() == "finished") {
                        customerArrived()
                    } else if ($('#customerstatus').val() == "noshow") {
                        customerNoShow()
                    }
                }

               }


            },

            eventRender: function (event, element, view) {
                var title = element.find('.fc-title, .fc-list-item-title');

                if(view.name=='month')
                title.text(event.monthtitle)

                var isdone = event.done
                if (isdone == "no") {
                    title.html("<i id='customerArrived' onclick='setCustomerStatus(\"arrived\")' class='cstatus fas p-1 fa-hourglass' style='color:red'></i> " + title.text());
                } else if (isdone == "finished") {
                    title.html("<i id='customerFinished' onclick='setCustomerStatus(\"finished\")' class='cstatus fas p-1 fa-thumbs-up' style='color:blue'></i> " + title.text());
                } else if (isdone == "yes") {
                    title.html("<i id='waitingCustomer' onclick='setCustomerStatus(\"noshow\")' class='cstatus fas p-1 fa-check-square' style='color:green'></i> " + title.text());
                } else if (isdone == "noshow") {
                    title.html("<i id='customernoshow' onclick='setCustomerStatus(\"waiting\")' class='cstatus fas p-1 fa-eye-slash' style='color:black'></i> " + title.text());
                }
                var schflag = event.schflag
                if (schflag == "complete") {
                    element.css('color', 'white')
                    element.css('background-color', '#001a00')
                }

                if (event.schcolor != '')
                    element.css('background-color', event.schcolor)

                var timespan = element.find('.fc-time span');
                if(event.schflag == 'complete')timespan.append(" (DONE)")
                
                
            },

            eventDragStart: function (event) {
                resid = event.resourceId
            },

            eventDrop: function (event, delta, revertFunc, oldEvent) {

                sd = event.start.format()
                ed = event.end.format()
                id = event.id
                rid = event.resourceId
                hrs = event.hrs
                colorcode = event.colorcode

                if (event.linkedori != '' && resid != event.resourceId) {
                    sbconfirm("Confirmation", "All future appointments for this job will follow into the bay you are moving it to. Do you still want to continue?",

                        function () {
                            moveevent(sd, id, rid, colorcode, hrs)
                        },

                        function () {
                            revertFunc()
                        })

                } else
                    moveevent(sd, id, rid, colorcode, hrs)


            },
            viewRender: function (view) {

                $('.fc-prev-button,.fc-next-button,.fc-today-button,.fc-month-button,.fc-agendaWeek-button,.fc-agendaDay-button,.fc-agendaTwoDay-button,.fc-printbtn-button,.fc-printdaybtn-button').removeClass().addClass('fc-button btn btn-secondary me-1');

                var axis = $('.fc-axis');
                var currview = $('#calendar').fullCalendar('getView')
                if (view.type == "agendaDay") {

                    // get the total hours for the day
                    todaydate = $('#calendar').fullCalendar('getDate');
                    todaydate = moment(todaydate._d)
                    todaydate = moment(todaydate).add(1, 'days');
                    todaydate = todaydate.format("MM/DD/YYYY")

                    ds = "shopid=<?php echo $shopid; ?>&d=" + todaydate + "&t=getdaytotal"
                    //console.log(ds)
                    $.ajax({
                        data: ds,
                        url: "calendardata.php",
                        type: "post",
                        success: function (r) {
                            if ($.isNumeric(r)) {
                                r = parseFloat(r)
                                result = r.toFixed(2)
                                if ($('.schtitle').length)
                                    $('.schtitle').html("(" + result + " Scheduled Hours)</span>")
                                else
                                    $('.fc-center h2').css("font-size", "x-large").css("font-weight", "bold").append("<div class='schtitle' style='font-size:medium;padding-top:2px;font-weight:normal'>(" + result + " Scheduled Hours)</span>")
                            }
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        }
                    });

                } else if (view.type == "agendaTwoDay") {
                    // get the date range displayed in
                    dr = $('.fc-center h2').html()
                    tar = dr.split(" ")
                    ed = new Date(tar[4])
                    sed = new Date(tar[4])
                    ed = moment(ed)
                    sed = moment(sed)
                    sd = ed.subtract(2, "days")

                    // now get the totals for each day in the range
                    ds = "shopid=<?php echo $shopid; ?>&sd=" + sd.format("MM/DD/YYYY") + "&ed=" + sed.format("MM/DD/YYYY") + "&t=get3daytotal"
                    console.log(ds)
                    $.ajax({
                        data: ds,
                        url: "calendardata.php",
                        type: "post",
                        success: function (r) {
                            rar = r.split("|")
                            fval = rar[0]
                            fdar = fval.split("~")
                            fdate = fdar[0]
                            fhrs = fdar[1]

                            sval = rar[1]
                            sdar = sval.split("~")
                            sdate = sdar[0]
                            shrs = sdar[1]

                            tval = rar[2]
                            tdar = tval.split("~")
                            tdate = tdar[0]
                            thrs = tdar[1]

                            ths = document.getElementsByTagName("th")
                            n = 1
                            $.each(ths, function () {
                                if ($(this).hasClass("fc-day-header") && $(this).attr("data-date") == fdate) {
                                    $(this).append(" (" + fhrs + " hrs)")
                                }
                                if ($(this).hasClass("fc-day-header") && $(this).attr("data-date") == sdate) {
                                    $(this).append(" (" + shrs + " hrs)")
                                }
                                if ($(this).hasClass("fc-day-header") && $(this).attr("data-date") == tdate) {
                                    $(this).append(" (" + thrs + " hrs)")
                                }

                            })
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        }
                    });


                }
                $('#currentview').val(view.type)
                for (var i = 0; i < axis.length; i++) {
                    var element = axis[i];
                    var p = element.parentElement;
                    var n = element.cloneNode(true);
                    p.appendChild(n);
                }
            },
            eventResize: function (event, jsEvent, ui, view) {
                ed = event.end.format()
                sd = event.start.format()
                id = event.id

                showLoader()

                // post it with ajax, then
                $.ajax({
                    type: "post",
                    data: "t=changeevent&id=" + id + "&end=" + ed + "&start=" + sd,
                    url: "calendardata.php",
                    success: function (r) {

                        if (r != "success") {
                            sbalert(r);
                        }
                        $('#calendar').fullCalendar('refetchEvents');
                        hideLoader()
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },
                });

            },

        });

    });

    function resizeCalendar(calendarView) {
        if (calendarView.name === 'agendaWeek' || calendarView.name === 'agendaDay') {
            calendarView.setHeight(9999);
        }
    }

    function viewHistory() {

        cid = $('#historycid').val()
        vid = $('#historyvid').val()
        eModal.iframe({
            title: 'Vehicle History - Click any line for more detail',
            url: 'historyclosed.php?shopid=<?php echo $shopid; ?>&cid=' + cid + '&vid=' + vid,
            size: eModal.size.xl

        });


    }

    function FormatPhoneNumber(num) {
        var str = num.toString();

        var matched = str.match(/\d+\.?\d*/g);

        // 10 digit
        if (matched.length === 3) {
            return '(' + matched[0] + ') ' + matched[1] + '-' + matched[2];
            // 7 digit
        } else if (matched.length === 2) {
            return matched[0] + '-' + matched[1];
        }
        // no formatting attempted only found integers (i.e. 1234567890)
        else if (matched.length === 1) {
            // 10 digit
            if (matched[0].length === 10) {
                return '(' + matched[0].substr(0, 3) + ') ' + matched[0].substr(3, 3) + '-' + matched[0].substr(6);
            }
            // 7 digit
            if (matched[0].length === 7) {
                return matched[0].substr(0, 3) + '-' + matched[0].substr(3);
            }
        }

        // Format failed, return number back
        return num;
    }


    function goToRO() {
        roid = $('#schroid').val();
        if (roid >= 1000) {
            location.href = '<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?roid=' + roid
        }
    }

    function showReminder(shopname, shopphone) {

        $('#editmodal').modal('hide')

        $('#updateemailto').val($('#editemail').val())


        if ($('#editcellphone').val().length > 0) {
            $('#updatecellphone').val(FormatPhoneNumber($('#editcellphone').val()))
            $('#nonformatcell').val($('#editcellphone').val())
        } else {
            $('#updatecellphone').val($('#editcellphone').val())
            $('#nonformatcell').val($('#editcellphone').val())
        }

        fname = $('#editfirstname').val()
        lname = $('#editlastname').val()
        $('#updateremindermsg').val(fname + ' ' + lname + ',' + '\n' + '\n' + 'This is an appointment reminder from ' + shopname + '. <?= addslashes($companyaddress . ', ' . $companycity)?>' + '\n' + '\n' + 'You have an appointment at ' + $('#editsd2').val() + ' for service on your ' + $('#edityear').val() + ' ' + $('#editmake').val() + ' ' + $('#editmodel').val() + '\n' + '\n' + 'If you have any questions, please give us a call at ' + FormatPhoneNumber(shopphone) + '\n' + '\n' + 'Sincerely, ' + '\n' + '\n' + shopname + '\n' + '\n' + 'Additional Comments: ')

        $('#sendremindermodal').modal('show');

    }

    function sendReminder(reminder) {

        em = $('#updateemailto').val()
        cp = $('#updatecellphone').val()
        nonformattedcell = $('#updatecellphone').val()
        nonformattedcell = nonformattedcell.replace(/[^\d]/g, '')

        msgsend = encodeURIComponent($('#updateremindermsg').val())
        id = $('#editid').val()

        if (reminder === "email" && em.length === 0) {
            sbalert("You must have an email to send an email reminder");
            return
        }
        if (reminder === "text" && cp.length === 0) {
            sbalert("You must have a cell phone to send an text message reminder");
            return
        }
        if ((reminder === "both" && em.length === 0) || (reminder === "both" && cp.length === 0)) {
            sbalert("You must have an email and cell phone to send a reminder to both");
            return
        }


        if ($("#updateemail").is(':checked')) {
            ue = "&updateemail=yes"
        } else {
            ue = "&updateemail=no"
        }

        if ($("#updatecell").is(':checked')) {
            uc = "&updatecell=yes"
        } else {
            uc = "&updatecell=no"
        }

        sub = encodeURIComponent($('#msgsubject').val())

        ds = "sub=" + sub + "&shopid=<?php echo $shopid; ?>&id=" + id + "&t=" + reminder + "&email=" + em + "&cell=" + nonformattedcell + ue + uc + "&msg=" + msgsend

        $.ajax({
            data: ds,
            url: "sendreminder.php",
            success: function (r) {
                if (r === "success") {
                    sbalert("Your reminder has been sent")
                    $('#sendremindermodal').modal('hide')
                } else {
                    sbalert(r)
                }
                $('#editmodal').modal('hide')
                $('#calendar').fullCalendar('refetchEvents')
                $('#updateeemail').attr('checked', false)
                $('#updatecell').attr('checked', false)
            }
        })
    }


    function setCustomerStatus(stat) {

        if('<?= $_COOKIE['mode']?>' == 'full')
        $('#customerstatus').val(stat)

    }

    function customerArrived() {

        id = $('#statusid').val()
        ds = "t=updatedone&s=yes&shopid=<?php echo $shopid; ?>&id=" + id
        showLoader()

        $.ajax({

            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {
                $('#statusid').val('')
                $('#customerstatus').val('')
                $('#calendar').fullCalendar('refetchEvents');
                hideLoader()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#statusid').val('')
                $('#customerstatus').val('')
            }

        });

    }

    function customerNoShow() {
        id = $('#statusid').val()
        ds = "t=updatedone&s=noshow&shopid=<?php echo $shopid; ?>&id=" + id
        showLoader()
        $.ajax({

            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {
                $('#statusid').val('')
                $('#customerstatus').val('')
                $('#calendar').fullCalendar('refetchEvents');
                hideLoader();
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#statusid').val('')
                $('#customerstatus').val('')
            }

        });

    }

    function waitingCustomer() {

        id = $('#statusid').val()
        ds = "t=updatedone&s=no&shopid=<?php echo $shopid; ?>&id=" + id
        showLoader()
        $.ajax({

            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {
                console.log(r)
                $('#statusid').val('')
                $('#customerstatus').val('')
                $('#calendar').fullCalendar('refetchEvents');
                hideLoader()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#statusid').val('')
                $('#customerstatus').val('')
            }

        });

    }

    function printView() {
        var view = $('#calendar').fullCalendar('getView');
        viewtype = view.name
        sd = view.intervalStart.format("MM/DD/YYYY");
        var type = viewtype.replace("agenda", "");
        if (viewtype == "agendaTwoDay") {
            type = "Three Days"
        }
        eModal.iframe({
            title: "Show Calendar Entries for "+type.capitalize(),
            url: "calendarprint.php?sd=" + sd + "&viewtype=" + viewtype,
            size: eModal.size.xl,
            buttons: [
                {text: 'Close', style: 'warning', close: true}
            ]
        });
    }

    function finishedCustomer() {

        showLoader()

        id = $('#statusid').val()
        ds = "t=updatefinished&s=no&shopid=<?php echo $shopid; ?>&id=" + id

        $.ajax({

            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {

                $('#statusid').val('')
                $('#customerstatus').val('')
                $('#calendar').fullCalendar('refetchEvents');
                hideLoader()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#statusid').val('')
                $('#customerstatus').val('')
            }

        });


    }


    function reminderSet(reminderval) {

        if (reminderval !== "no") {
            $('#reminderdiv').show()
        } else {
            $('#reminderdiv').hide()
        }

    }

    function setCookie(cname, cvalue, exdays) {
        var d = new Date();
        d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
        var expires = "expires=" + d.toUTCString();
        document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
    }

    function convertToRO(skip = false) {
        var cid = $('#customerid').val(cid)
        var cid2 = $('#historycid').val()
        var onhold2 = $('#onhold2').val(onhold2)

        if (onhold == "yes") {
            sbalert("This customer is on a credit hold. Please go to Accounting - Accounts Receivable to release this hold.")

        } else {

            showLoader()

            fn = $('#editfirstname').val()
            ln = $('#editlastname').val()
            svc = $('#editservice').val()
            yr = $('#edityear').val()
            mk = $('#editmake').val()
            md = $('#editmodel').val()
            em = $('#editemail').val()
            cl = $('#editcellphone').val()
            cid = $('#editcustomerid').val()
            vid = $('#editvehid').val()
            schid = $('#statusid').val()
            quoteid = $('#editquoteid').val()


            address = $('#editaddress').val()
            city = $('#editcity').val()
            state = $('#editstate').val()
            zipcode = $('#editzip').val()

            onhold = $('#editonhold')


            if (cid > 0 && vid > 0 && $.isNumeric(cid) && $.isNumeric(vid)) {

                if(quoteid > 0 && !skip)
                {
                  $('#editmodal').modal('hide')
                  $('#quotecheckmodal').modal('show')
                  hideLoader()
                }
                else
                {
                    finishedCustomer()
                    localStorage.setItem("schid", schid);
                    localStorage.setItem("svc", svc);
                    if($('#editwaiter').length)
                    localStorage.setItem("waiter", $('#editwaiter').val());
                    setTimeout(function () {
                        location.href = "<?= COMPONENTS_PRIVATE ?>/v2/createro/addconcernswithcat.php?cid=" + cid + "&vid=" + vid + "&schid=" + schid
                    }, 1000)
                }
            } else {

                finishedCustomer()

                if (typeof (Storage) !== "undefined") {
                    localStorage.setItem("fn", fn);
                    localStorage.setItem("ln", ln);
                    localStorage.setItem("svc", svc);
                    localStorage.setItem("year", yr);
                    localStorage.setItem("make", mk);
                    localStorage.setItem("model", md);
                    localStorage.setItem("email", em);
                    localStorage.setItem("cellphone", cl);
                    // pick up onhold
                    localStorage.setItem("onhold", cl);

                    localStorage.setItem("address", address);
                    localStorage.setItem("city", city);
                    localStorage.setItem("state", state);
                    localStorage.setItem("zip", zipcode);

                    localStorage.setItem("cid", cid);
                    localStorage.setItem("a2ro", "true");
                    localStorage.setItem("schid", schid);
                    if($('#editwaiter').length)
                    localStorage.setItem("waiter", $('#editwaiter').val());

                }

                setTimeout(function () {
                    location.href = '<?= COMPONENTS_PRIVATE ?>/v2/customer/customer-search.php'
                }, 1000)
            }

        }
    }

    function createRO(cid,vid)
    {
        
        showLoader()

        cid = $('#editcustomerid').val()
        vid = $('#editvehid').val()
        schid = $('#statusid').val()
        quoteid = $('#editquoteid').val()
        if($('#editwaiter').length)
        waiter = $('#editwaiter').val()
        else
        waiter = ''

        finishedCustomer()

        ds = "&oshopid=<?php echo $shopid; ?>&cid="+cid+"&vid="+vid+"&shopid=<?php echo $shopid; ?>"+"&waiter=" + waiter +"&schid=" + schid

        $.ajax({

            data: ds,
            type: "post",
            url: "<?= COMPONENTS_PRIVATE?>/src/private/components/createro-v2/createro.php",
            success: function(r) {

                if (r.indexOf("|") > 0) {
                    rar = r.split("|")
                    var newroid = rar[1]
                    ds = "t=addissuestoro&shopid=<?php echo $shopid; ?>&quoteid="+quoteid+"&updateinv=<?= $updateinvonadd?>&roid="+newroid
                    $.ajax({
                        data: ds,
                        type: "get",
                        url: "<?= COMPONENTS_PRIVATE?>/src/private/components/quotes-v2/quoteactions.php",
                        success: function(r) {
                            localStorage.setItem("schid",schid)
                            location.href = '<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?roid=' + newroid +'&quoteid='+quoteid+'&schid='+schid+'&newquote'
                        }
                    });
                } else {
                    sbalert("You must have a valid vehicle and customer to create an RO")
                }
            }
        });
    }

    function addEvent() {

        // check for all values
        ac = $('#addcolumn').val()
        afn = $('#addfirstname').val()
        aln = $('#addlastname').val()
        ay = $('#addyear').val()
        am = $('#addmake').val()
        amm = $('#addmodel').val()
        as = encodeURIComponent($('#addservice').val())
        ah = $('#addhours').val()
        asd = $('#addsd').val()
        vid = $('#vehid').val()
        cid = $('#customerid').val()
        em = $('#addemail').val()
        cl = $('#addcellphone').val()
        rm = $('#sendreminder').val()
        schroid = $('#schroid').val()
        quoteid = $('#quoteid').val()
        sales = $('#sales').val()
        if($('#waiter').length)
        waiter = $('#waiter').val()
        else
        waiter = ''

        if ($('#rollcheck').is(":checked"))
            roll = '1'
        else
            roll = '0'

        if (onhold == "yes") {
            sbconfirm("Customer On Hold", "This customer is on a credit hold. Please go to Accounting - Accounts Receiveable to release this hold or confirm to Create Appointment.",
                function () {


                    if (rm != "no") {
                        if (rm == "sms" && cl == "") {
                            sbalert("If you have a reminder set for Text Message, you must enter a cell phone")
                            return
                        }
                        if (rm == "email" && em == "") {
                            sbalert("If you have a reminder set for Email, you must enter an email address")
                            return
                        }
                        if (rm == "both" && (em == "" || cl == "")) {
                            sbalert("If you have a reminder set for Both, you must enter an email address and a cell phone")
                            return
                        }
                    }

                    if (aln.length > 0 && ay.length > 0 && ah.length > 0 && asd.length > 0) {
                        ds = "schroid=" + schroid + "&shopid=<?php echo $shopid; ?>&t=addnewitem&ac=" + ac + "&sd=" + asd + "&afn=" + afn + "&aln=" + aln + "&ay=" + ay + "&am=" + am + "&amm=" + amm + "&as=" + as + "&ah=" + ah + "&cid=" + cid + "&vid=" + vid + "&em=" + em + "&cl=" + cl + "&rm=" + rm + "&empid=" + sales + "&waiter=" + waiter +"&roll=" + roll + "&quoteid=" + quoteid

                        $.ajax({
                            data: ds,
                            type: "post",
                            url: "calendardata.php",
                            success: function (r) {

                                if (r == "success") {
                                    $('#addmodal').modal('hide')
                                    $('#schroid').val('')
                                    $('#passedroid').val('')
                                    $('#calendar').fullCalendar('refetchEvents')
                                } else
                                    sbalert(r)
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                            }
                        });
                    } else {
                        sbalert("Last name, vehicle year, number of hours and start date and time are required.")
                    }

                });


        } else {

            if (rm != "no") {
                if (rm == "sms" && cl == "") {
                    sbalert("If you have a reminder set for Text Message, you must enter a cell phone")
                    return
                }
                if (rm == "email" && em == "") {
                    sbalert("If you have a reminder set for Email, you must enter an email address")
                    return
                }
                if (rm == "both" && (em == "" || cl == "")) {
                    sbalert("If you have a reminder set for Both, you must enter an email address and a cell phone")
                    return
                }
            }

            if (aln.length > 0 && ay.length > 0 && ah.length > 0 && asd.length > 0) {
                showLoader()
                ds = "schroid=" + schroid + "&shopid=<?php echo $shopid; ?>&t=addnewitem&ac=" + ac + "&sd=" + asd + "&afn=" + afn + "&aln=" + aln + "&ay=" + ay + "&am=" + am + "&amm=" + amm + "&as=" + as + "&ah=" + ah + "&cid=" + cid + "&vid=" + vid + "&em=" + em + "&cl=" + cl + "&rm=" + rm + "&empid=" + sales + "&waiter=" + waiter +"&roll=" + roll + "&quoteid=" + quoteid

               $.ajax({
                    data: ds,
                    type: "post",
                    url: "calendardata.php",
                    success: function (r) {

                        if (r == "success") {
                            $('#addmodal').modal('hide')
                            $('#schroid').val('')
                            $('#passedroid').val('')
                            $('#calendar').fullCalendar('refetchEvents')
                        } else
                            sbalert(r)

                        hideLoader()
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                });
            } else {
                sbalert("Last name, vehicle year, number of hours and start date and time are required.")
            }
        }
    }

    function delEvent() {

        sbconfirm("Are you sure?", "Are you sure you want to cancel this appointment?",
            function () {
                id = $('#editid').val()
                ds = "shopid=<?php echo $shopid; ?>&t=delete&id=" + id
                $.ajax({
                    data: ds,
                    type: "post",
                    url: "calendardata.php",
                    success: function (r) {
                        console.log(r)
                        if (r == "success") {
                            $('#editmodal').modal('hide')
                            $('#calendar').fullCalendar('refetchEvents')
                        }
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                });

            });

    }

    function noReminders() {

        showLoader()

        id = $('#editid').val()
        ds = "shopid=<?php echo $shopid; ?>&t=noreminder&id=" + id
        $.ajax({
            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {

                if (r == "success") {
                    $('#editmodal').modal('hide')
                    $('#calendar').fullCalendar('refetchEvents')
                }

                hideLoader()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    function markDone(flag) {

        showLoader()

        id = $('#editid').val()
        ds = "shopid=<?php echo $shopid; ?>&t=markdone&flag="+flag+"&id="+id
        $.ajax({
            data: ds,
            type: "post",
            url: "calendardata.php",
            success: function (r) {

                if (r == "success") {
                    $('#editmodal').modal('hide')
                    $('#calendar').fullCalendar('refetchEvents')
                }

                hideLoader()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }


    function saveEvent() {

        // check for all values
        efn = $('#editfirstname').val()
        eln = $('#editlastname').val()
        ey = $('#edityear').val()
        em = $('#editmake').val()
        emm = $('#editmodel').val()
        hrs = $('#edithours').val()
        es = encodeURIComponent($('#editservice').val())
        eml = $('#editemail').val()
        cl = $('#editcellphone').val()
        id = $('#editid').val()
        sd = $('#editsd').val()
        if($('#editwaiter').length)
        waiter = $('#editwaiter').val()
        else
        waiter = ''

        if ($('#editrollspan').is(':visible') && $('#editrollcheck').is(":checked"))
            roll = '1'
        else
            roll = '0'

        if (efn.length > 0 && eln.length > 0 && ey.length > 0) {

            showLoader()

            ds = "shopid=<?php echo $shopid; ?>&t=saveall&efn=" + efn + "&eln=" + eln + "&ey=" + ey + "&em=" + em + "&emm=" + emm + "&es=" + es + "&id=" + id + "&eml=" + eml + "&cl=" + cl + "&sd=" + sd + "&hrs=" + hrs + "&roll=" + roll + "&waiter="+waiter

            $.ajax({
                data: ds,
                type: "post",
                url: "calendardata.php",
                success: function (r) {

                    hideLoader()

                    if (r == "success") {
                        $('#editmodal').modal('hide')
                        $('#calendar').fullCalendar('refetchEvents')
                    } else
                        sbalert(r)
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        } else {
            sbalert("First name, last name and vehicle year are required")
        }

    }

    function searchAppt() {

        st = $('#search-term').val()
        $("#srchresults > tbody").html("");
        if (st.length > 0) {

            showLoader()

            $.ajax({

                data: "t=search&st=" + st + "&shopid=<?php echo $shopid; ?>",
                url: "calendardata.php",
                type: "post",
                success: function (r) {
                    $('#srchresults>tbody').append(r);
                    hideLoader()
                    $('#searchmodal').modal('show')
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });

        }

    }

    function goto(d) {

        $('#calendar').fullCalendar('gotoDate', d)
        $('#searchmodal').modal('hide')
    }

    function moveevent(sd, id, rid, colorcode, hrs) {
        showLoader()
        $.ajax({

            data: "t=moveevent&shopid=&sd=" + sd + "&id=" + id + "&rid=" + rid + "&colorcode=" + colorcode + "&hrs=" + hrs,
            url: "calendardata.php",
            type: "post",
            success: function (r) {
                if (r != "success") {
                    sbalert(r);
                }
                $('#calendar').fullCalendar('refetchEvents');
                hideLoader()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });
    }

    function openAddVeh() {
        $('#vehyear').val('')
        $('#vehmake').val('')
        $('#vehmodel').val('')
        $('#vehvin').val('')
        $('#addvehmodal').modal('show')
    }

    function decodeVIN() {

        vin = $('#vehvin').val()
        p = false

        if (vin.length == 17) {
            p = true
        }

        if (p == true) {

            showLoader()
            $.ajax({
                data: "vin=" + vin + "&carfaxloc=<?= $cfl?>",
                url: "<?= COMPONENTS_PRIVATE ?>/v2/customer/vehicle-vin-decode.php",
                dataType: "json",
                success: function (r) {

                    if (r.status == 'success') {
                        rvin = r.vin
                        yr = r.yr
                        mk = r.mk
                        md = r.md
                        tr = r.tr
                        bd = r.bd
                        en = r.en

                        $('#vehyear').val(yr)
                        $('#vehmake').val(mk)
                        $('#vehmodel').val(md + " " + tr + " " + bd)
                        hideLoader()
                    } else {
                        $('#vehspinner').hide()
                        sbalert("The VIN was not able to be decoded.  Please verify the VIN number")
                    }
                }
            });
        } else {
            sbalert("You must enter a 17 digit VIN to decode it")
        }
    }

    function addVehicle() {
        if ($('#vehyear').val() == "" || $('#vehmake').val() == "" || $('#vehmodel').val() == "") {
            sbalert("Please enter year, make, model of the vehicle");
            return;
        }

        if ($('#customerid').val() == "") {
            sbalert("Please select customer");
            return;
        }

        $('.btn-md').attr('disabled', 'disabled')

        ds = "shopid=<?= $shopid?>&cid=" + $('#customerid').val() + "&addyear=" + $('#vehyear').val() + "&addmake=" + $('#vehmake').val() + "&addmodel=" + $('#vehmodel').val() + "&addvin=" + $('#vehvin').val()
        $.ajax({

            data: ds + "&dupcheck=yes",
            type: "post",
            url: "<?= COMPONENTS_PRIVATE ?>/v2/customer/customer-vehicle-add.php",
            success: function (r) {
                if (r.indexOf("success") >= 0) {
                    var veharr = r.split('success|');
                    $('.btn-md').attr('disabled', false)
                    $('#addyear').val($('#vehyear').val())
                    $('#addmake').val($('#vehmake').val())
                    $('#addmodel').val($('#vehmodel').val())
                    $('#vehid').val(veharr[1])
                    $('#addvehmodal').modal('hide')
                    sbalert("Vehicle Added")
                } else if (r.indexOf("duplicate") >= 0) {
                    var cusarr = r.split('duplicate|');
                    sbconfirm("Are you sure?", "Vehicle with this VIN already belongs to " + cusarr[1] + ". Do you still want to continue?",
                        function () {

                            $('.btn-conf').attr('disabled', 'disabled')

                            $.ajax({

                                data: ds,
                                type: "post",
                                url: "<?= COMPONENTS_PRIVATE ?>/v2/customer/customer-vehicle-add.php",
                                success: function (r) {
                                    if (r.indexOf("success") >= 0) {
                                        var veharr = r.split('success|');
                                        $('#addyear').val($('#vehyear').val())
                                        $('#addmake').val($('#vehmake').val())
                                        $('#addmodel').val($('#vehmodel').val())
                                        $('#vehid').val(veharr[1])
                                        $('#addvehmodal').modal('hide')
                                        sbalert("Vehicle Added")
                                    }
                                    $('.btn-md').attr('disabled', false)
                                    $('.btn-conf').attr('disabled', false)
                                }
                            });
                        });

                }
            }

        });

    }

    $(document).ready(function () {

        setInterval(function () {

            var currview = $('#calendar').fullCalendar('getView')

            if (currview.name == "agendaDay") {

                // get the total hours for the day
                todaydate = $('#calendar').fullCalendar('getDate');
                todaydate = moment(todaydate._d)
                todaydate = moment(todaydate).add(1, 'days');
                todaydate = todaydate.format("MM/DD/YYYY")

                ds = "shopid=<?php echo $shopid; ?>&d=" + todaydate + "&t=getdaytotal"
                $.ajax({
                    data: ds,
                    url: "calendardata.php",
                    type: "post",
                    success: function (r) {
                        if ($.isNumeric(r)) {
                            r = parseFloat(r)
                            result = r.toFixed(2)
                            $('.schtitle').remove()
                            $('.fc-center h2').css("font-size", "x-large").css("font-weight", "bold").append("<div class='schtitle' style='font-size:medium;padding-top:2px;font-weight:normal'>(" + result + " Scheduled Hours)</span>")

                        }
                        $('#calendar').fullCalendar('refetchEvents');
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                });

            } else
                $('#calendar').fullCalendar('refetchEvents');
            //console.log("refresh events")
        }, 30000);

        <?php
        if (isset($_GET['schdate'])) {
            echo "\r\ngoto('" . $_GET['schdate'] . "');\r\n";
        }
        ?>

        $('.fc-button').hover(function () {
            $(this).removeClass('fc-state-hover');
        });

    });

    /*
    const edt = document.querySelector('.editdatetimepicker');
    var myDatetimepicker = new mdb.Datetimepicker(edt, {
        datepicker: {format: 'mm/dd/yyyy'},
        timepicker: {format24: false},
        inline: true
    })

    const adt = document.querySelector('.adddatetimepicker');
    var myDatetimepicker = new mdb.Datetimepicker(adt, {
        datepicker: {format: 'mm/dd/yyyy'},
        timepicker: {format24: false},
        inline: true
    })
    */

</script>
