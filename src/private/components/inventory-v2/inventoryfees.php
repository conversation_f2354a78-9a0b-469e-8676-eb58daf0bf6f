<?php

$shopid = $_COOKIE['shopid'];

// Global Variables
$date = new DateTime('now');
$component = "inventory-v2";
// Page Variables
$title = 'Inventory';
$subtitle = "";

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal($component);
include getMenuGlobal($component);
?>

<!-- Main Container -->
<main id="main-container">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/inventory/inventory.php" class="text-secondary">Inventory
                            List</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">Manage Inventory Fees</h2>
                    </div>
                    <hr/>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" id="id">

    <?php if (!$shopIsReadOnly): ?>
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="form-outline mb-4">
                    <input id="name" class="form-control" type="text">
                    <label class="form-label" for="name">Fee Name</label>
                </div>

                <div class="form-outline mb-4">
                    <input id="amt" class="form-control" type="text">
                    <label class="form-label" for="amt">Fee Amount</label>
                </div>

                <div class="form-row mb-4">
                    <select class="select" id="percentdollar">
                        <option value="dollar">Dollar</option>
                        <option value="percent">Percent</option>
                    </select>
                    <label class="form-label select-label" for="percentdollar">Percent or Dollar</label>
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-row mb-4">
                    <select class="select" id="taxable">
                        <option value="yes">Yes</option>
                        <option value="no">No</option>
                    </select>
                    <label class="form-label select-label" for="taxable">Taxable</label>
                </div>

                <div class="form-row mb-4">
                    <select class="select" id="qtyflag">
                        <option value="no">No</option>
                        <option value="yes">Yes</option>
                    </select>
                    <label class="form-label select-label" for="qtyflag">Multiply by the Part Quantity sold</label>
                </div>
            </div>

            <div class="col-md-12">
                <input name="Button1" onclick="saveChanges()" class="btn btn-primary btn-md" type="button" value="Save">
            </div>
        </div>
    <?php endif; ?>

    <div class="row mb-2">
        <div class="col-md-12">
            <h4>Existing Fees:</h4>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <table class="sbdatatable w-100">
                <thead>
                <tr>
                    <th>Fee Name</th>
                    <th>Fee Amount</th>
                    <th>Percent/Dollar Amt</th>
                    <th>Taxable</th>
                    <th>Quantity Flag</th>
                    <?php if (!$shopIsReadOnly): ?>
                        <th class="text-center">Delete</th>
                    <?php endif; ?>
                </tr>
                </thead>
                <tbody>
                <?php
                $stmt = "select * from otherfees where shopid = '$shopid'";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $result = $query->get_result();
                    while ($row = $result->fetch_assoc()) {
                        ?>
                        <tr>
                            <td onclick="editFee('<?php echo strtoupper($row['feename']) ?>','<?php echo strtoupper($row['feeamount']) ?>','<?php echo strtoupper($row['dollarpercent']) ?>','<?php echo strtoupper($row['id']) ?>','<?php echo strtolower($row['taxable']) ?>','<?php echo strtolower($row['qtyflag']) ?>' )"><?php echo strtoupper($row['feename']) ?>
                                &nbsp;
                            </td>
                            <td onclick="editFee('<?php echo strtoupper($row['feename']) ?>','<?php echo strtoupper($row['feeamount']) ?>','<?php echo strtoupper($row['dollarpercent']) ?>','<?php echo strtoupper($row['id']) ?>','<?php echo strtolower($row['taxable']) ?>','<?php echo strtolower($row['qtyflag']) ?>')"><?php echo number_format($row['feeamount'], 2) ?>
                                &nbsp;
                            </td>
                            <td onclick="editFee('<?php echo strtoupper($row['feename']) ?>','<?php echo strtoupper($row['feeamount']) ?>','<?php echo strtoupper($row['dollarpercent']) ?>','<?php echo strtoupper($row['id']) ?>','<?php echo strtolower($row['taxable']) ?>','<?php echo strtolower($row['qtyflag']) ?>')"><?php echo strtoupper($row['dollarpercent']) ?>
                                &nbsp;
                            </td>
                            <td onclick="editFee('<?php echo strtoupper($row['feename']) ?>','<?php echo strtoupper($row['feeamount']) ?>','<?php echo strtoupper($row['dollarpercent']) ?>','<?php echo strtoupper($row['id']) ?>','<?php echo strtolower($row['taxable']) ?>','<?php echo strtolower($row['qtyflag']) ?>')"><?php echo strtoupper($row['taxable']) ?>
                                &nbsp;
                            </td>
                            <td onclick="editFee('<?php echo strtoupper($row['feename']) ?>','<?php echo strtoupper($row['feeamount']) ?>','<?php echo strtoupper($row['dollarpercent']) ?>','<?php echo strtoupper($row['id']) ?>','<?php echo strtolower($row['taxable']) ?>','<?php echo strtolower($row['qtyflag']) ?>')"><?php echo strtoupper($row['qtyflag']) ?>
                                &nbsp;
                            </td>

                            <?php if (!$shopIsReadOnly): ?>
                                <td class="text-center text-primary">
                                    <i onclick="deleteFee('<?php echo strtoupper($row['id']) ?>')" class="fas fa-trash">
                                </td>
                            <?php endif; ?>
                        </tr>
                        <?php
                    }
                }
                ?>
                </tbody>
            </table>
        </div>
    </div>
</main>
<!-- END Main Container -->

<?php
include getScriptsGlobal('');
include getScriptsComponent($component);
include getFooterComponent($component);
?>

<script>
    function editFee(n, a, p, id, taxable, q) {
        $('#name').val(n)
        $('#name').focus().blur()
        $('#amt').val(a)
        $('#amt').focus().blur()
        $('#taxable').val(taxable)
        $('#taxable').focus().blur()
        $('#percentdollar').val(p.toLowerCase())
        $('#percentdollar').focus().blur()
        $('#qtyflag').val(q)
        $('#qtyflag').focus().blur()
        $('#id').val(id)
        $('#id').focus().blur()
    }

    function saveChanges() {
        var ds = 't=au' + '&shopid=<?php echo $shopid; ?>' + '&id=' + $('#id').val() + '&qtyflag=' + $('#qtyflag').val() + '&name=' + encodeURIComponent($('#name').val()) + '&taxable=' + $('#taxable').val() + '&amt=' + $('#amt').val() + '&pd=' + $('#percentdollar').val();

        $.ajax({
            url: "saveinventoryfees.php",
            type: "post",
            data: ds,
            success: function (r) {
                if (r == "success") {
                    parent.location.reload()
                } else {
                    swal("There was an error saving");
                }
            }
        });
    }

    function deleteFee(id) {
        sbconfirm(
            'Delete Fee?',
            'Are you sure you want to delete this fee?',
            function () {
                $.ajax({
                    url: "saveinventoryfees.php",
                    data: "name=&amt=&pd=&taxable=&qtyflag=&t=d&id=" + id + "&shopid=<?php echo $shopid; ?>",
                    type: "post",
                    success: function (r) {
                        if (r == "success") {
                            location.reload()
                        } else {
                            sbalert("There was an error saving")
                        }
                    }
                });
            }
        );
    }

    function managePO() {
        location.href = 'inventoryorders.php'
    }

    function addPO() {
        location.href = 'inventoryorder.php'
    }

    function addPart() {
        location.href = 'addinventory.php'
    }

    function invFees() {
        location.href = 'inventoryfees.php'
    }

    function showInv() {
        location.href = 'inventory.php'
    }
</script>

</body>
<?php
mysqli_close($conn);
?>

</html>
