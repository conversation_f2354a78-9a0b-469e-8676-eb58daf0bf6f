<?php
require_once CONN;

$shopid = filter_var($_COOKIE["shopid"], FILTER_SANITIZE_STRING);
$empid = isset($_COOKIE['empid']) ? $_COOKIE['empid'] : '';

if ($empid != "Admin") {
    $stmt = "select upper(EditInventory) from employees where id = ? and shopid = ?";

    if ($query = $conn->prepare($stmt)) {

        $query->bind_param("is", $empid, $shopid);
        $query->execute();
        $query->bind_result($EditInventory);
        $query->fetch();
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    if ($EditInventory == 'NO') die("Access Denied");
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';