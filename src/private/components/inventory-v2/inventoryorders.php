﻿<?php

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
// Global Variables
$date = new DateTime('now');
$component = "inventory-v2";
// Page Variables
$title = 'Inventory';
$subtitle = "";

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal($component);
include getMenuGlobal($component);

$showtaxonro = 'no';
$sstmt = "SELECT showtaxonpo FROM settings where shopid = ?";
if ($query = $conn->prepare($sstmt)){
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($showtaxonro);
    $query->fetch();
    $query->close();
}
?>
<!-- Main Container -->
<main id="main-container">
    <div class="d-flex justify-content-between mb-2">
        <div class="title">
            <h2>Manage PO's</h2>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <p>The following list of Inventory PO's have not been received or closed</p>
        </div>

        <?php if (!$shopIsReadOnly): ?>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary btn-lg" type="button" onclick="location.href='inventoryorder.php'">Add New PO</button>
            </div>
        <?php endif; ?>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="form-outline">
                <input class="form-control" id="srch">
                <label class="form-label" for="srch">Search by PO Number or Vendor</label>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-row">
                <select onchange="changeType(this.value)" class="select" id="potype">
                    <option value="INV">Inventory</option>
                    <option value="RO">Repair Orders</option>
                </select>
                <label class="form-label select-label" for="potype">PO Type</label>
            </div>
        </div>
    </div>

    <table id="maintbl" class="sbdatatable w-100">
        <thead>
            <tr>
                <th>PO #</th>
                <th>Date Issue</th>
                <th>Supplier</th>
                <th>Status</th>
                <th>Delivery</th>
                <th>Expected</th>
                <th>Total PO</th>
                <th>Ordered By</th>
                <th>Type</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php
            $stmt = "select * from po where shopid = ? AND ordertype = 'INV' order by ponumber desc limit 1000";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("s", $shopid);
                $query->execute();
                $results = $query->get_result();
                $pistmt = "select sum(partcost*quantity) as pc from partsinventoryorder where shopid = ? and ponumber = ?";
                if ($piquery = $conn->prepare($pistmt)) {
                    $pi_po_number = 0;
                    $piquery->bind_param("si", $shopid, $pi_po_number);
                    while ($rs = $results->fetch_assoc()) {
                        $pi_po_number = $rs["ponumber"];
                        $piquery->execute();
                        $piquery->store_result();
                        $piquery->bind_result($pc);
                        $piquery->fetch();
                        if (is_null($pc)) {
                            $pc = asDollars(0);
                        } else {
                            if ($pc < 0)
                                $pc = '(' . asDollars(abs($pc)) . ')';
                            else
                                $pc = asDollars($pc);
                        }
            ?>
                        <tr>
                            <td><?= $rs['ponumber'] ?></td>
                            <td><?php echo date('m/d/Y', strtotime($rs['issuedate'])); ?></td>
                            <td><?php echo $rs['issuedto']; ?>&nbsp;</td>
                            <td><?php echo strtoupper($rs['status']); ?>&nbsp;</td>
                            <td><?php echo $rs['deliverymethod']; ?></td>
                            <td><?php echo date('m/d/Y', strtotime($rs['expectedarrival'])); ?></td>
                            <td><?php echo $pc; ?>&nbsp;</td>
                            <td><?php echo strtoupper($rs['orderedby']); ?>
                                &nbsp;
                            </td>
                            <td><?php echo $rs['ordertype']; ?></td>
                            <td>
                                <?php if ($rs["status"] != "Closed" && !$shopIsReadOnly): ?>
                                    <button type="button" class="btn btn-secondary btn-xs" onclick="location.href='inventoryorderreceive.php?id=<?php echo $rs["id"]; ?>'">Receive</button>
                                    <button type="button" class="btn btn-secondary btn-xs" onclick="location.href='inventoryorderedit.php?id=<?php echo $rs['id']; ?>'">Edit</button>
                                <?php endif; ?>
                                
                                <button type="button" class="btn btn-secondary" onclick="location.href='inventoryorderview.php?id=<?php echo $rs['id']; ?>'">View</button>
                                <button type="button" class="btn btn-secondary btn-xs" onclick="printPO('<?php echo $rs["id"]; ?>')">Print</button>
                            </td>
                        </tr>
            <?php
                    }
                    $piquery->close();
                }
                $query->close();
            } else {
                echo "po Prepare Failed: (" . $conn->errno . ") " . $conn->error;
            }
            ?>
        </tbody>
    </table>

    <div id="hider"></div>
</main>
<!-- END Main Container -->

<?php
include getScriptsGlobal('');
include getScriptsComponent($component);
include getFooterComponent($component);
?>
<script>
    function changeType(t) {

        ds = "shopid=<?php echo $shopid; ?>&type=" + t
        $.ajax({
            data: ds,
            url: "<?= COMPONENTS_PRIVATE ?>/v2/inventory/inventorypodata.php",
            type: "post",
            error: function(xhr, ajaxOptions, thrownError) {
            },
            success: function(r) {
                $('#maintbl').children("tbody").html(r)
                $('#srch').focus()
            }
        })

    }

    $(document).ready(function() {
        $(".sbdatatable").dataTable({
            paging: false,
            searching: false,
            responsive: true,
            fixedHeader: true,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            retrieve: true,
            order: []
        });
        $('#srch').focus()

    });

    $('#srch').keyup(function() {
        showLoader()
        searchTable($(this).val());

    });


    function searchTable(inputVal) {

        var table = $('#maintbl');

        table.find('tbody tr').each(function(index, row) {
            var allCells = $(row).find('td');
            if (allCells.length > 0) {
                var found = false;
                allCells.each(function(index, td) {
                    var regExp = new RegExp(inputVal, 'i');
                    if (regExp.test($(td).text())) {
                        found = true;
                        return false;
                    }
                });
                if (found == true) {
                    $(row).show();
                    hideLoader()
                } else {
                    $(row).hide();
                    $('#hide').toggle()
                }
            }
        });

    }


    function printExternalPage(url) {
        eModal.iframe({
            title: 'Print PO',
            url: url,
            size: eModal.size.md,
            buttons: [
            {
                text: 'Print',
                style: 'primary',
                close:false,
                click: function(){
                    contents = $("#emodal-box").find("iframe");
                    contents[0].contentWindow.focus();
                    contents[0].contentWindow.print();
                }
            }]
        });
    }

    function printPO(id, typ) {
        printExternalPage("<?= COMPONENTS_PRIVATE ?>/v2/po/inventoryorderprintpdf.php?typ=" + typ + "&id=" + id + "&shopid=<?php echo $shopid; ?>");
    }

    function showAll() {
        sbconfirm(
            'Are you sure?',
            'Depending on how many parts you have in stock, this list could take some time to load. Do you wish to continue?',
            function() {
                location.href = "inventory.php?showall=yes"
            }
        );
    }
</script>
</body>

</html>

<?php
mysqli_close($conn);
?>
