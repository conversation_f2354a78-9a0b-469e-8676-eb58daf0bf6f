﻿<!DOCTYPE html>
<?php
/**@var mysqli $conn */
require_once CONN;

$shopid = isset($_COOKIE['shopid']) ? filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING) : "";
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

if (!defined("ASSETS")) {
    define("ASSETS", "https://" . $_SERVER['SERVER_NAME'] . "/src/public/assets");
}

function hours_diff($date1, $date2)
{
    $date1 = date_create($date1);
    $date2 = date_create($date2);

    $diff = $date2->diff($date1);

    $hours = $diff->h;
    $hours = $hours + ($diff->days * 24);

    return $hours;
}

?>
<html>
<head>
    <meta charset='utf-8'/>
    <script src="<?= SCRIPT ?>/jquery.js"></script>
    <link href='<?= ASSETS ?>/empschedule/lib/fullcalendar.css' rel='stylesheet'/>
    <link href='<?= ASSETS ?>/empschedule/lib/fullcalendar.print.css' rel='stylesheet' media='print'/>
    <script src='<?= ASSETS ?>/empschedule/lib/moment.min.js'></script>
    <script src='<?= ASSETS ?>/empschedule/lib/jquery.min.js'></script>
    <script src='<?= ASSETS ?>/empschedule/lib/fullcalendar.min.js'></script>
    <script src="<?= SCRIPT ?>/plugins/jquery-ui/jquery-ui.js" type="text/javascript"></script>
    <script src="<?= ASSETS ?>/empschedule/datetimepicker/dtp.js" type="text/javascript"></script>
    <script src="<?= SCRIPT ?>/jquery.validate.min.js" type="text/javascript"></script>
    <link href='<?= ASSETS ?>/empschedule/css/dtp.css' rel='stylesheet'/>
    <title>Employee Schedule</title>
    <link type="text/css" rel="stylesheet" href="themes/areas.css?v=1757"/>
    <link type="text/css" rel="stylesheet" href="<?= SCRIPT ?>/plugins/jquery-ui/jquery-ui.css"/>

    <script>

        var delt;
        var shopIsReadOnly = <?= $shopIsReadOnly ? 'true' : 'false'; ?>;

        $(document).ready(function () {

            $('#calendar').fullCalendar({
                header: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'month,agendaWeek,agendaDay'
                },
                defaultView: 'agendaWeek',
                defaultDate: '<?= date("Y-m-d") ?>',
                editable: !shopIsReadOnly,
                eventLimit: true,
                droppable: true,
                minTime: "06:00:00",
                maxTime: "21:00:00",
                events: [
                    <?php
                    $stmt = "select * from schedule_employee where shopid = ?";
                    if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $results = $query->get_result();
                    while ($rs = $results->fetch_assoc()) {
                    $date1 = date_create($rs['startdate']);
                    $date2 = date_create($rs['enddate']);
                    $diff = date_diff($date1, $date2);
                    $hours = hours_diff($rs['startdate'], $rs['enddate']);
                    $startDateTS = strtotime($rs["startdate"]);
                    $endDateTs = strtotime($rs['enddate']);
                    if($hours > 24){
                    ?>
                    {
                        id: <?= $rs["ID"] ?>,
                        backgroundColor: '#<?= $rs["colorcode"] ?>',
                        textColor: "#000000",
                        title: '<?= str_replace("'", "\'", $rs["LastName"]) . " " . str_replace("'", "\'", $rs["Reason"]) ?>',
                        lastname: '<?= str_replace("'", "\'", $rs["LastName"]) ?>',
                        reason: '<?= str_replace("'", "\'", $rs["Reason"]); ?>',
                        start: '<?= $rs["startdate"] ?>',
                        end: '<?= $rs['enddate'] ?>',
                        allDay: true
                    },
                    <?php
                    } else {
                    ?>
                    {
                        id: <?= $rs["ID"] ?>,
                        backgroundColor: '#<?= $rs["colorcode"] ?>',
                        textColor: "#000000",
                        title: '<?= str_replace("'", "\'", $rs["LastName"]) . " " . str_replace("'", "\'", $rs["Reason"]) ?>',
                        lastname: '<?= str_replace("'", "\'", $rs["LastName"]) ?>',
                        reason: '<?= str_replace("'", "\'", $rs["Reason"]) ?>',
                        start: '<?= $rs["startdate"] ?>',
                        end: '<?= $rs['enddate'] ?>',
                        ctype: '<?= $rs['startdate'] == $rs['enddate'] ? 'same' : ''?>'
                    },
                    <?php
                    }
                    //   echo " },\n";

                    }
                    }
                    ?>
                ],
                eventClick: function (event) {
                    if (shopIsReadOnly) {
                        return;
                    }

                    $('#lastname').val("")
                    $('#reason').val("")
                    $('#sd').val("")
                    $('#ed').val("")
                    $('#color').val("")
                    $('#lastname').val(event.lastname)
                    $('#reason').val(event.reason)
                    $('#sd').val(event.start.format('MM/DD/YYYY HH:mm'))
                    console.log(event.end);
                    console.log(event.ctype);
                    if (event.ctype == 'same') {
                        $('#ed').val(event.start.format('MM/DD/YYYY HH:mm'))
                    }
                    else {
                        $('#ed').val(event.end.format('MM/DD/YYYY HH:mm'))
                    }
                    bc = event.backgroundColor
                    $('#color').css("background-color", bc)
                    bc = bc.replace("#", "")
                    $('#color option[value="' + bc + '"]').attr('selected', true)
                    $('#fullCalModal').dialog();
                    $('#fullCalModal').dialog({
                        buttons: {
                            'Delete': function () {
                                c = confirm("This will delete this appointment.  Are you sure?")
                                if (c) {

                                    // now delete from the database
                                    mydata = "type=delete&shopid=<?= $shopid ?>&id=" + event.id
                                    //console.log(mydata)
                                    $.ajax({
                                        url: "updateevent.php",
                                        data: mydata,
                                        success: function (result) {
                                            //console.log(result)
                                            if (result == "success") {
                                                $('#calendar').fullCalendar('removeEvents', event.id);
                                                $("#fullCalModal").dialog().dialog('close');
                                            }

                                        }
                                    });

                                }

                            },

                            'Save': function () {
                                mydata = "type=update&id=" + event.id + "&shopid=<?= $shopid ?>&sd=" + $('#sd').val() + "&ed=" + $('#ed').val() + "&ln=" + $('#lastname').val() + "&r=" + $('#reason').val() + "&color=" + $('#color').val()
                                $.ajax({
                                    url: "updateevent.php",
                                    data: mydata,
                                    success: function (result) {
                                        console.log(result)
                                        if (result == "success") {
                                            $('#calendar').fullCalendar('removeEvents', event.id);
                                            // check for difference between start and end to determine if its and all day evene
                                            sd = new Date($('#sd').val())
                                            ed = new Date($('#ed').val())
                                            timediff = Math.abs(sd - ed)
                                            timediff = (timediff / 60000) / 60
                                            //console.log(timediff)
                                            if (timediff > 24) {
                                                ad = true
                                            } else {
                                                ad = false
                                            }
                                            //console.log(ad)
                                            var newEvent = {
                                                title: $('#lastname').val() + " " + $('#reason').val(),
                                                start: $('#sd').val(),
                                                end: $('#ed').val(),
                                                backgroundColor: "#" + $('#color').val(),
                                                textColor: "#000000",
                                                lastname: $('#lastname').val(),
                                                reason: $('#reason').val(),
                                                id: event.id,
                                                allDay: ad,
                                                ctype : (($('#sd').val() == $('#ed').val())? "same":"")
                                            };
                                            $('#calendar').fullCalendar('renderEvent', newEvent, 'stick');
                                            $("#fullCalModal").dialog().dialog('close');
                                        }

                                    }
                                });

                            },
                            'Cancel': function () {

                                $(this).dialog('close');
                            }
                        }
                    });
                    $('#fullCalModal').dialog("option", "title", "Edit Schedule");
                    $('#fullCalModal').dialog({width: 400});
                    $('#fullCalModal').dialog('open');
                },
                dayClick: function (date, jsEvent, view) {
                    if (shopIsReadOnly) {
                        return;
                    }
                    
                    $('#lastname').val("")
                    $('#reason').val("")
                    $('#sd').val(date.format('MM/DD/YYYY HH:mm'))
                    $('#ed').val(date.format('MM/DD/YYYY HH:mm'))
                    $('#fullCalModal').dialog();
                    $('#fullCalModal').dialog({
                        buttons: {

                            'Save': function () {
                                mydata = "type=add&shopid=<?= $shopid ?>&sd=" + $('#sd').val() + "&ed=" + $('#ed').val() + "&ln=" + $('#lastname').val() + "&r=" + $('#reason').val() + "&color=" + $('#color').val()
                                $.ajax({
                                    url: "updateevent.php",
                                    data: mydata,
                                    success: function (result) {
                                        console.log(result)
                                        if (result.indexOf("success|") >= 0) {
                                            tar = result.split("|")
                                            aid = tar[1]
                                            sd = new Date($('#sd').val())
                                            ed = new Date($('#ed').val())
                                            timediff = Math.abs(sd - ed)
                                            timediff = (timediff / 60000) / 60
                                            //console.log(timediff)
                                            if (timediff > 24) {
                                                ad = true
                                            } else {
                                                ad = false
                                            }
                                            var newEvent = {
                                                title: $('#lastname').val() + " " + $('#reason').val(),
                                                start: $('#sd').val(),
                                                end: $('#ed').val(),
                                                backgroundColor: "#" + $('#color').val(),
                                                textColor: "#000000",
                                                lastname: $('#lastname').val(),
                                                reason: $('#reason').val(),
                                                id: aid,
                                                allDay: ad,
                                                ctype : (($('#sd').val() == $('#ed').val())? "same":"")
                                            };
                                            $('#calendar').fullCalendar('renderEvent', newEvent, 'stick');
                                            $("#fullCalModal").dialog().dialog('close');
                                        }

                                    }
                                });

                            },
                            'Cancel': function () {

                                $(this).dialog('close');
                            }
                        }
                    });
                    $('#fullCalModal').dialog("option", "title", "Create New Schedule");
                    $('#fullCalModal').dialog({width: 400});
                    $('#fullCalModal').dialog('open');
                },
                eventDrop: function (event, delta, revertFunc) {
                    console.log(event);
                    sd = event.start.format()
                    ed = event.end.format()
                    id = event.id
                    rid = event.resourceId
                    moveevent(id, sd, ed)
                },
                eventResize: function( event, jsEvent, ui, view ) {
                    ed = event.end.format()
                    sd = event.start.format()
                    id = event.id
                    moveevent(id, sd, ed);
                },

            });

            //$('#fc-view-container').css('height','100%')
            setTimeout(function () {
                $('#calendar').fullCalendar('refetchEvents');
                console.log("refresh events")
            }, 60000);


        });

        function moveevent(id,sd, ed){
            $.ajax({

                data: "type=move&shopid=&sd="+sd+"&ed="+ed+"&id="+id,
                url: "updateevent.php",
                type: "post",
                success: function(r){
                    if (r != "success"){
                        console.log(r);
                    }
                    $('#calendar').fullCalendar('refetchEvents');
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        }
    </script>
    <style>

        body {
            margin: 0px;
            padding: 0;
            font-family: "Lucida Grande", Helvetica, Arial, Verdana, sans-serif;
            font-size: 14px;
        }

        #calendar {
            max-width: 95%;
            margin: 0 auto;
        }

        #defaultview {
            position: absolute;
            right: 0px;
            top: 0px;
            padding: 10px;
            border: 1px black solid;
            border-radius: 5px;
            background-color: maroon;
            color: white;
            cursor: pointer
        }

        #backtosettings {
            position: absolute;
            left: 0px;
            top: 0px;
            padding: 10px;
            border: 1px black solid;
            border-radius: 5px;
            background-color: maroon;
            color: white;
            cursor: pointer
        }

        #defaultview:hover, #backtosettings:hover {
            background-color: #CC3300
        }
    </style>
</head>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<div style="background-image:url('<?= IMAGE ?>/newimages/wipheader.jpg');color:white;font-weight:bold;text-align:center;height:50px;margin-top:0px;font-size:large">
    Employee Schedule<br>
    <span style="font-size:small">Click an appointment to Edit, Click a time slot to Add</span>
    <span onclick="location.href='<?= COMPONENTS_PRIVATE ?>/wip/wip.php'" id="backtosettings">Back to WIP</span>
</div>

<div style="z-index:999;display:none" id="fullCalModal" class="modal fade">
    <form id="eventform" action="">
        <div class="modal-dialog">
            <div class="modal-content">
                <div id="modalBody" class="modal-body">
                    <table style="width: 100%">
                        <tr>
                            <td>Emp. Last Name:&nbsp;</td>
                            <td><input id="lastname" name="Text4" type="text" style="width: 200px" required/>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>Comments/Reason:&nbsp;</td>
                            <td><input id="reason" name="Text4" type="text" style="width: 200px"/>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>Start Date/Time:&nbsp;</td>
                            <td><input name="sd" type="text" id="sd" style="width: 200px" readonly required/>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>End Date/Time:&nbsp;</td>
                            <td><input name="ed" type="text" id="ed" style="width: 200px" readonly required/>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>Color:&nbsp;</td>
                            <td>
                                <select onclick="changeColor()" style="background-color:#CCFFCC" id="color"
                                        name="color">
                                    <option style="background-color:#CCFFCC" value="CCFFCC">Green</option>
                                    <option style="background-color:#FFFFCC" value="FFFFCC">Yellow</option>
                                    <option style="background-color:#CAE4FF" value="CAE4FF">Blue</option>
                                    <option style="background-color:#FFCC99" value="FFCC99">Orange</option>
                                    <option style="background-color:#FFCCFF" value="FFCCFF">Pink</option>
                                    <option style="background-color:#CC99FF" value="CC99FF">Purple</option>
                                    <option style="background-color:#DAB692" value="DAB692">Brown</option>
                                    <option style="background-color:#CCCCCC" value="CCCCCC">Silver</option>
                                </select>
                            </td>
                        </tr>
                    </table>

                </div>
                <div class="modal-footer">
                    <!--<button class="btn btn-primary"><a id="eventUrl" target="_blank">Event Page</a></button> -->

                    <br/>
                </div>
            </div>
        </div>
    </form>
</div>
<div id='calendar'></div>
<script>
    function changeColor() {

        $('#color').css("background-color", "#" + $('#color').val())

    }


    $.datetimepicker.setLocale('en');

    $('#sd').datetimepicker({
        format: 'm/d/Y H:i',
        timepickerScrollbar: true,
        step: 15,
        useCurrent: false
    });
    $('#ed').datetimepicker({
        format: 'm/d/Y H:i',
        timepickerScrollbar: true,
        step: 15,
        useCurrent : false
    });


</script>
</body>
</html>
