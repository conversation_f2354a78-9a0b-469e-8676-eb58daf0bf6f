<!DOCTYPE html>
<html>
<?php

$time_start = microtime(true);

require CONN;
//$shopid = $_COOKIE['shopid'];
$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$disabled = $shopIsReadOnly ? "disabled" : "";
$empid = (isset($_COOKIE['empid']) ? $_COOKIE['empid'] : '');

//$cid = $_GET['cid'];
$cid = filter_var($_GET['cid'], FILTER_SANITIZE_STRING);


//check for create part sale button
if ($empid == "Admin") {
    $createCT = "yes";
    $deletecustomer = "YES";
} else {
    $stmt = "select createCT,upper(deletecustomer) from employees where shopid = '$shopid' and id = '$empid'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($createCT, $deletecustomer);
        $query->fetch();
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
}

$vid = 0;
if (isset($_GET['vid'])) {
    $vid = filter_var($_GET['vid'], FILTER_SANITIZE_STRING);
    $stmt = "update vehicles set customerid = $cid where shopid = '$shopid' and vehid = $vid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
    } else {
        echo $conn->error;
    }
}

$yr = '';
$mk = '';
$md = '';
$cr = '';
$qvin = '';

if (isset($_GET['schid'])) {
    $quotestr = "&schid=" . filter_var($_GET['schid'], FILTER_SANITIZE_STRING);
} elseif (isset($_GET['quoteid'])) {
    $quotestr = "&quoteid=" . filter_var($_GET['quoteid'], FILTER_SANITIZE_STRING);
    $stmt = "select year,make,model,color,vin from quotes where shopid = '{$shopid}' and id = " . $_GET['quoteid'];
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($yr, $mk, $md, $cr, $qvin);
        $query->fetch();
        $query->close();
    }
} else {
    $yr = '';
    $mk = '';
    $md = '';
    $cr = '';
    $qvin = '';

    $quotestr = "";
}

$stmt = "select smsnum from smsnumbers where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->store_result();
    $rc = $query->num_rows();
    if ($rc > 0) {
        $query->bind_result($smsnum);
        $query->fetch();
    } else {
        $smsnum = "";
    }
    $query->close();
}


$stmt = "select readonly,shopnotice,customuserfield1,customuserfield2,customuserfield3,vehiclefield1label,vehiclefield2label,vehiclefield3label,vehiclefield4label,vehiclefield5label,vehiclefield6label,vehiclefield7label,vehiclefield8label,carfaxlocation,companystate,profitboost,cfpid from company where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($readonly, $shopnotice, $cu1, $cu2, $cu3, $cv1, $cv2, $cv3, $cv4, $cv5, $cv6, $cv7, $cv8, $cfl, $companystate, $isprofitboost, $cfpid);
    $query->fetch();
    $query->close();
} else {
    echo "1 Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select showcfp,notesalert from settings where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($showcfp,$notesalert);
    $query->fetch();
    $query->close();
}

$stmt = "select yearlabel,makelabel,modellabel,enginelabel,cylinderlabel,translabel,licenselabel,statelabel,fleetlabel,currmileagelabel,colorlabel,drivelabel,vinlabel from vehiclelabels where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($yearlabel, $makelabel, $modellabel, $enginelabel, $cylinderlabel, $translabel, $licenselabel, $statelabel, $fleetlabel, $currmileagelabel, $colorlabel, $drivelabel, $vinlabel);
    $query->fetch();
    $query->close();
} else {
    echo "2 Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

if (empty($yearlabel)) $yearlabel = "Year";
if (empty($makelabel)) $makelabel = "Make";
if (empty($modellabel)) $modellabel = "Model";
if (empty($enginelabel)) $enginelabel = "Engine";
if (empty($cylinderlabel)) $cylinderlabel = "Cylinders";
if (empty($translabel)) $translabel = "Transmission";
if (empty($licenselabel)) $licenselabel = "License";
if (empty($statelabel)) $statelabel = "License State";
if (empty($fleetlabel)) $fleetlabel = "Fleet Number";
if (empty($currmileagelabel)) $currmileagelabel = "Current Mileage";
if (empty($colorlabel)) $colorlabel = "Color";
if (empty($drivelabel)) $drivelabel = "Drive Type";
if (empty($vinlabel)) $vinlabel = "VIN";


$stmt = "select `column 27`,preferredcontactmethod,CustomerID,LastName,FirstName,Address,City,State,Zip,HomePhone,WorkPhone,CellPhone,Pager,Fax,EMail,Wholesale,UserDefined1,UserDefined2,UserDefined3,Comments,ServiceReminder,Specials,Follow,Discount,contact,cellprovider,active,taxexempt,spousename,spousecell,spousework,shippingto,shippingaddress,shippingcity,shippingstate,shippingzip,shippingphone,billto,billtoaddress,billtocity,billtostate,billtozip,billtophone,creditlimit,extension,customertype,onhold,accounttype,notes from customer where shopid = '$shopid' and customerid = $cid";

if ($query = $conn->prepare($stmt)) {

    $query->execute();
    $query->store_result();
    $num_stmt_rows = $query->num_rows;
    if ($num_stmt_rows > 0) {
        $query->bind_result($discountpercent, $preferredcontactmethod, $CustomerID, $LastName, $FirstName, $Address, $City, $State, $Zip, $HomePhone, $WorkPhone, $CellPhone, $Pager, $Fax, $EMail, $Wholesale, $UserDefined1, $UserDefined2, $UserDefined3, $Comments, $ServiceReminder, $Specials, $Follow, $Discount, $contact, $cellprovider, $active, $taxexempt, $spousename, $spousecell, $spousework, $shippingto, $shippingaddress, $shippingcity, $shippingstate, $shippingzip, $shippingphone, $billto, $billtoaddress, $billtocity, $billtostate, $billtozip, $billtophone, $creditlimit, $extension, $customertype, $onhold, $accounttype, $notes);
        $query->fetch();
    } else {
        $discountpercent = "";
        $preferredcontactmethod = "";
        $CustomerID = "";
        $LastName = "";
        $FirstName = "";
        $Address = "";
        $City = "";
        $State = "";
        $Zip = "";
        $HomePhone = "";
        $WorkPhone = "";
        $CellPhone = "";
        $Pager = "";
        $Fax = "";
        $EMail = "";
        $Wholesale = "";
        $UserDefined1 = "";
        $UserDefined2 = "";
        $UserDefined3 = "";
        $Comments = "";
        $ServiceReminder = "";
        $Specials = "";
        $Follow = "";
        $Discount = "";
        $contact = "";
        $cellprovider = "";
        $active = "";
        $taxexempt = "";
        $spousename = "";
        $spousecell = "";
        $spousework = "";
        $shippingto = "";
        $shippingaddress = "";
        $shippingcity = "";
        $shippingstate = "";
        $shippingzip = "";
        $shippingphone = "";
        $billto = "";
        $billtoaddress = "";
        $billtocity = "";
        $billtostate = "";
        $billtozip = "";
        $billtophone = "";
        $creditlimit = "";
        $extension = "";
        $customertype = "";
        $onhold = "";
        $accounttype = "";
    }
    $query->close();
} else {
    echo "Customer failed: (" . $conn->errno . ") " . $conn->error;
}

if ($qvin != '') {
    $decodevin = $qvin;
} elseif (isset($_GET['vin'])) {
    $decodevin = trim(filter_var($_GET['vin'], FILTER_SANITIZE_STRING));
} else {
    $decodevin = "";
}

// Get outstanding balance set for > 10000 for now

$highbal = 0;

$stmt = "SELECT sum(balance) as balance  ";
$stmt .= "FROM repairorders ";
$stmt .= "WHERE shopid = ? ";
$stmt .= "  AND customerid = ? ";
$stmt .= " AND rotype != 'NO APPROVAL'";
$stmt .= "  AND round(balance,2) > 0 ";
//echo $stmt;

if ($query =  $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $cid);
    $query->execute();
    $roresult = $query->get_result();
} else {
    echo "Repair Order credit limit query failed (" . $conn->errno . ") " . $conn->error;
}

$ro = mysqli_fetch_assoc($roresult);

$highbal = $ro["balance"];

$currdate = date('Y-m-d');
$appts = array();
$stmt = "select schdate,schtime,year,make,model from schedule where shopid = ? and customerid = ? and schdate > ? AND deleted = 'no'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $cid, $currdate);
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array())
        $appts[] = $rs;
}

$cfpphone = $cfpaltphone = $cfplink = '';

if($showcfp=='yes' && !empty($cfpid))
{
    $phones = array($CellPhone,$HomePhone, $WorkPhone,$spousecell,$spousework);
    foreach($phones as $phone)
    {
        if(!empty($phone))
        {
            if (empty($cfpphone)) {
                $cfpphone = $phone;
            } elseif ($phone != $cfpphone && empty($cfpaltphone)) $cfpaltphone = $phone;
        }
    }

    $data = array("merchantId" => $cfpid, "phone" => $cfpphone,"altphone"=>$cfpaltphone, "zip" => $Zip, "firstName" => $FirstName, "lastName" => $LastName, "street" => $Address, "state" => $State, "city" => $City, "email" => $EMail, "middle" => '', 'amount' => '');

    if (in_array($shopid, array('13846', '6062'))) {
        $data["partnerKey"] = "SBPS9teaU";
        $url = "https://p2-6182--partial.sandbox.my.site.com/360payments/services/apexrest/remotelend?" . http_build_query($data); //sandbox
    } else {
        $data["partnerKey"] = "Sb24F110";
        $url = "https://360-partners.force.com/360payments/services/apexrest/remotelend?" . http_build_query($data);
    }

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_URL, $url);
    $result = curl_exec($ch);
    curl_close($ch);

    $cfpresult = json_decode($result);

    if (isset($cfpresult->applyUrl) && !empty($cfpresult->applyUrl)) {
        $href = $cfpresult->applyUrl;
        $cfplink = '<a href="' . $href . '" target="_blank">' . $cfpresult->result . ' Click to Apply</a>';
    }
}

?>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><![endif]-->
<!--> <html class="no-focus"> <!--<![endif]-->

<head>
    <meta charset="utf-8">

    <title><?= getPageTitle() ?></title>

    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon' />
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        .modal-lg-history {
            width: 80% !important;
            /* respsonsive width */
            /*margin-left:-40%;  width/2) */
        }

        .table-borderless tbody tr td,
        .table-borderless tbody tr th,
        .table-borderless thead tr th {
            border: none;

        }

        .modal-big {
            max-height: 75%;
            overflow-y: auto;
        }

        .sbp-form-control-sm {
            text-transform: uppercase;
            color: #999999;
            font-weight: bold;
            padding: 3px 20px 3px 20px !important;
            border-radius: 3px;
            border: 1px silver solid;
            width: 200px;
        }

        #vehtotals {
            color: red;
            padding-top: 10px;

        }

        .select2-results__message {
            display: none !important;
        }
    </style>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<div style="position:absolute;top:0px;left:0%;width:100%;display:none;z-index:9999;text-align:center" class="alert alert-success" id="saved">Changes have been saved</div>
<!-- Page Container -->
<!--
        Available Classes:

        'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

        'sidebar-l'                  Left Sidebar and right Side Overlay
        'sidebar-r'                  Right Sidebar and left Side Overlay
        'sidebar-mini'               Mini hoverable Sidebar (> 991px)
        'sidebar-o'                  Visible Sidebar by default (> 991px)
        'sidebar-o-xs'               Visible Sidebar by default (< 992px)

        'side-overlay-hover'         Hoverable Side Overlay (> 991px)
        'side-overlay-o'             Visible Side Overlay by default (> 991px)

        'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

        'header-navbar-fixed'        Enables fixed header
    -->
<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
    <!-- Side Overlay-->
    <aside id="side-overlay">
        <!-- Side Overlay Scroll Container -->
        <div id="side-overlay-scroll">
            <!-- Side Header -->
            <div class="side-header side-content">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default pull-right" type="button" data-toggle="layout" data-action="side_overlay_close">
                    <i class="fa fa-times"></i>
                </button>
                <span>
                        <img class="img-avatar img-avatar32" src="<?= IMAGE ?>/avatars/avatar10.jpg" alt="">
                        <span class="font-w600 push-10-l">Walter Fox</span>
                    </span>
            </div>
            <!-- END Side Header -->

        </div>
        <!-- END Side Overlay Scroll Container -->
    </aside>
    <!-- END Side Overlay -->

    <!-- Sidebar -->
    <nav id="sidebar">
        <!-- Sidebar Scroll Container -->
        <div id="sidebar-scroll">
            <!-- Sidebar Content -->
            <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
            <div class="sidebar-content">
                <!-- Side Header -->
                <div class="side-header side-content bg-white-op">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                        <i class="text-primary">
                            <?php getLogo() ?></i>
                        <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                    </a>
                </div>
                <!-- END Side Header -->

                <!-- Side Content -->
                <div class="side-content">
                    <ul class="nav-main">
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-wrench"></i><span class="sidebar-mini-hide">Work In Process</span></a>
                        </li>
                        <li>
                            <a href="#" onclick="history.go(-1)"><i class="fa fa-arrow-left"></i><span class="sidebar-mini-hide">Back to Search</span></a>
                        </li>

                    </ul>
                </div>
                <!-- END Side Content -->
            </div>
            <!-- Sidebar Content -->
        </div>
        <!-- END Sidebar Scroll Container -->
    </nav>
    <!-- END Sidebar -->

    <!-- Header -->
    <header id="header-navbar" class="content-mini content-mini-full">
        <!-- Header Navigation Right -->
        <ul class="nav-header pull-right">
            <li>
                <div id="shopnotice">
                    <?php echo $_COOKIE['shopname'] . " #" . $shopid . "<br>" . $_COOKIE['username'] . '<a class="btn btn-primary btn-sm btn-logoff" href="' . COMPONENTS_PUBLIC . '/login/logoff.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Logoff</span></a>'; ?>
                </div>
            </li>
        </ul>
        <!-- END Header Navigation Right -->

        <!-- Header Navigation Left -->

        <ul class="nav-header pull-left">
            <li class="hidden-md hidden-lg">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                    <i class="fa fa-navicon"></i>
                </button>
            </li>
            <li class="hidden-xs hidden-sm">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                    <i class="fa fa-bars"></i>
                </button>
            </li>
            <li>
                <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                    <i class="si si-grid"></i>
                </button>
            </li>
            <li class="visible-xs">
                <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                    <i class="fa fa-search"></i>
                </button>
            </li>
            <li>
                <?php if (strlen($shopnotice) > 0) {
                    echo '<div class="shopnotice">' . $shopnotice . '</div>';
                } ?>
            </li>
        </ul>

        <!-- END Header Navigation Left -->
    </header>
    <!-- END Header -->

    <!-- Main Container -->
    <main class="container-fluid" id="main-container">
        <form id="mainform" class="form-horizontal push-10-t" name="mainform">
            <div class="row">
                <div class="sbp-header">
                    <?php if ($shopIsReadOnly) : ?>
                        VIEW
                    <?php else : ?>
                        EDIT
                    <?php endif; ?>
                    CUSTOMER / VEHICLE INFORMATION
                </div>
                <?php if(!empty($cfplink))echo("<center>$cfplink</center><br>");?>
                <?php
                if ($creditlimit > 0 && $highbal > $creditlimit) {
                    ?>
                    <div style="text-align:center;padding:20px;margin-bottom:10px;font-weight:bold;font-size:x-large;text-transform:uppercase" class="alert-danger">This customer is over their credit limit!</div>
                    <?php
                }
                ?>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" tabindex="1" value="<?php echo  $LastName; ?>" type="text" id="lastname" name="lastname" <?= $disabled?> >
                                <label for="lastname">Last Name / Company</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9" style="left: 0px; top: 0px">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" tabindex="2" value="<?php echo $FirstName; ?>" type="text" id="firstname" name="firstname" <?= $disabled?>>
                                <label for="firstname">First Name</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" tabindex="3" value="<?php echo $Address; ?>" type="text" id="address" name="address" <?= $disabled?>>
                                <label for="address">Address</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" onblur="getCS(this.value)" style="padding:10px;" tabindex="4" value="<?php echo $Zip; ?>" type="text" id="zip" name="zip" <?= $disabled?>>
                                <label for="zip">Zip/Postal Code</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" tabindex="5" type="text" id="city" value="<?php echo $City; ?>" name="city" <?= $disabled?>>
                                <label for="city" id="cityfloatinglabel">City</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" tabindex="6" type="text" id="state" value="<?php echo $State; ?>" name="state" <?= $disabled?>>
                                <label for="state" id="statefloatinglabel">State</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-md-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" tabindex="7" type="email" id="email" value="<?php echo $EMail; ?>" name="email" <?= $disabled?>>
                                <label for="">Email</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-md-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="7" type="text" id="contact" name="contact" value="<?php echo strtoupper($contact); ?>" <?= $disabled?>>
                                <label for="contact" for="">Business Contact</label>
                            </div>
                        </div>
                    </div>
                    <?php if ($isprofitboost == 'yes') { ?>
                        <br>
                        <div class="form-group">
                            <div class="col-md-9">
                                <div class="form-material floating">
                                    <select class="form-control" style="padding:10px;" size="1" tabindex="16" id="accounttype" name="accounttype" <?= $disabled?>>
                                        <option value="">Select</option>
                                        <?php
                                        $stmt = "select type from pbaccounttypes where shopid = '" . $shopid . "' order by type";
                                        $result = $conn->query($stmt);
                                        while ($row = $result->fetch_array()) { ?>
                                            <option <?= $accounttype == $row['type'] ? 'selected' : '' ?>><?= $row['type'] ?></option>
                                        <?php } ?>
                                    </select>
                                    <label for="accounttype" id="accounttypelabel">Account Type</label>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <div class="form-group">
                        <div class="col-md-9">
                            <div id="notecreditlimit" class="form-material floating" style="color:red">
                                <?php
                                $tcnt = 0;
                                $tro = 0;
                                $sdate = "";
                                // now we fetch customer history
                                $stmt = "SELECT count(*) c, sum(totalro) tro from repairorders where shopid = ? and customerid = ? and status = 'closed' and rotype!= 'no approval'";
                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("si", $shopid, $CustomerID);
                                    $query->execute();
                                    $query->bind_result($tcnt, $tro);
                                    $query->fetch();
                                    $query->close();
                                }
                                //get last visit
                                $stmt = "select statusdate from repairorders where shopid = ? and customerid = ? and status = 'closed' and rotype != 'no approval' order by roid desc limit 1";
                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("si", $shopid, $CustomerID);
                                    $query->execute();
                                    $query->bind_result($sdate);
                                    $query->fetch();
                                    $query->close();
                                }

                                if ($tcnt > 0) {
                                    $tavg = round($tro / $tcnt, 2);
                                    echo "Customer Sales Data:<br>";
                                    echo "RO Count: " . $tcnt . " @ " . $tavg . " = " . round($tro, 2);
                                    echo "<br>Last RO: " . date("m/d/Y", strtotime($sdate));
                                }

                                if (!empty($appts)) {
                                    echo ("<br><span style='color:#000'>Future Appointments:<br><ul style='padding-left:20px'>");
                                    foreach ($appts as $appt) {
                                        echo ("<li>" . date('m/d/Y - g:iA', strtotime($appt['schdate'] . ' ' . $appt['schtime'])) . " - " . $appt['year'] . ' ' . $appt['make'] . ' ' . $appt['model'] . "</li>");
                                    }
                                    echo ("</ul></span>");
                                }
                                ?>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" type="text" tabindex="8" id="homephone" value="<?php echo $HomePhone; ?>" name="homephone" onKeyUp="javascript:return mask(this.value,this,'3,7','-');" onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled?>>
                                <label for="homephone">Home Phone</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" type="text" tabindex="9" id="workphone" value="<?php echo $WorkPhone; ?>" name="workphone" onKeyUp="javascript:return mask(this.value,this,'3,7','-');" onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled?>>
                                <label for="workphone">Work Phone</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" type="text" tabindex="10" id="extension" value="<?php echo $extension; ?>" name="extension" <?= $disabled?>>
                                <label for="extension">Ext.</label>
                            </div>
                        </div>
                    </div>


                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" type="text" tabindex="11" id="cellphone" value="<?php echo $CellPhone; ?>" name="cellphone" onKeyUp="javascript:return mask(this.value,this,'3,7','-');" onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled?>>
                                <label for="cellphone">Cell Phone <?php if (strlen($CellPhone) > 0 && $smsnum != "") {
                                        echo "<a href='#' onclick='sendSMS($CellPhone)'>Send Text Message</a>";
                                    } ?></label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" type="text" tabindex="12" id="fax" value="<?php echo $Fax; ?>" name="fax" onKeyUp="javascript:return mask(this.value,this,'3,7','-');" onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled?>>
                                <label for="fax">Fax</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" type="text" tabindex="13" id="spousename" value="<?php echo $spousename; ?>" name="spousename" <?= $disabled?>>
                                <label for="spousename">Other Contact Name</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" type="text" tabindex="14" id="spousework" value="<?php echo $spousework; ?>" name="spousework" onKeyUp="javascript:return mask(this.value,this,'3,7','-');" onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled?>>
                                <label for="spousework">Other Contact Work Phone</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" type="text" tabindex="15" id="spousecell" value="<?php echo $spousecell; ?>" name="spousecell" onKeyUp="javascript:return mask(this.value,this,'3,7','-');" onBlur="javascript:return mask(this.value,this,'3,7','-');" <?= $disabled?>>
                                <label for="spousecell">Other Contact Cell Phone</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9" style="border:1px red solid;padding-top:10px;">
                            <div class="form-material floating">
                                <textarea class="form-control sbp-form-control" style="padding:10px;" type="text" tabindex="15" id="notes" name="notes" <?= $disabled?>>
                                    <?php echo $notes; ?>
                                </textarea>
                                <label for="comments">Alerts</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating"><?php //echo $customertype;
                                ?>
                                <select class="form-control" id="customertype" style="padding:10px;" tabindex="16" name="customertype" size="1" <?= $disabled?>>
                                    <option <?php if (trim(strtolower($customertype)) == 'cash') {
                                        echo 'selected';
                                    } ?> value="cash">CASH</option>
                                    <option <?php if (strtolower($customertype) == 'net 10') {
                                        echo 'selected';
                                    } ?> value="net 10">NET 10</option>
                                    <option <?php if (strtolower($customertype) == 'net 15') {
                                        echo 'selected';
                                    } ?> value="net 15">NET 15</option>
                                    <option <?php if (strtolower($customertype) == 'net 30') {
                                        echo 'selected';
                                    } ?> value="net 30">NET 30</option>
                                    <option <?php if (strtolower($customertype) == 'net 60') {
                                        echo 'selected';
                                    } ?> value="net 60">NET 60</option>
                                    <option <?php if (strtolower($customertype) == 'net 90') {
                                        echo 'selected';
                                    } ?> value="net 90">NET 90</option>
                                </select>
                                <label for="customertype">Customer Pay Type</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating"><?php //echo $customertype;
                                ?>
                                <select class="form-control" id="preferredcontactmethod" style="padding:10px;" tabindex="16" name="preferredcontactmethod" size="1" <?= $disabled?>>

                                    <option <?php if (strtolower($preferredcontactmethod) == "text") {
                                        echo "selected";
                                    } ?> value="text">Text</option>
                                    <option <?php if (strtolower($preferredcontactmethod) == "phone") {
                                        echo "selected";
                                    } ?> value="phone">Phone</option>
                                    <option <?php if (strtolower($preferredcontactmethod) == "email") {
                                        echo "selected";
                                    } ?> value="email">Email</option>
                                </select>
                                <label for="customertype">Preferred Contact Method</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <label style="font-weight:bold" class="css-input switch switch-primary switch-md">
                                <input type="checkbox" id="follow" onchange="save(this.id)" <?= $disabled?> <?php if (strtolower($Follow) == 'yes') {
                                    echo 'checked';
                                } ?>><span></span> Follow Up</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <label style="font-weight:bold" class="css-input switch switch-primary switch-md">
                                <input type="checkbox" id="taxexempt" onchange="save(this.id)" <?= $disabled?><?php if (strtolower($taxexempt) == 'yes') {
                                    echo 'checked';
                                } ?>><span></span> Tax Exempt</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <label style="font-weight:bold" class="css-input switch switch-primary switch-md">
                                <input type="checkbox" id="active" onchange="<?= (strtolower($active) == 'yes' && $deletecustomer != 'YES') ? '' : 'save(this.id)' ?>" <?= $disabled?> <?= (strtolower($active) == 'yes' && $deletecustomer != 'YES') ? 'disabled="disabled"' : '' ?> <?php if (strtolower($active) == 'yes') {
                                    echo 'checked';
                                } ?>><span></span> Active</label>
                        </div>
                    </div>
                    <br>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;<?php if (strlen($cu1) == 0) {
                                    echo 'display:none;';
                                } ?>" type="text" tabindex="13" id="userdefined1" value="<?php echo $UserDefined1; ?>" name="customuserfield1" <?= $disabled?>>
                                <label for="userdefined1"><?php echo strtoupper($cu1); ?></label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;<?php if (strlen($cu2) == 0) {
                                    echo 'display:none;';
                                } ?>" type="text" tabindex="14" id="userdefined2" value="<?php echo $UserDefined2; ?>" name="customuserfield2" <?= $disabled?>>
                                <label for="userdefined2"><?php echo strtoupper($cu2); ?></label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;<?php if (strlen($cu3) == 0) {
                                    echo 'display:none;';
                                } ?>" type="text" tabindex="15" id="userdefined3" value="<?php echo $UserDefined3; ?>" name="customuserfield" <?= $disabled?>>
                                <label for="userdefined3"><?php echo strtoupper($cu3); ?></label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:10px;" type="text" tabindex="15" id="discountpercent" value="<?php echo $discountpercent; ?>" name="discountpercent" <?= $disabled?>>
                                <label for="discountpercent">Discount Percent (Number ONLY (15 for 15%)</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <select class="form-control sbp-form-control" id="Discount" style="padding:10px" <?= $disabled?>>
                                    <?php
                                    $pd = "";
                                    $ld = "";
                                    $bd = "";
                                    if ($Discount == "") {
                                        echo '<option value="none">Select</option>';
                                    } elseif (strtolower($Discount) == "parts") {
                                        $pd = "selected";
                                    } elseif (strtolower($Discount) == "labor") {
                                        $ld = "selected";
                                    } elseif (strtolower($Discount) == "partsandlabor") {
                                        $bd = "selected";
                                    }
                                    ?>
                                    <option <?php echo $pd; ?> value="parts">Parts</option>
                                    <option <?php echo $ld; ?> value="labor">Labor</option>
                                    <option <?php echo $bd; ?> value="partsandlabor">Parts and Labor</option>
                                </select>
                                <label for="discountpercent">Apply Discount To:</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-9" style="padding-top:10px;">
                            <div class="form-material floating">
                                <textarea class="form-control sbp-form-control" style="padding:10px;" type="text" tabindex="15" id="comments" name="comments" <?= $disabled?>>
                                    <?php echo $Comments; ?>
                                </textarea>
                                <label for="comments">Comments</label>
                            </div>
                        </div>
                    </div>

                    <?php if ( !$shopIsReadOnly ) : ?>
                        <div class="form-group">
                            <div class="col-sm-9">
                                <div class="alert alert-success">
                                    <br><span>Any changes made here will be auto-saved.</span>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

            </div>


            <div class="row" id="vehiclelist">
                <div class="col-md-12">

                    <div class="row">
                        <div class="col-md-6" style="left: 0px; top: 0px">
                            <div>

                                <?php
                                if ($creditlimit > 0 && $highbal > $creditlimit) {
                                    ?>
                                    <button id="bscr" type="button" onclick="$('#shipmodal').modal('show')" class="btn btn-primary">Billing / Shipping / Credit Limit</button>

                                    <?php
                                } else {
                                    ?>
                                    <button type="button" onclick="$('#shipmodal').modal('show')" class="btn btn-primary">Billing / Shipping / Credit Limit</button>
                                    <?php
                                } // end if of credit limit chech
                                ?>

                                <?php if ( !$shopIsReadOnly ) : ?>
                                    <button type="button" onclick="$('#addvehmodal').modal('show')" class="btn btn-success">Add Vehicle</button>
                                    <?php if (strtolower($createCT) == "yes") : ?>
                                        <button type="button" onclick="location.href='<?= COMPONENTS_PRIVATE ?>/partsale/createps.php?id=<?php echo $cid; ?>'" class="btn btn-danger">Create Part Sale</button>
                                    <?php endif; ?>
                                    <?php if ($deletecustomer == 'YES') : ?>
                                        <button type="button" onclick="delCustomer()" class="btn btn-warning">Mark Customer Inactive</button>
                                    <?php endif; ?>
                                    <button type="button" onclick="updateAllROs()" class="btn btn-danger">Apply Current Info to All Repair Orders</button>
                                <?php endif; ?>
                            </div>

                            <input type="text" id="vehiclefilter" onkeyup="getVehicles(this.value)" style="margin-top:10px;text-transform:uppercase" placeholder="Search Vehicles by VIN, Year Make Model, Fleet #" class="form-control">
                        </div>
                    </div>
                    <h3>Vehicles</h3>
                    <table id="vehtable" class="table table-condensed table-striped table-hover table-header-bg">
                        <thead>
                        <tr>
                            <td style="height: 34px">Year/Make/Model</td>
                            <td style="height: 34px">Transmission</td>
                            <td style="height: 34px">VIN</td>
                            <td style="height: 34px">Fleet #</td>
                            <td style="height: 34px">License</td>
                            <?php if ( !$shopIsReadOnly ) : ?>
                                <td style="height: 34px;text-align: right">Delete</td>
                            <?php endif; ?>
                        </tr>
                        </thead>
                        <tbody id="table-body">
                        <?php
                        if (isset($_GET['cid'])) {
                            $cid = $_GET['cid'];
                            $stmt = "select year,make,model,vin,fleetno,licnumber,vehid,transmission from vehicles where shopid = ? and customerid = ?";

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("si", $shopid, $cid);
                                $query->execute();
                                $result = $query->get_result();
                                while ($row = $result->fetch_array()) {
                                    ?>
                                    <tr>
                                        <td onclick="editVehicle(<?php echo $row['vehid']; ?>)"><?php echo strtoupper($row['year'] . ' ' . $row['make'] . ' ' . $row['model']); ?></td>
                                        <td onclick="editVehicle(<?php echo $row['vehid']; ?>)"><?php echo strtoupper($row['transmission']); ?></td>
                                        <td onclick="editVehicle(<?php echo $row['vehid']; ?>)"><?php echo strtoupper($row['vin']); ?></td>
                                        <td onclick="editVehicle(<?php echo $row['vehid']; ?>)"><?php echo strtoupper($row['fleetno']); ?></td>
                                        <td onclick="editVehicle(<?php echo $row['vehid']; ?>)"><?php echo strtoupper($row['licnumber']); ?></td>
                                        <?php if ( !$shopIsReadOnly ) : ?>
                                            <td style="text-align: right"><button class="btn btn-sm btn-danger" onclick="$('#vehid').val('<?= $row['vehid'] ?>');delVehicle()" type="button">Delete</button></td>
                                        <?php endif; ?>
                                    </tr>

                                    <?php
                                }
                            }
                        }
                        ?>
                        </tbody>
                    </table>

                </div>
            </div>

        </form>
    </main>
    <!-- END Main Container -->

    <!-- Footer -->
    <!-- END Footer -->
</div>
<!-- END Page Container -->

<div id="vehmodal" class="modal fade" id="modal-large" data-keyboard="false" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden" value="<?php echo $cid; ?>">
    <input id="vehid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">
                        <?php if ( !$shopIsReadOnly ) : ?>
                            Edit
                            <?php else : ?>
                                View
                        <?php endif; ?>
                        Vehicle Information
                    </h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div id="vehiclemaininfo" class="col-md-6">
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" type="text" id="year" name="year" <?= $disabled; ?>>
                                        <label for="year" id="yearlabel"><?php echo strtoupper($yearlabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="2" type="text" id="make" name="make" <?= $disabled; ?>>
                                        <label for="make" id="makelabel"><?php echo strtoupper($makelabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="3" type="text" id="model" name="model" <?= $disabled; ?>>
                                        <label for="model" id="modellabel"><?php echo strtoupper($modellabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="4" type="text" id="vin" name="vin" <?= $disabled; ?>>
                                        <label for="vin" id="vinlabel"><?php echo strtoupper($vinlabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="5" type="text" id="licnumber" name="licnumber" <?= $disabled; ?>>
                                        <label for="licnumber" id="licenselabel"><?php echo strtoupper($licenselabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="6" type="text" id="transmission" name="transmission" <?= $disabled; ?>>
                                        <label for="transmission" id="transmissionlabel"><?php echo strtoupper($translabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="7" type="text" id="engine" name="engine" <?= $disabled; ?>>
                                        <label for="engine" id="enginelabel"><?php echo strtoupper($enginelabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" type="text" tabindex="8" id="cyl" name="cyl" <?= $disabled; ?>>
                                        <label for="cyl" id="cyllabel"><?php echo strtoupper($cylinderlabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" type="text" tabindex="9" id="drivetype" name="drivetype" <?= $disabled; ?>>
                                        <label for="drivetype" id="drivetypelabel"><?php echo strtoupper($drivelabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" type="text" tabindex="10" id="color" name="color" <?= $disabled; ?>>
                                        <label for="color" id="colorlabel"><?php echo strtoupper($colorlabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" type="text" tabindex="10" id="fleetno" name="fleetno" <?= $disabled; ?>>
                                        <label for="fleetno" id="fleetnolabel"><?php echo strtoupper($fleetlabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" type="text" tabindex="10" id="licstate" name="licstate" <?= $disabled; ?>>
                                        <label for="licstate" id="statelabel"><?php echo strtoupper($statelabel); ?></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" type="text" id="currentmileage" name="currentmileage" <?= $disabled; ?>>
                                        <label for="currentmileage" id="currentmileagelabel"><?php echo strtoupper($currmileagelabel); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div id="custom1group" class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv1) == 0) {
                                            echo 'display:none;';
                                        } ?>" type="text" tabindex="10" id="custom1" name="custom1" <?= $disabled; ?>>
                                        <label for="custom1" id="custom1label"><?php echo strtoupper($cv1); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div id="custom2group" class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv2) == 0) {
                                            echo 'display:none;';
                                        } ?>" type="text" tabindex="10" id="custom2" name="custom2" <?= $disabled; ?>>
                                        <label for="custom2" id="custom2label"><?php echo strtoupper($cv2); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div id="custom3group" class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv3) == 0) {
                                            echo 'display:none;';
                                        } ?>" type="text" tabindex="10" id="custom3" name="custom3" <?= $disabled; ?>>
                                        <label for="custom3" id="custom3label"><?php echo strtoupper($cv3); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div id="custom4group" class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv4) == 0) {
                                            echo 'display:none;';
                                        } ?>" type="text" tabindex="10" id="custom4" name="custom4" <?= $disabled; ?>>
                                        <label for="custom4" id="custom4label"><?php echo strtoupper($cv4); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div id="custom5group" class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv5) == 0) {
                                            echo 'display:none;';
                                        } ?>" type="text" tabindex="10" id="custom5" name="custom5" <?= $disabled; ?>>
                                        <label for="custom5" id="custom5label"><?php echo strtoupper($cv5); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div id="custom6group" class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv6) == 0) {
                                            echo 'display:none;';
                                        } ?>" type="text" tabindex="10" id="custom6" name="custom6" <?= $disabled; ?>>
                                        <label for="custom6" id="custom6label"><?php echo strtoupper($cv6); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div id="custom7group" class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv7) == 0) {
                                            echo 'display:none;';
                                        } ?>" type="text" tabindex="10" id="custom7" name="custom7" <?= $disabled; ?>>
                                        <label for="custom7" id="custom7label"><?php echo strtoupper($cv7); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div id="custom8group" class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv8) == 0) {
                                            echo 'display:none;';
                                        } ?>" type="text" tabindex="10" id="custom8" name="custom8" <?= $disabled; ?>>
                                        <label for="custom8" id="custom8label"><?php echo strtoupper($cv8); ?></label>
                                    </div>
                                </div>
                            </div>
                            <div id="notesdiv" class="form-group">
                                <div class="col-sm-9" style="max-height:200px;overflow-y:scroll">
                                    <div class="form-material floating" id="vehiclenotes"></div>

                                </div>
                            </div>
                            <?php if (!$shopIsReadOnly) : ?>
                                <div id="" class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <span class="btn btn-info" onclick="addNotes()">Add/Edit Notes</span>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <div id="" class="form-group">
                                <div class="col-sm-9">
                                    <div id="vehtotals"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-danger" onclick="recommendRepairs()" type="button">Recommended Repairs</button>
                <?php if (!$shopIsReadOnly) : ?>
                    <button class="btn btn-md btn-primary" onclick="transferVehicle()" type="button">Transfer to New Owner</button>
                    <button class="btn btn-md btn-info" onclick="createappointment()" type="button">Create Appointment</button>
                <?php endif; ?>
                <button class="btn btn-md btn-danger" onclick="showHistory()" type="button">History</button>

                <?php if (!$shopIsReadOnly && $_COOKIE['createro'] == 'yes') : ?>
                    <button class="btn btn-md btn-success" type="button" onclick="createNewRO('<?php echo $onhold; ?>')">Create RO</button>
                <?php endif; ?>

                <button class="btn btn-md btn-default" type="button" onclick="location.reload()">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="shipmodal" class="modal fade" id="modal-large" data-keyboard="false" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">
                        <?php if ($shopIsReadOnly) : ?>
                            Edit 
                        <?php else : ?>
                            View
                        <?php endif; ?>
                        Billing / Shipping / Credit Limit
                    </h3>
                </div>
                <div id="shopinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div id="vehiclemaininfo" class="col-md-6">
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" value="<?php echo $shippingto; ?>" tabindex="1" type="text" id="shippingto" name="shippingto" <?= $disabled; ?>>
                                        <label for="shippingto" id="shippingtolabel">Shipping To:</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" value="<?php echo $shippingaddress; ?>" tabindex="2" type="text" id="shippingaddress" name="shippingaddress" <?= $disabled; ?>>
                                        <label for="shippingaddress" id="shippingaddresslabel">Shipping Address:</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="3" value="<?php echo $shippingcity; ?>" type="text" id="shippingcity" name="shippingcity" <?= $disabled; ?>>
                                        <label for="shippingcity" id="shippingcitylabel">Shipping City</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="4" value="<?php echo $shippingstate; ?>" type="text" id="shippingstate" name="shippingstate" <?= $disabled; ?>>
                                        <label for="shippingstate" id="shippingstatelabel">Shipping State</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="5" value="<?php echo $shippingzip; ?>" type="text" id="shippingzip" name="shippingzip" <?= $disabled; ?>>
                                        <label for="shippingzip" id="shippingziplabel">Shipping Zip</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="6" value="<?php echo $shippingphone; ?>" type="text" id="shippingphone" name="shippingphone" <?= $disabled; ?>>
                                        <label for="shippingphone" id="shippingphonelabel">Shipping Phone</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" tabindex="7" value="<?php echo $creditlimit; ?>" type="text" id="creditlimit" name="creditlimit" <?= $disabled; ?>>
                                        <label for="creditlimit" id="creditlimitlabel">Credit Limit</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" value="<?php echo $billto; ?>" type="text" id="billto" name="billto" <?= $disabled; ?>>
                                        <label for="billto" id="billtolabel">Bill To:</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" value="<?php echo $billtoaddress; ?>" type="text" tabindex="10" id="billtoaddress" name="billtoaddress" <?= $disabled; ?>>
                                        <label for="billtoaddress" id="billtoaddresslabel">Bill To Address</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" value="<?php echo $billtocity; ?>" type="text" tabindex="10" id="billtocity" name="billtocity" <?= $disabled; ?>>
                                        <label for="billtocity" id="billtocitylabel">Bill To City</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" value="<?php echo $billtostate; ?>" type="text" tabindex="10" id="billtostate" name="billtostate" <?= $disabled; ?>>
                                        <label for="billtostate" id="billtostatelabel">Bill To State</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" value="<?php echo $billtozip; ?>" type="text" tabindex="10" id="billtozip" name="billtozip" <?= $disabled; ?>>
                                        <label for="billtozip" id="billtoziplabel">Bill To Zip</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="form-material floating">
                                        <input class="form-control sbp-form-control" style="padding:20px;" value="<?php echo $billtophone; ?>" type="text" tabindex="10" id="billtophone" name="billtophone" <?= $disabled; ?>>
                                        <label for="billtophone" id="billtophonelabel">Bill To Phone</label>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="historymodal" class="modal fade modal-big" id="modal-large" data-keyboard="false" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-lg-history">
        <div class="block block-themed block-transparent remove-margin-b">
            <div class="modal-content">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Vehicle History</h3>
                </div>

                <div class="block-content modal-big">
                    <div class="row">
                        <div class="col-md-12"></div>
                        <div id="historycontent" class="modal-body"></div>
                    </div>
                </div>
                <div style="margin-top:20px;" class="modal-footer">
                    <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>


<div id="notesmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Add/Edit Vehicle Notes</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <textarea class="form-control sbp-form-control" style="padding:20px;height:250px;" tabindex="1" id="vehnotes" name="vehnotes"></textarea>
                                    <label for="vehnotes">Notes</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-warning" type="button" onclick="saveNotes()">Save Notes</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<form id="vehaddform">
    <div id="addvehmodal" class="modal fade" id="modal-large" data-keyboard="false" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true">
        <input id="customerid" type="hidden">
        <div class="modal-dialog modal-lg" id="modelDialog1">
            <div class="modal-content">
                <div class="block block-themed block-transparent remove-margin-b">
                    <div class="block-header bg-primary-dark">
                        <ul class="block-options">
                            <li>
                                <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                            </li>
                        </ul>
                        <h3 class="block-title">Add Vehicle</h3>
                    </div>
                    <div id="vehinfo" class="block-content"></div>
                    <div class="block-content">
                        <div class="row">
                            <div id="vehiclemaininfo" class="col-md-6">
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" onblur="" style="padding:20px;" type="text" value="<?php echo $decodevin; ?>" id="addvin" name="addvin">
                                            <button style="display:block" type="button" onclick="decodeVIN()" class="btn btn-primary btn-sm">Decode VIN</button>
                                            <label for="addvin" id="addvinlabel"><?php echo strtoupper($vinlabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;" type="text" id="addyear" name="addyear" value="<?php echo $yr; ?>">
                                            <label for="addyear" id="addyearlabel"><?php echo strtoupper($yearlabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" list="makelist" style="padding:20px;" onblur="getModelList()" onfocus="adjustLabel('addmakelabel')" type="text" autocomplete="off" id="addmake" name="addmake" value="<?php echo $mk; ?>">
                                            <datalist id="makelist">
                                                <?php
                                                // data = ["ABS System Diagnosis","ABS Wheel Speed Sensor Replacement","AC Compressor Replacement"
                                                $data = array();
                                                $stmt = "select make from makes";
                                                if ($query = $conn->prepare($stmt)) {
                                                    $query->execute();
                                                    $r = $query->get_result();
                                                    $c = 0;
                                                    while ($rs = $r->fetch_array()) {
                                                        echo "<option value='" . $rs['make'] . "'>";
                                                    }
                                                }
                                                ?>
                                            </datalist>

                                            <label for="addmake" id="addmakelabel"><?php echo strtoupper($makelabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" list="modellist" onfocus="adjustLabel('addmodellabel')" style="padding:20px;" type="text" id="addmodel" name="addmodel" value="<?php echo $md; ?>">
                                            <datalist id="modellist"></datalist>
                                            <label for="addmodel" id="addmodellabel"><?php echo strtoupper($modellabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-md-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;" type="text" id="addengine" name="addengine">
                                            <label for="addengine" id="addenginelabel"><?php echo strtoupper($enginelabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;" type="text" id="addcyl" name="addcyl">
                                            <label for="addcyl" id="addcyllabel"><?php echo strtoupper($cylinderlabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;" type="text" id="adddrivetype" name="adddrivetype">
                                            <label for="adddrivetype" id="adddrivetypelabel"><?php echo strtoupper($drivelabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;" type="text" id="addcolor" name="addcolor" value="<?php echo $cr; ?>">
                                            <label for="addcolor" id="addcolorlabel"><?php echo strtoupper($colorlabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;" type="text" id="addfleetno" name="addfleetno">
                                            <label for="addfleetno" id="fleetnolabel"><?php echo strtoupper($fleetlabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;" type="text" id="addlicnumber" name="addlicnumber">
                                            <label for="addlicnumber" id="addlicnumberlabel"><?php echo strtoupper($licenselabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <select class="form-control sbp-form-control" style="padding:20px;" id="addtransmission" name="addtransmission" style="color: red;"></select>
                                            <label for="addtransmission" id="addtransmissionlabel"><?php echo strtoupper($translabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;" type="text" id="addvehstate" name="addvehstate" value="<?= $companystate ?>">
                                            <label for="addvehstate" id="addstatelabel"><?php echo strtoupper($statelabel); ?></label>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;" type="text" id="addcurrmiles" name="addcurrmiles">
                                            <label for="addcurrmiles" id="addcurrmileslabel"><?php echo strtoupper($currmileagelabel); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div id="custom1group" class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv1) == 0) {
                                                echo 'display:none;';
                                            } ?>" type="text" id="addcustom1" name="addcustom1">
                                            <label for="addcustom1" id="custom1label"><?php echo strtoupper($cv1); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div id="custom2group" class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv2) == 0) {
                                                echo 'display:none;';
                                            } ?>" type="text" id="addcustom2" name="addcustom2">
                                            <label for="addcustom2" id="custom2label"><?php echo strtoupper($cv2); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div id="custom3group" class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv3) == 0) {
                                                echo 'display:none;';
                                            } ?>" type="text" id="addcustom3" name="addcustom3">
                                            <label for="addcustom3" id="custom31label"><?php echo strtoupper($cv3); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div id="custom4group" class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv4) == 0) {
                                                echo 'display:none;';
                                            } ?>" type="text" id="addcustom4" name="addcustom4">
                                            <label for="addcustom4" id="custom4label"><?php echo strtoupper($cv4); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div id="custom5group" class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv5) == 0) {
                                                echo 'display:none;';
                                            } ?>" type="text" id="addcustom5" name="addcustom5">
                                            <label for="addcustom5" id="custom5label"><?php echo strtoupper($cv5); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div id="custom6group" class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv6) == 0) {
                                                echo 'display:none;';
                                            } ?>" type="text" id="addcustom6" name="addcustom6">
                                            <label for="addcustom6" id="custom6label"><?php echo strtoupper($cv6); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div id="custom7group" class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv7) == 0) {
                                                echo 'display:none;';
                                            } ?>" type="text" id="addcustom7" name="addcustom7">
                                            <label for="addcustom7" id="custom7label"><?php echo strtoupper($cv7); ?></label>
                                        </div>
                                    </div>
                                </div>
                                <div id="custom8group" class="form-group">
                                    <div class="col-sm-9">
                                        <div class="form-material floating">
                                            <input class="form-control sbp-form-control" style="padding:20px;<?php if (strlen($cv8) == 0) {
                                                echo 'display:none;';
                                            } ?>" type="text" id="addcustom8" name="addcustom8">
                                            <label for="addcustom8" id="custom8label"><?php echo strtoupper($cv8); ?></label>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                    </div>
                </div>
                <div style="margin-top:20px;" class="modal-footer">
                    <button class="btn btn-md btn-primary" onclick="launchNewScanner()" type="button">BOSS Vin</button>
                    <?php
                    if ($cfl != 'no') {
                        ?>
                        <button class="btn btn-md btn-danger" onclick="openCarFax()" type="button">Open Carfax</button>
                        <?php
                    }
                    ?>
                    <!-- if read only dont allow saving of vehicle -->
                    <?php
                    if ($readonly == "no") {
                        ?>
                        <button class="btn btn-md btn-warning" onclick="saveVehicle('')" type="button">Save Vehicle</button>
                        <?php
                    }
                    ?>

                    <button class="btn btn-md btn-success" onclick="saveAndCreateRO()" type="button">Save and Create RO</button>
                    <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</form>

<div id="smsmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Send Text Message</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" type="text" value="" id="smscell" name="smscell">
                                    <label id="smscelllabel" for="material-text2">Cell Number</label>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <textarea class="form-control sbp-form-control" style="padding:20px;" tabindex="2" id="smsmsg" name="smsmsg"></textarea>
                                    <label for="material-text2">Message</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-warning" type="button" onclick="sendSMSMessage()">Send Message</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>
<div id="emailRRModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="z-index: 99999 !important;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Email Recommended Repairs To Customer</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" id="RRemailto" name="RRemailto" value="<?php echo $EMail; ?>" type="text">
                                    <label for="RRemailto">Email Address</label>
                                </div>
                            </div>
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" type="text" style="padding:20px;" tabindex="1" value="Your Recommended Repairs List from <?php echo $_COOKIE['shopname']; ?>" id="RRemailsubject" name="RRemailsubject" placeholder="Subject">
                                    <label for="RRemailsubject">Subject</label>
                                </div>
                            </div>
                            <br><br>
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <textarea class="form-control sbp-form-control" type="text" style="padding:20px;height:100px;" tabindex="1" id="RRemailmessage" name="RRemailmessage" placeholder="Message">Your Recommended Repairs List from  <?php echo str_replace("#","",str_replace("&"," and ",$_COOKIE['shopname'])) ?> </textarea>
                                    <label for="RRemailmessage">Message</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <input type="hidden" id="RR_HTML" name="RR_HTML" />
                <button class="btn btn-md btn-info" type="button" onclick="sendRREmail()">Send Email</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="scanmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Scan VIN</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div id="vehiclemaininfo" style="text-align:center" class="col-md-12">
                            <p>If this is your first time scanning a VIN, please install the app on your phone or tablet by clicking the appropriate button below. If you have already installed
                                the app, click the Scan VIN button. After scanning, you can retrieve your scanned VIN by clicking Retrieve Scanned VIN button</p>
                            <p id="scannedvins"></p>
                            <button style="width:250px;" class="btn btn-primary btn-lg" type="button" onclick="launchScanner()">SCAN VIN</button>
                            <button style="width:250px;" class="btn btn-success btn-lg" type="button" onclick="getScannedVins()">
                                Retrieve Scanned VIN</button>
                            <br><br>
                        </div>
                    </div>

                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-warning btn-sm" type="button" onclick="installAndroid()">Install Android App</button>
                <button class="btn btn-warning btn-sm" type="button" onclick="installApple()">Install Apple App</button>
                <button class="btn btn-default btn-sm" type="button" onclick="$('#scanmodal').modal('hide')">Close</button>
            </div>
        </div>
    </div>

</div>

<div id="newvinscanmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">New VIN Scan</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div style="text-align:center" class="col-md-12">
                            <div id="pictureslot" style="height: 200px;display: none;margin-bottom: 20px;"></div>
                            <div class="btn-group" style="text-align: center;">
                                <button type="button" onclick="$('#ocrimage').click()" class="btn btn-warning">Upload Picture</button>
                            </div>
                            <form name="ocrform" enctype="multipart/form-data" style="display: none;">
                                <input type="file" class="form-control" name="ocrimage" id="ocrimage" accept="image/png,image/jpeg,image/png,image/gif,image/bmp,android/force-camera-workaround">
                            </form>
                            <br><br>
                        </div>
                    </div>

                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-success" id="btn-ocrscan" type="button">Scan</button>
                <button class="btn btn-default" type="button" onclick="$('#newvinscanmodal').modal('hide')">Close</button>
            </div>
        </div>
    </div>
</div>
<!-- Apps Modal -->
<div id="carfaxmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Carfax Vehicle Lookup</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" onblur="$('#cflic').focus()" style="padding:20px;" tabindex="1" type="text" value="" id="cfvin" name="cfvin">
                                    <label for="material-text2">VIN</label>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" onblur="$('#cfst').focus()" style="padding:20px;" tabindex="2" type="text" id="cflic" name="cflic">
                                    <label for="material-text2">License</label>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" style="padding:20px;" tabindex="3" type="text" id="cfst" name="cfst">
                                    <label for="material-text2">License State</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-warning" type="button" onclick="quickVIN()">Lookup</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
<script src="<?= SCRIPT ?>/tipped.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>

<script src="<?= SCRIPT ?>/app.js"></script>
<script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
<script src="<?= SCRIPT ?>/plugins/masked-inputs/jquery.maskedinput.min.js"></script>
<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/easymodal/easymodal.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/typeahead/typeahead.js"></script>


<!-- Page Plugins -->

<script>
    var shopIsReadOnly = <?= $shopIsReadOnly ? 'true' : 'false'; ?>;

    function updateAllROs() {

        swal({
                title: "Are you sure?",
                text: "Are you sure you want to UPDATE ALL repair orders for this customer to this information",
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                html: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Yes, UPDATE ALL RO's",
                closeOnConfirm: false
            },
            function() {
                $.ajax({

                    data: "shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>",
                    url: "customer-update-all-ros.php",
                    type: "post",
                    success: function(r) {
                        console.log(r)
                        if (r == "success") {
                            swal("All RO's successfully updated to this information")
                        }
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                });
            });

    }

    function sendSMS(cell) {

        $('#smsmodal').modal('show')
        $('#smscell').val(cell)
        adjustLabel("smscelllabel")
        setTimeout(function() {
            $('#smsmsg').focus()
        }, 500)

    }

    function sendSMSMessage() {

        to = $('#smscell').val();
        msg = encodeURIComponent($('#smsmsg').val())

        $.ajax({
            data: "sendsms=save&from=<?php echo $smsnum; ?>&to=" + to + "&msg=" + msg,
            type: "post",
            url: "<?php echo INTEGRATIONS; ?>/bandwidth/sendsmsv2.php",
            success: function(r) {
                if (r == "success") {
                    $('#smsmodal').modal('hide')
                    swal("Message sent successfully")
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        })

    }

    // blink billing shipping credit limit button if over credit limit
    setInterval(function() {
        $("#bscr").toggleClass("btn-danger btn-primary");
    }, 1000); // put timeout here, e.g. 1000 milliseconds is 1 second

    function adjustLabel(labelname) {

        // addmakelabel
        $('#' + labelname).css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")


    }

    function getModelList() {

        make = $('#addmake').val()
        $.ajax({
            data: "make=" + make,
            url: "customer-model-list.php",
            success: function(r) {
                $('#modellist').append(r)
            }
        })

    }

    jQuery(function() {
        // Init page helpers (Slick Slider plugin)
        App.initHelpers('slick');

    });

    function mask(str, textbox, loc, delim) {
        var x = event.which || event.keyCode;
        if (x != 8) {
            var locs = loc.split(',');
            for (var i = 0; i <= locs.length; i++) {
                for (var k = 0; k <= str.length; k++) {
                    if (k == locs[i]) {
                        if (str.substring(k, k + 1) != delim) {
                            str = str.substring(0, k) + delim + str.substring(k, str.length)
                        }

                    }

                }

            }
            textbox.value = str
        }
    }

    function showHistory() {
        // close the vehicle modal, open large modal with history
        eModal.iframe({
            title: 'Repair History',
            url: "customer-vehicle-history.php?shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&vid=" + $('#vehid').val(),
            size: eModal.size.xl,
            buttons: [{
                text: 'Close',
                style: 'warning',
                close: true
            }]
        });

    }

    function recommendRepairs() {
        let buttons = [
            {
                text: 'Print',
                style: 'info',
                click : function (){
                    $('iframe').attr("id","emodal-iframe")                    
                    rr = document.getElementById("emodal-iframe")
                    rr.focus()
                    rr.contentWindow.scrollTo(0,0);
                    setTimeout(function() {
                        rr.contentWindow.print()
                    }, 700);
                }
            },
            {
                text: 'Close',
                style: 'warning',
                close: true
            }
        ];
        if (!shopIsReadOnly) {
            buttons.push({
                text: 'Email',
                style: 'primary',
                click : function (){
                    $('#emodal-box iframe').attr("id","rr_iframe")
                    rr = document.getElementById("rr_iframe")
                    $("#emailRRModal").modal('show')
                }
            });
        }
        // close the vehicle modal, open large modal with recommended repairs
        eModal.iframe({
            title: 'Recommended Repairs',
            url: "customer-recommend-repair.php?shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&vid=" + $('#vehid').val(),
            size: eModal.size.xl,
            buttons: buttons
        });

    }

    function sendRREmail(){
        //   console.log("send email");
            $('#spinner').show()
            sendto = encodeURIComponent($('#RRemailto').val())
            emailsubject = encodeURIComponent($('#RRemailsubject').val())
            emailmessage = encodeURIComponent($('#RRemailmessage').val())
        if (sendto != "" && emailsubject != "" && emailmessage != "") {
            ds = "shopid=<?php echo $shopid; ?>&cid=<?php echo $CustomerID; ?>&sendfrom=<?php echo urlencode($_COOKIE['shopname']);?>&sendto=" + sendto + "&subject=" + emailsubject + "&message=" + emailmessage + "&vid=" + $('#vehid').val()
            $.ajax({
                data: ds,
                url: "<?= COMPONENTS_PRIVATE ?>/customer/recrepair_email.php",
                method: "POST",
                success: function(r){
                    r = r.trim()
                    if (r == "success"){
                        swal("Recommended Repairs Sent")
                        $('#emailRRModal').modal('hide')
                    }else{
                        console.log(r)
                        swal(r)
                    }

                    $('#spinner').hide()
                }
            });

        }
    }

    function transferVehicle() {

        eModal.iframe({
            title: 'Select Customer',
            url: "customer-select-transfer.php?shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&vid=" + $('#vehid').val(),
            size: eModal.size.xl,
            buttons: [{
                text: 'Close',
                style: 'warning',
                close: true
            }]
        });

    }

    function createappointment() {

        var cvid = "<?php echo $cid . '-' ?>" + $('#vehid').val()

        localStorage.setItem("calendarcvid", cvid)
        location.href = '<?= COMPONENTS_PRIVATE ?>/calendar/calendar.php'

    }

    function delCustomer() {

        swal({
                title: "Are you sure?",
                text: "Are you sure you want to mark this customer inactive?",
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                html: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Mark Customer Inactive",
                closeOnConfirm: false
            },
            function() {
                $.ajax({

                    data: "t=customer&shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>",
                    url: "customer-delete.php",
                    type: "post",
                    success: function(r) {
                        console.log(r)
                        if (r == "success") {
                            location.href = 'customer-find.php'
                        }
                    },

                });
            });

    }

    function delVehicle() {

        vid = $('#vehid').val()
        swal({
                title: "Are you sure?",
                text: "Are you sure you want to delete this Vehicle?",
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                html: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Delete Vehicle",
                closeOnConfirm: false
            },
            function() {
                $.ajax({

                    data: "t=vehicle&shopid=<?php echo $shopid; ?>&vid=" + vid,
                    url: "customer-delete.php",
                    type: "post",
                    success: function(r) {
                        console.log(r)
                        if (r == "success") {
                            location.reload()
                        }
                    },

                });
            });

    }


    function getVehicles(srchfor) {

        srchfor = encodeURIComponent(srchfor)
        ds = "shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&sf=" + srchfor
        console.log(ds)
        $.ajax({
            data: ds,
            url: "customer-vehicle-filter.php",
            success: function(r) {
                $('#table-body').html(r)
            }
        });

    }

    function decodeVIN() {

        vin = $('#addvin').val()
        vin = vin.trim()
        p = false

        if (vin.length == 17) {
            p = true
        }

        if (p == true) {

            $('#spinner').show()
            $('#decoding').show()
            $.ajax({
                data: "vin=" + vin + "&carfaxloc=<?= $cfl?>",
                url: "vehicle-vin-decode.php",
                dataType: "json",
                error: function(xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function(r) {

                    if (r.status == 'success') {
                        rvin = r.vin
                        yr = r.yr
                        mk = r.mk
                        md = r.md
                        tr = r.tr
                        bd = r.bd
                        en = r.en
                        tra = r.tra.split("|")

                        dr = r.dr

                        $('#addyear').val(yr)
                        $('#addmake').val(mk)
                        $('#addmodel').val(md + " " + tr + " " + bd)
                        $('#addengine').val(en)
                        $('#addtransmission').html('')
                        for (var t of tra) {
                            $('#addtransmission').append("<option>" + t.trim() + "</option>")
                        }
                        $('#adddrivetype').val(dr)
                        console.log("decoding vin")
                        if (yr.length > 0) {
                            $('#addyearlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (mk.length > 0) {
                            $('#addmakelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (md.length > 0) {
                            $('#addmodellabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (en.length > 0) {
                            $('#addenginelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (tra.length > 0) {
                            $('#addtransmissionlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (dr.length > 0) {
                            $('#adddrivetypelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        $('#cyl').focus()
                        $('#spinner').hide()
                        $('#decoding').fadeOut('slow')
                    } else {
                        $('#spinner').hide()
                        $('#decoding').fadeOut('slow')
                        swal("The VIN was not able to be decoded.  Please verify the VIN number")
                    }
                }
            });
        } else {
            swal("You must enter a 17 digit VIN to decode it")
        }


    }

    function createNewRO(onhold) {
        //console.log ("Onhold flag" + onhold)
        if (onhold == "yes") {
            swal("This customer is on a credit hold. Please go to Accounting - Accounts Receivable to release this hold.")
        } else {
            // check for a current RO for this customer and vehicle
            $.post({
                data: 'shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&vid=' + $('#vehid').val(),
                url: "searchopenro.php",
                error: function(xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function(r) {
                    if (r == "0") {
                        swal({
                            title: "Are you sure?",
                            text: "Create RO for this Vehicle?",
                            type: "warning",
                            showCancelButton: true,
                            cancelButtonClass: "btn-default",
                            html: true,
                            confirmButtonClass: "btn-danger",
                            confirmButtonText: "Create RO",
                            closeOnConfirm: false
                        }, function() {
                            location.href = '<?= COMPONENTS_PRIVATE ?>/createro/addconcerns.php?cid=<?php echo $cid . $quotestr; ?>&vid=' + $('#vehid').val()
                        });
                    } else {
                        swal({
                            title: "WARNING - WARNING",
                            text: "There is an existing RO for this customer and vehicle. Do you want to create a possible duplicate",
                            type: "warning",
                            showCancelButton: true,
                            cancelButtonClass: "btn-default",
                            html: true,
                            confirmButtonClass: "btn-danger",
                            confirmButtonText: "Create RO",
                            closeOnConfirm: false
                        }, function() {
                            location.href = '<?= COMPONENTS_PRIVATE ?>/createro/addconcerns.php?cid=<?php echo $cid . $quotestr; ?>&vid=' + $('#vehid').val()
                        });
                    }
                }
            });
        }
    }

    function cannotcreateNewRO() {
        //console.log("You are in Cannot Create New RO")

        swal("Can not create RO. Customer is on hold")


    }

    function openCarFax() {

        $('#addvehmodal').modal('hide')
        $('#carfaxmodal').modal('show')

    }

    function saveAndCreateRO() {

        if ($('#addmake').val() == "") {
            swal("Please add make of the vehicle");
            return;
        }

        // save the vehicle, get the vehid, the redirect to the location.href='../createro/addconcerns.php?cid='+$('#customerid').val()+'&vid='+vid
        ds = $('#vehaddform').serialize()
        ds = ds + "&shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>"
        $.ajax({

            data: ds + "&dupcheck=yes",
            type: "post",
            url: "customer-vehicle-add.php",
            success: function(r) {console.log(r);
                if (r.indexOf("success") >= 0) {
                    // now redirect to create RO
                    rar = r.split("|")
                    vid = rar[1]
                    location.href = '<?= COMPONENTS_PRIVATE ?>/createro/addconcerns.php?cid=<?php echo $cid . $quotestr; ?>&vid=' + vid
                } else if (r.indexOf("duplicate") >= 0) {
                    var cusarr = r.split('duplicate|');
                    swal({
                        title: "Are you sure?",
                        text: "Vehicle with this VIN already belongs to "+cusarr[1]+". Do you still want to continue?",
                        type: "warning",
                        showCancelButton: true,
                        cancelButtonClass: "btn-default",
                        confirmButtonClass: "btn-danger",
                        confirmButtonText: "Yes",
                        cancelButtonText: "No",
                        closeOnConfirm: false
                    }, function() {

                        $.ajax({

                            data: ds,
                            type: "post",
                            url: "customer-vehicle-add.php",
                            success: function(r) {
                                if (r.indexOf("success") >= 0) {
                                    // now redirect to create RO
                                    rar = r.split("|")
                                    vid = rar[1]
                                    location.href = '<?= COMPONENTS_PRIVATE ?>/createro/addconcerns.php?cid=<?php echo $cid . $quotestr; ?>&vid=' + vid
                                }
                            }
                        });
                    });

                }
            }

        });

    }

    function save(id) {

        if ($('#' + id).is(':checked')) {
            ds = "t=checkbox&shopid=<?php echo $shopid; ?>&i=" + id + "&v=yes&cid=<?php echo $cid; ?>"
        } else {
            ds = "t=checkbox&shopid=<?php echo $shopid; ?>&i=" + id + "&v=no&cid=<?php echo $cid; ?>"
        }

        //console.log(ds)
        $.ajax({
            type: "get",
            data: ds,
            url: "customer-update.php",
            success: function(r) {
                $('#saved').show()
                setTimeout(function() {
                    $('#saved').fadeOut(1500)
                }, 1000);

            }
        });

    }

    function quickVIN() {
        $('#spinner').show()
        vin = $('#cfvin').val()
        lic = $('#cflic').val()
        st = $('#cfst').val()
        proceed = false

        if (vin.length == 17) {
            proceed = true
        }
        if (lic.length > 0 && st.length > 0) {
            proceed = true
        }

        if (proceed == false) {
            swal("VIN or License and State are required")
            return
        }

        if (proceed == true) {
            // get the veh info from quickvin
            ds = "vin=" + vin + "&lic=" + lic + "&lst=" + st + "&shopid=<?php echo $shopid; ?>"
            $.ajax({
                data: ds,
                url: "<?= INTEGRATIONS ?>/carfax/carfax-quickvin.php",
                success: function(r) {
                    if (r.indexOf("success") >= 0) {
                        //console.log(r)
                        rar = r.split("|")
                        //"success|".$yr."|".$mk."|".$md."|".$cyl."|".$en."|".$tr."|".$dr."|".$vin;
                        yr = rar[1]
                        mk = rar[2]
                        md = rar[3]
                        cyl = rar[4]
                        en = rar[5]
                        tr = rar[6]
                        dr = rar[7]
                        rvin = rar[8]
                        if (rvin.length == 17) {
                            vin = rvin
                        }

                        $('#carfaxmodal').modal('hide').on('hidden.bs.modal', function() {
                            $('#addvehmodal').modal('show')
                        })

                        // close the carfax modal, open the addvehmodal and enter the values
                        $('#addvin').val(rvin)
                        $('#addyear').val(yr)
                        $('#addmake').val(mk)
                        $('#addmodel').val(md)
                        $('#addengine').val(en)

                        if(st != '')
                        $('#addvehstate').val(st)
                        
                        if(tr!='')
                        $('#addtransmission').html("<option>" + tr.trim() + "</option>")

                        $('#adddrivetype').val(dr)
                        $('#addlicnumber').val(lic)

                        if (yr.length > 0) {
                            $('#addyearlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (mk.length > 0) {
                            $('#addmakelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (md.length > 0) {
                            $('#addmodellabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (en.length > 0) {
                            $('#addenginelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (vin.length > 0) {
                            $('#addvinlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (cyl.length > 0) {
                            $('#addcyllabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (tr.length > 0) {
                            $('#addtransmissionlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (dr.length > 0) {
                            $('#adddrivetypelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (lic.length > 0) {
                            $('#addlicnumberlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        $('#spinner').hide()
                    } else {
                        $('#carfaxmodal').modal('hide')
                        $('#addvehmodal').modal('show')
                        swal("No result found for this license or VIN")
                        $('#spinner').hide()
                    }

                }
            });
        }

    }


    // original values
    lastname = "<?php echo str_replace('"', '\"', $LastName); ?>"
    firstname = "<?php echo str_replace('"', '\"', $FirstName); ?>"
    address = "<?php echo str_replace('"', '\"', $Address); ?>"
    city = "<?php echo $City; ?>"
    state = "<?php echo $State; ?>"
    zip = "<?php echo $Zip; ?>"
    homephone = "<?php echo $HomePhone; ?>"
    workphone = "<?php echo $WorkPhone; ?>"
    cellphone = "<?php echo $CellPhone; ?>"
    fax = "<?php echo $Fax; ?>"
    email = "<?php echo $EMail; ?>"
    userdefined1 = "<?php echo $UserDefined1; ?>"
    userdefined2 = "<?php echo $UserDefined2; ?>"
    userdefined3 = "<?php echo $UserDefined3; ?>"
    comments = "<?php echo addslashes(str_replace(chr(13), ' ', str_replace(chr(10), ' ', $Comments))); ?>"
    follow = "<?php echo $Follow; ?>"
    contact = "<?php echo $contact; ?>"
    active = "<?php echo $active; ?>"
    taxexempt = "<?php echo $taxexempt; ?>"
    spousename = "<?php echo str_replace('"', '', $spousename); ?>"
    spousecell = "<?php echo $spousecell; ?>"
    spousework = "<?php echo $spousework; ?>"
    shippingto = "<?php echo $shippingto; ?>"
    shippingaddress = "<?php echo $shippingaddress; ?>"
    shippingcity = "<?php echo $shippingcity; ?>"
    shippingstate = "<?php echo $shippingstate; ?>"
    shippingzip = "<?php echo $shippingzip; ?>"
    shippingphone = "<?php echo $shippingphone; ?>"
    billto = "<?php echo $billto; ?>"
    billtoaddress = "<?php echo $billtoaddress; ?>"
    billtocity = "<?php echo $billtocity; ?>"
    billtostate = "<?php echo $billtostate; ?>"
    billtozip = "<?php echo $billtozip; ?>"
    billtophone = "<?php echo $billtophone; ?>"
    creditlimit = "<?php echo $creditlimit; ?>"
    customertype = "<?php echo $customertype; ?>"

    vehid = ''
    year = ''
    make = ''
    model = ''
    miles = ''
    license = ''
    vin = ''
    engine = ''
    drive = ''
    trans = ''
    cyl = ''
    fleetno = ''
    state = ''
    color = ''
    custom1 = ''
    custom2 = ''
    custom3 = ''
    custom4 = ''
    custom5 = ''
    custom6 = ''
    custom7 = ''
    custom8 = ''

    function getScannedVins() {
        $('#spinner').show()
        $.ajax({
            url: "retrievescannedvin-addveh.php",
            success: function(r) {
                $('#scannedvins').html(r)
                $('#spinner').hide()
            }
        });

    }

    function createROVINScan(customerid, shopid, vehid) {
        //console.log(customerid+":"+shopid+":"+vehid)
        swal({
                title: "Are you sure?",
                text: "VIN will be removed",
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                confirmButtonClass: "btn-success",
                confirmButtonText: "Create RO",
                closeOnConfirm: true,
                closeOnCancel: true
            },
            function(isConfirm) {
                if (isConfirm) {
                    location.href = '<?= COMPONENTS_PRIVATE ?>/createro/addconcerns.php?cid=' + customerid + '&vid=' + vehid
                }
            });
    }


    $('.form-control').blur(function() {

        id = $(this).attr("id")

        if (id != "smsmsg" && id != "smscell" && id != "ocrimage") {
            if ((id.substring(0, 3) != 'add' && id.substring(0, 2) != 'cf' && id != "vehnotes") || id == "address") {
                v = $(this).val()
                v = v.toUpperCase()

                if (id != "preferredcontactmethod" && id != 'Discount' && id != "accounttype") {
                    previousval = eval(id)
                    //console.log(previousval)
                    if (previousval.length > 0) {
                        previousval = previousval.toUpperCase()
                    }
                } else {
                    previousval = "x"
                }
                if (id == "homephone" || id == "workphone" || id == "cellphone" || id == "fax" || id == "spousecell" || id == "spousework") {
                    v = v.replace(/\D/g, '')
                    //  console.log("phone v:"+v)
                }
                if (id == "discountpercent") {
                    id = "`column 27`"
                }
                v = encodeURIComponent(v)
                //console.log(previousval + ":" + v)
                if (previousval != v) {
                    // update the info
                    //console.log(vehid)
                    ds = "shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&i=" + id + "&v=" + v + "&vid=" + vehid
                    // console.log("DS:"+ds)
                    $.ajax({
                        data: ds,
                        url: "customer-update.php",
                        success: function(r) {
                            //console.log(r)
                            $('#saved').show()
                            setTimeout(function() {
                                $('#saved').fadeOut(1500)
                            }, 1000);
                            window[id] = v
                            //console.log("newval: " + window[id])
                        },
                        error: function(xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        }
                    });
                }
            }
        }
    });


    $(document).ready(function() {
        $('#close-sidebar').on('click', function() {
            $('#logomain').toggle()
        });

        /*$("#homephone").mask("(*************")
        $("#workphone").mask("(*************? ext:999999")
        $("#cellphone").mask("(*************")
        $("#spousework").mask("(*************? ext:999999")
        $("#spousecell").mask("(*************")
        $("#billtophone").mask("(*************")
        $("#shippingphone").mask("(*************")*/
        $('#lastname').focus()

        <?php
        if (isset($_GET['addveh']) && $vid == 0) {
            echo "$('#addvehmodal').modal('show')\r\n";
            echo "$('#addyear').val(localStorage.getItem('year'))\r\n";
            echo "$('#addmake').val(localStorage.getItem('make'))\r\n";
            echo "$('#addmodel').val(localStorage.getItem('model'))\r\n";
            echo "$('#addyearlabel').css('-webkit-transform','translateY(-24px)').css('-ms-transform','translateY(-24px)').css('transform','translateY(-24px)').css('font-size','small').css('font-color','gray').css('font-weight','bold');\r\n";
            echo "$('#addmakelabel').css('-webkit-transform','translateY(-24px)').css('-ms-transform','translateY(-24px)').css('transform','translateY(-24px)').css('font-size','small').css('font-color','gray').css('font-weight','bold');\r\n";
            echo "$('#addmodellabel').css('-webkit-transform','translateY(-24px)').css('-ms-transform','translateY(-24px)').css('transform','translateY(-24px)').css('font-size','small').css('font-color','gray').css('font-weight','bold');\r\n";
        }

        if ($isprofitboost == 'yes') {
            echo "$('#accounttypelabel').css('-webkit-transform','translateY(-24px)').css('-ms-transform','translateY(-24px)').css('transform','translateY(-24px)').css('font-size','small').css('font-color','gray').css('font-weight','bold');\r\n";
        }


        if (isset($_GET['vin']) && $vid == 0) {
            echo "$('#addvehmodal').modal('show')\r\n";
            echo "setTimeout(function(){\$('#addvehmodal').focus},500)\r\n";
        }

        if ($vid != 0) {
            echo "swal('Vehicle was successfully transferred')\r\n";
        }

        if ($cfl == $shopid && !isset($_GET['reload'])) {
            //echo '$(\'#carfaxmodal\').modal(\'show\');';
            //echo 'setTimeout(function(){$(\'#cfvin\').focus()},300);';
        }

        if (strlen($decodevin) == 17) {
            echo "setTimeout(function(){decodeVIN()},1000);";
        }
        ?>
        $('#createro').click();

        <?php
        if (isset($_GET['transfer'])) {
            echo "swal('Vehicle successfully transferred to this customer')\r\n";
        }
        ?>

        localStorage.removeItem("fn");
        localStorage.removeItem("ln");
        localStorage.removeItem("year");
        localStorage.removeItem("make");
        localStorage.removeItem("model");
        localStorage.removeItem("email");
        localStorage.removeItem("cellphone");
        localStorage.removeItem("cid");
        localStorage.removeItem("a2ro");

        $("#addtransmission").select2({
            tags: true,
            width: '100%',
            dropdownParent: $("#modelDialog1")
        });

        $('#ocrimage').on('change', function() {
            if ($(this).val() != '') {
                $('#pictureslot').html("<img src='" + URL.createObjectURL(event.target.files[0]) + "' width='200' height='200' border='1'>").show();
            }

        })

        $('#btn-ocrscan').on('click', function() {
            if ($('#ocrimage').val() == '') {
                swal("Please select VIN image to upload")
                return
            }

            var $this = $(this);
            $this.attr('disabled', 'disabled');
            $this.html('Please Wait...');

            formData = new FormData(document.forms.namedItem("ocrform"));
            $.ajax({
                url: '<?= COMPONENTS_PRIVATE ?>/vinscanner/html/dist/ocrscan.php',
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function(data) {

                    if (data.status == 'success') {
                        $('#addvin').val(data.vin)
                        $('#addyear').val(data.yr)
                        $('#addmake').val(data.mk)
                        $('#addmodel').val(data.md + " " + data.tr + " " + data.bd)
                        $('#addengine').val(data.en)
                        tra = data.tra.split("|")
                        dr = data.dr

                        $('#addtransmission').html('')
                        for (var t of tra) {
                            $('#addtransmission').append("<option>" + t.trim() + "</option>")
                        }
                        $('#adddrivetype').val(dr)
                        if (data.yr.length > 0) {
                            $('#addyearlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (data.mk.length > 0) {
                            $('#addmakelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (data.md.length > 0) {
                            $('#addmodellabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (data.en.length > 0) {
                            $('#addenginelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (tra.length > 0) {
                            $('#addtransmissionlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (dr.length > 0) {
                            $('#adddrivetypelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        $('#addcyl').focus()
                        $('#addvehmodal').modal('show');
                    } else
                        swal(data.status != '' ? data.status : "No Results Found")

                    $('#ocrimage').val('');
                    $('#newvinscanmodal').modal('hide');
                    $('#pictureslot').html('').hide();
                    $this.attr('disabled', false);
                    $this.html('Scan');
                }
            });

        });

        <?php if ($notesalert == 'yes' && !empty($notes)) { ?>
        swal("<?= preg_replace( "/(\r|\n)/", " ", $notes) ?>")
        <?php } ?>
    });

    function saveVehicle(x) {

        if ($('#addmake').val() == "") {
            swal("Please add make of the vehicle");
            return;
        }

        ds = $('#vehaddform').serialize()
        ds = ds + "&shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>"
        $.ajax({

            data: ds + "&dupcheck=yes",
            type: "post",
            url: "customer-vehicle-add.php",
            success: function(r) {
                if (r.indexOf("success") >= 0) {
                    location.href = 'customer-edit.php?reload=no&cid=<?php echo $cid; ?>'
                } else if (r.indexOf("duplicate") >= 0) {
                    var cusarr = r.split('duplicate|');
                    swal({
                        title: "Are you sure?",
                        text: "Vehicle with this VIN already belongs to "+cusarr[1]+". Do you still want to continue?",
                        type: "warning",
                        showCancelButton: true,
                        cancelButtonClass: "btn-default",
                        confirmButtonClass: "btn-danger",
                        confirmButtonText: "Yes",
                        cancelButtonText: "No",
                        closeOnConfirm: false
                    }, function() {

                        $.ajax({

                            data: ds,
                            type: "post",
                            url: "customer-vehicle-add.php",
                            success: function(r) {
                                if (r.indexOf("success") >= 0) {
                                    location.href = 'customer-edit.php?reload=no&cid=<?php echo $cid; ?>'
                                }
                            }
                        });
                    });

                }
            }

        });

    }

    function getCS(z) {

        $.ajax({
            data: "z=" + z,
            url: "getcsfromzip.php",
            success: function(r) {
                if (r.indexOf("success|") >= 0) {
                    rar = r.split("|")
                    $('#city').focus()
                    $('#city').val(rar[1])
                    $('#state').focus()
                    $('#state').val(rar[2])
                    $('#cityfloatinglabel').css("-webkit-transform", "translateY(-24px)");
                    $('#cityfloatinglabel').css("-ms-transform", "translateY(-24px)");
                    $('#cityfloatinglabel').css("transform", "translateY(-24px)");
                    $('#statefloatinglabel').css("-webkit-transform", "translateY(-24px)");
                    $('#statefloatinglabel').css("-ms-transform", "translateY(-24px)");
                    $('#statefloatinglabel').css("transform", "translateY(-24px)");
                    $('#statefloatinglabel').css("font-size", "small").css("font-color", "gray").css("font-weight", "bold");
                    $('#cityfloatinglabel').css("font-size", "small").css("font-color", "gray").css("font-weight", "bold");
                    $('#email').focus()
                }
            }
        });

    }


    $('#vehmodal').on('hidden.bs.modal', function() {
        $("#mainform input").prop("disabled", false);
    })

    $('#vehmodal').on('shown.bs.modal', function() {
        $("#mainform input").prop("disabled", true);
        document.activeElement.blur();
        $('#year').focus()

    })
    $('#addvehmodal').on('hidden.bs.modal', function() {
        $("#mainform input").prop("disabled", false);
    })

    $('#addvehmodal').on('shown.bs.modal', function() {
        $("#mainform input").prop("disabled", true);
        document.activeElement.blur();
        $('#addvin').focus()
        $('#addvinlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");

    })


    function launchScanner() {

        location.href = 'sbpvinscan://?returnURL=https://<?php echo $_SERVER['SERVER_NAME']; ?>/sbp/api/vinscan/?roid=XXXX,shopid=<?php echo $shopid; ?>'
        $('#addvehmodal').modal('hide')
        $('#scanmodal').modal('show')

    }

    function launchNewScanner() {

        $('#addvehmodal').modal('hide')
        $('#newvinscanmodal').modal('show')

    }

    function editVehicle(vid) {

        $('#vehmodal').modal('show')
        $("#mainform input").prop("disabled", true);

        $.ajax({

            data: "shopid=<?php echo $shopid; ?>&vid=" + vid,
            url: "customer-vehicle-details.php",
            success: function(r) {
                // console.log(r)
                if (r.indexOf("||") >= 0) {
                    rar = r.split("||")
                    // console.log(rar[16])
                    vehid = rar[1];
                    $('#vehid').val(vehid)
                    year = rar[3];
                    $('#year').val(rar[3]);
                    $('#yearlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    make = rar[4];
                    $('#make').val(rar[4]);
                    $('#makelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    model = rar[5];
                    $('#model').val(rar[5]);
                    $('#modellabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    miles = rar[6];
                    $('#miles').val(rar[6]);
                    $('#mileslabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    license = rar[7];
                    $('#licnumber').val(license);
                    $('#licenselabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    state = rar[8];
                    $('#licstate').val(state);
                    $('#statelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    vin = rar[9];
                    $('#vin').val(rar[9]);
                    $('#vinlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    engine = rar[10];
                    $('#engine').val(rar[10]);
                    $('#enginelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    drivetype = rar[11];
                    $('#drivetype').val(rar[11]);
                    $('#drivetypelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    transmission = rar[12];
                    $('#transmission').val(rar[12]);
                    $('#transmissionlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    cyl = rar[13];
                    $('#cyl').val(rar[13]);
                    $('#cyllabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    fleetno = rar[14];
                    $('#fleetno').val(rar[14]);
                    $('#fleetnolabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    color = rar[15];
                    $('#color').val(rar[15]);
                    $('#colorlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    custom1 = rar[16];
                    $('#custom1').val(rar[16]);
                    $('#custom1label').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    custom2 = rar[17];
                    $('#custom2').val(rar[17]);
                    $('#custom2label').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    custom3 = rar[18];
                    $('#custom3').val(rar[18]);
                    $('#custom3label').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    custom4 = rar[19];
                    $('#custom4').val(rar[19]);
                    $('#custom4label').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    custom5 = rar[20];
                    $('#custom5').val(rar[20]);
                    $('#custom5label').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    custom6 = rar[21];
                    $('#custom6').val(rar[21]);
                    $('#custom6label').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    custom7 = rar[22];
                    $('#custom7').val(rar[22]);
                    $('#custom7label').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    custom8 = rar[23];
                    $('#custom8').val(rar[23]);
                    $('#custom8label').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    currmileage = rar[24];
                    $('#currentmileage').val(rar[24]);
                    $('#currentmileagelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)");
                    tro = parseFloat(rar[25]).toFixed(2)
                    cnt = rar[26]
                    if (cnt > 0) {
                        avg = (tro / cnt).toFixed(2)
                    } else {
                        avg = 0.00
                    }
                    tmsg = "Ro Count: " + cnt + " @ " + avg + " = " + tro
                    appts = rar[27]

                    if (appts != '') tmsg = tmsg + "<br>" + appts

                    $('#vehtotals').html(tmsg)

                    setTimeout(function() {
                        $('#year').focus()
                    }, 500)
                }
            }

        });

        // now get the notes
        $.ajax({

            data: "vehid=" + vid + "&shopid=<?php echo $shopid; ?>",
            url: "customer-vehicle-get-notes.php",
            type: "post",
            success: function(r) {
                rar = r.split("|")
                //$('#vehnotes').val(rar[0])
                $('#vehiclenotes').html(rar[1])
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(xhr.status + ":" + thrownError)
            },

        })
    }


    function addNotes() {

        $('#notesmodal').modal('show')
        vid = $('#vehid').val()
        // now get the notes
        setTimeout(function() {
            $('#vehnotes').focus()
            $.ajax({

                data: "vehid=" + vid + "&shopid=<?php echo $shopid; ?>",
                url: "customer-vehicle-get-notes.php",
                type: "post",
                success: function(r) {
                    rar = r.split("|")
                    $('#vehnotes').val(rar[0])
                    //$('#vehiclenotes').html(rar[1])
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status + ":" + thrownError)
                },

            })
        }, 500)
    }

    function saveNotes() {

        notes = encodeURIComponent($('#vehnotes').val())
        vid = $('#vehid').val()
        ds = "notes=" + notes + "&shopid=<?php echo $shopid; ?>&vehid=" + vid
        $.ajax({
            data: ds,
            url: "customer-vehicle-add-notes.php",
            type: "post",
            success: function(r) {
                // now get the notes and update the vehiclenotes div
                $.ajax({

                    data: "vehid=" + vid + "&shopid=<?php echo $shopid; ?>",
                    url: "customer-vehicle-get-notes.php",
                    type: "post",
                    success: function(r) {
                        rar = r.split("|")
                        $('#vehnotes').val(rar[0])
                        $('#vehiclenotes').html(rar[1])
                        $('#notesmodal').hide()
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status + ":" + thrownError)
                    },

                })
            },
            error: function(xhr, ajaxOptions, thrownError) {
                //console.log(xhr.status+":"+thrownError)
            }
        });

    }

    function closeModal(id) {
        $('.modal-backdrop').remove();
        $('#' + id).modal('hide')
    }

    function decodeVINScan(vin) {

        $('#addvehmodal').modal('show')
        $('#scanmodal').modal('hide')
        p = false

        if (vin.length == 17) {
            p = true
        }

        if (p == true) {

            $('#spinner').show()
            $('#saved').show()
            $.ajax({
                data: "vin=" + vin,
                url: "<?= COMPONENTS_PRIVATE ?>/vehicle/vehicle-vin-decode.php",
                success: function(r) {
                    //console.log(r)
                    if (r.indexOf("success|") >= 0) {
                        rar = r.split("|")
                        rvin = rar[1]
                        yr = rar[2]
                        mk = rar[3]
                        md = rar[4]
                        tr = rar[5]
                        bd = rar[6]
                        en = rar[7]
                        $('#addvin').val(rvin)
                        $('#addyear').val(yr)
                        $('#addmake').val(mk)
                        $('#addmodel').val(md + " " + tr + " " + bd)
                        $('#addengine').val(en)
                        //console.log("decoding vin")
                        if (yr.length > 0) {
                            $('#addyearlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (mk.length > 0) {
                            $('#addmakelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (md.length > 0) {
                            $('#addmodellabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (en.length > 0) {
                            $('#addenginelabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        if (rvin.length > 0) {
                            $('#addvinlabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
                        }
                        $('#addcyl').focus()
                        $('#spinner').hide()
                        $('#saved').fadeOut('slow')
                        $('#scanmodal').hide()
                        //$('.modal-backdrop').remove();
                    } else {
                        $('#spinner').hide()
                        $('#saved').fadeOut('slow')
                        $('#scanmodal').hide()
                        //$('.modal-backdrop').remove();
                        swal("The VIN was not able to be decoded.  Please verify the VIN number")
                    }
                }
            });
        } else {
            swal("You must enter a 17 digit VIN to decode it")
        }



    }

    function checkDup() {
        ln = $('#lastname').val()
        fn = $('#firstname').val()
        ln = encodeURIComponent(ln.trim())
        fn = encodeURIComponent(fn.trim())
        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&ln=" + ln + "&fn=" + fn,
            type: "post",
            url: "checkdupcustomer.php",
            success: function(r) {
                if (r.indexOf("found") >= 0) {
                    swal({
                            title: "Are you sure?",
                            text: "This may be a duplicate customer.  Click Continue Entering or Cancel to search for the customer",
                            type: "warning",
                            showCancelButton: true,
                            cancelButtonClass: "btn-default",
                            confirmButtonClass: "btn-danger",
                            confirmButtonText: "Continue Entering",
                            closeOnConfirm: true,
                            closeOnCancel: true
                        },
                        function(isConfirm) {
                            if (isConfirm) {
                                $('#address').focus()
                            } else {
                                location.href = 'customer-search.php?ln=' + encodeURIComponent(ln) + "&fn=" + encodeURIComponent(fn)
                            }
                        });
                }
            }
        });

    }

    function removeVIN(vin, shopid) {

        swal({
                title: "Are you sure?",
                text: "VIN will be removed",
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Remove VIN",
                closeOnConfirm: true,
                closeOnCancel: true
            },
            function(isConfirm) {
                if (isConfirm) {
                    $.ajax({
                        data: "vin=" + vin + "&shopid=<?php echo $shopid; ?>",
                        url: "removevin.php",
                        error: function(xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status + ":" + thrownError)
                        },
                        success: function() {
                            getScannedVins()
                        }
                    });

                }
            });


    }

    function saveCustomer() {
        formdata = $('#mainform').serialize()
        $.ajax({
            data: formdata + "&shopid=<?php echo $shopid; ?>",
            url: "customer-add-action.php",
            type: "post",
            success: function(r) {
                //console.log(r)
                if (r.indexOf("success|") >= 0) {
                    rar = r.split("|")
                    location.href = '<?= COMPONENTS_PRIVATE ?>/vehicle/vehicle-add.php?cid=' + rar[1]
                }
            }
        })
    }
</script>
<img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>
<?php if (isset($conn)) {
    mysqli_close($conn);
}
$time_end = microtime(true);
$execution_time = ($time_end - $time_start) / 60;
?>

</html>