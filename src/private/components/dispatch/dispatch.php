<!DOCTYPE html>
<html>
<?php
require CONN;
if (!defined('ASSETS')) define('ASSETS', "https://" . $_SERVER['SERVER_NAME'] . "/src/public/assets");
$shopid = $_COOKIE['shopid'];
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

$stmt = "select installcode from company where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {

    $query->execute();
    $query->bind_result($approvedlaborondispatch);
    $query->fetch();
    $query->close();

}
?>

<head>
    <title>Shop Boss Dispatch</title>
    <meta charset='utf-8'/>
    <link href='<?= ASSETS ?>/dispatch/lib/fullcalendar.min.css' rel='stylesheet'/>
    <link href='<?= ASSETS ?>/dispatch/lib/fullcalendar.print.min.css' rel='stylesheet' media='print'/>
    <link href='<?= ASSETS ?>/dispatch/scheduler.min.css' rel='stylesheet'/>
    <link href='<?= CSS ?>/bootstrap.min.css' rel='stylesheet'/>
    <link href='<?= SCRIPT ?>/plugins/jquery-ui/jquery-ui.min.css' rel='stylesheet'/>
    <link href='<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css' rel='stylesheet'/>
    <link href='<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.css' rel='stylesheet'/>
    <link href='<?= SCRIPT ?>/plugins/qtip/jquery.qtip.min.css' rel='stylesheet'/>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <script src='<?= SCRIPT ?>/plugins/moment/moment.js'></script>
    <script src='<?= SCRIPT ?>/jquery.js'></script>
    <script src='<?= SCRIPT ?>/bootstrap.min.js'></script>
    <script src='<?= SCRIPT ?>/plugins/jquery-ui/jquery-ui.js'></script>
    <script src='<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js'></script>
    <script src='<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.js'></script>
    <script src='<?= SCRIPT ?>/plugins/qtip/jquery.qtip.min.js'></script>


    <script src='<?= ASSETS ?>/dispatch/lib/fullcalendar.min.js'></script>
    <script src='<?= ASSETS ?>/dispatch/scheduler.min.js'></script>
    <script>
        var shopIsReadOnly = <?= $shopIsReadOnly ? 'true' : 'false'; ?>;

        $(function () { // document ready

            mystartdate = moment().subtract(10, 'days')
            mystartdate = moment(mystartdate).format('YYYY-MM-DD')

            myenddate = moment().add(90, 'days')
            myenddate = moment(myenddate).format('YYYY-MM-DD')
            //console.log(mystartdate+"|"+myenddate)

            $('#external-events .fc-event').each(function () {

                // store data so the calendar knows to render an event upon drop
                currtext = $(this).text()
                currarray = currtext.split("|")
                thistitle = currarray[0]
                for (i = 1; i < currarray.length; i++) {
                    // $laborid|$labord|$hrs|$tech|$roid
                    laborid = currarray[1]
                    labord = currarray[2]
                    hrs = currarray[3]
                    tech = currarray[4]
                    roid = currarray[5]
                    comid = currarray[6]
                }

                // "{id: '$id',start:'$sd',end:'$ed',tech:'$tech',labor:'$labor',hrs:'$hrs',roid:'$roid',comid:'$comid',title:'#$roid - $tech - $labor ($hrs hours)',resourceId: '$rid'},";

                $(this).data('event', {
                    title: $.trim(thistitle), 	// use the element's text as the event title
                    //stick: true, 				// maintain when user navigates (see docs on the renderEvent method)
                    id: laborid,
                    labord: labord,
                    hrs: hrs,
                    tech: tech,
                    roid: roid,
                    comid: comid
                });

                // make the event draggable using jQuery UI
                if ( !shopIsReadOnly ){
                    $(this).draggable({
                        opacity: .5,
                        zIndex: 9999,
                        revert: true,      // will cause the event to go back to its
                        revertDuration: 0  //  original position after the drag
                    });
                }
            });


            $('body').mousemove(function (event) {
                trash = $('#trash')
                boxleft = trash.offset().left
                boxtop = trash.offset().top
                boxwidth = trash.outerWidth()
                boxheight = trash.height()
                boxright = parseFloat(boxleft) + parseFloat(boxwidth)
                boxbottom = parseFloat(boxtop) + parseFloat(boxheight)

                if (event.pageX >= boxleft && event.pageX <= boxright && event.pageY >= boxtop && event.pageY <= boxbottom) {
                    $('#trash').addClass("trash-hover")
                    $('#trash').droppable({
                        tolerance: 'pointer',
                        hoverClass: "trash-hover",
                        over: function (event, ui) {
                            //console.log("hovering over trash")
                            $(this).addClass("bigger")
                            ui.draggable.addClass("btn-danger").addClass("smallbutton")
                        },
                        out: function (event, ui) {
                            $('#trash').removeClass("bigger")
                            ui.draggable.removeClass("btn-danger").removeClass("smallbutton")
                        },
                        drop: function (event, ui) {
                            //console.log(ui)
                            swal({
                                    title: "Are you sure?",
                                    text: "You will not be able to recover this file!",
                                    type: "warning",
                                    showCancelButton: true,
                                    confirmButtonClass: "btn-danger",
                                    confirmButtonText: "Yes, delete it!",
                                    closeOnConfirm: true
                                },
                                function (r) {
                                    if (r === true) {
                                        ui.draggable.remove()
                                        id = ui.draggable.attr("id")
                                        id = id.replace("unsch-", "")
                                        ds = "t=deletelaboritem&laborid=" + id
                                        $.ajax({
                                            type: "post",
                                            data: ds,
                                            url: "dispatchdata.php",
                                            success: function (r) {
                                                //console.log(r)
                                                if (r == "success") {
                                                    //$('#calendar').fullCalendar( 'removeEvents');
                                                    $('#calendar').fullCalendar('refetchEvents');
                                                    reloadJobs()
                                                } else {
                                                    console.log(r)
                                                }
                                            },
                                            error: function (xhr, ajaxOptions, thrownError) {
                                                console.log(xhr.status);
                                                console.log(xhr.responseText);
                                                console.log(thrownError);
                                            },
                                        });

                                    } else {
                                        ui.draggable.removeClass("btn-danger").removeClass("smallbutton")
                                    }
                                });

                        }
                    });
                } else {
                    if ($('#trash').hasClass("trash-hover")) {
                        $('#trash').removeClass("trash-hover")
                    }
                }

                ns = $('#unscheduled')
                nsboxleft = ns.offset().left
                nsboxtop = ns.offset().top
                nsboxwidth = ns.outerWidth()
                nsboxheight = ns.height()
                nsboxright = parseFloat(nsboxleft) + parseFloat(nsboxwidth)
                nsboxbottom = parseFloat(nsboxtop) + parseFloat(nsboxheight)
                //console.log(nsboxleft+"|"+event.pageX+"|" +":"+ nsboxright +":"+ nsboxtop+"|"+event.pageY+"|" +":"+ nsboxbottom)

                if (event.pageX >= nsboxleft && event.pageX <= nsboxright && event.pageY >= nsboxtop && event.pageY <= nsboxbottom) {
                    $('#unscheduled').addClass("unscheduled-hover")
                    //console.log("true:"+$('#unscheduled').attr("class"))
                } else {
                    //console.log("false:"+$('#unscheduled').attr("class"))
                    if ($('#unscheduled').hasClass("unscheduled-hover")) {
                        $('#unscheduled').removeClass("unscheduled-hover")
                    }
                }

            });


            var isEventOverDiv = function (x, y) {

                var external_events = $('#external-events');
                var offset = external_events.offset();
                offset.right = external_events.width() + offset.left;
                offset.bottom = external_events.height() + offset.top;

                // Compare
                if (x >= offset.left
                    && y >= offset.top
                    && x <= offset.right
                    && y <= offset.bottom) {
                    return true;
                }
                return false;

            }

            resourcedata = [
                <?php
                $rstr = "";
                $stmt = "select bay1,bay2,bay3,bay4,bay5,bay6,bay7,bay8,bay9,bay10,bay11,bay12,bay13,bay14,bay15,bay16,bay17,bay18,bay19,bay20 from shopbays where shopid = '$shopid'";
                //echo "//".$stmt;
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $query->store_result();
                    $num_bay_rows = $query->num_rows;
                    //echo $num_bay_rows;
                    if ($num_bay_rows > 0) {
                        $query->bind_result($bay1, $bay2, $bay3, $bay4, $bay5, $bay6, $bay7, $bay8, $bay9, $bay10, $bay11, $bay12, $bay13, $bay14, $bay15, $bay16, $bay17, $bay18, $bay19, $bay20);
                        $query->fetch();
                        if (strlen($bay1) > 0) {
                            $rstr .= "{ id: 'a', title: '" . addslashes($bay1) . "', eventColor: \"#006699\" },";
                            $lastrid = 'b';
                        } else {
                            $rstr .= "{ id: 'a', title: 'BAY 1', eventColor: \"#006699\"},";
                            $lastrid = 'b';
                        }
                        if (strlen($bay2) > 0) {
                            $rstr .= "{ id: 'b', title: '" . addslashes($bay2) . "', eventColor: \"#b36b00\" },";
                            $lastrid = 'c';
                        } else {
                            $rstr .= "{ id: 'b', title: 'BAY 2', eventColor: '#b36b00' },";
                            $lastrid = 'c';
                        }
                        if (strlen($bay3) > 0) {
                            $rstr .= "{ id: 'c', title: '" . addslashes($bay3) . "', eventColor: \"#006600\" },";
                            $lastrid = 'd';
                        } else {
                            $rstr .= "{ id: 'c', title: 'BAY 3', eventColor: '#006600' },";
                            $lastrid = 'd';
                        }
                        if (strlen($bay4) > 0) {
                            $rstr .= "{ id: 'd', title: '" . addslashes($bay4) . "', eventColor: \"#800000\" },";
                            $lastrid = 'e';
                        } else {
                            $rstr .= "{ id: 'd', title: 'BAY 4', eventColor: '#800000' },";
                            $lastrid = 'e';
                        }
                        if (strlen($bay5) > 0) {
                            $rstr .= "{ id: 'e', title: '" . addslashes($bay5) . "', eventColor: \"#993300\" },";
                            $lastrid = 'f';
                        } else {
                            $rstr .= "{ id: 'e', title: 'BAY 5', eventColor: '#993300' },";
                            $lastrid = 'f';
                        }
                        if (strlen($bay6) > 0) {
                            $rstr .= "{ id: 'f', title: '" . addslashes($bay6) . "', eventColor: \"#4d004d\" },";
                            $lastrid = 'g';
                        } else {
                            $rstr .= "{ id: 'f', title: 'BAY 6', eventColor: '#4d004d' },";
                            $lastrid = 'g';
                        }
                        if (strlen($bay7) > 0) {
                            $rstr .= "{ id: 'g', title: '" . addslashes($bay7) . "', eventColor: \"#b38600\" },";
                            $lastrid = 'h';
                        } else {
                            $rstr .= "{ id: 'g', title: 'BAY 7', eventColor: '#b38600' },";
                            $lastrid = 'h';
                        }
                        if (strlen($bay8) > 0) {
                            $rstr .= "{ id: 'h', title: '" . addslashes($bay8) . "', eventColor: \"#006699\" },";
                            $lastrid = 'i';
                        }
                        if (strlen($bay9) > 0) {
                            $rstr .= "{ id: 'i', title: '" . addslashes($bay9) . "', eventColor: \"#006699\" },";
                            $lastrid = 'j';
                        }
                        if (strlen($bay10) > 0) {
                            $rstr .= "{ id: 'j', title: '" . addslashes($bay10) . "', eventColor: \"#006600\" },";
                            $lastrid = 'k';
                        }
                        if (strlen($bay11) > 0) {
                            $rstr .= "{ id: 'k', title: '" . addslashes($bay11) . "', eventColor: \"#800000\" },";
                            $lastrid = 'l';
                        }
                        if (strlen($bay12) > 0) {
                            $rstr .= "{ id: 'l', title: '" . addslashes($bay12) . "', eventColor: \"#993300\" },";
                            $lastrid = 'm';
                        }
                        if (strlen($bay13) > 0) {
                            $rstr .= "{ id: 'm', title: '" . addslashes($bay13) . "', eventColor: \"#4d004d\" },";
                            $lastrid = 'n';
                        }
                        if (strlen($bay14) > 0) {
                            $rstr .= "{ id: 'n', title: '" . addslashes($bay14) . "', eventColor: \"#b38600\" },";
                            $lastrid = 'o';
                        }
                        if (strlen($bay15) > 0) {
                            $rstr .= "{ id: 'o', title: '" . addslashes($bay15) . "', eventColor: \"#006699\" },";
                            $lastrid = 'p';
                        }
                        if (strlen($bay16) > 0) {
                            $rstr .= "{ id: 'p', title: '" . addslashes($bay16) . "', eventColor: \"#b36b00\" },";
                            $lastrid = 'q';
                        }
                        if (strlen($bay17) > 0) {
                            $rstr .= "{ id: 'q', title: '" . addslashes($bay17) . "', eventColor: \"#006600\" },";
                            $lastrid = 'r';
                        }
                        if (strlen($bay18) > 0) {
                            $rstr .= "{ id: 'r', title: '" . addslashes($bay18) . "', eventColor: \"#800000\" },";
                            $lastrid = 's';
                        }
                        if (strlen($bay19) > 0) {
                            $rstr .= "{ id: 's', title: '" . addslashes($bay19) . "', eventColor: \"#993300\" },";
                            $lastrid = 't';
                        }
                        if (strlen($bay20) > 0) {
                            $rstr .= "{ id: 't', title: '" . addslashes($bay20) . "', eventColor: \"#4d004d\" },";
                            $lastrid = 't';
                        }
                        $rstr = substr($rstr, 0, strlen($rstr) - 1);
                        echo $rstr;
                    } else {
                        echo "{ id: 'a', title: 'BAY 1', eventColor: \"#006699\" },"
                            . "{ id: 'b', title: 'BAY 2', eventColor: '#b36b00' },"
                            . "{ id: 'c', title: 'BAY 3', eventColor: '#006600' },"
                            . "{ id: 'd', title: 'BAY 4', eventColor: '#800000' },"
                            . "{ id: 'e', title: 'BAY 5', eventColor: '#993300' },"
                            . "{ id: 'f', title: 'BAY 6', eventColor: '#4d004d' },"
                            . "{ id: 'g', title: 'BAY 7', eventColor: '#b38600' }";
                        $lastrid = 'h';

                    }
                    $query->close();
                } else {
                    echo "{ id: 'a', title: 'BAY 1', eventColor: \"#006699\" },"
                        . "{ id: 'b', title: 'BAY 2', eventColor: '#b36b00' },"
                        . "{ id: 'c', title: 'BAY 3', eventColor: '#006600' },"
                        . "{ id: 'd', title: 'BAY 4', eventColor: '#800000' },"
                        . "{ id: 'e', title: 'BAY 5', eventColor: '#993300' },"
                        . "{ id: 'f', title: 'BAY 6', eventColor: '#4d004d' },"
                        . "{ id: 'g', title: 'BAY 7', eventColor: '#b38600' }";
                    $lastrid = 'h';
                }
                ?>
            ]

            var customButtons = {};

            if (!shopIsReadOnly){
                customButtons = {
                    addColumn: {
                        text: 'Add Column',
                        click: function () {
                            $('#headermodal').modal('show');
                            $('#editrid').val('<?php echo $lastrid; ?>');
                            $('#edittitle').val("New Column Name").select();
                            setTimeout(function () {
                                $('#edittitle').focus()
                            }, 500)
                        }
                    },
                    resetColumn: {
                        text: "Reset All Columns",
                        click: function () {
                            swal({
                                    title: "Are you sure?",
                                    text: "Are you sure you want to reset ALL columns?",
                                    type: "warning",
                                    showCancelButton: true,
                                    confirmButtonClass: "btn-danger",
                                    confirmButtonText: "Yes, Reset All",
                                    closeOnConfirm: true
                                },
                                function () {
                                    $.ajax({
                                        data: "t=clear&shopid=<?php echo $shopid; ?>",
                                        url: "dispatchdata.php",
                                        type: "post",
                                        success: function (r) {
                                            console.log(r)
                                            if (r == "success") {
                                                location.reload()
                                            }
                                        }
                                    })
                                });
                        }
                    }
                };
            }
        
            $('#calendar').fullCalendar({
                customButtons: customButtons,
                defaultView: 'agendaDay',
                defaultDate: '<?php echo date("Y-m-d"); ?>',
                minTime: "06:00:00",
                maxTime: "20:00:00",
                slotMinutes: 15,
                slotDuration: '00:15:00',
                slotLabelFormat: 'hh:mm',
                droppable: true,
                selectHelper: true,
                editable: !shopIsReadOnly,
                selectable: !shopIsReadOnly,
                allDaySlot: false,
                columnFormat: "ddd M/D/Y",
                eventLimit: true, // allow "more" link when too many events
                header: {
                    left: 'prev,next today addColumn resetColumn',
                    center: 'title',
                    right: 'agendaDay,agendaTwoDay,agendaWeek,month'
                },

                views: {
                    agenda: {
                        titleFormat: 'ddd MMM D'
                    },
                    agendaTwoDay: {
                        type: 'agenda',
                        duration: {days: 3},

                        // views that are more than a day will NOT do this behavior by default
                        // so, we need to explicitly enable it
                        //groupByResource: false

                        //// uncomment this line to group by day FIRST with resources underneath
                        groupByDateAndResource: true
                    }
                },

                //// uncomment this line to hide the all-day slot
                //allDaySlot: false,

                resources: resourcedata,
                events: {
                    url: 'dispatchdata.php',
                    type: 'POST',
                    data: {
                        t: "getlist",
                        sd: mystartdate,
                        ed: myenddate
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },

                },

                resourceRender: function (resourceObj, labelTds, bodyTds) {
                    labelTds.on('click', function () {
                        rid = resourceObj.id
                        rtitle = resourceObj.title
                        $('#headermodal').modal('show')
                        $('#editrid').val(rid)
                        $('#edittitle').val(rtitle)
                    });
                },
                /*select: function(start, end, jsEvent, view, resource) {
                    console.log(
                        'select',
                        start.format(),
                        end.format(),
                        resource ? resource.id : '(no resource)'
                    );
                },*/
                dayClick: function (date, jsEvent, view, resource) {
                    if (!shopIsReadOnly && "<?= $_COOKIE['mode'] ?>" === "full") {
                        if (resource !== undefined) {
                            $('#addmodal').modal('show')
                            sd = date.format("YYYY-MM-DD HH:mm")
                            $('#addsd').val(sd)
                            $('#addcolumn').val(resource.id)

                            setTimeout(function () {
                                $('#addroid').focus()
                                $('#addsd').datetimepicker({
                                    format: 'YYYY-MM-DD HH:mm',
                                    defaultDate: new Date(sd),
                                    sideBySide: true
                                });
                                $('#added').datetimepicker({
                                    format: 'YYYY-MM-DD HH:mm',
                                    //useCurrent: true,
                                    sideBySide: true
                                });
                            }, 500)
                        } else {
                            swal("You can only enter new dispatch items in the Day View or 3 Day View")
                        }
                    }
                },

                eventClick: function (calEvent, jsEvent, view) {
                    if (!shopIsReadOnly) {
                        //return;
                        event_start = calEvent.start
                        event_end = calEvent.end
                        $('#editmodal').modal('show')
                        $('#editmodalheader').css("backgroundColor", calEvent.backColor)
                        $('#tech').html(calEvent.tech)
                        $('#labor').html(calEvent.labor)
                        $('#hrs').html(calEvent.hrs)
                        $('#roid').html(calEvent.roid)
                        $('#sd').val(event_start.format("MM/DD/YYYY HH:mm"))
                        if (event_end != null) {
                            $('#ed').val(event_end.format("MM/DD/YYYY HH:mm"))
                        } else {
                            d = moment();
                            $('#ed').val(d.format("MM/DD/YYYY HH:mm"))
                        }
                        hsd = $.fullCalendar.formatDate(calEvent.start, "YYYY-MM-DD HH:mm:ss")
                        if (event_end != null) {
                            hed = $.fullCalendar.formatDate(calEvent.end, "YYYY-MM-DD HH:mm:ss")
                        } else {
                            hed = $.fullCalendar.formatDate(moment(), "YYYY-MM-DD HH:mm:ss")
                        }
                        $('#calid').val(calEvent.id)

                        $('#sd').datetimepicker({
                            inline: true,
                            defaultDate: new Date(hsd),
                            format: 'YYYY-MM-DD HH:mm',
                            useCurrent: false,
                            sideBySide: true
                        });
                        $('#ed').datetimepicker({
                            inline: true,
                            format: 'YYYY-MM-DD HH:mm',
                            defaultDate: new Date(hed),
                            useCurrent: false,
                            sideBySide: true
                        });

                    }
                },

                eventRender: function (event, element) {
                    element.find('span.fc-title').html(element.find('span.fc-title').text());

                    //$('#calendar').fullCalendar( 'removeEvents');
                    //$('#calendar').fullCalendar( 'refetchEvents' );
                    //console.log("laborid:")
                },

                eventDrop: function (event, delta, revertFunc) {

                    console.log("event drop:" + delta)
                    sd = event.start.format()
                    ed = event.end

                    if (ed) {
                        ed = event.end.format()
                    } else {
                        var ed = "none"
                    }
                    //console.log(sd+"|"+ed)
                    id = event.id
                    rid = event.resourceId
                    ds = "t=moveevent&id=" + id + "&ed=" + ed + "&sd=" + sd + "&rid=" + rid
                    //console.log("event drop:"+ds)
                    if (Date.parse(sd)) {
                        $.ajax({
                            type: "post",
                            data: ds,
                            url: "dispatchdata.php",
                            success: function (r) {
                                //console.log(r)
                                setTimeout(function () {
                                    //$('#calendar').fullCalendar( 'removeEvents');
                                    //$('#calendar').fullCalendar( 'refetchEvents' );
                                }, 200);

                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                            },
                        });
                    }


                },
                eventDragStop: function (event, jsEvent) {

                    //console.log(event+":"+resource)
                    console.log(jsEvent)
                    //console.log(event.start.format()+"|"+event.end.format())
                    trash = $('#trash')
                    boxleft = trash.offset().left
                    boxtop = trash.offset().top
                    boxwidth = trash.outerWidth()
                    boxheight = trash.height()
                    boxright = parseFloat(boxleft) + parseFloat(boxwidth)
                    boxbottom = parseFloat(boxtop) + parseFloat(boxheight)
                    //console.log("boxtop:"+boxtop+"\r\nboxbottom:"+boxbottom+"\r\nboxleft:"+boxleft+"\r\nboxright:"+boxright)

                    ns = $('#unscheduled')
                    nsboxleft = ns.offset().left
                    nsboxtop = ns.offset().top
                    nsboxwidth = ns.outerWidth()
                    nsboxheight = ns.height()
                    nsboxright = parseFloat(nsboxleft) + parseFloat(nsboxwidth)
                    nsboxbottom = parseFloat(nsboxtop) + parseFloat(nsboxheight)
                    //console.log(nsboxleft+"|"+event.pageX+"|" +":"+ nsboxright +":"+ nsboxtop+"|"+event.pageY+"|" +":"+ nsboxbottom)
                    sd = new Date(event.start.format())
                    ed = event.end
                    if (ed) {
                        ed = new Date(event.end.format())
                        var hours = Math.abs(sd - ed) / 3600000
                        //console.log(hours)


                        if (jsEvent.pageX >= boxleft && jsEvent.pageX <= boxright && jsEvent.pageY >= boxtop && jsEvent.pageY <= boxbottom) {
                            swal({
                                    title: "Are you sure?",
                                    text: "You will not be able to add this to the Dispatch Schedule if you remove it",
                                    type: "warning",
                                    showCancelButton: true,
                                    confirmButtonClass: "btn-danger",
                                    confirmButtonText: "Yes, remove it",
                                    closeOnConfirm: true
                                },
                                function () {
                                    //console.log(event.id)
                                    // mark as scheduled
                                    $.ajax({
                                        type: "post",
                                        data: "t=deletecalitem&id=" + event.id,
                                        url: "dispatchdata.php",
                                        success: function (r) {
                                            //console.log(r)
                                            if (r == "success") {
                                                //$('#calendar').fullCalendar( 'removeEvents');
                                                $('#calendar').fullCalendar('refetchEvents');

                                            } else {
                                                console.log(r)
                                            }
                                        },
                                        error: function (xhr, ajaxOptions, thrownError) {
                                            console.log(xhr.status);
                                            console.log(xhr.responseText);
                                            console.log(thrownError);
                                        },
                                    });
                                });
                        }


                        if (jsEvent.pageX >= nsboxleft && jsEvent.pageX <= nsboxright && jsEvent.pageY >= nsboxtop && jsEvent.pageY <= nsboxbottom) {
                            ds = "t=reschedulecalitem&laborid=" + event.laborid + "&dispatchid=" + event.id

                            console.log("query string:" + ds)
                            laborid = event.laborid
                            if (laborid != 0) {
                                $.ajax({
                                    type: "post",
                                    data: ds,
                                    url: "dispatchdata.php",
                                    success: function (r) {
                                        console.log(r)
                                        if (r == "success") {
                                            //$('#calendar').fullCalendar( 'removeEvents');
                                            $('#calendar').fullCalendar('refetchEvents');
                                            reloadJobs()
                                        } else {
                                            console.log(r)
                                        }
                                    },
                                    error: function (xhr, ajaxOptions, thrownError) {
                                        console.log(xhr.status);
                                        console.log(xhr.responseText);
                                        console.log(thrownError);
                                    },
                                });
                            } else {
                                swal("This was not a labor job from an RO so you cannot add it to this list")
                            }
                        }
                    }
                },
                viewRender: function (view) {
                    var axis = $('.fc-axis');
                    var currview = $('#calendar').fullCalendar('getView')
                    $('#currentview').val(view.type)
                    for (var i = 0; i < axis.length; i++) {
                        var element = axis[i];
                        var p = element.parentElement;
                        var n = element.cloneNode(true);
                        p.appendChild(n);
                    }
                },
                eventResize: function (event, jsEvent, ui, view) {
                    ed = event.end.format()
                    id = event.id
                    // post it with ajax, then
                    $.ajax({
                        type: "post",
                        data: "t=changeevent&id=" + id + "&end=" + ed,
                        url: "dispatchdata.php",
                        success: function (r) {
                            setTimeout(function () {
                                //$('#calendar').fullCalendar( 'removeEvents');
                                //$('#calendar').fullCalendar( 'refetchEvents' );
                            }, 200);
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        },
                    });

                },
                eventReceive: function (event) {

                    console.log(event)


                    startdate = event.start.format()
                    sd = new Date(startdate)
                    laborid = $('#laborid').val()
                    rid = event.resourceId

                    if (rid === null) {
                        swal("You must add events on the Day View or 3 Day View")
                        $("#calendar").fullCalendar('removeEvents', event.id);
                        return

                    } else {
                        if (event.end === null) {

                            event.end = new Date(sd.setHours(sd.getHours() + 1));

                        }
                        $('#unsch-' + event.id).hide()
                        $('#calendar').fullCalendar('updateEvent', event)

                        // post it with ajax, then
                        ds = "t=addevent&rid=" + rid + "&laborid=" + laborid + "&start=" + startdate
                        console.log(ds)
                        $.ajax({
                            type: "post",
                            data: ds,
                            url: "dispatchdata.php",
                            success: function (r) {
                                //console.log(r)
                                if (r == "success") {
                                    setTimeout(function () {
                                        $('#calendar').fullCalendar('removeEvents');
                                        $('#calendar').fullCalendar('refetchEvents');
                                        //reloadJobs()
                                    }, 200);

                                }
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                console.log(xhr.status);
                                console.log(xhr.responseText);
                                console.log(thrownError);
                            },
                        });
                    }
                },

                drop: function (event) {
                    rid = event.resourceId

                    if (rid !== null) {
                        console.log("drop")
                        // is the "remove after drop" checkbox checked?
                        rawhtml = $(this).html()
                        larray = rawhtml.split("|")
                        laborid = larray[1]
                        $('#laborid').val(laborid)
                    }
                }

            });


        });


        function reloadJobs() {

            $.ajax({
                type: "post",
                data: "t=jobs&approvedlaborondispatch=<?php echo $approvedlaborondispatch; ?>",
                url: "dispatchdata.php",
                success: function (r) {
                    //console.log("running job list")
                    $('#external-events-listing').html(r)
                    setTimeout(function () {
                        $('#external-events .fc-event').each(function () {

                            // store data so the calendar knows to render an event upon drop
                            currtext = $(this).text()
                            currarray = currtext.split("|")
                            thistitle = currarray[0]
                            for (i = 1; i < currarray.length; i++) {
                                // $laborid|$labord|$hrs|$tech|$roid
                                laborid = currarray[1]
                                labord = currarray[2]
                                hrs = currarray[3]
                                tech = currarray[4]
                                roid = currarray[5]
                                comid = currarray[6]
                            }

                            // "{id: '$id',start:'$sd',end:'$ed',tech:'$tech',labor:'$labor',hrs:'$hrs',roid:'$roid',comid:'$comid',title:'#$roid - $tech - $labor ($hrs hours)',resourceId: '$rid'},";

                            $(this).data('event', {
                                title: $.trim(thistitle), 	// use the element's text as the event title
                                stick: true, 				// maintain when user navigates (see docs on the renderEvent method)
                                id: laborid,
                                labord: labord,
                                hrs: hrs,
                                tech: tech,
                                roid: roid,
                                comid: comid
                            });

                            // make the event draggable using jQuery UI
                            if ( !shopIsReadOnly ){
                                $(this).draggable({
                                    opacity: .5,
                                    zIndex: 9999,
                                    revert: true,      // will cause the event to go back to its
                                    revertDuration: 0  //  original position after the drag
                                });
                            }
                        });
                    }, 200);
                    /*currhtml = $('#unscheduled').html()
                    console.log(currhtml.length)
                    if (currhtml.length > 77){
                        $('#unscheduled').show();
                    }*/
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
            });


        }

        function deleteJob(laborid) {

            swal({
                    title: "Are you sure?",
                    text: "You will not be able to add this to the Dispatch Schedule if you remove it",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonClass: "btn-danger",
                    confirmButtonText: "Yes, remove it",
                    closeOnConfirm: true
                },
                function () {
                    // mark as scheduled
                    $.ajax({
                        type: "post",
                        data: "t=deletejob&laborid=" + laborid,
                        url: "dispatchdata.php",
                        success: function (r) {
                            if (r == "success") {
                                reloadJobs()
                            } else {
                                console.log(r)
                            }
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        },
                    });
                });

        }

        function resizeCalendar(calendarView) {
            //console.log(calendarView.name)
            if (calendarView.name === 'agendaWeek' || calendarView.name === 'agendaDay') {
                // if height is too big for these views, then scrollbars will be hidden
                calendarView.setHeight(9999);
            }
        }


        $('#external-events .fc-event').click(function () {

            //console.log($(this).text())

        });
    </script>

    <style>

        body {
            margin: 0;
            padding: 0;
            font-family: "Lucida Grande", Helvetica, Arial, Verdana, sans-serif;
            font-size: 14px;
        }


        .fc-license-message {
            display: none
        }

        [data-resource-id=a] {
            background-color: #006699;
            opacity: .05
        }

        [data-resource-id=b] {
            background-color: #b36b00;
            opacity: .05
        }

        [data-resource-id=c] {
            background-color: #006600;
            opacity: .05
        }

        [data-resource-id=d] {
            background-color: #800000;
            opacity: .05
        }

        [data-resource-id=e] {
            background-color: #993300;
            opacity: .05
        }

        [data-resource-id=f] {
            background-color: #4d004d;
            opacity: .05
        }

        [data-resource-id=g] {
            background-color: #b38600;
            opacity: .05
        }

        [data-resource-id=h] {
            background-color: #666633;
            opacity: .05
        }

        [data-resource-id=i] {
            background-color: #5c5c8a;
            opacity: .05
        }

        [data-resource-id=j] {
            background-color: #5c8a8a;
            opacity: .05
        }

        [data-resource-id=k] {
            background-color: #808080;
            opacity: .05
        }

        [data-resource-id=l] {
            background-color: #660033;
            opacity: .05
        }

        [data-resource-id=m] {
            background-color: #c6538c;
            opacity: .05
        }

        [data-resource-id=n] {
            background-color: #e68a00;
            opacity: .05;
        }

        [data-resource-id=o] {
            background-color: #ff66cc;
            opacity: .05;
        }


        .fc-day-header:nth-child(even) {
            background-color: #CCCCCC;
            padding: 6px;
        }

        .fc-day-header:nth-child(odd) {
            background-color: #808080;;
            padding: 6px;
            color: white
        }

        th.fc-resource-cell {
            padding: 5px;
            opacity: .95;
            color: white;
            text-shadow: 0px 0px 3px rgba(0, 0, 0, 1);
        }

        #editmodalheader {
            background-color: #002F5E;
            color: white;
            border-radius: 3px;
        }

        .modal-content {
            border-radius: 3px
        }

        .fc-content {
            font-size: 9pt;
            color: white
        }

        .fc-time > span {
            font-size: 9pt;
        }

        [data-time].fc-minor {
            border-bottom: 1px gray solid
        }

        [data-time]:hover {
            background-color: #FFFFCC
        }

        #external-events {
            margin-top: 5px;

        }

        #external-events h4 {
            font-size: 14px;
        }

        #external-events .fc-event {
            margin: 10px 0px 10px 0px;
            cursor: pointer;
            padding: 7px 10px 7px 10px;

        }

        .smallbutton {

            width: 20px;
            height: 15px;
        }

        .bigger {
        }

        .trash {
            padding: 2px 10px 2px 10px;
            border: 3px red dashed;
            width: 45%;
            height: 40px;
            display: block;
            float: right;
            text-align: center;
            font-size: x-large;
            color: red;
            border-radius: 3px;
        }

        .trash-hover {
            background-color: red;
            color: white;

        }

        .csv {
            display: none
        }

        .h3 {
            font-size: medium;
            font-weight: bold
        }

        #calendar {
            max-height: 100%;
        }

        #unscheduled {
            border: 1px silver dashed;
            min-height: 500px;
            padding: 5px;
            border-radius: 4px;

        }

        .unscheduled-hover {
            background-color: #F3F3F3;
            border: 2px black dashed;
        }

        h4 {
            font-weight: bold
        }

        #external-events .fc-event:nth-child(even) {
            background-color: #CCCCCC;
            padding: 6px;
            color: black
        }

        #external-events .fc-event:nth-child(odd) {
            background-color: #808080;;
            padding: 6px;

        }

        td {
            cursor: pointer
        }

        .tooltip {
            z-index: 9999;
            position: absolute;
        }
    </style>
</head>
<body>
<?php include(COMPONENTS_PRIVATE_PATH . "/shared/analytics.php"); ?>
<input type="hidden" id="laborid">
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-2">
                    <div id='external-events'>
                        <div id='external-events-listing'>
                            <a class="btn btn-danger" type="button" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">To WIP</a><span class="trash" id="trash"><i class="fa fa-trash-o"></i></span>
                            <p class="h3">Drag Jobs to Trash <i class="fa fa-arrow-up"></i> or Schedule <i class="fa fa-arrow-right"></i></p>
                            <div id="unscheduled"><h4>Jobs to Schedule<i class='fa fa-arrow-down'></i></h4>
                                <?php
                                $stmt = "select labor,laborid,laborhours,l.tech,ro.roid from repairorders ro inner join labor l on ro.shopid = l.shopid and ro.roid = l.roid"
                                    . " inner join complaints c on l.shopid = c.shopid and l.roid = c.roid and l.complaintid = c.complaintid where"
                                    . " ro.datein >= '2017-03-22' and ro.status != 'closed' and ro.rotype != 'no approval' and ro.shopid = '$shopid' and schedulecat != 'scheduled'";
                                if ($shopid == "4075") {
                                    $stmt .= " and ro.roid >= 4665";
                                }
                                if ($approvedlaborondispatch == "yes") {
                                    $stmt .= " and c.acceptdecline != 'pending' and c.acceptdecline != 'declined'";
                                }
                                $stmt .= " order by roid asc";
                                //echo $stmt;
                                if ($query = $conn->prepare($stmt)) {
                                    $query->execute();
                                    $result = $query->get_result();
                                    while ($row = $result->fetch_array()) {
                                        $laborid = $row['laborid'];
                                        $labord = strtoupper(substr($row['labor'], 0, 30)) . "...";
                                        $hrs = $row['laborhours'];
                                        $roid = $row['roid'];
                                        $tech = strtoupper($row['tech']);
                                        echo "<div id='unsch-$laborid' class='fc-event'><b>#$roid - $tech</b> - $labord ($hrs Hours)<br>|$laborid</span></div>";
                                    }
                                } else {
                                    echo "MTD Sales failed: (" . $conn->errno . ") " . $conn->error;
                                }

                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-10">
                    <div id='calendar'></div>
                </div>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="currentview"><input type="hidden" id="hsd"><input type="hidden" id="hed">


<div id="editmodal" class="modal fade" role="dialog">
    <div class="modal-dialog">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" id="editmodalheader">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Edit Event</h4>
            </div>
            <div class="modal-body">
                <div id="eventinfo">
                    <input type="hidden" id="calid">
                    <b>RO #:</b> <span id="roid"></span>
                    <b>Tech:</b> <span id="tech"></span><br>
                    <b>Labor:</b> <span id="labor"></span>
                    <b>Hours:</b> <span id="hrs"></span><br><br>
                    <b>Start Date / Time</b><br>
                    <input type="text" id="sd" class="form-control"><br><br>
                    <b>End Date / Time</b><br>
                    <input type="text" id="ed" class="form-control">

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning" style="float:left" onclick="location.href='<?= COMPONENTS_PRIVATE ?>/ro/ro.php?roid='+$('#roid').html()">Go To RO</button>
                <button type="button" class="btn btn-primary" onclick="saveEvent()">Save</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>

<div id="headermodal" class="modal fade" role="dialog">
    <div class="modal-dialog">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" id="editmodalheader">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Edit Header</h4>
            </div>
            <div class="modal-body">
                <div id="eventinfo">
                    <input type="hidden" id="editrid">
                    <b>Header Title - Letters and numbers only</b>
                    <input style="text-transform:uppercase" type="text" class="form-control" id="edittitle">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="saveTitle()">Save Title</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>


<div id="addmodal" class="modal fade" role="dialog">
    <div class="modal-dialog">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header" id="editmodalheader">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Edit Event</h4>
            </div>
            <div class="modal-body">
                <div id="eventinfo">
                    <b>RO #:</b>
                    <input type="text" id="addroid" class="form-control"><br>
                    <b>Tech:</b>
                    <select id="addtech" class="form-control">
                        <?php
                        $stmt = "select employeelast, employeefirst from employees where shopid = ? and showtechlist = 'yes' and active = 'yes'";
                        if ($query = $conn->prepare($stmt)) {
                            try {
                                $query->bind_param("s", $shopid);
                                /* execute statement */
                                $query->execute();

                                /* bind result variables */
                                $query->bind_result($emplast, $empfirst);
                                $emplast = strtoupper($emplast);
                                $empfirst = strtoupper($empfirst);
                                /* fetch values */
                                while ($query->fetch()) {
                                    //echo $emplast.",$".$empfirst;
                                    echo "<option value='$emplast, $empfirst'>$emplast, $empfirst</option>";

                                }
                                //$query->close();

                            } catch (ErrorException $e) {
                                echo 'error: ' . $e->getMessage();
                            }

                        } else {
                            echo "get employees Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }

                        ?>
                    </select>
                    <br>
                    <b>Column:</b>
                    <select id="addcolumn" class="form-control">
                        <?php
                        $rstr = "";
                        $stmt = "select bay1,bay2,bay3,bay4,bay5,bay6,bay7,bay8,bay9,bay10,bay11,bay12,bay13,bay14,bay15,bay16,bay17,bay18,bay19,bay20 from shopbays where shopid = '$shopid'";
                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $query->store_result();
                            $num_roid_rows = $query->num_rows;
                            if ($num_roid_rows > 0) {
                                $query->bind_result($bay1, $bay2, $bay3, $bay4, $bay5, $bay6, $bay7, $bay8, $bay9, $bay10, $bay11, $bay12, $bay13, $bay14, $bay15, $bay16, $bay17, $bay18, $bay19, $bay20);
                                $query->fetch();
                                if (strlen($bay1) > 0) {
                                    $rstr .= "<option value='a'>$bay1</option>";
                                }
                                if (strlen($bay2) > 0) {
                                    $rstr .= "<option value='b'>$bay2</option>";
                                }
                                if (strlen($bay3) > 0) {
                                    $rstr .= "<option value='c'>$bay3</option>";
                                }
                                if (strlen($bay4) > 0) {
                                    $rstr .= "<option value='d'>$bay4</option>";
                                }
                                if (strlen($bay5) > 0) {
                                    $rstr .= "<option value='e'>$bay5</option>";
                                }
                                if (strlen($bay6) > 0) {
                                    $rstr .= "<option value='f'>$bay6</option>";
                                }
                                if (strlen($bay7) > 0) {
                                    $rstr .= "<option value='g'>$bay7</option>";
                                }
                                if (strlen($bay8) > 0) {
                                    $rstr .= "<option value='h'>$bay8</option>";
                                }
                                if (strlen($bay9) > 0) {
                                    $rstr .= "<option value='i'>$bay9</option>";
                                }
                                if (strlen($bay10) > 0) {
                                    $rstr .= "<option value='j'>$bay10</option>";
                                }
                                if (strlen($bay11) > 0) {
                                    $rstr .= "<option value='k'>$bay11</option>";
                                }
                                if (strlen($bay12) > 0) {
                                    $rstr .= "<option value='l'>$bay12</option>";
                                }
                                if (strlen($bay13) > 0) {
                                    $rstr .= "<option value='m'>$bay13</option>";
                                }
                                if (strlen($bay14) > 0) {
                                    $rstr .= "<option value='n'>$bay14</option>";
                                }
                                if (strlen($bay15) > 0) {
                                    $rstr .= "<option value='o'>$bay15</option>";
                                }
                                if (strlen($bay16) > 0) {
                                    $rstr .= "<option value='p'>$bay16</option>";
                                }
                                if (strlen($bay17) > 0) {
                                    $rstr .= "<option value='q'>$bay17</option>";
                                }
                                if (strlen($bay18) > 0) {
                                    $rstr .= "<option value='r'>$bay18</option>";
                                }
                                if (strlen($bay19) > 0) {
                                    $rstr .= "<option value='s'>$bay19</option>";
                                }
                                if (strlen($bay20) > 0) {
                                    $rstr .= "<option value='t'>$bay20</option>";
                                }
                                echo $rstr;
                            } else {
                                echo "<option value='a'>BAY 1</option>"
                                    . "<option value='b'>BAY 2</option>"
                                    . "<option value='c'>BAY 3</option>"
                                    . "<option value='d'>BAY 4</option>"
                                    . "<option value='e'>BAY 5</option>"
                                    . "<option value='f'>BAY 6</option>"
                                    . "<option value='g'>BAY 7</option>";

                            }
                            $query->close();
                        } else {
                            echo "<option value='a'>BAY 1</option>"
                                . "<option value='b'>BAY 2</option>"
                                . "<option value='c'>BAY 3</option>"
                                . "<option value='d'>BAY 4</option>"
                                . "<option value='e'>BAY 5</option>"
                                . "<option value='f'>BAY 6</option>"
                                . "<option value='g'>BAY 7</option>";
                        }
                        ?>
                    </select>
                    <br>
                    <b>Labor:</b>
                    <input style="text-transform:uppercase" type="text" id="addlabor" class="form-control"><br>
                    <b>Hours:</b>
                    <input type="text" id="addhours" class="form-control"><br>
                    <b>Start Date / Time</b><br>
                    <input type="text" id="addsd" class="form-control"><br>
                    <b>End Date / Time</b><br>
                    <input type="text" id="added" class="form-control">

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="addEvent()">Save</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>

<script>

    function addEvent() {

        console.log("starting add")
        // check for all values
        roid = $('#addroid').val()
        tech = $('#addtech').val()
        labor = $('#addlabor').val()
        hours = $('#addhours').val()
        sd = $('#addsd').val()
        ed = $('#added').val()
        addcolumn = $('#addcolumn').val()

        if (roid.length > 0 && labor.length > 0 && hours.length > 0 && sd.length > 0 && ed.length > 0) {
            console.log("adding")
            $.ajax({
                data: "shopid=<?php echo $shopid; ?>&t=addnewitem&sd=" + sd + "&ed=" + ed + "&tech=" + tech.toUpperCase() + "&labor=" + labor.toUpperCase() + "&hours=" + hours + "&roid=" + roid + "&rid=" + addcolumn,
                type: "post",
                url: "dispatchdata.php",
                success: function (r) {
                    if (r.indexOf("success|") >= 0) {
                        rar = r.split("|")
                        newid = rar[1]
                        //$('#calendar').fullCalendar( 'removeEvents');
                        //$('#calendar').fullCalendar( 'refetchEvents' );
                        $('#addsd').val('')
                        $('#added').val('')
                        $('#addlabor').val('')
                        $('#addhours').val('')
                        $('#addroid').val('')
                        $('#addmodal').modal('hide')

                        var newEvent = {
                            id: newid,
                            title: "#" + roid + " - " + tech + " - " + labor + " (" + hours + " hours)",
                            start: sd,
                            end: ed,
                            resourceId: addcolumn,
                            tech: tech,
                            labor: labor,
                            hrs: hours,
                            roid: roid,
                            comid: 0,
                            laborid: 0,
                            allDay: 0
                        };
                        $('#calendar').fullCalendar('renderEvent', newEvent, 'stick');
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        } else {
            swal("All fields are required")
        }

    }

    function isAlphaNumeric(str) {
        var code, i, len;

        for (i = 0, len = str.length; i < len; i++) {
            code = str.charCodeAt(i);
            if (!(code > 47 && code < 58) && // numeric (0-9)
                !(code > 64 && code < 91) && // upper alpha (A-Z)
                !(code > 96 && code < 123)) { // lower alpha (a-z)
                return false;
            }
        }
        return true;
    };

    function saveTitle() {

        title = $('#edittitle').val()
        rid = $('#editrid').val()
        if (title.length > 0) {
            $.ajax({
                data: "t=title&shopid=<?php echo $shopid; ?>&rid=" + rid + "&title=" + title,
                type: "post",
                url: "dispatchdata.php",
                success: function (r) {
                    console.log(r)
                    if (r == "success") {
                        location.reload()
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        } else {
            swal("You must enter a name for the column or cancel")
        }

    }

    function saveEvent() {

        sd = $('#sd').val()
        ed = $('#ed').val()
        id = $('#calid').val()

        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&t=changedate&sd=" + sd + "&ed=" + ed + "&id=" + id,
            type: "post",
            url: "dispatchdata.php",
            success: function (r) {
                if (r == "success") {
                    //$('#calendar').fullCalendar( 'removeEvents');
                    $('#calendar').fullCalendar('refetchEvents');
                    $('#sd').val('')
                    $('#ed').val('')
                    $('#calid').val('')
                    $('#editmodal').modal('hide')
                    window.location.reload()
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });

    }

    $('.ui-widget-content').hover(function () {
        if (!$(this).html()) {
            for (i = 0; i < 7; i++) {
                $(this).append('<td class="temp_cell" style="border: 0px; width:' + (Number($('.fc-day').width()) + 2) + 'px"></td>');
            }

            $(this).children('td').each(function () {
                $(this).hover(function () {
                    $(this).css({'background-color': '#ffef8f', 'cursor': 'pointer'});
                }, function () {
                    $(this).prop('style').removeProperty('background-color');
                });
            });
        }
    }, function () {
        $(this).children('.temp_cell').remove();
    });


    $(document).ready(function () {

        $('.fc-resource-cell').each(function () {
            $(this).attr("title", "<p style='font-size:10pt;'><b>Click to edit the header<br><br>Click an empty box below to add a custom dispatch item<b></p>")
        })

        $('.fc-resource-cell[title]').qtip({
            position: {
                target: 'mouse', // Track the mouse as the positioning target
                adjust: {x: 15, y: 15} // Offset it slightly from under the mouse
            },
            style: {classes: 'qtip-tipsy'}
        });

    });

    /*currhtml = $('#unscheduled').html()
    console.log(currhtml.length)
    if (currhtml.length > 77){
        $('#unscheduled').show();
    }*/

    setInterval(function () {
        reloadJobs()
    }, 10000);
    console.log(<?= json_encode($_SERVER['USERNAME']) ?>);
</script>
</body>
<?php if (isset($conn)) {
    mysqli_close($conn);
} ?>


</html>
