<!DOCTYPE html>
<?php
require CONN;
$shopid = $_COOKIE['shopid'];
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$fn = "";
$ln = "";
if (isset($_GET['ln'])) {
    $ln = $_GET['ln'];
}
if (isset($_GET['ln'])) {
    $fn = $_GET['fn'];
}
if (isset($_GET['quoteid'])) {
    if ($_GET['quoteid'] > 0) {
        setcookie("quoteid", $_GET['quoteid'], time() + 3600);
    }
}

$stmt = "select shopnotice from company where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    //echo $query->fullQuery."<BR>";
    $query->bind_result($shopnotice);
    //echo $query->fullQuery;
    $query->fetch();
    $query->close();
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

?>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">

    <title><?= getPageTitle() ?></title>

    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon' />
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">


    <style>
        .modal-content {
            -webkit-border-radius: 0px !important;
            -moz-border-radius: 0px !important;
            border-radius: 0px !important;
        }

        #thiscustomer {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 1);
            position: absolute;
            top: 0px;
            left: 0px;
            padding: 5px;
            text-align: left;
            font-weight: bold;
            color: white;
            width: 100%;
        }
    </style>
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    <!-- Page Container -->
    <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
    <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
        <!-- Side Overlay-->
        <aside id="side-overlay">
            <!-- Side Overlay Scroll Container -->
            <div id="side-overlay-scroll">
                <!-- Side Header -->
                <div class="side-header side-content">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default pull-right" type="button" data-toggle="layout" data-action="side_overlay_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <span>
                        <img class="img-avatar img-avatar32" src="<?= IMAGE ?>/avatars/avatar10.jpg" alt="">
                        <span class="font-w600 push-10-l">Walter Fox</span>
                    </span>
                </div>
                <!-- END Side Header -->

            </div>
            <!-- END Side Overlay Scroll Container -->
        </aside>
        <!-- END Side Overlay -->

        <!-- Sidebar -->
        <nav id="sidebar">
            <!-- Sidebar Scroll Container -->
            <div id="sidebar-scroll">
                <!-- Sidebar Content -->
                <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                <div class="sidebar-content">
                    <!-- Side Header -->
                    <div class="side-header side-content bg-white-op">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                            <i class="fa fa-times"></i>
                        </button>
                        <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                            <i class="text-primary">
                                <?php getLogo() ?></i>
                            <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                        </a>
                    </div>
                    <!-- END Side Header -->

                    <!-- Side Content -->
                    <div class="side-content">
                        <ul class="nav-main">
                            <li>
                                <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-wrench"></i><span class="sidebar-mini-hide">Work In Process</span></a>
                            </li>
                            <li>
                                <a href="campaigns.php" onclick=""><i class="fa fa-download"></i><span class="sidebar-mini-hide">Campaigns</span></a>
                            </li>
                            <li>
                                <a href="phone.php" onclick=""><i class="fa fa-phone"></i><span class="sidebar-mini-hide">Phone Follow Up</span></a>
                            </li>
                            <li>
                                <a href="customer-search.php" onclick=""><i class="fa fa-users"></i><span class="sidebar-mini-hide">Manage Customer Follow Up</span></a>
                            </li>
                        </ul>
                    </div>
                    <!-- END Side Content -->
                </div>
                <!-- Sidebar Content -->
            </div>
            <!-- END Sidebar Scroll Container -->
        </nav>
        <!-- END Sidebar -->

        <!-- Header -->
        <header id="header-navbar" class="content-mini content-mini-full">
            <!-- Header Navigation Right -->
            <ul class="nav-header pull-right">
                <li>
                    <div id="shopnotice">
                        <?php echo $_COOKIE['shopname'] . " #" . $shopid . "<br>" . $_COOKIE['username'] . '<a class="btn btn-primary btn-sm btn-logoff" href="' . COMPONENTS_PUBLIC . '/login/logoff.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Logoff</span></a>'; ?>
                    </div>
                </li>
            </ul>
            <!-- END Header Navigation Right -->

            <!-- Header Navigation Left -->

            <ul class="nav-header pull-left">
                <li class="hidden-md hidden-lg">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                        <i class="fa fa-navicon"></i>
                    </button>
                </li>
                <li class="hidden-xs hidden-sm">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                        <i class="fa fa-bars"></i>
                    </button>
                </li>
                <li>
                    <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                    <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                        <i class="si si-grid"></i>
                    </button>
                </li>
                <li class="visible-xs">
                    <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                    <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                        <i class="fa fa-search"></i>
                    </button>
                </li>
                <li>
                    <?php if (strlen($shopnotice) > 0) {
                        echo '<div class="shopnotice">' . $shopnotice . '</div>';
                    } ?>
                </li>
            </ul>

            <!-- END Header Navigation Left -->
        </header>
        <!-- END Header -->

        <!-- Main Container -->
        <main id="main-container">
            <div class="row">
                <img alt="" src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
                <div style="padding:20px;margin:0px" class="form-group">
                    <div class="col-sm-4">
                        <div class="form-material floating">
                            <input class="form-control input-sm" style="padding:1px;text-transform:uppercase" type="text" id="sf" value="<?php if (strlen($ln) > 0) {
                                                                                                                                                echo $ln . ',' . $fn;
                                                                                                                                            } ?>" name="sf">
                            <label for="sf">Search name, phone, partial VIN, license or fleet #
                            </label>
                        </div>

                    </div>
                </div>
            </div>
            <div id="custmainrow" class="col-lg-12 scrollable"></div>


        </main>
        <!-- END Main Container -->

        <!-- Footer -->
        <!-- END Footer -->
    </div>
    <!-- END Page Container -->

    <?php if (!$shopIsReadOnly): ?>
        <div id="markmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
            <input id="customerid" type="hidden">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="block block-themed block-transparent remove-margin-b">
                        <div class="block-header bg-primary-dark">
                            <ul class="block-options">
                                <li>
                                    <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                </li>
                            </ul>
                            <h3 class="block-title"></h3>
                        </div>
                        <div class="block-content">
                            <div class="row">
                                <div style="text-align:left" class="col-md-12">
                                    <br><br>
                                    <b><span id="followlabel"></span></b><br><br>
                                    <select class="form-control" id="custstatus">
                                        <option value="yes">Yes - Follow up this customer</option>
                                        <option value="no">No - DO NOT follow up this customer</option>
                                    </select>
                                    <br><br>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div style="margin-top:20px;" class="modal-footer">
                        <button class="btn btn-primary btn-sm" type="button" onclick="saveFollow()">Save</button>
                        <button class="btn btn-default btn-sm" type="button" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- END Apps Modal -->
    <?php endif; ?>



    <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/jquery.typewatch.js"></script>
    <script src="<?= SCRIPT ?>/plugins/underscorejs/underscore.js"></script>

    <!-- Page Plugins -->

    <!-- Page JS Code
        <script src="assets/js/pages/base_pages_dashboard.js"></script>-->
    <script>
        jQuery(function() {
            // Init page helpers (Slick Slider plugin)
            App.initHelpers('slick');
        });

        function saveFollow() {

            cid = $('#customerid').val()
            f = $('#custstatus').val()
            ds = "dt=setfollow&cid=" + cid + "&shopid=<?php echo $shopid; ?>&follow=" + f;
            console.log(ds)
            $.ajax({
                data: ds,
                url: "getdata.php",
                type: "post",
                success: function(r) {
                    console.log(r)
                    $('#custstatus' + cid).html(f.toUpperCase())
                    $('#markmodal').modal('hide')
                    $('#customerid').val('')
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }

            });

        }

        $(document).ready(function() {
            $('#close-sidebar').on('click', function() {
                $('#logomain').toggle()
            });

        });

        $("#sf").bind('keyup', _.debounce(function() {
            $('#spinner').show()
            ds = "sf=" + $(this).val() + "&shopid=<?php echo $shopid; ?>"
            console.log(ds)
            $.ajax({
                data: ds,
                url: "customer-data.php",
                success: function(r) {
                    //console.log(r)
                    $('#custmainrow').html(r)
                    $('#spinner').hide()
                }
            });


        }, 200));


        $(document).ready(function() {
            $('#sf').focus()

            $('#spinner').show()
            ds = "sf=" + $('#sf').val() + "&shopid=<?php echo $shopid; ?>"
            console.log(ds)
            $.ajax({
                data: ds,
                url: "customer-data.php",
                success: function(r) {
                    //console.log(r)
                    $('#custmainrow').html(r)
                    $('#spinner').hide()
                }
            });

            $('#createro').click();


        });

        function editCust(id, fn, ln) {

            $('#customerid').val(id)
            $('.block-title').html("Change Follow Up Status for " + fn + " " + ln)
            $('#followlabel').html("Set Follow Up Status for " + fn + " " + ln)
            $('#markmodal').modal('show')

        }
    </script>

    <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>
<?php if (isset($conn)) {
    mysqli_close($conn);
} ?>


</html>