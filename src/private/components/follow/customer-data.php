<?php

require(CONN);
$shopid = $_GET['shopid'];
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$sf = $_GET['sf'];
$sfvin = '%'.$_GET['sf'].'%';

$shoplist = "";
$stmt = "select shopid,joinedshopid from joinedshops where shopid = '$shopid'";
if($query = $conn->prepare($stmt)){
    $query->execute();
    $result = $query->get_result();
	while($row = $result->fetch_array()) {
		$shoplist = "'".$shoplist.$row['joinedshopid']."',";
	}
}
if ($shoplist == ""){
	$shoplist = "'".$shopid."'";
}else{
	$shoplist .= "'".$shopid."'";
}
?>
<div id="table-container" class="table-responsive">
<table id="wiplist-table" class="table table-striped table-hover table-condensed table-header-bg">
	<thead>
		<tr class="header">
			<th>Name</th>
			<th>Address</th>
			<th>Home</th>
			<th>Work</th>
			<th>Cell</th>
			<th>Follow Up</th>
		</tr>
	</thead>
	<tbody>

<?php
if($shopid=='9970')$limitstr='';else $limitstr=" limit 800";
$stmt = "select c.lastname,c.firstname,c.address,c.homephone,c.workphone,c.cellphone,c.customerid,c.shopid,follow from customer c "
. " where c.shopid in ($shoplist) and (firstname like '%$sf%' or lastname like '%$sf%' or homephone like '%$sf%' "
. "or workphone like '%$sf%' or cellphone like '%$sf%') and active != 'no' order by lastname asc {$limitstr}";

//echo $stmt;

if($query = $conn->prepare($stmt)){
    $query->execute();
    $result = $query->get_result();
	while($row = $result->fetch_array()) {
		$name = strtoupper($row['lastname'].", ".$row['firstname']);
		$onclickCust = !$shopIsReadOnly
			? "onclick=\"editCust('{$row['customerid']}','" . strtoupper($row['firstname']) . "','" . strtoupper($row['lastname']) . "','{$row['shopid']}')\""
			: "";
?>
		<tr <?= $onclickCust; ?>>
			<td><?php echo substr($name,0,40); ?></td>
			<td><?php echo strtoupper($row['address']); ?></td>
			<td style="min-width:120px;"><?php echo formatPhone($row['homephone']); ?></td>
			<td style="min-width:120px;"><?php echo formatPhone($row['workphone']); ?></td>
			<td style="min-width:120px;"><?php echo formatPhone($row['cellphone']); ?></td>
			<td id="custstatus<?php echo $row['customerid']; ?>" style="min-width:120px;"><?php echo strtoupper($row['follow']); ?></td>
		</tr>
<?php
	}
}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

?>
	</tbody>
</table>

<?php
mysqli_close($conn);
?>
