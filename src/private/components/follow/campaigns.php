<!DOCTYPE html>
<html>
<?php
require CONN;
$shopid = $_COOKIE['shopid'];
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>

<head>
    <!-- BEGIN TRACKJS -->
    <script type="text/javascript">
        window._trackJs = {
            token: '9c8174c8d10d4834812899217ac22f44'
        };
    </script>
    <script type="text/javascript" src="https://cdn.trackjs.com/releases/current/tracker.js"></script>
    <!-- END TRACKJS -->
    <meta charset="utf-8">

    <title><?= getPageTitle() ?></title>

    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/favicon.ico' type='image/x-icon' />
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.css">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
    </style>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    <div style="position:absolute;top:0px;left:0%;width:100%;display:none;z-index:9999;text-align:center" class="alert alert-success" id="saved">Changes have been saved</div>
    <!-- Page Container -->
    <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
    <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
        <!-- Side Overlay-->
        <aside id="side-overlay">
            <!-- Side Overlay Scroll Container -->
            <div id="side-overlay-scroll">
                <!-- Side Header -->
                <div class="side-header side-content">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default pull-right" type="button" data-toggle="layout" data-action="side_overlay_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <span>
                        <img class="img-avatar img-avatar32" src="<?= IMAGE ?>/avatars/avatar10.jpg" alt="">
                        <span class="font-w600 push-10-l">Walter Fox</span>
                    </span>
                </div>
                <!-- END Side Header -->

            </div>
            <!-- END Side Overlay Scroll Container -->
        </aside>
        <!-- END Side Overlay -->

        <!-- Sidebar -->
        <nav id="sidebar">
            <!-- Sidebar Scroll Container -->
            <div id="sidebar-scroll">
                <!-- Sidebar Content -->
                <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                <div class="sidebar-content">
                    <!-- Side Header -->
                    <div class="side-header side-content bg-white-op">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                            <i class="fa fa-times"></i>
                        </button>
                        <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                            <i class="text-primary">
                                <?php getLogo() ?></i>
                            <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                        </a>
                    </div>
                    <!-- END Side Header -->

                    <!-- Side Content -->
                    <div class="side-content">
                        <ul class="nav-main">
                            <li>
                                <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-wrench"></i><span class="sidebar-mini-hide">Work In Process</span></a>
                            </li>
                            <li>
                                <a href="campaigns.php" onclick=""><i class="fa fa-download"></i><span class="sidebar-mini-hide">Campaigns</span></a>
                            </li>
                            <li>
                                <a href="phone.php" onclick=""><i class="fa fa-phone"></i><span class="sidebar-mini-hide">Phone Follow Up</span></a>
                            </li>
                            <li>
                                <a href="customer-search.php" onclick=""><i class="fa fa-users"></i><span class="sidebar-mini-hide">Manage Customer Follow Up</span></a>
                            </li>
                        </ul>
                    </div>
                    <!-- END Side Content -->
                </div>
                <!-- Sidebar Content -->
            </div>
            <!-- END Sidebar Scroll Container -->
        </nav>
        <!-- END Sidebar -->

        <!-- Header -->
        <header id="header-navbar" class="content-mini content-mini-full">
            <!-- Header Navigation Right -->
            <ul class="nav-header pull-right">
                <li>
                    <div id="shopnotice">
                        <?php echo $_COOKIE['shopname'] . " #" . $shopid . "<br>" . $_COOKIE['username'] . '<a class="btn btn-primary btn-sm btn-logoff" href="' . COMPONENTS_PUBLIC . '/login/logoff.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Logoff</span></a>'; ?>
                    </div>
                </li>
            </ul>
            <!-- END Header Navigation Right -->

            <!-- Header Navigation Left -->

            <ul class="nav-header pull-left">
                <li class="hidden-md hidden-lg">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                        <i class="fa fa-navicon"></i>
                    </button>
                </li>
                <li class="hidden-xs hidden-sm">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                        <i class="fa fa-bars"></i>
                    </button>
                </li>
                <li>
                    <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                    <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                        <i class="si si-grid"></i>
                    </button>
                </li>
                <li class="visible-xs">
                    <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                    <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                        <i class="fa fa-search"></i>
                    </button>
                </li>
                <li>

                </li>
            </ul>

            <!-- END Header Navigation Left -->
        </header>
        <!-- END Header -->

        <!-- Main Container -->
        <main class="container-fluid" id="main-container" style="padding:50px;">
            <br><br>
            <?php if (!$shopIsReadOnly): ?>
                <span class="btn btn-info" style="float:right" onclick="location.href='newcampaign.php'">Add New Campaign</span>
            <?php endif; ?>
            <span class="btn btn-info" style="float:right;margin-right: 10px;" onclick="location.href='campaignlogs.php'">Email Logs</span><br><br>
            <table class="table table-condensed table-striped table-header-bg">
                <thead>
                    <tr>
                        <td>Campaign Name</td>
                        <td>Days</td>
                        <td>Active</td>
                        <?php if (!$shopIsReadOnly): ?>
                            <td>Delete</td>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $stmt = "select id,campaign,`type`,active,message from campaigns where shopid = '$shopid'";
                    if ($query = $conn->prepare($stmt)) {
                        $query->execute();
                        $result = $query->get_result();
                        while ($row = $result->fetch_array()) {
                            if (strtolower($row['active']) == "yes") {
                                $class = "btn-warning";
                            } else {
                                $class = "btn-info";
                            }
                            $activeStatus = strtoupper($row['active']);
                            $onclickActive = !$shopIsReadOnly 
                                ? "onclick=\"markActive({$row['id']},'{$activeStatus}')\"" 
                                : "";
                            
                            $onclickCampaign = !$shopIsReadOnly
                                ? "onclick=\"editCampaign('{$row['id']}')\""
                                : "";
                            ?>
                            <tr>
                                <td <?= $onclickCampaign; ?>><?php echo $row['campaign']; ?></td>
                                <td <?= $onclickCampaign; ?>><?php echo $row['type']; ?></td>
                                <td>
                                    <span class="btn 
                                    <?php echo $class; ?> btn-sm" <?= $onclickActive; ?>><?php echo strtoupper($row['active']); ?></span>
                                </td>
                                <?php if (!$shopIsReadOnly): ?>
                                    <td><span class="btn btn-danger" onclick="delCampaign(<?php echo $row['id']; ?>)">Delete</span></td>
                                <?php endif; ?>
                            </tr>
                            <?php
                        }
                    }
                    ?>
                </tbody>
            </table>
        </main>
        <!-- END Main Container -->

        <!-- Footer -->
        <!-- END Footer -->
    </div>
    <!-- END Page Container -->

    <?php if (!$shopIsReadOnly): ?>
        <!-- Modals -->
        <div id="downloadmodal" class="modal fade" id="modal-large" data-keyboard="false" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="block block-themed block-transparent remove-margin-b">
                        <div class="block-header bg-primary-dark">
                            <ul class="block-options">
                                <li>
                                    <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                </li>
                            </ul>
                            <h3 class="block-title">Download File</h3>
                        </div>
                        <div id="vehinfo" class="block-content"></div>
                        <div class="block-content">
                            <div class="row">
                                <div id="vehiclemaininfo" style="text-align:center" class="col-md-12">
                                    <a class="btn btn-success" href="" id="dlink">Download File</a>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div style="margin-top:20px;" class="modal-footer">
                        <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>



    <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>

    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    <script src="<?= SCRIPT ?>/plugins/masked-inputs/jquery.maskedinput.min.js"></script>
    <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/easymodal/easymodal.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
    <script src="<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.js"></script>

    <!-- Page Plugins -->

    <!-- Page JS Code
        <script src="assets/js/pages/base_pages_dashboard.js"></script>-->
    <script>
        jQuery(function() {
            // Init page helpers (Slick Slider plugin)
            App.initHelpers('slick');
        });

        function editCampaign(id) {

            location.href = 'editcampaign.php?id=' + id

        }

        function delCampaign(id) {

            swal({
                    title: "Are you sure?",
                    text: "Are you sure you want to delete this Campaign?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonClass: "btn-danger",
                    confirmButtonText: "Yes, delete it!",
                    closeOnConfirm: false
                },
                function() {
                    ds = "dt=delete&shopid=<?php echo $shopid; ?>&id=" + id
                    $.ajax({
                        data: ds,
                        url: "getdata.php",
                        type: "post",
                        success: function(r) {
                            console.log(r)
                            if (r == "success") {
                                location.reload()
                            }
                        },
                        error: function(xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        }
                    });

                });


        }

        function markActive(id, stat) {

            if (stat == "YES") {
                nstat = "no"
            } else {
                nstat = "yes"
            }
            ds = "dt=changestatus&shopid=<?php echo $shopid; ?>&id=" + id + "&stat=" + nstat
            $.ajax({
                data: ds,
                url: "getdata.php",
                type: "post",
                success: function(r) {
                    console.log(r)
                    if (r == "success") {
                        location.reload()
                    }
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });

        }

        function getData() {

            dt = $('#datatype').val()
            ds = ""
            if (dt == "ro") {
                sd = $('#startdate').val()
                ed = $('#enddate').val()
                if (sd.length > 0 && ed.length > 0) {
                    ds = "shopid=<?php echo $shopid; ?>&dt=" + dt + "&sd=" + sd + "&ed=" + ed
                } else {
                    swal("For RO Data, you must enter start and end dates")
                    return
                }
            } else {
                ds = "dt=" + dt + "&shopid=<?php echo $shopid; ?>"
            }

            if (ds.length > 0) {
                $('#spinner').show()
                $.ajax({
                    data: ds,
                    url: "getdata.php",
                    type: "post",
                    success: function(r) {
                        console.log(r)
                        $('#spinner').hide()
                        $('#dlink').attr("href", r)
                        $('#downloadmodal').modal('show')
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                })
            }
        }

        function setDate(v) {

            if (v == "ro") {
                $('#dateselect').show()
            } else {
                $('#dateselect').hide()
            }

        }

        $(document).ready(function() {
            $('#startdate').datetimepicker({
                format: "MM/DD/YYYY"
            })
            $('#enddate').datetimepicker({
                format: "MM/DD/YYYY"
            })
        });
    </script>
    <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>

</html>
<?php if (isset($conn)) {
    mysqli_close($conn);
} ?>