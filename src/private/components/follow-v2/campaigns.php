<?php
$component = "campaigns-v2";
include getHeadGlobal($component);
include getRulesGlobal($component);

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

echo "<body>";
include getHeaderGlobal($component);
include "menu.followup.php";
?>
<main id="reports" class="min-vh-100">

  <div class="container-fluid">

   <div class="row">

        <div class="col-md-6">
            <h2>Campaigns</h2>
        </div>
        <div class="col-md-6 d-flex justify-content-end">
            <?php if (!$shopIsReadOnly): ?>
                <button type="button" class="btn btn-secondary me-3" onclick="addCampaign()">Add New Campaign</button>
            <?php endif; ?>
           <button type="button" class="btn btn-secondary" onclick="logs()">Email Logs</button>
        </div>
    </div>

    <div class="row mt-4">
        <table class="w-100 sbdatatable" id="campaigns-table">
            <thead>
                <tr>
                    <th>Campaign Name</th>
                    <th>Days</th>
                    <th>Active</th>
                    <?php if (!$shopIsReadOnly): ?>
                        <th>Delete</th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody>
                <?php
                $stmt = "select id,campaign,`type`,active,message from campaigns where shopid = '$shopid'";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $result = $query->get_result();
                    while ($row = $result->fetch_array()) {
                        if (strtolower($row['active']) == "yes") {
                            $class = "btn-warning";
                        } else {
                            $class = "btn-info";
                        }
                        $onclickCampaign = !$shopIsReadOnly 
                            ? "onclick=\"editCampaign({$row['id']})\"" 
                            : "";
                        ?>
                        <tr>
                            <td <?= $onclickCampaign; ?>><?php echo $row['campaign']; ?></td>
                            <td <?= $onclickCampaign; ?>><?php echo $row['type']; ?></td>
                            <td>
                                <?php echo strtoupper($row['active']); ?>
                                <?php if (!$shopIsReadOnly): ?>
                                    <a href="javascript:void(null)" onclick="markActive(<?php echo $row['id']; ?>,'<?php echo strtoupper($row['active']); ?>')">
                                        <i class="fas fa-refresh ms-2"  style="color:var(--primary)"></i>
                                    </a>
                                <?php endif; ?>
                            </td>
                            <?php if (!$shopIsReadOnly): ?>
                                <td><a href="javascript:void(null)" onclick="delCampaign(<?php echo $row['id']; ?>)"><i class="fas fa-trash" style="color:var(--primary)"></i></a></td>
                            <?php endif; ?>
                        </tr>
                    <?php
                    }
                }
                ?>
            </tbody>
        </table>
    </div>
  </div>
</main>
           
<?php include getScriptsGlobal($component);?>
    
    <script>

         $(document).ready(function () {

            $("#campaigns-table").dataTable({
                responsive: true,
                paging: false,
                info: false,
                columnDefs: [
                    {orderable: false, targets: -1}
                ],
                order: [],
            });
        })

        function editCampaign(id) {

            eModal.iframe({
                title:'Edit Campaign',
                url: 'campaign.php?id=' + id,
                size: eModal.size.xl

            });

        }

        function delCampaign(id) {

            sbconfirm("Delete Campaign","Are you sure you want to delete this Campaign?",
                    
                function() {
                    showLoader()
                    ds = "dt=delete&shopid=<?php echo $shopid; ?>&id=" + id
                    $.ajax({
                        data: ds,
                        url: "getdata.php",
                        type: "post",
                        success: function(r) {

                            if (r == "success") {
                                location.reload()
                            }
                        },
                        error: function(xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        }
                    });

                });


        }

        function markActive(id, stat) {

            if (stat == "YES") {
                nstat = "no"
            } else {
                nstat = "yes"
            }
            ds = "dt=changestatus&shopid=<?php echo $shopid; ?>&id=" + id + "&stat=" + nstat
            showLoader()
            $.ajax({
                data: ds,
                url: "getdata.php",
                type: "post",
                success: function(r) {

                    if (r == "success") {
                        location.reload()
                    }
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });

        }

        function addCampaign()
        {
            eModal.iframe({
                title:'Add Campaign',
                url: 'campaign.php',
                size: eModal.size.xl

            });
        }

        function logs()
        {
            eModal.iframe({
                title:'Campaign Logs',
                url: 'campaignlogs.php',
                size: eModal.size.xl

            });
        }

        
    </script>

</body>

</html>
