<?php
require CONN;

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

foreach ($_POST as $k => $v){
	if ($k == "t"){
		$t = $v;
	}elseif ($k == "sd"){
		$sd = date("Y-m-d",strtotime($v));
	}elseif ($k == "ed"){
		$ed = date("Y-m-d",strtotime($v));
	}elseif ($k == "shopid"){
		$shopid = $v;
	}elseif ($k == "comm"){
		$comm = $v;
	}elseif ($k == "roid"){
		$roid = $v;
	}elseif ($k == "user"){
		$user = $v;
	}

}

if ($t == "data"){
	$sd = date("Y-m-d",strtotime($_POST['sd']));
	$ed = date("Y-m-d",strtotime($_POST['ed']));
	$shopid = $_POST['shopid'];
	
	$stmt = "select r.customer,r.vehinfo,r.statusdate,r.roid,r.customerphone,r.customerwork,r.cellphone,r.customerid from repairorders r,customer c where r.origshopid=c.shopid and r.customerid=c.customerid and r.shopid = '$shopid' and r.statusdate >= '$sd' and r.statusdate <= '$ed' and r.status = 'closed' and r.rotype != 'no approval' and c.follow='yes'";
	if ($query = $conn->prepare($stmt)){
	
		$query->execute();
		$result = $query->get_result();
		echo "<table class='table table-sm'>";
		echo "<thead><tr>";
		echo "<th>Customer</th>";
		echo "<th>Vehicle</th>";
		echo "<th>Phone</th>";
		echo "<th>Date Closed</th>";
		echo "</tr></thead><tbody>";

		while ($row = $result->fetch_array()){
			echo "<tr id='row" . $row['roid'] . "' >";
			echo "<td style='border-top:3px gray solid'>" . $row['customer'];
			if ( !$shopIsReadOnly ){
				echo "<br><span onclick='showRO(" . $row['roid'] . ")' style='cursor:pointer;color:blue;text-decoration:underline'>RO #" . $row['roid'] . "</span>";
			}
			echo "</td>";
			echo "<td>" . $row['vehinfo'] . "</td><td>";
			
			if (strlen($row['customerphone']) > 5){
				echo "HM:" . formatPhone($row['customerphone']);
				if (strlen($row['customerwork']) > 5 || strlen($row['cellphone']) > 5){
					echo "<br>";
				}
			}
			if (strlen($row['customerwork']) > 5){
				echo "WK:" . formatPhone($row['customerwork']);
				if (strlen($row['cellphone']) > 5){
					echo "<br>";
				}
			}
			if (strlen($row['cellphone']) > 5){
				echo "CL:" . formatPhone($row['cellphone']);
			}
			if (strlen($row['customerphone']) < 5 && strlen($row['customerphone']) < 5 && strlen($row['customerphone']) < 5){
				echo "&nbsp;";
			}
			echo "</td>";
			echo "<td>" . $row['statusdate'];
			if ( !$shopIsReadOnly ){
				echo "<a href='javascript:void(null)' onclick='addComm(" . $row['roid'] . ")' class='text-primary ms-4'><i class='fas fa-plus'></i> Add New Communication</a>";
			}
			echo "</td>";
			//' now get the last 5 communications
			$tstmt = "select `datetime`,`comm`,`by` from repairordercommhistory where shopid = '" . $shopid . "' and roid = " . $row['roid'] . " order by datetime desc limit 5";
			if ($tquery = $conn->prepare($tstmt)){
				$tquery->execute();
				$tresult = $tquery->get_result();
				echo "</tr><tr><td colspan='4'><table style='width:95%;margin:auto;' id='tbl" . $row['roid'] . "' class='table table-sm'>";
				echo "<tr><td colspan='3'>Communication Log for " . $row['customer'] . " on RO #" . $row['roid'] . "</td></tr>";
				while ($trow = $tresult->fetch_array()){
					echo "<tr><td>&nbsp;&nbsp;&nbsp; - " . $trow['datetime'] . "</td><td>" . $trow['comm'] . "</td><td>" . $trow['by'] . "</td></tr>";
				}
				echo "</table>";
			}else{
				echo $conn->error;
			}

		}
		echo "</tbody></table>";
	}

}

if ($t == "update"){
	
	$currtime = localTimeStamp($shopid);
		
	$stmt = "insert into repairordercommhistory (shopid,roid,datetime,comm,`by`) values ('$shopid',$roid,'$currtime',?,?)";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("ss",$comm,$user);
		$query->execute();
		$conn->commit();
		echo "success";
	}else{
		echo $conn->error;
	}

}


elseif ($t == "logdata"){
	$sd = date("Y-m-d",strtotime($_POST['sd']));
	$ed = date("Y-m-d",strtotime($_POST['ed']));
	$shopid = $_POST['shopid'];
	
	$stmt = "select c.campaign,l.customer,l.email,l.ts from campaignlogs l, campaigns c where c.shopid=l.shopid and c.id=l.campaignid and l.shopid = ? and date(l.ts) >= ? and date(l.ts) <= ? order by l.id desc";
	if ($query = $conn->prepare($stmt)){
	    $query->bind_param("sss",$shopid,$sd,$ed);
		$query->execute();
		$result = $query->get_result();
		echo "<table class='table table-sm mt-4'>";
		echo "<thead><tr>";
		echo "<th>Campaign</th>";
		echo "<th>Customer</th>";
		echo "<th>Email</th>";
		echo "<th>Timestamp</th>";
		echo "</tr></thead><tbody>";

		while ($row = $result->fetch_array()){
			echo "<tr>";
			echo "<td>" . $row['campaign'] . "</td>";
			echo "<td>" . $row['customer'] . "</td>";
			echo "<td>" . $row['email'] . "</td>";
			echo "<td>" . date('m/d/Y g:i A',strtotime($row['ts'])) . "</td>";
			echo "</tr>";
		}
		echo "</tbody></table>";
	}

}


?>
<?php if(isset($conn)){mysqli_close($conn);} ?>