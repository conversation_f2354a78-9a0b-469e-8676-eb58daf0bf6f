<?php
$component = "customerfollowup-v2";
include getHeadGlobal($component);
include getRulesGlobal($component);

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

echo "<body>";
include getHeaderGlobal($component);
include "menu.followup.php";
?>
<main id="reports" class="min-vh-100">

  <div class="container-fluid">

    <div class="d-flex justify-content-between">
        <div class="title">
            <h2>Manage Customer Follow up</h2>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="form-outline mb-4">
                <input class="form-control" type="text" id="sf" name="sf">
                <label class="form-label" for="sf">Search name, phone, partial VIN, license or fleet #</label>
            </div>
        </div>

        <div id="custmainrow"></div>
    </div>
  </div>
</main>

<?php if (!$shopIsReadOnly): ?>
    <div id="markModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title" id="markModalLabel">Follow Up</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">   
                    <input id="customerid" type="hidden">
                    <input id="shopid" type="hidden">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-row">
                                <select class="select" id="custstatus">
                                    <option value="yes">Yes - Follow up this customer</option>
                                    <option value="no">No - DO NOT follow up this customer</option>
                                </select>
                                <label class="form-label select-label" id="followlabel" for="custstatus"></label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer d-flex justify-content-center">
                    <button class="btn btn-primary btn-sm" type="button" onclick="saveFollow()">Save</button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php include getScriptsGlobal($component);?>
<script src="<?= SCRIPT ?>/debounce.js"></script>
  
    <script>
        function saveFollow() {
            cid = $('#customerid').val()
            f = $('#custstatus').val()
            shopid = $('#shopid').val()
            ds = "dt=setfollow&cid=" + cid + "&shopid="+shopid+"&follow=" + f;
            showLoader()
            $.ajax({
                data: ds,
                url: "getdata.php",
                type: "post",
                success: function(r) {
                    $('#custstatus' + cid).html(f.toUpperCase())
                    $('#markModal').modal('hide')
                    $('#customerid').val('')
                    hideLoader()
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        }

        $('#sf').on("keyup", $.debounce(1000, function () {
            showLoader()
            ds = "sf=" + $(this).val() + "&shopid=<?php echo $shopid; ?>"
            $.ajax({
                data: ds,
                url: "customer-data.php",
                success: function(r) {
                    $('#custmainrow').html(r)
                    hideLoader()
                }
            });
         }));


        $(document).ready(function() {
            $('#sf').focus()
            showLoader()
            ds = "sf=" + $('#sf').val() + "&shopid=<?php echo $shopid; ?>"
            $.ajax({
                data: ds,
                url: "customer-data.php",
                success: function(r) {
                    $('#custmainrow').html(r)
                    hideLoader()
                }
            });
        });

        function editCust(id, fn, ln, shopid) {
            $('#customerid').val(id)
            $('#shopid').val(shopid)
            $('#markModalLabel').html("Change Follow Up Status for " + fn + " " + ln)
            $('#followlabel').html("Set Follow Up Status for " + fn + " " + ln)
            $('#markModal').modal('show')
        }
    </script>
</body>
</html>
