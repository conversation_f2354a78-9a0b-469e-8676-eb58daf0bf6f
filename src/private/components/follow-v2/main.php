<?php
$component = "phonefollowup-v2";
include getHeadGlobal($component);
include getRulesGlobal($component);

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

echo "<body>";
include getHeaderGlobal($component);
include "menu.followup.php";
?>
<main id="reports" class="min-vh-100">

  <div class="container-fluid">

    <div class="d-flex justify-content-between">
        <div class="title">
            <h2>Follow Up Communications <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Search for Customers during the dates selected below"></i></h2>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-4">

			<div class="form-outline mb-4 datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                <input type="text" onkeydown="return false" autocomplete="off" class="form-control" id="sd" data-mdb-toggle="datepicker">
                <label class="form-label" for="sd">Start Date</label>
            </div>

            <div class="form-outline mb-4 datepicker" data-mdb-inline="true" data-mdb-format="mm/dd/yyyy">
                <input type="text" onkeydown="return false" autocomplete="off" class="form-control" id="ed" data-mdb-toggle="datepicker">
                <label class="form-label" for="ed">End Date</label>
            </div>

            <div class="mb-4">
                <button type="button" class="btn btn-primary" onclick="getROList()">Search</button>
            </div>
            <div class="col-md-8"></div>
        </div>
    </div>

    <div id="rolistresults" class="row"></div>

  </div>
</main>

<?php if (!$shopIsReadOnly): ?>
	<div id="commModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content p-4">
				<div class="modal-header ps-1 pe-1">
					<h5 class="modal-title" id="commModalLabel">Add New Communication</h5>
					<button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">   
					<input type="hidden" id="roid"> 
					<div class="form-outline">
					<textarea name="commvalue" id="commvalue" cols="20" class="form-control" rows="4"></textarea>
					<label class="form-label" for="commvalue">Enter your communication info here</label>
					</div>
				</div>
				<div class="modal-footer d-flex justify-content-center">
					<button type="button" class="btn btn-primary" onclick="saveComm()">Save</button>
				</div>
			</div>
		</div>
	</div>
<?php endif; ?>         

<?php include getScriptsGlobal($component);?>

   <script>

	function getROList(){

		ds = "t=data&shopid=<?php echo $shopid; ?>&sd="+$('#sd').val()+"&ed="+$('#ed').val()
		showLoader()
		$.ajax({
			data: ds,
			type: "post",
			url: "phonecommdata.php",
			success: function(r){
				$('#rolistresults').html(r)
				hideLoader()
			},
			error: function (xhr, ajaxOptions, thrownError) {
				console.log(xhr.status);
				console.log(xhr.responseText);
				console.log(thrownError);
			}
		})

	}

			
			
	function addComm(roid){

		$('#commModal').modal('show')
		$('#roid').val(roid)

	}

	function saveComm(){

		roid = $('#roid').val()
		comm = encodeURIComponent($('#commvalue').val())
		ds = "t=update&shopid=<?php echo $shopid; ?>&roid="+roid+"&comm="+comm+"&user=<?php echo $_COOKIE['username']; ?>"
        showLoader()

		$.ajax({
			data: ds,
			type: "post",
			url: "phonecommdata.php",
			success: function(r){
				if (r == "success"){
					getROList()
					$('#commModal').modal('hide')
					$('#roid').val('')

				}
			},
			error: function (xhr, ajaxOptions, thrownError) {
				console.log(xhr.status);
				console.log(xhr.responseText);
				console.log(thrownError);
			}
		})

	}


</script>
</body>
</html>

