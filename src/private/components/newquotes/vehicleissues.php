<?php
require CONN;
$shopid = isset($_GET['shopid'])?$_GET['shopid']:'';
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
$quoteid = isset($_GET['quoteid'])?$_GET['quoteid']:'';
$fees = isset($_GET['fees'])?$_GET['fees']:0;
$quoteroid = $_GET['quoteroid']??'';
$tparts = 0;
$tpartscost = 0;
$tlabor = 0;
$tlaborhrs = 0;
$gtlaborhrs = 0;
$gtpcost = 0;

$empid = isset($_COOKIE['empid'])?$_COOKIE['empid']:'';


$tsublet = 0;
$totaljob = 0;
$egbutton = "";
$stmt = "select readonly,lineitemcomplete,sortvi,estguide,alldatausername,newpackagetype,showpartscostonro,showtechoverhours,defaultsublettaxrate from company where shopid = '$shopid'";
//echo $stmt;
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->store_result();
    $query->bind_result($readonly,$lineitemcomplete,$sortvi,$estguide,$alldatausername,$plan,$showpartscostonro,$showtechoverhours,$sublettaxrate);
    $query->fetch();
}else{
	echo "Company Prepare Failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select pph from settings where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($haspph);
    $query->fetch();
    $query->close();
}

if ($shopid == "demo"){
	$plan = "platinum";
}
if ($estguide == "yes"){
	$egbutton = "yes";
}else{
	$egbutton = "no";
}
if ($alldatausername == "motorfull"){
	$egbutton = "motorfull";
}elseif ($alldatausername == "motorest"){
	$egbutton = "motorest";
}else{
	$egbutton = "no";
}


if($haspph == "yes")
{
$stmt = "SELECT count(DISTINCT complaintid) FROM quotelabor where shopid = ? AND quoteid = ? AND complaintid>0";

if ($query = $conn->prepare($stmt))
{
	$query->bind_param("si",$shopid,$quoteid);
    $query->execute();
	$query->bind_result($distinctcom);
	$query->fetch();
	$query->close();
}

$pphfees = ($distinctcom>0?$fees/$distinctcom:0);
$pphdisc = ($distinctcom>0?round($discount/$distinctcom,2):0);

$stmt = "select target from profitboost where shopid = ?";
if ($query = $conn->prepare($stmt))
{
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($target);
    $query->fetch();
    $query->close();
}
}

$stmt = "select complaintid from quotecomplaints where shopid = ? and quoteid = ? limit 1";
if ($query = $conn->prepare($stmt))
{
$query->bind_param("si",$shopid,$quoteid);
$query->execute();
$query->store_result();
$numrows = $query->num_rows();
 if ($numrows < 1)
 {
 	$stmt = "select coalesce(complaintid,0) from quotecomplaints where shopid = ? order by complaintid desc,quoteid desc limit 1";
	if ($query = $conn->prepare($stmt)){

		$query->bind_param("s",$shopid);
	    $query->execute();
	    $query->store_result();
	    $num_rows = $query->num_rows;
	    if ($num_rows > 0){
		   	$query->bind_result($complaintid);
		    $query->fetch();
		}else{
			$complaintid = 1000;
		}
	    $query->close();
	}

	// now get existing complaints to get the correct display order
	$stmt = "select displayorder from quotecomplaints where shopid = ? and quoteid = ? order by displayorder desc limit 1";
	if ($query = $conn->prepare($stmt)){

		$query->bind_param("si",$shopid,$quoteid);
	    $query->execute();
	    $query->store_result();
	    $num_rows = $query->num_rows;
	    if ($num_rows > 0){
		   	$query->bind_result($lastdisplayorder);
		    $query->fetch();
		}else{
			$lastdisplayorder = 0;
		}
	    $query->close();

	}

	$newcomid = $complaintid + 1;
    $lastdisplayorder += 1;

    $stmt = "insert into quotecomplaints (shopid,quoteid,complaint,acceptdecline,complaintid,issue,displayorder) values ('$shopid',$quoteid,'QUOTE','Pending',$newcomid,'None',$lastdisplayorder)";
	if ($query = $conn->prepare($stmt))
	{
		$query->execute();
		$conn->commit();
		$query->close();
	}

	$stmt = "update quoteparts set complaintid='$newcomid' where shopid = ? and quoteid = ?";
	if ($query = $conn->prepare($stmt))
	{
		$query->bind_param("si",$shopid,$quoteid);
		$query->execute();
		$conn->commit();
		$query->close();
	}

	$stmt = "update quotelabor set complaintid='$newcomid' where shopid = ? and quoteid = ?";
	if ($query = $conn->prepare($stmt))
	{
		$query->bind_param("si",$shopid,$quoteid);
		$query->execute();
		$conn->commit();
		$query->close();
	}

	$stmt = "update quotesublet set complaintid='$newcomid' where shopid = ? and quoteid = ?";
	if ($query = $conn->prepare($stmt))
	{
		$query->bind_param("si",$shopid,$quoteid);
		$query->execute();
		$conn->commit();
		$query->close();
	}

 }
}

$numrows = 0;
$lents = "";
$comsfornewcanned = "";
$query = "select * from quotecomplaints where shopid = ? and quoteid = ? and cstatus = 'no' order by displayorder asc";
if ($stmt = $conn->prepare($query)){
	try{
		$stmt->bind_param("si",$shopid,$quoteid);
		$stmt->execute();
	    $result = $stmt->get_result();
	    $stmt->store_result();
	    $icntr = 1;
	    $runicntr = "1,";
	    while($row = $result->fetch_assoc()) {
?>
			<div style="padding:0px;margin:0px;" class="table-responsive bg_pending" id="issue_<?php echo $row['complaintid'];?>">
			<a id="issuelink_<?php echo $icntr; ?>" name="<?php echo $icntr; ?>"></a>
				<table class="table table-condensed table-striped" id="issue_table_<?php echo $row['complaintid'];?>">
					<tr>
						<td style="padding:0px;" colspan="7">
						<div class="sbphover-row-header alert alert-success" style="margin:5px;padding:10px;border-radius:5px;-webkit-box-shadow: 2px 2px 5px 0px rgba(0,0,0,0.75);
-moz-box-shadow: 2px 2px 5px 0px rgba(0,0,0,0.75);
box-shadow: 2px 2px 5px 0px rgba(0,0,0,0.75);">
							<div style="font-weight:bold" class="issuesec" data-id="<?php echo $row['complaintid'];?>"><span class="glyphicon glyphicon-folder-open"></span> &nbsp;#<?php echo $icntr; ?> &nbsp;<span class="issue"><?php echo trim(strtoupper(iconv('UTF-8', 'ASCII//TRANSLIT',$row['complaint'])));?></span>
							<?php
							if (strlen($row['issue']) > 0){
							?>
							<span style="float:right">CATEGORY: <?php echo strtoupper($row['issue']); ?></span>
							<?php
							}
							?>
                         </div>
						</div>
						</td>
					</tr>
					<?php if(empty($quoteroid) && !$shopIsReadOnly):?>
						<tr>
							<td colspan="7">
							<?php
								$motorecho = "";
								
								$array=array('6062','10798','13931','8219','10250','17032','11727','15600','16916','3741','15487','14401','12869','12349','3044','12021','3607','6038','12146','12873','13931','10250','17032','5520','9299','8934','3439','14993');
								if ($plan == "none"){
									if ($egbutton == "motorfull"){
										$motorfunction = chr(34) . $row['complaintid'] . chr(34) . chr(44) . chr(34) . "39" . chr(34);

										$motorecho.="<button type='button' class='btn btn-primary btn-sm btn-sm-sbp mytooltip' style='width:150px' title='Parts and Labor Guide' onclick='loadMotor(" . $motorfunction . ")'>MOTOR Full Labor Guide</button>";
									}elseif ($egbutton == "motorest"){
										$motorfunction = chr(34) . $row['complaintid'] . chr(34) . chr(44) . chr(34) . "40" . chr(34);
										$motorecho.="<button type='button' class='btn btn-primary btn-sm btn-sm-sbp mytooltip' style='width:150px' title='Parts and Labor Guide' onclick='loadMotor(" . $motorfunction . ")'>MOTOR Estimating Guide</button>";
									}
								}elseif ($plan == "silver"){
									$motorfunction = "";
									$motorecho = "";
								}elseif ($plan == "gold"){
									$motorfunction = chr(34) . $row['complaintid'] . chr(34) . chr(44) . chr(34) . "40" . chr(34);
									$motorecho.="<button type='button' class='btn btn-primary btn-sm btn-sm-sbp mytooltip ' style='width:150px' title='Parts and Labor Guide' onclick='loadMotor(" . $motorfunction . ")'>MOTOR Estimating Guide</button>";
								}elseif ($plan == "platinum" ||  $plan == "platinum trial" || $plan == "premier" || $plan == "premier plus"){
									$motorfunction = chr(34) . $row['complaintid'] . chr(34) . chr(44) . chr(34) . "39" . chr(34);
									$motorecho.="<button type='button' class='btn btn-primary btn-sm btn-sm-sbp mytooltip' style='width:150px' title='Parts and Labor Guide' onclick='loadMotor(" . $motorfunction . ")'>MOTOR Full Labor Guide</button>";
								}
								
								echo $motorecho;
							?>

								
								<button title='Add Parts to this Issue' onclick="addPart(<?php echo $row['complaintid'];?>)" class="btn btn-primary btn-sm btn-sm-sbp mytooltip">Parts</button>
								<button title='Add Labor to this Issue' onclick="addLabor(<?php echo $row['complaintid'];?>)" class="btn btn-primary btn-sm btn-sm-sbp mytooltip">Labor</button>
								<button title='Add a Sublet item to this issue' onclick="addSublet(<?php echo $row['complaintid'];?>)" class="btn btn-primary btn-sm btn-sm-sbp mytooltip">Sublet</button>
								<button onclick="addCannedJob(<?php echo $row['complaintid'];?>)" title='Add a canned job (canned jobs are multiple parts and labor in one job)' class="btn btn-primary btn-sm btn-sm-sbp mytooltip">Canned Jobs</button>
							</td>
						</tr>
					<?php endif; ?>

					<?php 

			// now get the parts
			$currcommid = $row['complaintid'];
			$pquery = "select * from quoteparts where shopid = ? and quoteid = ? and complaintid = ? order by id asc";
			$partsgp = 0;
			$laborgp = 0;
			$subgp = 0;

			if ($pstmt = $conn->prepare($pquery)){
				$pstmt->bind_param("sii",$shopid,$quoteid,$currcommid);
				$pstmt->execute();

				$presult = $pstmt->get_result();
				$pstmt->store_result();
    			$num_parts_rows = $presult->num_rows;

				while ($prow = $presult->fetch_assoc()){

					if (strtolower($prow['taxable']) == 'yes'){
						$taxstr = " (TAX)";
					}else{
						$taxstr = " (NON-TAX)";
					}
					$extcost = round($prow['partcost']*$prow['qty'],2);
					$tparts += round($prow['extprice'],2);
					$tpartscost += round($extcost,2);
					$gtpcost+= round($extcost,2);
					$statuscolor = "white";

					$partsgp += ($prow['extprice'] - ($prow['partcost']*$prow['qty']));

					$onclickPart = !$shopIsReadOnly 
						? "onclick=\"editPart('{$prow['id']}')\"" 
						: "";
?>
				<tr class="sbphover-row">
					<td class="line-hover" <?= $onclickPart; ?> style="width:20%"><strong>(P)</strong> <?php echo strtoupper($prow['partnumber']);?></td>
					<td class="line-hover" <?= $onclickPart; ?> style="width:40%">
					<?php
							echo strtoupper($prow['part']) . "<span style='font-size:8pt'> (Cost Each:" . number_format(round($prow['partcost'],2),2) . "/ Cost Total: " .  number_format($extcost,2) . ")</span>" ;
					?>
					</td>
					<td class="line-hover" <?= $onclickPart; ?> style="width:15%;text-align:center"><?php echo $prow['qty']." @ ".number_format(round($prow['price'],2),2).$taxstr;?></td>
					<td class="line-hover" <?= $onclickPart; ?> style="width:10%;color:red;text-align:center"><?php if ($prow['discount'] > 0){echo $prow['discount']."% Disc";} ?></td>
					<td class="line-hover" <?= $onclickPart; ?> style="width:10%;text-align:right">$<?php echo number_format(round($prow['extprice'],2),2);?></td>
					<td></td>
					
					<?php if( !$shopIsReadOnly ): ?>
						<td class="mytooltip" title="Click to delete this part permanently from the quote" style="width:2%;color:red">
							<?php if(empty($quoteroid)){?><i onclick="deleteItem(<?php echo $prow['id'];?>,'PART')" class="fa fa-times-circle"></i><?php }?>
						</td>
					<?php endif; ?>
				</tr>
<?php
				}
			}

			$lquery = "select extlabor,hours,taxable,complaintid,id,rate,labor,tech from quotelabor where shopid = ? and quoteid = ? and complaintid = ? order by id asc";
			if ($lstmt = $conn->prepare($lquery)){
				$lstmt->bind_param("sii",$shopid,$quoteid,$currcommid);
				$lstmt->execute();

				$lresult = $lstmt->get_result();
				$lstmt->store_result();
				$num_labor_rows = $lresult->num_rows;
				while ($lrow = $lresult->fetch_assoc()){
					$tlabor += $lrow['extlabor'];
					$tlaborhrs += $lrow['hours'];
					$gtlaborhrs =  $gtlaborhrs + $lrow['hours'];
					$labtaxline = strtolower($lrow["taxable"]);

					if ((empty($labtaxline) || $labtaxline == 'yes')){
						$ltax = " (TAX)";
					}else{
						$ltax = " (NON-TAX)";
					}
		
					$onclickLabor = !$shopIsReadOnly 
						? "onclick=\"editLabor('{$lrow['id']}')\"" 
						: "";
?>
				<tr class="sbphover-row">
					<td class="line-hover" <?= $onclickLabor; ?> style="width:20%"><strong>(L)</strong> <?= substr(html_entity_decode(strtoupper($lrow['tech']),ENT_QUOTES | ENT_HTML5),0,15) ?></td>
					<td class="line-hover" <?= $onclickLabor; ?> style="width:40%"><span><?php echo strtoupper($lrow['labor']);?></span></td>
					<td class="line-hover" <?= $onclickLabor; ?> style="width:15%;text-align:center"><?php echo $lrow['hours']." @ ".number_format(round($lrow['rate'],2),2).$ltax;?></td>
					<td></td>
					<td class="line-hover" <?= $onclickLabor; ?> style="width:10%;text-align:right">$<?php echo number_format(round($lrow['extlabor'],2),2);?></td>
					<td></td>

					<?php if( !$shopIsReadOnly ): ?>
						<td class="mytooltip" title="Click to delete this labor item permanently from the repair order" style="width:2%;color:red">
							<?php if(empty($quoteroid)){?><i onclick="deleteItem(<?php echo $lrow['id'];?>,'LABOR')" class="fa fa-times-circle"></i><?php }?>
						</td>
					<?php endif; ?>
				</tr>
<?php
				}
			}
			// add the sublet here

			$squery = "select description,cost,price,cost,complaintid,id,invnum from quotesublet where shopid = ? and quoteid = ? and complaintid = ? order by id asc";
			if ($sstmt = $conn->prepare($squery)){
				$sstmt->bind_param("sii",$shopid,$quoteid,$currcommid);
				$sstmt->execute();

				$sresult = $sstmt->get_result();
				$sstmt->store_result();
				$num_sublet_rows = $sresult->num_rows;
				while ($srow = $sresult->fetch_assoc()){
					$tsublet += round($srow['price'],2);
					$subgp += ($srow['price'] - $srow['cost']);

					if ($sublettaxrate > 0){
                        $stax = " (TAX)";
                    }else{
                        $stax = " (NON-TAX)";
                    }

				$onclickSublet = !$shopIsReadOnly 
					? "onclick=\"editSublet('{$srow['id']}')\"" 
					: "";
?>
				<tr class="sbphover-row">
                    <td class="line-hover" <?= $onclickSublet; ?> style="width:20%"><strong>(S)</strong> Sublet #<?php echo strtoupper($srow['invnum']);?></td>
					<td class="line-hover" <?= $onclickSublet; ?> style="width:40%"><?php echo strtoupper($srow['description']);?></td>
					<td class="line-hover" <?= $onclickSublet; ?> style="width:15%;text-align:center"><?php echo "1 @ ".number_format(round($srow['price'],2),2).$stax;?></td>
					<td class="line-hover" <?= $onclickSublet; ?> style="width:10%">&nbsp;</td>
					<td class="line-hover" <?= $onclickSublet; ?> style="width:10%;text-align:right">$<?php echo number_format(round($srow['price'],2),2);?></td>
					<td></td>
					
					<?php if( !$shopIsReadOnly ): ?>					
						<td class="mytooltip" title="Click to delete this sublet item permanently from the repair order" style="width:2%;color:red">
							<?php if(empty($quoteroid)){?><i onclick="deleteItem(<?php echo $srow['id'];?>,'SUBLET')" class="fa fa-times-circle"></i><?php }?>
						</td>
					<?php endif; ?>
				</tr>
<?php
				}
			}
			
			$totaljob = $tsublet+$tlabor+$tparts;
			$comsfornewcanned .= $currcommid."|";

			$pif = '';

			if($haspph == "yes" && $num_labor_rows > 0)
			{
				$pif1 = $partsgp+$subgp+$tlabor+$pphfees-$pphdisc;
                $pif = $pif1/$tlaborhrs;
			}

			$onclickComplaint = !$shopIsReadOnly 
				? "onclick=\"showPPFIssue('{$row['complaintid']}')\"" 
				: "";
?>
				<tr>
					<td style="border-top:1px silver solid;border-bottom:1px silver solid;font-weight:bold;font-size: 13px;" class="text-left">Total this Concern:<br><?php if($pif!=''){?><span style="margin-left:5px;font-size: 9px;" class="btn btn-xs btn-<?= $pif>=$target?'success':'danger'?>" <?= $onclickComplaint; ?> data-pif="<?= $pif ?>">PPH: <?= asdollars($pif)?></span><?php }?></td>
					<td style="border-top:1px silver solid;border-bottom:1px silver solid;font-weight:bold" class="text-right"><?php if (strtolower($showpartscostonro) == "yes") {?>Parts Cost: <?php echo "$".number_format($tpartscost,2); ?><br>Parts Price: <?php echo "$".number_format($tparts,2); ?><?php }else{?>Parts: <?php echo "$".number_format($tparts,2); ?><?php }?></td>
					<td style="text-align:right;font-weight:bold;border:1px silver solid;">Labor Hrs: <?php echo $tlaborhrs; ?></td>
					<td style="text-align:right;font-weight:bold;border:1px silver solid;">Labor: <?php echo "$".number_format($tlabor,2); ?></td>
					<td style="text-align:right;font-weight:bold;border:1px silver solid;">Sublet: <?php echo "$".number_format($tsublet,2); ?></td>
					<td style="text-align:right;font-weight:bold;border:1px silver solid;">Total: <?php echo "$".number_format($totaljob,2); ?></td>
					
					<?php if( !$shopIsReadOnly ): ?>
						<td></td>
					<?php endif; ?>
				</tr>
			</table>
			</div>

<?php
		$tlabor = 0;
		$tlaborhrs = 0;
		$tparts = 0;
		$tpartscost = 0;
		$tsublet = 0;
		$icntr++;
		$runicntr .= $icntr.",";
	    }


    }
	catch(ErrorException $e) {
	}

}

if (($icntr-1) == 0){
	echo "<br><br><div style='text-align:center;font-weight:bold;font-size:14px;margin:20px;' class='alert alert-danger'>You have no vehicle issues or customer concerns.  Please add them first, then add your parts and labor.  Click the Vehicle Issues button on the left</div><br><br><br><br>";

}

echo "<div style='text-align:center;font-weight:bold;font-size:14px;margin:20px;' class='alert alert-success'>";
if (strtolower($showpartscostonro) == "yes")echo "Total Parts Cost: $".$gtpcost."&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
echo "Total Labor Hrs: ". $gtlaborhrs . "</div>";



if (strpos($lents,"0") === false){
	echo "<input type='hidden' id='lents' value='no'>";
}else{
	echo "<input type='hidden' id='lents' value='yes'>";
}
echo '<input type="hidden" id="comsfornewcanned" value="'.$comsfornewcanned.'">';
echo "<input type='hidden' id='icntrs' value='". substr($runicntr,0,strlen($runicntr)-1) . "'>";

mysqli_close($conn);
?>
