<?php
require(CONN);
?>
<!DOCTYPE html>
<?php

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$quoteid = isset($_REQUEST['quoteid']) ? filter_var($_REQUEST['quoteid'], FILTER_SANITIZE_STRING) : 0;
$empid = (isset($_COOKIE['empid']) ? $_COOKIE['empid'] : '');
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

$stmt = "select readonly,feesonquote,alldatausername,newpackagetype,carfaxlocation,datestarted, defaulttaxrate, defaultlabortaxrate, defaultsublettaxrate,userfee1max,userfee2max,userfee3max,hst,pst,gst,qst,chargehst,chargepst,chargegst,chargeqst,userfee1taxable,userfee2taxable,userfee3taxable,userfee1applyon,userfee2applyon,userfee3applyon,updateinvonadd from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $query->bind_result($readonly, $feesonquote, $alldatausername, $newpackagetype, $cfl, $datestarted, $defaulttaxrate, $defaultlabortaxrate, $defaultsublettaxrate, $userfee1max, $userfee2max, $userfee3max, $hst, $pst, $gst, $qst, $chargehst, $chargepst, $chargegst, $chargeqst, $userfee1taxable, $userfee2taxable, $userfee3taxable, $userfee1applyon, $userfee2applyon, $userfee3applyon, $updateinvonadd);
    $query->fetch();
    $feesonquote = strtolower($feesonquote);
    $userfee1max = doubleval($userfee1max);
    $userfee2max = doubleval($userfee2max);
    $userfee3max = doubleval($userfee3max);
    $query->close();
}

$feesonquote = strtolower($feesonquote);
$plan = strtolower($newpackagetype);
$motor = $alldatausername;

if ($empid == "Admin") {
    $showgpinro = "yes";
    $partsordering = "yes";
} else {
    $stmt = "select showgpinro,partsordering from employees where shopid = '$shopid' and id = $empid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $query->bind_result($showgpinro, $partsordering);
        $query->fetch();
        $query->close();
    }
}

$stmt = "select pph,showgponro,motor from settings where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($haspph, $showgponro, $motorsetting);
    $query->fetch();
    $query->close();
}

if ($plan == "none") {
    if ($motor == "motorfull") {
        $pid = "39";
    } else {
        if ($motor == "motorest") {
            $pid = "40";
        }
    }
}
if ($plan == "gold") {
    $pid = "40";
} else {
    if ($plan == "platinum" || $plan == "premier" || $plan == "premier plus") {
        $pid = "39";
    }
}

$worldpac = "no";
$wpstmt = "select 'yes' as wp from companyadds where shopid = ? and name = 'WorldPac Integrated Parts Ordering'";
if ($query = $conn->prepare($wpstmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $result = $query->get_result();
    $query->store_result();
    $num_rows = $result->num_rows;
    if ($num_rows > 0) {
        $worldpac = "yes";
    }
    $query->close();
}

if ($plan == "gold" || $plan == "platinum" || $plan == "premier" || $plan == "premier plus") {
    $worldpac = "yes";
}

$pdusername = $pdtype = '';

$stmt = "select username,apikey from apilogin where shopid = '$shopid' and companyname='prodemand'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->bind_result($pdusername, $pdtype);
    $query->fetch();
    $query->close();
}

$stmt = "select quotedate,customer,address,city,state,zip,phone,email,year,make,model,color,vin,notes,writer,fee1label,fee2label,fee3label,fee1amount,fee2amount,fee3amount,fee1percent,fee2percent,fee3percent,notes,cid,vid,roid,totalfees,salestax,totalparts,totallabor,totalsublet from quotes where shopid = ? and id = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $quoteid);
    $query->execute();
    $query->bind_result($quotedate, $customer, $address, $city, $state, $zip, $phone, $email, $year, $make, $model, $color, $vin, $notes, $writer, $fee1label, $fee2label, $fee3label, $fee1amount, $fee2amount, $fee3amount, $fee1percent, $fee2percent, $fee3percent, $notes, $cid, $vid, $quoteroid, $totalfees, $salestax, $totalparts, $totallabor, $totalsublet);
    $query->fetch();
    $query->close();
}

if (!empty($cid)) {
    $cstmt = "select taxexempt from customer where shopid = ? and customerid = ?";
//else
//$cstmt = "select taxexempt from customer where shopid = ? and address = ? and city = ? and zip = ?";
    if ($cquery = $conn->prepare($cstmt)) {
        $cquery->bind_param("si", $shopid, $cid);
        $cquery->execute();
        $cquery->store_result();
        $cquery->bind_result($taxexempt);
        $cquery->fetch();
        if ($cquery->num_rows < 1) {
            $taxable = "yes";
        } else {
            if (strtolower($taxexempt) == "yes") {
                $taxable = "no";
            } else {
                $taxable = "yes";
            }
        }
        $cquery->close();
    }
} else
    $taxable = "yes";

$taxablefees = 0;

if (isset($_GET['recalc']) && $_GET['recalc'] == 'y') {
    $salestax = 0;

    $laborstmt = "select coalesce(round(sum(extlabor),2),0) tlabor, COALESCE(SUM(if(upper(taxable)='YES',extlabor,0)),0) from quotelabor where shopid = '$shopid' and quoteid = $quoteid";
    if ($lquery = $conn->prepare($laborstmt)) {
        $lquery->execute();
        $lquery->store_result();
        $lquery->bind_result($totallabor, $taxablelabor);
        $lquery->fetch();
    }

    $partstmt = "select coalesce(round(sum(extprice),2),0) tprts ,COALESCE(SUM(if(taxable='yes',extprice,0)),0) from quoteparts where shopid = '$shopid' and quoteid = $quoteid";
    if ($pquery = $conn->prepare($partstmt)) {
        $pquery->execute();
        $pquery->store_result();
        $pquery->bind_result($totalparts, $taxableparts);
        $pquery->fetch();
    }


    $substmt = "select coalesce(round(sum(price),2),0) from quotesublet where shopid = '$shopid' and quoteid = $quoteid";
    if ($squery = $conn->prepare($substmt)) {
        $squery->execute();
        $squery->store_result();
        $squery->bind_result($totalsublet);
        $squery->fetch();
    }

    if ($feesonquote == "yes") {
        if ($fee1percent > 0) {
            if ($userfee1applyon == "all") {
                $fee1amount = ($totalparts + $totallabor + $totalsublet) * ($fee1percent / 100);
            } else {
                $fee1amount = 0;
                if (stripos($userfee1applyon,"labor") !== false)
                    $fee1amount += $totallabor * ($fee1percent / 100);
                if (stripos($userfee1applyon,"parts") !== false)
                    $fee1amount += $totalparts * ($fee1percent / 100);
                if (stripos($userfee1applyon,"sublet") !== false)
                    $fee1amount += $totalsublet * ($fee1percent / 100);
            }

            $fee1max = $userfee1max;
            if ($fee1max > 0 && $fee1max < $fee1amount) {
                $fee1amount = $fee1max;
            }
            if (($totalparts + $totallabor + $totalsublet) == 0) {
                $fee1amount = 0;
            }
        }

        if ($fee2percent > 0) {
            if ($userfee2applyon == "all") {
                $fee2amount = ($totalparts + $totallabor + $totalsublet) * ($fee2percent / 100);
            } else {
                $fee2amount = 0;
                if (stripos($userfee2applyon, "labor") !== false)
                    $fee2amount += $totallabor * ($fee2percent / 100);
                if (stripos($userfee2applyon,"parts") !== false)
                    $fee2amount += $totalparts * ($fee2percent / 100);
                if (stripos($userfee2applyon,"sublet") !== false)
                    $fee2amount += $totalsublet * ($fee2percent / 100);
            }

            $fee2max = $userfee2max;
            if ($fee2max > 0 && $fee2max < $fee2amount) {
                $fee2amount = $fee2max;
            }
            if (($totalparts + $totallabor + $totalsublet) == 0) {
                $fee2amount = 0;
            }
        }

        if ($fee3percent > 0) {
            if ($userfee3applyon == "all") {
                $fee3amount = ($totalparts + $totallabor + $totalsublet) * ($fee3percent / 100);
            } else {
                $fee3amount = 0;
                if (stripos($userfee3applyon, "labor") !== false)
                    $fee3amount += $totallabor * ($fee3percent / 100);
                if (stripos($userfee3applyon, "parts") !== false)
                    $fee3amount += $totalparts * ($fee3percent / 100);
                if (stripos($userfee3applyon,"sublet") !== false)
                    $fee3amount += $totalsublet * ($fee3percent / 100);
            }

            $fee3max = $userfee3max;
            if ($fee3max > 0 && $fee3max < $fee3amount) {
                $fee3amount = $fee3max;
            }
            if (($totalparts + $totallabor + $totalsublet) == 0) {
                $fee3amount = 0;
            }
        }

        $totalfees = $fee1amount + $fee2amount + $fee3amount;

    } else {
        $fee1amount = $fee2amount = $fee3amount = 0;
        $fee1percent = $fee2percent = $fee3percent = 0;
        $totalfees = 0;
    }

    if ($taxable == 'yes') {
        if ($userfee1taxable == 'Taxable') $taxablefees += $fee1amount;
        if ($userfee2taxable == 'Taxable') $taxablefees += $fee2amount;
        if ($userfee3taxable == 'Taxable') $taxablefees += $fee3amount;

        $ptax = $ltax = $stax = $cantax = 0;

        if ($chargehst == "yes" && $hst > 0) $cantax += $hst;
        if ($chargegst == "yes" && $gst > 0) $cantax += $gst;
        if ($chargepst == "yes" && $pst > 0) $cantax += $pst;
        if ($chargeqst == "yes" && $qst > 0) $cantax += $qst;

        if ($cantax > 0 && $defaulttaxrate == 0 && $defaultlabortaxrate == 0 && $defaultsublettaxrate == 0) {
            $defaulttaxrate = $defaultlabortaxrate = $defaultsublettaxrate = $cantax;
        }

        if ($defaulttaxrate > 0) {
            $ptax = round(($taxableparts + $taxablefees) * ($defaulttaxrate / 100), 2);
        }
        if ($defaultlabortaxrate > 0) {
            $ltax = round($taxablelabor * ($defaultlabortaxrate / 100), 2);
        }
        if ($defaultsublettaxrate > 0) {
            $stax = round($totalsublet * ($defaultsublettaxrate / 100), 2);
        }

        $salestax = round($ptax + $ltax + $stax, 2);
    }
}

$fee1amount = round($fee1amount, 2);
$fee2amount = round($fee2amount, 2);
$fee3amount = round($fee3amount, 2);

$subtotal = sbpround($totallabor + $totalparts + $totalsublet + $totalfees, 2);
$subtotalwofees = $subtotal - $totalfees;
$totalquote = round($subtotal + $salestax, 2);

if (empty($quoteroid) && isset($_GET['recalc']) && $_GET['recalc'] == 'y') {
    $stmt = "update quotes set totalparts = ?,totallabor = ?,totalsublet = ?,totalfees = ?,salestax = ?,totalquote = ?,fee1amount = ?,fee2amount = ?,fee3amount = ? where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("dddddddddsi", $totalparts, $totallabor, $totalsublet, $totalfees, $salestax, $totalquote, $fee1amount, $fee2amount, $fee3amount, $shopid, $quoteid);
        $query->execute();
        $conn->commit();
        $query->close();
    }
    header('location:quote.php?quoteid=' . $quoteid);
    exit;
}

require(COMPONENTS_PRIVATE_PATH . "/gp/gpcalc_quote.php");
require(COMPONENTS_PRIVATE_PATH . "/pif/pifquotecalc.php");
?>
<!--[if IE 9]>
<html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus"> <!--<![endif]-->
<head>
    <meta charset="utf-8">
    <title><?= getPageTitle() ?></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon() ?>' type='image/x-icon'
    / >
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href='<?= SCRIPT ?>/plugins/jquery-ui/jquery-ui.min.css'/>
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        .col-md-6 {
            text-transform: uppercase;

        }

        .inputnav {
            padding: 0px;
            margin: 0px;
        }

        .inputul {
            list-style: none;
            text-align: left;
            padding: 0px;
            border: 0px;
            margin: 1px;

        }

        .input-top {
            text-align: left;
            font-weight: normal;
            font-size: small
        }

        .input-li {
            text-align: left;
            font-size: 12pt;
        }

        .caret {
            float: right;
            margin-top: 8px;
            margin-right: 5px;
        }

        .shopboss-mobile {
            font-size: medium;
        }

        a {
            color: maroon;
        }

        .shopboss-complaint {
            /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#1e5799+0,2b94e5+100 */
            /*background: rgb(30,87,153); /* Old browsers */
            /*background: -moz-linear-gradient(top,  rgba(30,87,153,1) 0%, rgba(43,148,229,1) 100%); /* FF3.6-15 */
            /*background: -webkit-linear-gradient(top,  rgba(30,87,153,1) 0%,rgba(43,148,229,1) 100%); /* Chrome10-25,Safari5.1-6 */
            /*background: linear-gradient(to bottom,  rgba(30,87,153,1) 0%,rgba(43,148,229,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
            /*filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1e5799', endColorstr='#2b94e5',GradientType=0 ); /* IE6-9 */
            background-color: white;
            color: black;
            font-weight: bold;
            font-size: 9pt;
            padding: 5px;
            border: 1px silver solid;
            border-radius: 5px;

        }

        .glyphicon {
            margin-left: 10px;
        }

        .sexy_line {
            margin: 1px 0;
            height: 1px;
            background: black;
            background: -webkit-gradient(linear, 0 0, 100% 0, from(white), to(white), color-stop(50%, black));
        }

        .btn-success-ro {
            height: 25px;
            padding: 0px 20px 0px 20px;
            margin-left: 2px;
            background: rgb(229, 235, 238); /* Old browsers */
            background: -moz-linear-gradient(top, rgba(229, 235, 238, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(245, 247, 249, 1) 100%); /* FF3.6-15 */
            background: -webkit-linear-gradient(top, rgba(229, 235, 238, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(245, 247, 249, 1) 100%); /* Chrome10-25,Safari5.1-6 */
            background: linear-gradient(to bottom, rgba(229, 235, 238, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(245, 247, 249, 1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e5ebee', endColorstr='#f5f7f9', GradientType=0); /* IE6-9 */
            color: black;
            border: 1px silver solid;
            border-radius: 3px;
        }

        .status-button {
            background: rgb(229, 235, 238); /* Old browsers */
            background: -moz-linear-gradient(top, rgba(229, 235, 238, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(245, 247, 249, 1) 100%); /* FF3.6-15 */
            background: -webkit-linear-gradient(top, rgba(229, 235, 238, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(245, 247, 249, 1) 100%); /* Chrome10-25,Safari5.1-6 */
            background: linear-gradient(to bottom, rgba(229, 235, 238, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(245, 247, 249, 1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e5ebee', endColorstr='#f5f7f9', GradientType=0); /* IE6-9 */
            color: black;
            border: 1px silver solid;

        }

        .status-button:hover {
            /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#d7dee3+1,d7dee3+51,d7dee3+100 */
            background: rgb(215, 222, 227); /* Old browsers */
            background: -moz-linear-gradient(top, rgba(215, 222, 227, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(215, 222, 227, 1) 100%); /* FF3.6-15 */
            background: -webkit-linear-gradient(top, rgba(215, 222, 227, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(215, 222, 227, 1) 100%); /* Chrome10-25,Safari5.1-6 */
            background: linear-gradient(to bottom, rgba(215, 222, 227, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(215, 222, 227, 1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#d7dee3', endColorstr='#d7dee3', GradientType=0); /* IE6-9 */

        }

        .btn-success-ro:hover {
            /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#d7dee3+1,d7dee3+51,d7dee3+100 */
            background: rgb(215, 222, 227); /* Old browsers */
            background: -moz-linear-gradient(top, rgba(215, 222, 227, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(215, 222, 227, 1) 100%); /* FF3.6-15 */
            background: -webkit-linear-gradient(top, rgba(215, 222, 227, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(215, 222, 227, 1) 100%); /* Chrome10-25,Safari5.1-6 */
            background: linear-gradient(to bottom, rgba(215, 222, 227, 1) 1%, rgba(215, 222, 227, 1) 51%, rgba(215, 222, 227, 1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#d7dee3', endColorstr='#d7dee3', GradientType=0); /* IE6-9 */
            color: black;
            border: 1px silver solid;
        }

        .btn-sm {
            padding: 1px
        }

        .btn-sm-green {
            padding: 4px;
            font-size: 10pt;
            border: 0px
        }

        .details {

        }

        .details:hover {
            background-color: #FFFF99;
            cursor: pointer;
        }

        .btn-success-ro {
            color: #fff !important;
            background-color: #46c37b !important;
            border-color: black;

        }

        .btn-success-ro:hover {
            background-color: #85D8A8 !important;
            color: black !important;
        }

        .expand-sbp {
            float: right;
            font-size: 14pt;
            color: red;
            cursor: pointer;
            -webkit-transition: all .4s;
            -moz-transition: all .4s;
            -o-transition: all .4s;
            transition: all .4s;
        }

        .issues-parent-expand {
            position: absolute;
            left: 230px;
            top: 0px;
            width: -moz-calc(100% - 231px);
            /* WebKit */
            width: -webkit-calc(100% - 231px);
            /* Opera */
            width: -o-calc(100% - 231px);
            /* Standard */
            width: calc(100% - 231px);
            height: 100%;
            z-index: 1060;
            background-color: #F5F5F5;
            -webkit-transition: all .4s;
            -moz-transition: all .4s;
            -o-transition: all .4s;
            transition: all .4s;
            padding: 20px;
        }

        .col-md-8 {
            -webkit-transition: all .4s;
            -moz-transition: all .4s;
            -o-transition: all .4s;
            transition: all .4s;

        }

        #hider {
            z-index: 9998;
            background-color: #CFDFEF;
            width: -moz-calc(100% - 231px);
            /* WebKit */
            width: -webkit-calc(100% - 231px);
            /* Opera */
            width: -o-calc(100% - 231px);
            /* Standard */
            width: calc(100% - 231px);
            height: 1000px;
            position: absolute;
            left: 230px;
            top: 0px;
            display: none;
        }

        #issues {
            border-left: 2px black solid;
            border-right: 2px black solid;
            border-bottom: 2px black solid;
        }

        .fa.fa-times-circle {
            font-size: large;
        }

        .fa.fa-times-circle:hover {
            font-size: large;
            color: red
        }

        .btn-caution-gp {
            background-color: #FFFF99;
            color: black;
            border: 1px black solid;
        }

        .btn-danger-gp {
            background-color: #FF0000;
            color: white;
            border: 1px black solid;
        }

        .btn-success-gp {
            background-color: #009933;
            color: white;
            border: 1px black solid;
        }

        .btn-caution-pif {
            background-color: #FFFF99;
            color: black;
            border: 1px black solid;
        }

        .btn-danger-pif {
            background-color: #FF0000;
            color: white;
            border: 1px black solid;
        }

        .btn-success-pif {
            background-color: #009933;
            color: white;
            border: 1px black solid;
        }

        .btn-pending {
            background-color: #CCCCCC;
            color: black;
            font-weight: bold;
            border: none;
        }

        .btn-pending:hover {
            background-color: #E1E1E1;
            color: black;
            font-weight: bold;
            border: none;
        }

        .bg_pending {
            background-color: #D2D2D2;
            color: black;
        }

        .table-striped-sbp > tbody > tr:nth-child(2n+1) > td, .table-striped > tbody > tr:nth-child(2n+1) > th {
            background-color: white;
        }

        .modal.fade {
            z-index: 10000000 !important;
        }

        .btn-sbp {
            height: 80px;
            width: 350px;
            font-size: x-large
        }

        #invoice {
            position: absolute;
            width: 90%;
            height: 90%;
            left: 5%;
            top: 50px;
            z-index: 9999;
            background-color: white;
            border: 1px black solid;
            border-radius: 5px;
            -webkit-box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
            -moz-box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
            box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
            display: none;
        }

        #hider {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 120%;
            background-color: gray;
            -ms-filter: "alpha(Opacity=70)";
            filter: alpha(opacity=70);
            -moz-opacity: .7;
            opacity: .7;
            z-index: 9997;
            display: none;
        }

        #invoicebuttons {
            text-align: center;
            position: absolute;
            top: 2px;
            left: 5%;
            width: 90%;
            display: block;
            z-index: 9998;
            background-color: white;
            border: 1px black solid;
            border-radius: 5px;
            padding: 5px;
            -webkit-box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
            -moz-box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
            box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
            display: none;
        }

        .sbphover-row {
            cursor: pointer;
        }

        .sbphover-row-header {
            cursor: pointer;
            color: #003300;
            /*background-color:#FF9900;*/
        }

        .sbphover-row-header:hover {
            color: black;
            /*background-color:#FFFF99*/
        }

        .alert-success {
            border: 2px #003300 solid;
        }

        .btn-sm-sbp {
            height: 26px;
            width: 90px;
        }

        .icon-motor {
            width: 150px;
            padding-bottom: 5px;
            padding-top: 0px;
        }

        #copybtn {
            border: 1px black solid;
            background-color: #EEEEEE;
            cursor: pointer;
            padding: 0px 5px 0px 5px;
            border-radius: 4px;
        }

        .auto-style1 {
            position: relative;
            min-height: 1px;
            float: left;
            width: 100%;
            font-size: large;
            padding-left: 15px;
            padding-right: 15px;
        }

        .auto-style2 {
            font-size: large;
            font-weight: bold;
        }

        .sweet-alert {
            z-index: 99999999 !important;
            position: absolute;

        }

        #customerinfo, #customer {
            background-color: white;
        }

        #customerinfo > .col-md-6:nth-child(4n+2), #customerinfo > .col-md-6:nth-child(4n-1) {
            background-color: whitesmoke;
        }

        @media print {
            body * {
                visibility: hidden;
            }

            #emodal-box iframe {
                visibility: visible;
                height: 100%;
                top: 0;
                bottom: 0;
                position: absolute;
                width: 100%;
                margin: 0;
                padding: 0;
            }
        }

        .no-border {
            border: none;
        }

        .ui-autocomplete {
            z-index: 10000001 !important;
            width: 900px;
            height: 350px;
            overflow-y: scroll;
            overflow-x: hidden;
        }

        @media (min-width: 992px) {
            #editquotemodal .modal-lg {
                width: 1100px
            }
        }


    </style>
</head>
<body>
<?php include(COMPONENTS_PRIVATE_PATH . "/shared/analytics.php"); ?>
<div id="header"></div>
<!-- Page Container -->
<!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">

    <!-- Sidebar -->
    <nav id="sidebar">
        <!-- Sidebar Scroll Container -->
        <div id="sidebar-scroll">
            <!-- Sidebar Content -->
            <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
            <div class="sidebar-content">
                <!-- Side Header -->
                <div class="side-header side-content bg-white-op">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button" data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                        <i class="text-primary">
                            <?php getLogo() ?></i>
                        <span class="h4 font-w600 sidebar-mini-hide">
							</span>
                    </a>
                </div>
                <!-- END Side Header -->

                <!-- Side Content -->
                <div class="side-content-sbp-ro side-content">
                    <ul class="nav-main">
                        <li>
                            <a href="quotes.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Quote List</span></a>
                        </li>
                        <li>
                            <a class="mytooltip" title='Print the Quote' onclick="printQuote()" href="#"> <i class="fa fa-file-text-o"></i><span class="sidebar-mini-hide">Print</span></a>
                        </li>
                        <?php if( !$shopIsReadOnly ): ?>
                            <li>
                                <a onclick="startSendQuote()" href="#"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Send Quote</span></a>
                            </li>
                        <?php endif; ?>
                        <?php if (empty($quoteroid)) { ?>

                            <?php if (!empty($pdusername) && ($plan == 'gold' || $plan == 'platinum' || $plan == 'premier' || $plan == 'premier plus')) {
                                if ($pdtype != '3') {
                                    ?>
                                    <li>
                                        <a class="mytooltip" title='ProDemand' onclick="loadProDemand('<?= $pdtype ?>')" href="#"><i class="fa fa-wrench"></i><span class="sidebar-mini-hide">ProDemand</span></a>
                                    </li>
                                <?php } else {
                                    ?>
                                    <li><a class="nav-submenu" data-toggle="nav-submenu" href="#"><i class="fa fa-wrench"></i><span class="sidebar-mini-hide">ProDemand</span></a>
                                        <ul>
                                            <li>
                                                <a class="mytooltip" title='ProDemand for Light Duty Vehicles' onclick="loadProDemand('1')" href="#"><i class="fa fa-wrench"></i><span class="sidebar-mini-hide">Light Duty</span></a>
                                            </li>
                                            <li>
                                                <a class="mytooltip" title='ProDemand for Heavy Duty Vehicles' onclick="loadProDemand('2')" href="#"><i class="fa fa-wrench"></i><span class="sidebar-mini-hide">Heavy Duty</span></a>
                                            </li>
                                        </ul>
                                    </li>

                                <?php }
                            } ?>

                            <?php if( !$shopIsReadOnly ): ?>
                                <li>
                                    <a onclick="sendCalendar()" href="#"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Send to Calendar</span></a>
                                </li>
                                <li>
                                    <a class="mytooltip" title='Add / Edit / ReOrder the Vehicle Issues' onclick="showIssues()" href="#"><i class="fa fa-clipboard"></i><span class="sidebar-mini-hide">Vehicle Issues</span></a>
                                </li>
                            <?php endif; ?>

                            <?php
                            if (!$shopIsReadOnly && strtolower($partsordering) == 'yes' && ($plan != 'silver' || ($plan == 'silver' && strtotime($datestarted) < strtotime('2022-06-09')))) {
                                ?>
                                <li>
                                    <a class="mytooltip" title='Place an electronic order for parts with Epicor / Nexpart / Worldpac' data-target="#partsordering" onclick="" data-toggle="modal" href="#"><i class="fa fa-credit-card"></i><span class="sidebar-mini-hide">Parts Ordering</span></a>
                                </li>
                                <?php
                            }
                        }
                        ?>
                        <li>
                            <a class="mytooltip" title='Retrieve Quick Lube Info' onclick="getQuickLube()" href="#"><i class="fa fa-book"></i><span class="sidebar-mini-hide">Lube Info</span></a>
                        </li>

                        <?php if( !$shopIsReadOnly ): ?>
                            <li>
                                <?php if (!empty($quoteroid)) { ?>
                                    <a href="<?= COMPONENTS_PRIVATE ?>/ro/ro.php?roid=<?= $quoteroid ?>" style="color:yellow"><i class="fa fa-check" style="color:yellow"></i><span class="sidebar-mini-hide">RO #<?= $quoteroid ?></span></a>
                                <?php } else { ?>
                                    <a class="mytooltip" title='Convert to RO' onclick="$('#btn-convert').show();convertRO()" href="#"><i class="fa fa-check"></i><span class="sidebar-mini-hide">Convert to RO</span></a>
                                <?php } ?>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
                <!-- END Side Content -->
            </div>
            <!-- Sidebar Content -->
        </div>
        <!-- END Sidebar Scroll Container -->
    </nav>
    <!-- END Sidebar -->

    <!-- Header -->
    <header style="text-align:center;font-weight:bold;font-size:18pt;background-color:#c60" id="header-navbar" class="content-mini content-mini-full">

        <span style='color: #fff'>QUOTE #<?php echo $quoteid; ?></span>

        <?php if (strtolower($showgponro) == 'yes' && strtolower($showgpinro) == 'yes'): ?>
            <button style="float:right" onclick="showGP()" id="gpbutton" class="btn <?php echo $gpclass; ?>">GP: <?php echo sbpround($quotegp, 0) * 100; ?>%</button>
        <?php endif; ?>

        <?php if ($haspph == "yes"): ?>

            <button data-pif="<?= $pif ?>" style="float:right" onclick="showPIF()" id="pifbutton" class="btn <?php echo $pifclass; ?>">PPH: <?php echo asdollars($pif, 2); ?></button>

        <?php endif; ?>

        <ul class="nav-header pull-left">
            <li class="hidden-md hidden-lg">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                    <i class="fa fa-navicon"></i>
                </button>
            </li>
            <li class="hidden-xs hidden-sm">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" id="close-sidebar" data-action="sidebar_mini_toggle" type="button">
                    <i class="fa fa-bars"></i>
                </button>
            </li>
            <li>
                <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                <button style="display:none" class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                    <i class="si si-grid"></i>
                </button>
            </li>
            <li class="visible-xs">
                <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                    <i class="fa fa-search"></i>
                </button>
            </li>
            <li>

            </li>
        </ul>

    </header>
    <!-- END Header -->
    <!-- Main Container -->

    <!-- Main Container -->
    <main class="container-fluid" id="main-container" style="display:block;">

        <div id="header-customer" style="margin:10px 1px 1px 10px;" class="row">
            <div class="col-md-4">
                <div style="border:1px silver solid;border-radius:5px; background-color: white" class="row">
                    <div class="col-md-6" style="background-color:white">Customer</div>
                    <div id="customerinfo">
                        <div class="col-md-6"><?php echo substr(strtoupper($customer), 0, 20); ?>&nbsp;<?php if (empty($quoteroid) && !$shopIsReadOnly) { ?>
                                <button onclick="$('#btn-convert').hide();editQuote()" style="float:right;height:18px" class="btn btn-sm btn-warning">Edit</button>
                        <?php } ?>
                        </div>
                        <div class="col-md-6">Address</div>
                        <div class="col-md-6"><?php echo strtoupper($address); ?>&nbsp;</div>
                        <div class="col-md-6" style="">CSZ</div>
                        <div class="col-md-6" style=""><?php echo strtoupper($city . ', ' . $state . ' ' . $zip); ?>&nbsp;</div>
                        <div class="col-md-6">Phone</div>
                        <div class="col-md-6"><?php echo strtoupper($phone); ?>&nbsp;</div>
                        <div class="col-md-6" style="">Email</div>
                        <div class="col-md-6" id="email" style="overflow:hidden;"><?php echo strtoupper($email); ?>&nbsp;</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div style="border:1px silver solid;border-radius:5px;" class="row">
                    <div class="col-md-6" style="background-color:white">Year</div>
                    <div class="col-md-6" style="background-color:white"><?php echo strtoupper($year); ?>&nbsp;<?php if (empty($quoteroid) && !$shopIsReadOnly) { ?>
                            <button onclick="$('#btn-convert').hide();editQuote()" style="float:right;height:18px" class="btn btn-sm btn-warning">Edit</button><?php } ?></div>
                    <div class="col-md-6">Make</div>
                    <div class="col-md-6"><?php echo strtoupper($make); ?>&nbsp;</div>
                    <div class="col-md-6" style="background-color:white">Model</div>
                    <div class="col-md-6" style="background-color:white"><?php echo strtoupper($model); ?>&nbsp;</div>
                    <div class="col-md-6">VIN</div>
                    <div class="col-md-6">
                        <?php
                        $vinlen = strlen($vin);
                        if ($vinlen = 17) {
                            for ($i = 0; $i <= $vinlen; $i++) {
                                $char = substr($vin, $i, 1);
                                if ($i == 9) {
                                    echo "<span style='font-weight:bold;color:red'>";
                                }
                                echo $char;
                            }

                            echo "</span>";
                        }
                        ?>
                        <span id="copybtn" data-clipboard-action="copy" data-clipboard-text="<?php echo strtoupper($vin); ?>">Copy</span></div>
                    <div class="col-md-6" style="background-color:white">Color</div>
                    <div class="col-md-6" style="background-color:white"><?php echo strtoupper($color); ?>&nbsp;</div>
                </div>
            </div>

            <div class="col-md-4">

                <div class="col-md-6" style="background-color:white">Date In</div>
                <div class="col-md-6" style="background-color:white"><?php $DateIn = date_create($quotedate);
                    echo date_format($DateIn, 'm/d/Y') ?>&nbsp;
                </div>

                <?php if (!$shopIsReadOnly): ?>
                    <div class="col-md-6">Comments</div>
                    <div class="col-md-6"><span style="color:maroon;cursor:pointer" onclick="$('#commentmodal').modal('show')">View<?php if (empty($quoteroid)) { ?> / Edit<?php } ?></span></div>
                <?php endif; ?>

                <div class="col-md-6" style="background-color:white">Writer</div>
                <?php if ($shopIsReadOnly): ?>
                    <div class="col-md-6" style="background-color:white"><?= strtoupper($writer) ?></div>
                    <?php else: ?>
                        <?php if (!empty($quoteroid)) { ?>
                            <div class="col-md-6" style="background-color:white"><?= strtoupper($writer) ?></div>
                        <?php } else { ?>
                            <div style="border:1px silver solid;border-radius:4px;" style="background-color:white" class="col-md-6">
                                <nav class="inputnav">
                                    <ul class="inputul" style="background-color:white">
                                        <div class="dropdown input-top dropdown-toggle" id="writer" style="font-size:10pt;color:maroon;cursor:pointer" data-toggle="dropdown"><?php echo strtoupper($writer); ?>
                                            <span style="float:right" class="fa fa-chevron-down"></span></div>
                                        <ul class="dropdown-menu">
                                            <?php
                                            // get the sources from the db
                                            $stmt = "select employeelast, employeefirst from employees where shopid = ? and active = 'yes' and `mode` = 'full'";
                                            if ($query = $conn->prepare($stmt)) {
                                                try {
                                                    $query->bind_param("s", $shopid);
                                                    /* execute statement */
                                                    $query->execute();

                                                    /* bind result variables */
                                                    $query->bind_result($emplast, $empfirst);

                                                    /* fetch values */
                                                    while ($query->fetch()) {
                                                        //echo $emplast.",$".$empfirst;
                                                        echo '<li id="inspection-li" class="input-li"><a href="#" onclick="setWriter(\'' . $empfirst . ' ' . $emplast . '\')">' . $empfirst . ' ' . $emplast . '</a></li>';

                                                    }

                                                } catch (ErrorException $e) {
                                                    echo 'error: ' . $e->getMessage();
                                                }

                                            } else {
                                                echo "6Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                            }
                                            ?>
                                        </ul>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        <?php } ?>
                <?php endif; ?>
            </div>
        </div>

        <div style="margin:10px 1px 1px 10px;" class="row">

            <div id="issues-parent" class="col-md-8">
                <div class="row">
                    <div id="vehicleissuesdiv" class="col-md-12 issuesheader depth" style="left: 0px; top: 0px;font-weight:bold;border-left:2px black solid;border-right:2px black solid;">
                        <span id="expandbutton" onclick="expand()" class="fa fa-arrows-alt expand-sbp" style="float:right"></span>VEHICLE ISSUES AND CUSTOMER CONCERNS
                    </div>
                </div>
                <div id="issues" class="row"></div>
            </div>
            <div id="right-side-buttons" class="col-md-4">
                <div style="padding-left:30px;margin-top:2px;border:1px black solid;border-radius:5px;width:100%" id="fees" class="container">
                    <div class="row">
                        <div class="row">
                            <div class="col-md-8 sbp-green-header">
                                <strong>FEES</strong></div>
                        </div>
                        <?php
                        if (strlen($fee1label) > 0) {
                            ?>
                            <div class="row">
                                <div class="col-md-8" style="left: 0px; top: 0px;background-color:white"><?php echo strtoupper($fee1label); ?></div>
                                <div class="col=md-8 sbp-fees-amount" style="background-color:white">
                                    <?php
                                    if ($fee1percent > 0)
                                        echo sbpround($fee1percent, 2) . '%';
                                    else
                                        echo '$' . sbpround($fee1amount, 2);
                                    ?>
                                </div>
                            </div>
                            <?php
                        }
                        if (strlen($fee2label) > 0) {
                            ?>
                            <div class="row">
                                <div class="col-md-8"><?php echo strtoupper($fee2label); ?></div>
                                <div class="col=md-8 sbp-fees-amount">
                                    <?php
                                    if ($fee2percent > 0)
                                        echo sbpround($fee2percent, 2) . '%';
                                    else
                                        echo '$' . sbpround($fee2amount, 2);
                                    ?>
                                </div>
                            </div>
                            <?php
                        }
                        if (strlen($fee3label) > 0) {
                            ?>
                            <div class="row">
                                <div class="col-md-8" style="background-color:white"><?php echo strtoupper($fee3label); ?></div>
                                <div class="col=md-8 sbp-fees-amount" style="background-color:white">
                                    <?php
                                    if ($fee3percent > 0)
                                        echo sbpround($fee3percent, 2) . '%';
                                    else
                                        echo '$' . sbpround($fee3amount, 2);
                                    ?>
                                </div>
                            </div>
                            <?php
                        }
                        ?>

                        <?php if (empty($quoteroid) && !$shopIsReadOnly) { ?>
                            <button class="btn btn-primary" data-toggle="modal" data-target="#feesmodal">EDIT FEES</button><?php } ?>
                    </div>
                </div>

                <?php
                if (strtolower($scrolltotalswindow) == "yes") {
                    $scrollstr = "position:fixed;width:30%";
                } elseif (strtolower($scrolltotalswindow) == "no") {
                    $scrollstr = "";
                }
                ?>
                <div id="totalsbox" style="<?php echo $scrollstr; ?>" class="row">
                    <div class="col-md-12">
                        <div class="row" style="margin-left:15px;margin-top:10px;">
                            <div style="background-color:white;padding:5px;" class="row">
                                <div class="col-md-4 sbp-ro-label-totals depth" style="left: 0px; top: 0px">TOTAL PARTS</div>
                                <div style="text-align:right" class="col-md-6 sbp-ro-totals"><b>$<?php echo sbpround($totalparts, 2); ?></b></div>
                            </div>
                            <div style="background-color:;padding:5px;" class="row">
                                <div class="col-md-4 sbp-ro-label-totals depth">TOTAL LABOR</div>
                                <div style="text-align:right" class="col-md-6 sbp-ro-totals"><b>$<?php echo sbpround($totallabor, 2); ?></b></div>
                            </div>
                            <div style="background-color:white;padding:5px;" class="row">
                                <div class="col-md-4 sbp-ro-label-totals depth">TOTAL SUBLET</div>
                                <div style="text-align:right" class="col-md-6 sbp-ro-totals"><b>$<?php echo sbpround($totalsublet, 2); ?></b></div>
                            </div>
                            <div style="background-color:;padding:5px;" class="row">
                                <div class="col-md-4 sbp-ro-label-totals depth">TOTAL FEES</div>
                                <div style="text-align:right" class="col-md-6 sbp-ro-totals"><b>$<?php echo sbpround($totalfees, 2); ?></b></div>
                            </div>
                            <div style="background-color:white;padding:5px;" class="row">
                                <div class="col-md-4 sbp-ro-label-totals depth">SUBTOTAL</div>
                                <div style="text-align:right" class="col-md-6 sbp-ro-totals"><b>$<?php echo sbpround(($subtotal), 2); ?></b></div>
                            </div>
                            <div style="background-color:;padding:5px;" class="row">
                                <div class="col-md-4 sbp-ro-label-totals depth">SALES TAX</div>
                                <div style="text-align:right" class="col-md-6 sbp-ro-totals"><b>$<?php echo sbpround($salestax, 2); ?></b></div>
                            </div>
                            <div style="background-color:white;padding:5px;" class="row">
                                <div class="col-md-4 sbp-ro-label-totals depth">TOTAL QUOTE</div>
                                <div style="text-align:right" class="col-md-6 sbp-ro-totals"><b>$<?php echo number_format($totalquote, 2); ?></b></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>
</main>
<!-- END Main Container -->

<!-- Footer -->
<!-- END Footer -->
</div>
<!-- END Page Container -->
<!-- Apps Modal -->
<!-- Apps Modal -->

<form id="feesform">
    <div id="feesmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
        <input id="customerid" type="hidden">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="block block-themed block-transparent remove-margin-b">
                    <div class="block-header bg-primary-dark">
                        <ul class="block-options">
                            <li>
                                <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                            </li>
                        </ul>
                        <h3 class="block-title">Fees</h3>
                    </div>
                    <div id="vehinfo" class="block-content"></div>
                    <div class="block-content">
                        <?php
                        if (!empty($fee1label)) {
                            ?>
                            <div class="row" style="margin-bottom:20px;">
                                <?php if ($fee1percent > 0) {
                                    ?>
                                    <div class="col-sm-2"><b><?php echo $fee1label; ?>:</b></div>
                                    <div class="col-sm-2">
                                        <input style="width:70px;" tabindex="1" value="<?php echo $fee1percent; ?>" class="feepercent" data-id="1" type="text" id="fee1percent" name="fee1percent"> %
                                    </div>
                                    <div class="col-sm-2">$ <input style="width:70px;" tabindex="1" value="<?php echo sbpround($fee1amount, 2); ?>" type="text" id="fee1amount" name="fee1amount"></div>
                                    <?php
                                } else {
                                    ?>
                                    <div class="col-sm-2"><b><?php echo $fee1label; ?>:</b></div>
                                    <div class="col-sm-2">$ <input style="width:70px;" tabindex="1" value="<?php echo sbpround($fee1amount, 2) ?>" type="text" id="fee1amount" name="fee1amount"></div>
                                    <?php
                                } ?>
                            </div>
                            <?php
                        }

                        if (!empty($fee2label)) {
                            ?>
                            <div class="row" style="margin-bottom:20px;">
                                <?php if ($fee2percent > 0) {
                                    ?>
                                    <div class="col-sm-2"><b><?php echo $fee2label; ?>:</b></div>
                                    <div class="col-sm-2">
                                        <input style="width:70px;" tabindex="1" value="<?php echo $fee2percent; ?>" class="feepercent" data-id="2" type="text" id="fee2percent" name="fee2percent"> %
                                    </div>
                                    <div class="col-sm-2">$ <input style="width:70px;" tabindex="1" value="<?php echo sbpround($fee2amount, 2); ?>" type="text" id="fee2amount" name="fee2amount"></div>
                                    <?php
                                } else {
                                    ?>
                                    <div class="col-sm-2"><b><?php echo $fee2label; ?>:</b></div>
                                    <div class="col-sm-2">$ <input style="width:70px;" tabindex="1" value="<?php echo sbpround($fee2amount, 2) ?>" type="text" id="fee2amount" name="fee2amount"></div>
                                    <?php
                                } ?>
                            </div>
                            <?php
                        }

                        if (!empty($fee3label)) {
                            ?>
                            <div class="row">
                                <?php if ($fee3percent > 0) {
                                    ?>
                                    <div class="col-sm-2"><b><?php echo $fee3label; ?>:</b></div>
                                    <div class="col-sm-2">
                                        <input style="width:70px;" tabindex="1" value="<?php echo $fee3percent; ?>" class="feepercent" data-id="3" type="text" id="fee3percent" name="fee3percent"> %
                                    </div>
                                    <div class="col-sm-2">$ <input style="width:70px;" tabindex="1" value="<?php echo sbpround($fee3amount, 2); ?>" type="text" id="fee3amount" name="fee3amount"></div>
                                    <?php
                                } else {
                                    ?>
                                    <div class="col-sm-2"><b><?php echo $fee3label; ?>:</b></div>
                                    <div class="col-sm-2">$ <input style="width:70px;" tabindex="1" value="<?php echo sbpround($fee3amount, 2) ?>" type="text" id="fee3amount" name="fee3amount"></div>
                                    <?php
                                } ?>
                            </div>
                            <?php
                        }

                        ?>
                    </div>
                </div>
                <div style="margin-top:20px;" class="modal-footer">
                    <button class="btn btn-md btn-info" type="button" onclick="saveFees()">Save</button>
                    <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</form>


<div id="issuemodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Edit Issue</h3>
                </div>
                <div class="block-content">
                    <input type="hidden" id="issuecomid">
                    <input type="text" style="border-top: 0;border-left: 0;border-right: 0;" id="issue" class="form-control sbp-form-control">
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-info" type="button" onclick="saveIssue()">Save</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="custconfirm" class="modal fade" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true" style="z-index:10001 !important;">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Edit Customer</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    Customer <span id="ecust"></span> information has been changed, Do you want to update the Customer information or update the quote only?
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-info" type="button" onclick="update_customer()">Update Customer</button>
                <button class="btn btn-md btn-default" type="button" onclick="check_vehicle()" data-dismiss="modal">Update Quote</button>
            </div>
        </div>
    </div>
</div>
<div id="vehconfirm" class="modal fade" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true" style="z-index:10001 !important;">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Edit Vehicle</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    Vehicle information has been changed, Do you want to create a new vehicle with new information or update the current vehicle.
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-info" type="button" onclick="create_vehicle('')">Create New Vehicle</button>
                <button class="btn btn-md btn-default" type="button" onclick="update_vehicle('')" data-dismiss="modal">Update Vehicle</button>
            </div>
        </div>
    </div>
</div>


<div id="partsordering" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Select Parts Ordering System</h3>
                </div>
                <div class="block-content">
                    <div class="row">
                        <div style="text-align:center" class="col-md-12">
                            <?php
                            $ptapikey = "";
                            $stmt = "select apikey from partstechshops where shopid = '$shopid'";
                            if ($query = $conn->prepare($stmt)) {
                                $query->execute();
                                $query->bind_result($ptapikey);
                                $query->fetch();
                                $query->close();
                            }


                            if (!empty($ptapikey)) {
                                ?>
                                <button id="partstechbutton" class="btn btn-danger btn-lg btn-sbp" onclick="launchPartsTech('partstech')" type="button">PartsTech Parts Ordering</button><br><br>
                                <?php
                            }
                            $epicorsupp = "";
                            $stmt = "select shopid FROM epicor WHERE shopid = '$shopid'";
                            if ($query = $conn->prepare($stmt)) {
                                $query->execute();
                                $query->bind_result($epicorsupp);
                                $query->fetch();
                                $query->close();
                            }

                            if (!empty($epicorsupp)) {

                                ?>
                                <button id="epicorbutton" class="btn btn-success btn-lg btn-sbp" onclick="showOrdering('epicor')" type="button">Epicor Parts Ordering</button><br><br>
                                <?php
                            }
                            ?>
                            <div id="epicorselect" class="row" style="display:none">
                                <div style="width:50%;margin:auto;border:2px silver outset;padding:20px;border-radius:5px;">
                                    <div class="form-material floating">
                                        <select id="epicorsupplier" class="form-control sbp-form-control">
                                            <?php
                                            $stmt = "select suppliername from epicor where shopid = ?";
                                            if ($query = $conn->prepare($stmt)) {
                                                $query->bind_param("s", $shopid);
                                                $query->execute();
                                                $result = $query->get_result();
                                                $query->store_result();
                                                while ($row = $result->fetch_assoc()) {
                                                    ?>
                                                    <option value="<?php echo $row['suppliername']; ?>"><?php echo $row['suppliername']; ?></option>
                                                    <?php
                                                }
                                            }
                                            ?>
                                        </select>
                                        <label for="epicorsupplierselect">Select Supplier</label>
                                    </div>
                                    <br>
                                    <div class="form-material floating">
                                        <select id="epicortype" class="form-control sbp-form-control">
                                            <option value="Transfer">Build Estimate</option>
                                            <option value="Order">Order Parts</option>
                                            <option value="TransferAndOrder">Both</option>
                                        </select>
                                        <label for="epicormodeselect">Select Mode</label>
                                    </div>
                                </div>
                                <br>
                                <button type="button" onclick="launchEpicor()" class="btn btn-success">Launch Epicor</button>
                                <button type="button" onclick="cancelOrdering('epicor')" class="btn btn-default">Cancel Epicor</button>
                            </div>
                            <?php
                            $nexpartsupp = "";
                            $stmt = "select `desc` FROM nexpart WHERE shopid = '$shopid'";
                            if ($query = $conn->prepare($stmt)) {
                                $query->execute();
                                $query->bind_result($nexpartsupp);
                                $query->fetch();
                                $query->close();
                            }

                            if (!empty($nexpartsupp)) {

                                ?>
                                <button id="nexpartbutton" class="btn btn-primary btn-lg btn-sbp" onclick="showOrdering('nexpart')" type="button">Nexpart Parts Ordering</button><br><br>
                                <?php
                            }
                            ?>
                            <div id="nexpartselect" class="row" style="display:none">
                                <div style="width:50%;margin:auto;border:2px silver outset;padding:20px;border-radius:5px;">
                                    <div class="form-material floating">
                                        <select id="nexpartcreds" class="form-control sbp-form-control">
                                            <?php
                                            $stmt = "select `desc`,username,password from nexpart where shopid = '$shopid'";
                                            if ($query = $conn->prepare($stmt)) {
                                                $query->execute();
                                                $result = $query->get_result();
                                                $query->store_result();
                                                while ($row = $result->fetch_assoc()) {
                                                    ?>
                                                    <option value="<?php echo $row['username'] . "|" . $row['password']; ?>"><?php echo $row['desc']; ?></option>
                                                    <?php
                                                }
                                            }
                                            ?>
                                        </select>
                                        <label for="epicorsupplierselect">Select Supplier</label>
                                    </div>
                                    <br>
                                </div>
                                <br>
                                <button type="button" onclick="launchNexpart()" class="btn btn-primary">Launch Nexpart</button>
                                <button type="button" onclick="cancelOrdering('nexpart')" class="btn btn-default">Cancel Nexpart</button>
                            </div>
                            <?php
                            if ($worldpac == 'yes') {
                                ?>
                                <button id="worldpacbutton" onclick="launchWorldpac()" class="btn btn-warning btn-lg btn-sbp" type="button">WorldPac Parts Ordering</button>
                                <?php
                            }

                            $rlstmt = "SELECT uid FROM repairlinkshops where shopid = ?";
                            if ($rlquery = $conn->prepare($rlstmt)) {
                                $rlquery->bind_param("s", $shopid);
                                $rlquery->execute();
                                $rlquery->bind_result($rluid);
                                $rlquery->fetch();
                                $rlquery->close();
                            }

                            if (!empty($rluid)) {
                                ?>
                                <br><br/>
                                <button type="button" onclick="launchRepairLink('<?= $rluid ?>')" class="btn btn-info btn-lg btn-sbp">RepairLink Parts Ordering</button>
                                <?php
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>


<div id="sendquotemodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Send Quote</h3>
                </div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control sbpucase" style="padding:20px;text-transform:uppercase" tabindex="1" id="emailto" name="emailto" value="<?php echo strtoupper($email); ?>" type="email">
                                    <label for="emailto">Enter an email address to send this quote to</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" style="padding:20px;text-transform:uppercase" tabindex="1" id="textto" name="textto" value="<?php echo str_ireplace(array('-', 'cell', 'work', 'home', ',', ' ', ',', '(', ')', ':'), '', $phone); ?>" type="text">
                                    <label for="textto">Enter phone number to send this quote to</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-info" type="button" onclick="sendQuote('email')">Send Email</button>
                <button class="btn btn-md btn-primary" type="button" onclick="sendQuote('text')">Send Text</button>
                <button class="btn btn-md btn-warning" type="button" onclick="sendQuote('both')">Send Both</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="commentmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 id="tctitle" class="block-title">Comments</h3>
                </div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div style="margin-bottom:20px;" class="col-md-12">
                                <div class="form-material floating">
                                    <textarea class="form-control sbp-form-control" <?php if (empty($quoteroid)){ ?>onblur="saveComments()"<?php } ?> style="padding:20px;" tabindex="1" id="comments"><?php echo $notes; ?></textarea>
                                    <label for="emailmessageaddress">Comments<?php if (empty($quoteroid)) { ?> (auto-saved)<?php } ?></label>
                                </div>
                            </div>
                            <div style="margin-bottom:20px;" id="commentssaved" class="col-md-12">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>


<div id="partstechmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" onclick="closePartsTechQuote()" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 id="tctitle" class="block-title">Open Parts Tech Quotes</h3>
                </div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-12">
                            <h3 id="partstechheader"></h3>
                            <p id="partstechmessage"></p>
                            <table id="partstechtable" class="table table-condensed table-striped table-header-bg">
                                <thead>
                                <tr>
                                    <td>Quote ID</td>
                                    <td class="text-right">Total</td>
                                </tr>
                                </thead>
                                <tbody id="partstechrows">

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-warning" type="button" onclick="openPartsTech();closePartsTechQuote()">Create New Quote</button>
                <button class="btn btn-md btn-default" type="button" onclick="closePartsTechQuote()">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="dupcusmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 id="tctitle" class="block-title">Duplicate Customers</h3>
                </div>
                <div class="block-content" style="max-height: 800px;overflow: auto;">
                    <div class="row">
                        <div class="col-md-12">
                            <p style="color:red">Duplicate customer exists with similar name or phone. Please select below the existing customer OR click create new customer</p>

                            <center>
                                <button class="btn btn-md btn-warning" type="button" onclick="createnewCustomer()">Create New Customer <?= !empty($customer) ? '"' . $customer . '"' : '' ?></button>
                                <br><br><b>OR</b><br><br></center>

                            <table class="table table-condensed table-striped table-header-bg">
                                <thead>
                                <tr>
                                    <td>Customer Name</td>
                                    <td>Address</td>
                                    <td>Phone</td>
                                    <td></td>
                                </tr>
                                </thead>
                                <tbody id="dupcusrows">

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="vehmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 id="tctitle" class="block-title">Customer Vehicles</h3>
                </div>
                <div class="block-content" style="max-height: 800px;overflow: auto;">
                    <div class="row">
                        <div class="col-md-12">
                            <p style="color:red">Please select the vehicles of selected customer OR click create new vehicle</p>

                            <center>
                                <button class="btn btn-md btn-warning" type="button" onclick="createnewVehicle()">Create New
                                    Vehicle <?= !empty($year) ? '"' . $year . ' ' . $make . ' ' . $model . '"' : '' ?></button>
                                <br><br><b>OR</b><br><br></center>

                            <table class="table table-condensed table-striped table-header-bg">
                                <thead>
                                <tr>
                                    <td>Year</td>
                                    <td>Make</td>
                                    <td>Model</td>
                                    <td>VIN</td>
                                    <td></td>
                                </tr>
                                </thead>
                                <tbody id="vehrows">

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="quicklubemodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 id="tctitle" class="block-title">Quick Lube Data</h3>
                </div>
                <div class="block-content" style="">
                    <div class="row">
                        <div class="col-md-12">
                            <div style="margin-bottom:20px;" id="quicklubedata" class="col-md-12">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <?php if( !$shopIsReadOnly ): ?>
                    <button class="btn btn-md btn-danger" type="button" onclick="clearQuickLube()" data-dismiss="modal">Clear Lube Info</button>
                <?php endif; ?>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="editquotemodal" class="modal fade" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true" style="z-index: 10000 !important;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 id="tctitle" class="block-title">Edit Customer / Vehicle</h3>
                </div>
                <div class="block-content">

                    <form id="quoteform" name="quoteform">
                        <input id="cid" name="cid" value="<?= $cid ?>" type="hidden"/>
                        <input id="vid" name="vid" value="<?= $vid ?>" type="hidden"/>

                        <div class="row">
                            <div class="col-md-6" style="border:none">

                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input autocomplete="off" class="form-control sbp-form-control" type="text" data-cust="<?= $customer ?>" value="<?= $customer ?>" id="customer" name="customer" tabindex="1"/>
                                            <label id="customerlabel" for="material-text2">Customer (L,F)</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input class="form-control sbp-form-control" tabindex="1" type="text" value="<?= $address ?>" id="address" name="address">
                                            <label id="addresslabel" for="material-text2">Address</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input class="form-control sbp-form-control" tabindex="1" type="text" value="<?= $zip ?>" id="zip" name="zip">
                                            <label id="ziplabel" for="material-text2">Zip</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input class="form-control sbp-form-control" tabindex="1" type="text" value="<?= $city ?>" id="city" name="city">
                                            <label id="citylabel" for="material-text2">City</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input class="form-control sbp-form-control" tabindex="1" type="text" value="<?= $state ?>" id="state" name="state">
                                            <label id="statelabel" for="material-text2">State</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input class="form-control sbp-form-control" tabindex="1" type="text" value="<?= $phone ?>" id="phone" name="phone">
                                            <label id="phonelabel" for="material-text2">Phone</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input class="form-control sbp-form-control" tabindex="1" type="text" value="<?= $email ?>" id="email" name="email">
                                            <label id="emaillabel" for="material-text2">Email</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6" style="border:none">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input class="form-control sbp-form-control" tabindex="1" type="text" value="<?= $year ?>" id="year" name="year">
                                            <label id="yearlabel" for="material-text2">Year</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input class="form-control sbp-form-control" tabindex="1" type="text" value="<?= $make ?>" id="make" name="make">
                                            <label id="makelabel" for="material-text2">Make</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input class="form-control sbp-form-control" tabindex="1" type="text" value="<?= $model ?>" id="model" name="model">
                                            <label id="modellabel" for="material-text2">Model</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input class="form-control sbp-form-control" tabindex="1" type="text" value="<?= $color ?>" id="color" name="color">
                                            <label id="colorlabel" for="material-text2">Color</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <div class="form-material">
                                            <input class="form-control sbp-form-control" tabindex="1" type="text" value="<?= $vin ?>" id="vin" name="vin">
                                            <label id="vinlabel" for="material-text2">VIN <a href="javascript:void(null)" onclick="decodeVIN()">(Decode VIN)</a></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-warning btn-md" style="display:none" id="btn-convert" type="button" onclick="convertRO()">Convert to RO</button>
                <button class="btn btn-md btn-primary btn-md" type="button" onclick="check_customer()">Save</button>
                <button class="btn btn-md btn-default btn-md" type="button" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?= getMotorModal($shopid); ?>

<button id="motorbutton" type="button" style="display:none;position:absolute;top:15px;right:0px;z-index:10000" onclick="closeMotor()" class="btn btn-lg btn-warning">Close MOTOR</button>
<iframe scrolling="yes" style="position:fixed;top:1%;left:0.5%;width:99%;height:98%;border:2px black solid;background-color:white;display:none;z-index:9999;padding:2px;" id="motorframe"></iframe>

<div id="hider"></div>
<input type="hidden" id="createcus">
<input type="hidden" id="createveh">

<script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
<script src="<?= SCRIPT ?>/tipped.js"></script>

<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->

<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
<script src="<?= SCRIPT ?>/app.js"></script>
<script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
<script src='<?= SCRIPT ?>/plugins/jquery-ui/jquery-ui.js'></script>
<script src="<?= SCRIPT ?>/emodal.js?v=6.1"></script>
<script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
<script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/clipboard/clipboard.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/annyang/2.6.0/annyang.js"></script>
<!-- Page Plugins -->


<script type="text/javascript">

    activecomid = ''

    $(document).ready(function () {

        $('#spinner').hide()

        var clipboard = new ClipboardJS('#copybtn');

        clipboard.on('success', function (e) {
            console.info('Action:', e.action);
            console.info('Text:', e.text);
            console.info('Trigger:', e.trigger);

            e.clearSelection();
        });

        clipboard.on('error', function (e) {
            console.error('Action:', e.action);
            console.error('Trigger:', e.trigger);
        });

        if (localStorage.getItem("expandvi") == "yes") {
            expand()
        }

        $('#main-container').fadeIn('slow')
        loadIssues()

        $('#customer').autocomplete({
            source: 'customerdata.php', minLength: 2,
            select: function (event, ui) {

                tar = ui.item.orival.split("~")

                $('#customer').val($.trim(tar[0]) + ", " + $.trim(tar[1]))
                $('#address').val($.trim(tar[2]))
                $('#city').val($.trim(tar[3]))
                $('#state').val($.trim(tar[4]))
                $('#zip').val($.trim(tar[5]))
                $('#phone').val($.trim(tar[6]))
                $('#email').val($.trim(tar[7]))
                $('#year').val($.trim(tar[8]))
                $('#make').val($.trim(tar[9]))
                $('#model').val($.trim(tar[10]))
                $('#vin').val($.trim(tar[11]))
                $('#color').val($.trim(tar[12]))
                $('#cid').val($.trim(tar[13]))
                $('#vid').val($.trim(tar[14]))
                return false
            }
        })

        $('.feepercent').blur(function () {

            id = $(this).data('id');
            perval = $(this).val();
            $.post("getfeeprice.php", {
                shopid:<?php echo $shopid; ?>,
                quoteid:<?php echo $quoteid; ?>,
                id: id,
                percent: perval
            }, function (data) {
                if (data == '') data = 0
                $('#fee' + id + 'amount').val(data);
            });
        });

        <?php if(empty($quoteroid)){?>

        $('#issues').on('click', '.issuesec', function () {

            $('#issue').val($(this).find('.issue').html())
            $('#issuecomid').val($(this).attr('data-id'))
            $('#issuemodal').modal('show')
            setTimeout(function () {
                $('#issue').focus()
            }, 1000)

        });

        <?php }?>
    })


    function loadIssues() {

        $.ajax({
            data: "quoteid=<?php echo $quoteid;?>&fees=<?= $totalfees?>&shopid=<?php echo $shopid;?>&cid=<?= $cid; ?>&quoteroid=<?= $quoteroid?>",
            url: "vehicleissues.php",
            error: function (xhr, ajaxOptions, thrownError) {
                $('#spinner').hide()
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
            success: function (r) {
                $('#issues').html(r)
                $('#spinner').hide()

                issuenums = $('#icntrs').val()
                issuear = issuenums.split(",")
                links = ""
                for (i = 0; i <= issuear.length - 2; i++) {
                    links += "<a onclick='scrollToIssue(\"issuelink_" + (i + 1) + "\")' style='margin-left:10px;' href='#'>" + (i + 1) + "</a>"
                }
                $('#vehicleissuesdiv').html("VEHICLE ISSUES AND CUSTOMER CONCERNS <span style='margin-left:20px;'>Links to Issues: " + links + "</span><span id='expandbutton' onclick='expand()' class='fa fa-arrows-alt expand-sbp' style='float:right'></span>")

                $('.sbphover-row').mouseover(function () {
                    $.each($(this).find('td.line-hover'), function () {
                        $(this).css("background-color", "#FFFF99");
                    });
                });
                $('.sbphover-row').mouseout(function () {
                    $.each($(this).find('td.line-hover'), function () {
                        $(this).css("background-color", "");
                    });
                });

                $('.sbphover-row-header').mouseover(function () {
                    $(this).css("background-color", "#FFFF99");
                });
                $('.sbphover-row-header').mouseout(function () {
                    $(this).css("background-color", "");
                });

            }
        });

    }


    function expand() {
        if ($('#issues-parent').hasClass('issues-parent-expand')) {
            //$('#issues-parent').hide()
            $('#right-side-buttons').hide();
            $('#issues-parent').removeClass('issues-parent-expand', 700)
            $('#issues-parent').addClass('col-md-9', 700)
            $('#expandbutton').removeClass('fa-compress').addClass('fa-expand')
            $('#header-customer').show()
            $('#issues-parent').show()
            setTimeout(function () {
                $('#right-side-buttons').fadeIn();
            }, 500)
            localStorage.setItem("expandvi", "no")
        } else {
            $('#issues-parent').hide()
            $('#right-side-buttons').fadeOut();
            $('#header-customer').hide()
            $('#issues-parent').addClass('issues-parent-expand', 500).removeClass('col-md-9', 500).css("z-index", "0")
            $('#expandbutton').removeClass('fa-expand').addClass('fa-compress')
            $('#issues-parent').fadeIn()
            $('#issues-status').val("expanded")
            localStorage.setItem("expandvi", "yes")
        }
    }

    function closeMotor() {
        $('body').css({'overflow': 'auto'});
        $.ajax({
            data: "t=check&shopid=<?php echo $shopid; ?>&roid=<?php echo $quoteid;  ?>",
            url: "updatemotorquote.php",
            success: function (r) {
                if (r == "found") {
                    showMotorInfo()
                } else {
                    $('#motorframe').attr("src", "").hide()
                    $('#hider').hide()
                    $('#motorbutton').hide()
                }
            }
        });


    }

    function showMotorInfo() {

        $('#motorframe').attr("src", "").hide()
        $('#hider').hide()
        $('#motorbutton').hide()

        eModal.iframe({
            title: 'Motor',
            url: "motorlist.php?roid=<?php echo $quoteid;  ?>&comid=" + activecomid,
            size: eModal.size.xl,
            buttons: [
                {text: 'Close', style: 'warning', close: true}
            ]
        });

    }

    function loadMotor(comid, pid) {
        <?php if($motorsetting == 'no'){?>
        $('#motorModal').modal('show');
        <?php }else{ ?>
        activecomid = comid
        motorsrc = "loadmotor.php?roid=<?php echo $quoteid; ?>&comid=quote&vin=<?= $vin?>&shopid=<?php echo $shopid; ?>&pid=" + pid
        $('#motorframe').attr("src", motorsrc).show()
        $('#hider').show()
        $('#motorbutton').show()
        <?php }?>
    }

    function addPart(comid) {

        eModal.iframe({
            title: 'Add a PART to this QUOTE',
            url: 'addpart.php?shopid=<?php echo $shopid . "&quoteid=" . $quoteid?>&comid=' + comid,
            size: eModal.size.xl,
            buttons: [
                {text: 'Close', style: 'info', close: true},
            ]

        });
    }

    function editPart(id) {

        <?php if(empty($quoteroid)){?>

        eModal.iframe({
            title: 'Edit PART of this QUOTE',
            url: 'partdetail.php?shopid=<?php echo $shopid . "&quoteid=" . $quoteid?>&quotepid=' + id,
            size: eModal.size.xl,
            buttons: [
                {text: 'Close', style: 'info', close: true},
            ]

        });

        <?php }?>
    }

    function showIssues() {
        eModal.iframe({
            title: 'Vehicle Issues',
            url: 'listissues.php?quoteid=<?php echo $quoteid; ?>',
            size: eModal.size.xl,
            buttons: [
                {text: 'Close', style: 'warning', close: true}

            ]

        });

    }

    function getQuickLubeData(y, m, md, e) {

        $('#spinner').show()
        ds = "shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&t=quicklubedata&yr=" + y + "&md=" + md + "&mk=" + m + "&eng=" + e
        //console.log(ds)
        $.ajax({

            data: ds,
            url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
            type: "post",
            success: function (r) {
                //console.log(r)
                json = JSON.parse(r)
                //console.log("typof:"+typeof json)
                if (typeof json == 'object') {
                    //console.log("object")
                    jsonarray = []
                    item = {}
                    item['year'] = json[0].year
                    item['make'] = json[0].make
                    item['model'] = json[0].model
                    item['engine'] = json[0].engine
                    item['viscosity'] = json[0].viscosity_1
                    item['oilCapacity'] = json[0].oilCapacity
                    item['oilCapacityDescription'] = json[0].oilCapacityDescription
                    item['oilDrainPlugTorque'] = json[0].oilDrainPlugTorque
                    item['OilFilterBrand_1'] = json[0].OilFilterBrand_1
                    item['OilFilterPartNumber_1'] = json[0].OilFilterPartNumber_1
                    item['OilFilterBrand_2'] = json[0].OilFilterBrand_2
                    item['OilFilterPartNumber_2'] = json[0].OilFilterPartNumber_2
                    item['CoolantCapacity'] = json[0].CoolantCapacity
                    item['CoolantCapacityDescription'] = json[0].CoolantCapacityDescription
                    item['shopid'] = "<?php echo $shopid; ?>";
                    item['vehid'] = "<?php echo $vid; ?>";
                    item['vin'] = "<?php echo $vin; ?>";

                    jsonarray.push(item)
                    jsonarray = encodeURIComponent(JSON.stringify(jsonarray))
                    //console.log("jsonarray:"+jsonarray)

                    // post to the quicklubedata table
                    $.ajax({

                        data: "shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&t=newquicklubedata&json=" + jsonarray,
                        url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
                        type: "post",
                        success: function (r) {
                            if (r == "Unable to save info without VIN") {
                                swal(r)
                            } else {
                                $('#spinner').hide()
                                getQuickLube()
                            }
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                            $('#spinner').hide()
                        }
                    })


                } else {
                    if (r == "no data") {
                        $('#quicklubemodal').modal('hide')
                        swal("No Data Available")
                    }
                }

                $('#spinner').hide()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#spinner').hide()
            }
        })

    }

    function getEngines(y, m, md) {

        $('#spinner').show()
        ds = "shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&t=quicklubedata&yr=" + y + "&md=" + md + "&mk=" + m
        //console.log(ds)
        $.ajax({

            data: ds,
            url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
            type: "post",
            success: function (r) {
                //console.log("engines")

                // now check what data was found and display to user for final selections
                if (r.indexOf("|") > 0) {

                    // we have results
                    rar = r.split("|")
                    typ = rar[0]
                    //console.log(typ)
                    json = JSON.parse(rar[1])
                    //console.log(json)

                    if (typ == "ymme") {
                        table = "<table class='table table-condensed table-striped table-header-bg'>"
                        table += "<thead><tr><td>Year</td><td>Make</td><td>Model</td><td>Engine</td></tr></thead>"
                        table += "<tbody>"
                        for (i = 0; i < json.length; i++) {
                            obj = json[i]
                            //for (key in obj)
                            table += "<tr><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.year + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.make.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.model.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.engine_name.toUpperCase() + "</td></tr>"
                        }

                        table += "</tbody></table>"
                        $('#quicklubedata').html(table)
                        $('#quicklubemodal').modal('show').draggable()
                        $('#spinner').hide()
                    }
                } else {
                    if (r == "no data") {
                        $('#quicklubemodal').modal('hide')
                        swal("No Data Available")
                    }
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#spinner').hide()
            }
        })


    }

    function getModels(selmake) {

        $('#spinner').show()
        ds = "shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&t=quicklubedata&yr=<?php echo $year; ?>&md=&mk=" + selmake
        $.ajax({

            data: ds,
            url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
            type: "post",
            success: function (r) {

                // now check what data was found and display to user for final selections
                if (r.indexOf("|") > 0) {

                    // we have results
                    rar = r.split("|")
                    typ = rar[0]
                    json = JSON.parse(rar[1])

                    if (typ == "ymm") {
                        table = "<table class='table table-condensed table-striped table-header-bg'>"
                        table += "<thead><tr><td>Year</td><td>Make</td><td>Model</td></tr></thead>"
                        table += "<tbody>"
                        for (i = 0; i < json.length; i++) {
                            obj = json[i]
                            //for (key in obj)
                            table += "<tr><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.year + "</td><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.make.replace("-", " ").toUpperCase() + "</td><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.model_name + "</td></tr>"
                        }

                        table += "</tbody></table>"
                        $('#quicklubedata').html(table)
                        $('#quicklubemodal').modal('show').draggable()
                        $('#spinner').hide()
                    }
                    if (typ == "ymme") {
                        table = "<table class='table table-condensed table-striped table-header-bg'>"
                        table += "<thead><tr><td>Year</td><td>Make</td><td>Model</td><td>Engine</td></tr></thead>"
                        table += "<tbody>"
                        for (i = 0; i < json.length; i++) {
                            obj = json[i]
                            //for (key in obj)
                            table += "<tr><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.year + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.make.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.model.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.engine_name.toUpperCase() + "</td></tr>"
                        }

                        table += "</tbody></table>"
                        $('#quicklubedata').html(table)
                        $('#quicklubemodal').modal('show').draggable()
                        $('#spinner').hide()
                    }
                } else {
                    if (r == "no data") {
                        $('#quicklubemodal').modal('hide')
                        swal("No Data Available")
                    }
                }

            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#spinner').hide()
            }
        })


    }

    function getQuickLube() {

        $('#spinner').show()
        ds = "shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&t=storedquicklubedata&vin=<?php echo $vin; ?>";
        $.ajax({

            data: ds,
            url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
            type: "post",
            success: function (r) {
                if (r !== "no data") {

                    $('#spinner').hide()

                    // now use the data to show the ql pop up
                    json = JSON.parse(r)

                    html = "<div class='row'>";
                    html += "<div class='col-md-12'>";
                    html += "<div class='row'>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Year</div><div class='col-md-9'>" + json[0] + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Make</div><div class='col-md-9'>" + json[1].toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Model</div><div class='col-md-9'>" + json[2].toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Engine</div><div class='col-md-9'>" + json[3].toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Viscosity</div><div class='col-md-9'>" + json[4].toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Capacity</div><div class='col-md-9'>" + json[5].toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Description</div><div class='col-md-9'>" + json[6].toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Plug Torque</div><div class='col-md-9'>" + json[7].toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Oil Brand 1</div><div class='col-md-9'>" + json[8].toUpperCase() + " #" + json[9].toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Oil Brand 2</div><div class='col-md-9'>" + json[10].toUpperCase() + " #" + json[11].toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Coolant Capacity</div><div class='col-md-9'>" + json[12].toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Coolant Description</div><div class='col-md-9'>" + json[13].toUpperCase() + "</div>";

                    html += "<div style='text-transform:uppercase' class='col-md-3'>Pronto #</div><div class='col-md-9'>" + json[17] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Valvoline #</div><div class='col-md-9'>" + json[18] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>PG #</div><div class='col-md-9'>" + json[19] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>FVP #</div><div class='col-md-9'>" + json[20] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Mighty #</div><div class='col-md-9'>" + json[21] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Carquest #</div><div class='col-md-9'>" + json[22] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Napa #</div><div class='col-md-9'>" + json[23] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Super Champ #</div><div class='col-md-9'>" + json[24] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Warner #</div><div class='col-md-9'>" + json[25] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Fram #</div><div class='col-md-9'>" + json[26] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Mobil #</div><div class='col-md-9'>" + json[27] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Pennzoil #</div><div class='col-md-9'>" + json[28] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Performax #</div><div class='col-md-9'>" + json[29] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Purolator #</div><div class='col-md-9'>" + json[30] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Quaker State #</div><div class='col-md-9'>" + json[30] + " ".toUpperCase() + "</div>";
                    html += "<div style='text-transform:uppercase' class='col-md-3'>Service Pro #</div><div class='col-md-9'>" + json[32] + " ".toUpperCase() + "</div>";
                    html += "<div style='color:red;font-weight:bold'>I understand and agree that the data provided here is from 3rd Parties. All data should be verified by shop personnel</div>";
                    html += "</div></div>";
                    html += "</div>";

                    $('#quicklubedata').html(html)
                    $('#quicklubemodal').modal('show').draggable()

                } else {
                    <?php
                    if (strlen($vehyear) == 2) {
                        $vyear = "20" . $vehyear;
                    } else {
                        $vyear = $vehyear;
                    }
                    ?>
                    ds = "shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&t=quicklubedata&yr=<?php echo $year; ?>&mk=<?php echo $make; ?>&md=<?php echo trim(addslashes($model)); ?>"
                    $.ajax({

                        data: ds,
                        url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
                        type: "post",
                        success: function (r) {
                            // now check what data was found and display to user for final selections
                            if (r.indexOf("|") > 0) {

                                // we have results
                                rar = r.split("|")
                                typ = rar[0]
                                json = JSON.parse(rar[1])

                                if (typ == "ym") {
                                    table = "<table class='table table-condensed table-striped table-header-bg'>"
                                    table += "<thead><tr><td>Make</td></tr></thead>"
                                    table += "<tbody>"
                                    for (i = 0; i < json.length; i++) {
                                        obj = json[i]
                                        //for (key in obj)
                                        table += "<tr><td onclick='getModels(\"" + obj.make + "\")'>" + obj.make_name + "</td></tr>"
                                    }
                                    //console.log("rendering table")
                                    table += "</tbody></table>"
                                    $('#quicklubedata').html(table)
                                    $('#quicklubemodal').modal('show').draggable()
                                    $('#spinner').hide()
                                }
                                if (typ == "ymm") {
                                    table = "<table class='table table-condensed table-striped table-header-bg'>"
                                    table += "<thead><tr><td>Year</td><td>Make</td><td>Model</td></tr></thead>"
                                    table += "<tbody>"
                                    for (i = 0; i < json.length; i++) {
                                        obj = json[i]
                                        //for (key in obj)
                                        table += "<tr><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.year + "</td><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.make.replace("-", " ").toUpperCase() + "</td><td onclick='getEngines(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\")'>" + obj.model_name + "</td></tr>"
                                    }

                                    table += "</tbody></table>"
                                    $('#quicklubedata').html(table)
                                    $('#quicklubemodal').modal('show').draggable()
                                    $('#spinner').hide()
                                }
                                if (typ == "ymme") {
                                    table = "<table class='table table-condensed table-striped table-header-bg'>"
                                    table += "<thead><tr><td>Year</td><td>Make</td><td>Model</td><td>Engine</td></tr></thead>"
                                    table += "<tbody>"
                                    for (i = 0; i < json.length; i++) {
                                        obj = json[i]
                                        //for (key in obj)
                                        table += "<tr><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.year + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.make.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.model.replace("-", " ").toUpperCase() + "</td><td onclick='getQuickLubeData(\"" + obj.year + "\",\"" + obj.make + "\",\"" + obj.model + "\",\"" + obj.engine + "\")'>" + obj.engine_name.toUpperCase() + "</td></tr>"
                                    }

                                    table += "</tbody></table>"
                                    $('#quicklubedata').html(table)
                                    $('#quicklubemodal').modal('show').draggable()
                                    $('#spinner').hide()
                                }
                            } else {
                                $('#quicklubemodal').modal('hide')
                                $('#spinner').hide()
                                swal("No information is available for this vehicle.")
                            }

                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                            $('#spinner').hide()
                        }
                    })
                }
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                $('#spinner').hide()
            }
        })
    }

    function clearQuickLube() {

        swal({
                title: "Are you sure?",
                text: "Are you sure you want to clear this lube data?",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Yes, delete it!",
                closeOnConfirm: true
            },
            function () {
                $.ajax({
                    data: "t=clearlube&shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&vin=<?php echo $vin; ?>",
                    url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
                    type: "post",
                    success: function (r) {
                        console.log(r)
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                })
            });

    }

    function addLabor(comid) {
        eModal.iframe({
            title: 'Add LABOR to this QUOTE',
            url: 'addlabor.php?shopid=<?php echo $shopid . "&quoteid=" . $quoteid?>&comid=' + comid,
            size: eModal.size.lg,
            buttons: [
                {text: 'Close', style: 'info', close: true},
            ]

        });
    }

    function editLabor(id) {

        <?php if(empty($quoteroid)){?>

        eModal.iframe({
            title: 'Edit Labor of this QUOTE',
            url: 'editlabor.php?shopid=<?php echo $shopid . "&quoteid=" . $quoteid?>&id=' + id,
            size: eModal.size.xl,
            buttons: [
                {text: 'Close', style: 'info', close: true},
            ]

        });

        <?php }?>
    }

    function addSublet(comid) {
        eModal.iframe({
            title: 'Add SUBLET to this QUOTE',
            url: 'addsublet.php?shopid=<?php echo $shopid . "&quoteid=" . $quoteid?>&comid=' + comid,
            size: eModal.size.lg,
            buttons: [
                {text: 'Close', style: 'info', close: true},
            ]

        });
    }

    function editSublet(id) {

        <?php if(empty($quoteroid)){?>

        eModal.iframe({
            title: 'Edit Sublet of this QUOTE',
            url: 'editsublet.php?shopid=<?php echo $shopid . "&quoteid=" . $quoteid?>&id=' + id,
            size: eModal.size.lg,
            buttons: [
                {text: 'Close', style: 'info', close: true},
            ]

        });

        <?php }?>
    }

    function deleteItem(id, type) {

        swal({
                title: "Are you sure?",
                text: "Are you sure you want to delete this " + type + "?",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger btn-md",
                confirmButtonText: "Yes, delete it!",
                closeOnConfirm: false
            },
            function () {

                $('.btn-md').attr('disabled', 'disabled')

                $.ajax({

                    data: "shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&t=deleteitem&type=" + type + "&id=" + id,
                    url: "quoteactions.php",
                    type: "post",
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },
                    success: function (r) {
                        if (r != "success") {
                            swal({
                                title: "Error Deleting part",
                                text: r,
                                type: "warning",
                                confirmButtonClass: "btn-danger",
                                confirmButtonText: "Ok",
                                closeOnConfirm: true
                            }, function () {
                                location.href = "quote.php?quoteid=<?= $quoteid?>&recalc=y"
                            })
                        } else {
                            location.href = "quote.php?quoteid=<?= $quoteid?>&recalc=y"
                        }
                    }

                });
            });

    }

    function addCannedJob(comid) {
        eModal.iframe({
            title: 'Add Canned Job',
            url: 'addcannedjob.php?shopid=<?php echo $shopid . "&quoteid=" . $quoteid?>&comid=' + comid,
            size: eModal.size.lg,
            buttons: [
                {text: 'Close', style: 'info', close: true},
            ]

        });
    }

    function setWriter(w) {
        wo = encodeURIComponent(w)
        $.ajax({
            data: "t=writer&shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&w=" + wo,
            type: "post",
            url: "quoteactions.php",
            success: function (r) {
                if (r == "success") {
                    $('#writer').html(w).append(' &nbsp;<i style="float:right" class="fa fa-chevron-down"></i>')
                } else
                    swal(r)
            }
        });
    }

    function saveComments() {

        c = $('#comments').val()
        oric = $('#oricomments').val()
        if (c == oric) return
        ds = "shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&t=comments&c=" + encodeURIComponent(c)
        $.ajax({
            type: "post",
            url: "quoteactions.php",
            data: ds,
            success: function (r) {
                $('#commentssaved').html("Comments Saved")
            }
        })

    }

    function editQuote() {
        $('#editquotemodal').modal('show')
    }

    function decodeVIN() {

        vin = $('#vin').val()
        p = false

        if (vin.length == 17) {
            p = true
        }

        if (p == true) {

            $('#spinner').show()
            $.ajax({
                data: "vin=" + vin + "&carfaxloc=<?= $cfl?>",
                url: "<?= COMPONENTS_PRIVATE ?>/customer/vehicle-vin-decode.php",
                dataType: "json",
                success: function (r) {

                    if (r.status == 'success') {
                        rvin = r.vin
                        yr = r.yr
                        mk = r.mk
                        md = r.md
                        tr = r.tr
                        bd = r.bd
                        en = r.en

                        $('#year').val(yr)
                        $('#make').val(mk)
                        $('#model').val(md + " " + tr + " " + bd)
                        $('#spinner').hide()
                    } else {
                        $('#spinner').hide()
                        swal("The VIN was not able to be decoded.  Please verify the VIN number")
                    }
                }
            });
        } else {
            swal("You must enter a 17 digit VIN to decode it")
        }


    }

    function check_customer() {
        $("#ecust").html('')
        if (($("#vid").val() != "" && $("#vid").val() != 0) && ($("#cid").val() != "" && $("#cid").val() != 0)) {
            var ds = $('#quoteform').serialize() + "&quoteid=<?php echo $quoteid; ?>&t=check_customer";
            $.ajax({
                data: ds,
                url: "vehicle_actions.php",
                type: "post",
                dataType: "json",
                success: function (r) {
                    console.log("check cust response "+r);
                    if (r.hasOwnProperty('response') && r.response == "success") {
                        //customer information hasn't changed, continue with saving.
                        check_vehicle();
                    } else {
                        // customer information has been changed
                        var customer = "";
                        if (r.hasOwnProperty('data')){
                            customer = r.data.customer;
                        }
                        console.log(customer);
                        $("#ecust").html(customer)
                        $("#custconfirm").modal("show");
                    }
                }
            });
        } else {
            console.log("check customer , no cid and vid");
            saveQuote();
        }
    }

    function update_customer() {
        var ds = $('#quoteform').serialize() + "&quoteid=<?php echo $quoteid; ?>&t=update_customer";
        $.ajax({
            data: ds,
            url: "vehicle_actions.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    //vehicle information hasn't changed, continue with saving.
                    check_vehicle();
                    console.log("customer inforamtion updated");
                }
            }
        });
    }

    function check_vehicle() {
        if (($("#vid").val() != "" && $("#vid").val() != 0) && ($("#cid").val() != "" && $("#cid").val() != 0)) {
            var ds = $('#quoteform').serialize() + "&quoteid=<?php echo $quoteid; ?>&t=check_vehicle";
            $.ajax({
                data: ds,
                url: "vehicle_actions.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        //vehicle information hasn't changed, continue with saving.
                        saveQuote();
                    } else {
                        // vehicle information has been changed
                        $("#vehconfirm").modal("show");
                    }
                }
            });
        } else {
            saveQuote();
        }
    }

    function update_vehicle() {
        var ds = $('#quoteform').serialize() + "&quoteid=<?php echo $quoteid; ?>&t=update_vehicle";
        $.ajax({
            data: ds,
            url: "vehicle_actions.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    //vehicle information hasn't changed, continue with saving.
                    saveQuote();
                    console.log("vehicle inforamtion updated");
                }
            }
        });
    }

    function create_vehicle() {
        var ds = $('#quoteform').serialize() + "&quoteid=<?php echo $quoteid; ?>&t=create_vehicle";
        $.ajax({
            data: ds,
            url: "vehicle_actions.php",
            type: "post",
            success: function (r) {
                if (r.length > 0) {
                    console.log("vehicle id : " + r + " new vehicle created");
                    $("#vid").val(r);
                    saveQuote();
                } else {
                    console.log("ERROR : " + r);
                }
            }
        });
    }

    function saveQuote(type) {
        $('.btn-md').attr('disabled', 'disabled')
        $('#spinner').show()

        formdata = $('#quoteform').serialize() + "&quoteid=<?php echo $quoteid; ?>&t=savequote"

        $.ajax({
            data: formdata,
            url: "quoteactions.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    if (type == 'convert') {
                        var createcus = $('#createcus').val()
                        var createveh = $('#createveh').val()

                        formdata = $('#quoteform').serialize() + "&quoteid=<?php echo $quoteid; ?>&t=cusvehcreate&createcus=" + createcus + "&createveh=" + createveh
                        console.log(formdata)

                        $.ajax({
                            data: formdata,
                            url: "quoteactions.php",
                            dataType: 'JSON',
                            type: "post",
                            success: function (r) {
                                if ($.isNumeric(r.cid) && $.isNumeric(r.vid) && r.cid > 0 && r.vid > 0)
                                    createRO(r.cid, r.vid)
                                else {
                                    swal("Failed")
                                    $('.btn-md').attr('disabled', false)
                                    $('#spinner').hide()
                                }

                            }
                        })

                    } else
                        location.reload()
                } else {
                    $('.btn-md').attr('disabled', false)
                    swal(r)
                    $('#spinner').hide()
                }


            }
        })
    }

    function printQuote() {
        eModal.iframe({
            title: 'Quote',
            url: "printpdfquote.php?shopid=<?= $shopid?>&quoteid=<?= $quoteid?>",
            size: eModal.size.xl,
            buttons: [
                {
                    text: 'Print',
                    style: 'info',
                    close: false,
                    click: function () {
                        $('#emodal-box iframe').attr("id", "ropdfframe")
                        pdf = document.getElementById("ropdfframe")
                        pdf.focus()
                        pdf.contentWindow.print()
                    }
                },

                {text: 'Close', style: 'danger', close: true}

            ]

        });
    }

    function startSendQuote() {
        $('#sendquotemodal').modal('show')
    }

    function sendQuote(t) {
        em = $('#emailto').val()
        cp = $('#textto').val()

        if (t === "email" && em.length === 0) {
            swal({
                title: "Missing Information",
                text: "You must have an email to send quote",
                type: "warning",
                confirmButtonClass: "btn-warning",
                confirmButtonText: "Ok",
                closeOnConfirm: true
            });
            return
        }
        if (t === "text" && cp.length === 0) {
            swal({
                title: "Missing Information",
                text: "You must have a cell phone to send text message",
                type: "warning",
                confirmButtonClass: "btn-warning",
                confirmButtonText: "Ok",
                closeOnConfirm: true
            });
            return
        }
        if ((t === "both" && em.length === 0) || (t === "both" && cp.length === 0)) {
            swal({
                title: "Missing Information",
                text: "You must have an email and cell phone to send quote to both",
                type: "warning",
                confirmButtonClass: "btn-warning",
                confirmButtonText: "Ok",
                closeOnConfirm: true
            });
            return
        }

        $('.btn-md').attr('disabled', 'disabled')

        if (t != 'text') {

            data = "shopid=<?= $shopid?>&quoteid=<?php echo $quoteid; ?>&emailto=" + $('#emailto').val()

            $.ajax({
                data: data,
                url: "printpdfquote.php",
                type: "post",
                success: function (r) {
                    if (t == 'email') {
                        if (r == "success") {
                            $('#sendquotemodal').modal('hide')
                            swal("Quote Sent")
                        } else
                            swal(r)

                        $('.btn-md').attr('disabled', false)
                    }

                }
            })
        }

        if (t != 'email') {
            data = "t=textquote&shopid=<?= $shopid?>&quoteid=<?php echo $quoteid; ?>&textto=" + $('#textto').val()

            $.ajax({
                data: data,
                url: "quoteactions.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        $('#sendquotemodal').modal('hide')
                        swal("Quote Sent")
                    } else
                        swal(r)

                    $('.btn-md').attr('disabled', false)
                }
            })
        }
    }

    function convertRO() {
        var cid = $('#cid').val()
        var vid = $('#vid').val()
        var createcus = $('#createcus').val()
        var createveh = $('#createveh').val()
        var year = encodeURIComponent($('#year').val())
        var make = encodeURIComponent($('#make').val())
        var model = encodeURIComponent($('#model').val())


        if (cid > 0 && vid > 0) {
            swal({
                    title: "Are you sure?",
                    text: "Are you sure you want to convert this QUOTE to RO?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonClass: "btn-danger btn-md",
                    confirmButtonText: "Yes, convert it!",
                    closeOnConfirm: false
                },
                function () {
                    createRO(cid, vid)
                })
        } else if ((cid == 0 || cid == '') && createcus != 'yes') {
            var cname = encodeURIComponent($('#customer').val())
            var cphone = $('#phone').val()

            if (cname == '') {
                editQuote()
                swal("Please enter customer details")
            } else {
                $('#spinner').show()
                ds = "t=checkdupcustomer&shopid=<?php echo $shopid; ?>&customer=" + cname + "&phone=" + cphone
                $.ajax({
                    data: ds,
                    type: "post",
                    dataType: 'JSON',
                    url: "quoteactions.php",
                    success: function (r) {

                        var rows = ''
                        $('#spinner').hide()

                        if (r.length > 0) {

                            for (var i = 0; i < r.length; i++) {
                                rows += "<tr><td>" + r[i].firstname + ' ' + r[i].lastname + "</td><td>" + r[i].address + ' ' + r[i].city + ' ' + r[i].state + ' ' + r[i].zip + "</td><td>" + r[i].cellphone + "</td><td><button class='btn btn-sm btn-success btn-md' onclick='selectDupcustomer(" + JSON.stringify(r[i]) + ")'>Select</button></td></tr>"
                            }

                            $('#dupcusrows').html(rows)
                            $('#editquotemodal').modal('hide')
                            $('#dupcusmodal').modal('show')
                        } else
                            createnewCustomer()

                    }
                });
            }
        } else if ((vid == 0 || vid == '') && createveh != 'yes') {
            if (cid > 0) {
                $('#spinner').show()
                ds = "t=getvehicles&shopid=<?php echo $shopid; ?>&cid=" + cid
                $.ajax({
                    data: ds,
                    type: "post",
                    dataType: 'JSON',
                    url: "quoteactions.php",
                    success: function (r) {

                        var rows = ''
                        $('#spinner').hide()

                        if (r.length > 0) {
                            for (var i = 0; i < r.length; i++) {
                                rows += "<tr><td>" + r[i].year + "</td><td>" + r[i].make + "</td><td>" + r[i].model + "</td><td>" + r[i].vin + "</td><td><button class='btn btn-sm btn-success btn-md' onclick='selectVehicle(" + JSON.stringify(r[i]) + ")'>Select</button></td></tr>"
                            }

                            $('#vehrows').html(rows)
                            $('#editquotemodal').modal('hide')
                            $('#vehmodal').modal('show')
                        } else
                            createnewVehicle()

                    }
                });
            } else if (year == '' || make == '' || model == '') {
                editQuote()
                swal("Please enter vehicle year, make, model")
            } else
                createnewVehicle()
        } else if (createveh == 'yes' && (year == '' || make == '' || model == '')) {
            editQuote()
            swal("Please enter vehicle year, make, model")
        } else if (createveh == 'yes' || createcus == 'yes') {
            saveQuote('convert')
        }
    }

    function createnewCustomer() {
        $('#createcus').val('yes')
        $('#cid').val('')
        $('#dupcusmodal').modal('hide')
        convertRO()
    }

    function createnewVehicle() {
        $('#createveh').val('yes')
        $('#vid').val('')
        $('#vehmodal').modal('hide')
        convertRO()
    }


    function createRO(cid, vid) {
        $('.btn-md').attr('disabled', 'disabled')
        $('#spinner').show()

        ds = "&oshopid=<?php echo $shopid; ?>&cid=" + cid + "&vid=" + vid + "&shopid=<?php echo $shopid; ?>"

        $.ajax({

            data: ds,
            type: "get",
            url: "<?= COMPONENTS_PRIVATE?>/createro/createro.php",
            success: function (r) {
                if (r.indexOf("|") > 0) {
                    rar = r.split("|")
                    var newroid = rar[1]
                    ds = "t=addissuestoro&shopid=<?php echo $shopid; ?>&quoteid=<?= $quoteid?>&updateinv=<?= $updateinvonadd?>&roid=" + newroid
                    $.ajax({
                        data: ds,
                        type: "get",
                        url: "quoteactions.php",
                        success: function (r) {
                            location.href = '<?= COMPONENTS_PRIVATE ?>/ro/ro.php?roid=' + newroid + '&quoteid=<?= $quoteid?>&newquote'
                        }
                    });
                } else {
                    swal("You must have a valid vehicle and customer to create an RO")
                }
            }
        });
    }

    function sendCalendar() {

        localStorage.setItem("calendarquoteid", '<?= $quoteid?>')
        location.href = '<?= COMPONENTS_PRIVATE ?>/calendar/calendar.php'

    }

    function launchNexpart() {

        unpw = $('#nexpartcreds').val();
        rar = unpw.split("|");
        un = rar[0];
        pw = rar[1];
        path = 'http://www.nexpart.com/extpart.php?rev=4.0&clientversion=2.2&requesttype=launch&sms_form=X&provider=NEXLINKCSBTECH&pwd=' + pw + '&nexpartuname=' + un + '&vin=' + $('#vin').val() + '&identifier=<?php echo $shopid; ?>-<?php echo $quoteid; ?>-new&webpost=<?= INTEGRATIONS; ?>/nexpart/returnquote.php';
        winPopUp(path)
    }


    function launchPartsTech(partstech) {

        path = "<?= INTEGRATIONS ?>/partstech/main.php?existing=no&version=new&shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&vin=" + $('#vin').val()
        winPopUp(path)

    }

    function launchRepairLink(uid) {
        var vin = '<?= $vin ?>';
        var year = '<?= $year ?>';
        var make = '<?= $make ?>';
        var model = '<?= $model ?>';
        uid = (typeof (uid) !== "undefined") ? uid : "";

        path = "<?= INTEGRATIONS ?>/repairlink/index.php?uid=" + uid + "&vin=" + vin + "&year=" + year + "&make=" + make + "&model=" + model + "&entity=quotes&entity_id=<?= $quoteid ?>&version=new";
        winPopUp(path);
    }

    function launchEpicor() {

        path = '<?= INTEGRATIONS ?>/epicor/main.php?quote=yes&version=new&vin=' + $('#vin').val() + '&shopid=<?php echo $shopid; ?>&om=Transfer&supp=' + $('#epicorsupplier').val() + '&roid=<?php echo $quoteid; ?>';
        winPopUp(path);

    }

    function launchWorldpac() {

        <?php
        if ($worldpac == "yes"){
        ?>
        vin = $('#vin').val()
        yr = $('#year').val()
        mk = $('#make').val()
        md = $('#model').val()

        if (vin.length > 0 && yr.length > 0 && mk.length > 0 && md.length > 0) {
            URL = '<?= INTEGRATIONS ?>/worldpac/mainquote.php?shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>';
            var mywin = window.open(URL, "npwin");
            myx = setInterval(function () {
                if (mywin.closed) {
                    window.location.href = window.location.href + '&recalc=y';
                    clearInterval(myx);
                }
            }, 1000)
        } else {
            swal('Without a year, make, model and VIN, Worldpac will error out.  Please enter and save this information before launching Worldpac');
        }
        <?php } else { ?>
        alert("You must add WorldPac parts ordering in settings before you can use this integration");
        <?php } ?>
    }


    function showOrdering(t) {

        if (t == "epicor") {
            $('#epicorselect').toggle();
            $('#epicorbutton').toggle();
            $('#nexpartbutton').toggle();
            $('#worldpacbutton').toggle();
            $('#partstechbutton').toggle();
        } else if (t == "nexpart") {
            $('#nexpartselect').toggle();
            $('#epicorbutton').toggle();
            $('#nexpartbutton').toggle();
            $('#worldpacbutton').toggle();
            $('#partstechbutton').toggle();

        } else if (t == "partstech") {
            $('#partstechselect').toggle();
            $('#epicorbutton').toggle();
            $('#nexpartbutton').toggle();
            $('#worldpacbutton').toggle();
            $('#partstechbutton').toggle();
        }

    }

    function winPopUp(URL) {
        h = screen.availHeight - 80
        w = screen.availWidth - 20
        str = 'addressbar=0,toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=yes,screenX=0,screenY=0,top=0,left=0,maximize=1,'
        str = str + 'height=' + h + ', width=' + w
        var mywin = window.open(URL, "ewin", str)

        if (URL.indexOf('partstech') <= -1) {
            wintimer = setInterval(function () {
                if (mywin.closed) {
                    window.location.href = window.location.href + '&recalc=y';
                }
            }, 1000)

        }

    }

    function loadProDemand(type) {
        vin = "<?php echo str_replace('"', '\"', $vin); ?>";
        $('body').css({'overflow': 'hidden'});
        $('#motorframe').show().attr("src", "prodemand.php?vin=" + vin + "&shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&pdtype=" + type);
    }

    function closeProDemand() {

        $('body').css({'overflow': 'auto'});
        $('#motorframe').attr("src", "").hide()
    }

    function saveFees() {
        $('.btn-md').attr('disabled', 'disabled')

        formdata = $('#feesform').serialize() + "&quoteid=<?php echo $quoteid; ?>&taxable=<?= $taxable?>&t=savefees"

        $.ajax({
            data: formdata,
            url: "quoteactions.php",
            type: "post",
            success: function (r) {
                if (r == "success")
                    location.reload()
                else {
                    $('.btn-md').attr('disabled', false)
                    swal(r)
                }


            }
        })
    }

    function saveIssue() {
        $('.btn-md').attr('disabled', 'disabled')

        var issue = encodeURIComponent($('#issue').val().toUpperCase())
        var comid = $('#issuecomid').val()

        $.ajax({
            data: "quoteid=<?php echo $quoteid; ?>&issue=" + issue + "&comid=" + comid + "&t=saveissue",
            url: "quoteactions.php",
            type: "post",
            success: function (r) {
                if (r == "success")
                    location.reload()
                else {
                    $('.btn-md').attr('disabled', false)
                    swal(r)
                }


            }
        })
    }

    function saveIssue() {
        $('.btn-md').attr('disabled', 'disabled')

        var issue = encodeURIComponent($('#issue').val().toUpperCase())
        var comid = $('#issuecomid').val()

        $.ajax({
            data: "quoteid=<?php echo $quoteid; ?>&issue=" + issue + "&comid=" + comid + "&t=saveissue",
            url: "quoteactions.php",
            type: "post",
            success: function (r) {
                if (r == "success")
                    location.reload()
                else {
                    $('.btn-md').attr('disabled', false)
                    swal(r)
                }


            }
        })
    }

    function selectDupcustomer(r) {
        $('.btn-md').attr('disabled', 'disabled')
        $('#cid').val(r.customerid)
        $('#customer').val(r.lastname + ', ' + r.firstname)
        $('#address').val(r.address)
        $('#city').val(r.city)
        $('#state').val(r.state)
        $('#zip').val(r.zip)
        $('#phone').val(r.cellphone)
        $('#email').val(r.email)
        $('#dupcusmodal').modal('hide')
        $('.btn-md').attr('disabled', false)
        $('#dupcusrows').html('')
        convertRO()
    }

    function selectVehicle(r) {
        $('.btn-md').attr('disabled', 'disabled')
        $('#vid').val(r.vehid)
        $('#year').val(r.year)
        $('#make').val(r.make)
        $('#model').val(r.model)
        $('#vin').val(r.vin)
        $('#color').val(r.color)
        $('#vehmodal').modal('hide')
        $('.btn-md').attr('disabled', false)
        $('#vehrows').html('')
        convertRO()
    }

    function showGP() {

        if ($('#gpbutton').attr("class") == "btn ") {
            swal("You have no parts or labor to calcuate GP")
        } else {
            eModal.iframe({
                title: 'Gross Profit',
                url: "<?= COMPONENTS_PRIVATE ?>/gp/gpdetail_quote.php?shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&subtotal=<?php echo sbpround($subtotal, 2);?>",
                size: eModal.size.lg,
                buttons: [
                    {
                        text: 'Print', style: 'info', close: false,
                        click: function () {
                            $('#emodal-box iframe').attr("id", "ropdfframe")
                            pdf = document.getElementById("ropdfframe")
                            pdf.focus()
                            pdf.contentWindow.print()
                        }
                    },
                    {text: 'Close', style: 'warning', close: true}
                ]
            });
        }
    }

    function showPPFIssue(compid) {

        eModal.iframe({
            title: 'Profit Per Hour (PPH)',
            url: "<?= COMPONENTS_PRIVATE ?>/pif/pifquote_issue_detail.php?shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&compid=" + compid + "&subtotal=<?php echo sbpround($subtotal, 2);?>&fees=<?php echo sbpround($totalfees, 2);?>",
            size: eModal.size.lg,
            buttons: [
                {
                    text: 'Print', style: 'info', close: false,
                    click: function () {
                        $('#emodal-box iframe').attr("id", "ropdfframe")
                        pdf = document.getElementById("ropdfframe")
                        pdf.focus()
                        pdf.contentWindow.print()
                    }
                },
                {text: 'Close', style: 'warning', close: true}
            ]
        });
    }

    function showPIF() {

        if ($('#gpbutton').attr("class") == "btn ") {
            swal("You have no PPH")
        } else {
            eModal.iframe({
                title: 'Profit Per Hour (PPH)',
                url: "<?= COMPONENTS_PRIVATE ?>/pif/pifquote_detail.php?shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&subtotal=<?php echo sbpround($subtotal, 2);?>&fees=<?php echo sbpround($totalfees, 2);?>",
                size: eModal.size.lg,
                buttons: [
                    {
                        text: 'Print', style: 'info', close: false,
                        click: function () {
                            $('#emodal-box iframe').attr("id", "ropdfframe")
                            pdf = document.getElementById("ropdfframe")
                            pdf.focus()
                            pdf.contentWindow.print()
                        }
                    },
                    {text: 'Close', style: 'warning', close: true}
                ]
            });
        }
    }

</script>
<img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
<div id="hider"></div>


</body>
<?php
mysqli_close($conn);
?>
</html>
