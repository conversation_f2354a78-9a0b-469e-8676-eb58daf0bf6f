<!DOCTYPE html>
<html>
<?php
require_once CONN;

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

$stmt = "select carfaxlocation,companystate from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($cfl, $state);
    $query->fetch();
    $query->close();
}
?>
<!--[if IE 9]>
<html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus"> <!--<![endif]-->
<head>
    <meta charset="utf-8">
    <title><?= getPageTitle() ?></title>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon() ?>' type='image/x-icon'
    / >
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href='<?= SCRIPT ?>/plugins/jquery-ui/jquery-ui.min.css'/>
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        .col-md-6 {
            border: 1px black solid
        }

        .col-md-8 {
            border: 1px black solid
        }

        .col-md-4 {
            border: 1px black solid
        }

        .ui-autocomplete {
            z-index: 3000 !important;
            width: 900px;
            height: 350px;
            overflow-y: scroll;
            overflow-x: hidden;
        }

        @media (min-width: 992px) {
            .modal-lg {
                width: 1100px
            }
        }

    </style>
</head>
<body>
<?php include(COMPONENTS_PRIVATE_PATH . "/shared/analytics.php"); ?>
<div id="mainalert"
     style="position:absolute;top:100px;width:50%;left:25%;display:none;z-index:9999;text-align:center;font-weight:bold;font-size:x-large"
     class="alert alert-success"></div>
<div id="header"></div>
<!-- Page Container -->
<!--
    Available Classes:

    'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

    'sidebar-l'                  Left Sidebar and right Side Overlay
    'sidebar-r'                  Right Sidebar and left Side Overlay
    'sidebar-mini'               Mini hoverable Sidebar (> 991px)
    'sidebar-o'                  Visible Sidebar by default (> 991px)
    'sidebar-o-xs'               Visible Sidebar by default (< 992px)

    'side-overlay-hover'         Hoverable Side Overlay (> 991px)
    'side-overlay-o'             Visible Side Overlay by default (> 991px)

    'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

    'header-navbar-fixed'        Enables fixed header
-->
<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">

    <!-- Sidebar -->
    <nav id="sidebar">
        <!-- Sidebar Scroll Container -->
        <div id="sidebar-scroll">
            <!-- Sidebar Content -->
            <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
            <div class="sidebar-content">
                <!-- Side Header -->
                <div class="side-header side-content bg-white-op">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button"
                            data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                        <i class="text-primary">
                            <?php getLogo() ?></i>
                        <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                    </a>
                </div>
                <!-- END Side Header -->

                <!-- Side Content -->
                <div class="side-content-sbp-ro side-content">
                    <ul class="nav-main">
                        <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Back to WIP</span></a>
                        </li>
                        <!-- <li>
                            <a href="<?= COMPONENTS_PRIVATE ?>/calendar/calendar.php"><i
                                        class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Schedule</span></a>
                        </li> -->
                        <?php if( !$shopIsReadOnly ): ?>
                            <li>
                                <a href="javascript:void(null)" onclick="$('#newquotemodal').modal('show')"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide" style="color:#FFFFE0;"><b>Create New Quote</b></span></a>
                            </li>
                        <?php endif; ?>
                        <li>
                            <a href="canned.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Canned Jobs</span></a>
                        </li>

                    </ul>
                </div>
                <!-- END Side Content -->
            </div>
            <!-- Sidebar Content -->
        </div>
        <!-- END Sidebar Scroll Container -->
    </nav>
    <!-- END Sidebar -->

    <!-- Header -->
    <header style="text-align:center;font-weight:bold;font-size:18pt" id="header-navbar"
            class="content-mini content-mini-full">

        <!-- Header Navigation Right -->
        Quote List

        <?php if( !$shopIsReadOnly ): ?>
            <button class="btn btn-md btn-warning" type="button" id="deleteQuotesButton"
                    style="float:right;" onclick="deleteSelected()">DELETE SELECTED OPEN QUOTES
            </button>
        <?php endif; ?>

        <!-- END Header Navigation Right -->

        <!-- Header Navigation Left -->

        <ul class="nav-header pull-left">
            <li class="hidden-md hidden-lg">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                    <i class="fa fa-navicon"></i>
                </button>
            </li>
            <li class="hidden-xs hidden-sm">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" id="close-sidebar"
                        data-action="sidebar_mini_toggle" type="button">
                    <i class="fa fa-bars"></i>
                </button>
            </li>
            <li>
                <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                <button style="display:none" class="btn btn-default pull-right" data-toggle="modal"
                        data-target="#apps-modal" type="button">
                    <i class="si si-grid"></i>
                </button>
            </li>
            <li class="visible-xs">
                <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search"
                        data-class="header-search-xs-visible" type="button">
                    <i class="fa fa-search"></i>
                </button>
            </li>
            <li>

            </li>
        </ul>

        <!-- END Header Navigation Left -->
    </header>
    <!-- END Header -->
    <!-- Main Container -->

    <!-- Main Container -->
    <main class="container-fluid" id="main-container" style="display:block;">

        <form id="mainform" name="mainform" method="post" action="quotes.php">
            <input name="sub" type="hidden"/>

            <br>

            <label class="css-input switch switch-primary switch-sm pull-right">
                <input type="checkbox" id="showconverted" onchange="toggleConverted()" checked>
                <span></span><b>Show Converted Quotes</b>
            </label>

            <input class="form-control" style="" type="text" placeholder="Search Quotes by Customer's name" id="srch" name="srch">

            <table class="table table-condensed table-striped table-header-bg" id="quotes-table">
                <thead>
                <tr>
                    <?php if( !$shopIsReadOnly ): ?>
                        <th class="headerrow" style="width: 0">
                            <input onclick="checkAll()" id="checkall" name="checkall" type="checkbox">
                        </th>
                        <th class="headerrow"><strong>Select</strong></th>
                        <?php else: ?>
                            <th class="headerrow" style="width: 0">Id</th>
                    <?php endif; ?>

                    <th class="headerrow"><strong>Date</strong></th>
                    <th class="headerrow"><strong>Customer</strong></th>
                    <th class="headerrow"><strong>Vehicle</strong></th>
                    <th class="headerrow"><strong>Total</strong></th>

                </tr>
                </thead>
                <tbody>
                <?php

                $stmt = "select id, customer, year, make, model, totalquote, salestax, quotedate, roid, city, state, address from quotes where shopid = ? and customer!='' order by id desc";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $results = $query->get_result();
                    $query->store_result();
                    if ($results->num_rows > 0) {
                        while ($rs = $results->fetch_assoc()) {

                            $address = str_replace("'", "''", $rs['address']);
                            $city = $rs['city'];
                            $zip = $rs['zip'];
                            $total_quote = $rs['totalquote'];

                            ?>
                            <?php if( !$shopIsReadOnly ): ?>
                                <input type="hidden" name="deletecheckbox<?php echo $rs["id"]; ?>" value="<?php echo $rs["id"]; ?>">
                            <?php endif; ?>
                            
                            <tr style="background-color: <?= !empty($rs['roid']) ? '#FFFFE0' : '' ?>;" class="<?= !empty($rs['roid']) ? 'converted' : '' ?>">
                                <?php if( !$shopIsReadOnly ): ?>
                                    <td class="datarow" style="width: 0; height: 22px;">
                                        <?php if (empty($rs['roid'])) { ?><input class="deletecheckbox" id="<?php echo $rs["id"]; ?>" name="checkbox<?php echo $rs["id"]; ?>" type="checkbox"><?php } ?>
                                    </td>
                                <?php endif; ?>

                                <td onclick="location.href='quote.php?quoteid=<?php echo $rs["id"]; ?>'"><?php echo $rs["id"]; ?>
                                    &nbsp;
                                </td>
                                <td onclick="location.href='quote.php?quoteid=<?php echo $rs["id"]; ?>'"><?php
                                    $quotedateTs = strtotime($rs["quotedate"]);
                                    $quoteDate = date('n/j/Y', $quotedateTs);
                                    echo $quoteDate;
                                    ?>
                                    &nbsp;
                                </td>
                                <td onclick="location.href='quote.php?quoteid=<?php echo $rs["id"]; ?>'"><?php echo $rs["customer"]; ?>
                                    &nbsp;
                                </td>
                                <td>
                                    <span onclick="location.href='quote.php?quoteid=<?php echo $rs["id"]; ?>'"><?php echo $rs["year"] . " " . $rs["make"] . " " . $rs["model"]; ?></span><?= !empty($rs['roid']) ? "&nbsp;&nbsp;&nbsp;<a target='_blank' href='" . COMPONENTS_PRIVATE . "/ro/ro.php?roid=" . $rs['roid'] . "'><i><small>RO #" . $rs['roid'] . '</small></i></a>' : '' ?>
                                    &nbsp;
                                </td>
                                <td onclick="location.href='quote.php?quoteid=<?php echo $rs["id"]; ?>'"><?php echo asDollars($total_quote); ?>
                                    &nbsp;
                                </td>
                            </tr>

                            <?php

                        }
                    } else {
                        echo "<tr><td colspan='5'>No quotes on file</td></tr>";
                    }
                    $query->close();
                } else {
                    echo "quots prepare failed: (" . $conn->errno . ") " . $conn->error;

                }
                ?>
                </tbody>
            </table>
        </form>

        <?php if( !$shopIsReadOnly ): ?>
            <div id="plateModal" class="modal fade" style="z-index: 1111;" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-xl" style="top:10%;">
                    <div class="modal-content">
                        <div class="block block-themed block-transparent remove-margin-b">
                            <div class="block-header bg-primary-dark">
                                <ul class="block-options">
                                    <li>
                                        <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                    </li>
                                </ul>
                                <h3 id="tctitle" class="block-title">Plate Lookup</h3>
                            </div>
                            <div class="block-content">

                                <div class="row">
                                    <div class="col-md-6" style="border:none">
                                        <div class="form-group">
                                            <div class="col-sm-12">
                                                <div class="form-material">
                                                    <input autocomplete="off" autofocus class="form-control sbp-form-control" type="text" id="platelicense" name="platelicense" tabindex="1"/>
                                                    <label id="platelicenselabel" for="material-text2">License #</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6" style="border:none">
                                        <div class="form-group">
                                            <div class="col-sm-12">
                                                <div class="form-material">
                                                    <input autocomplete="off" class="form-control sbp-form-control" type="text" value="<?= $state ?>" id="platestate" name="platestate" tabindex="1"/>
                                                    <label id="platelicenselabel" for="material-text2">License State</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div style="margin-top:20px;" class="modal-footer">
                            <button class="btn btn-md btn-success btn-sm" type="button" onclick="plateLookup()"><i class="fa fa-search" aria-hidden="true"></i> Lookup</button>
                            <button class="btn btn-md btn-default btn-sm" type="button" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true"></i> Cancel</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="newquotemodal" class="modal fade" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true" style="">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="block block-themed block-transparent remove-margin-b">
                            <div class="block-header bg-primary-dark">
                                <ul class="block-options">
                                    <li>
                                        <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                    </li>
                                </ul>
                                <h3 id="tctitle" class="block-title">Create New Quote</h3>
                            </div>
                            <div class="block-content">

                                <form id="newquoteform" name="newquoteform">
                                    <input id="cid" name="cid" type="hidden"/>
                                    <input id="vid" name="vid" type="hidden"/>

                                    <div class="row">
                                        <div class="col-md-6" style="border:none">

                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input autocomplete="off" class="form-control sbp-form-control" type="text" id="customer" name="customer" tabindex="1"/>
                                                        <label id="customerlabel" for="material-text2">Customer (L,F)</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="address" name="address">
                                                        <label id="addresslabel" for="material-text2">Address</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="zip" name="zip">
                                                        <label id="ziplabel" for="material-text2">Zip</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="city" name="city">
                                                        <label id="citylabel" for="material-text2">City</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="state" name="state">
                                                        <label id="statelabel" for="material-text2">State</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="phone" name="phone">
                                                        <label id="phonelabel" for="material-text2">Phone</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="email" name="email">
                                                        <label id="emaillabel" for="material-text2">Email</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6" style="border:none">
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="year" name="year">
                                                        <label id="yearlabel" for="material-text2">Year</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="make" name="make">
                                                        <label id="makelabel" for="material-text2">Make</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="model" name="model">
                                                        <label id="modellabel" for="material-text2">Model</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="color" name="color">
                                                        <label id="colorlabel" for="material-text2">Color</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="vin" name="vin">
                                                        <label id="vinlabel" for="material-text2">VIN <a href="javascript:void(null)" onclick="decodeVIN()">(Decode VIN)</a></label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <input class="form-control sbp-form-control" tabindex="1" type="text" id="comments" name="comments">
                                                        <label id="commentslabel" for="material-text2">Comments</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="form-material">
                                                        <select id="writer" name="writer" class="form-control sbp-form-control" tabindex="14">
                                                            <option value="none">Select</option>
                                                            <?php
                                                            $stmt = "select employeelast, employeefirst from employees where shopid = ? and active = 'yes'";
                                                            if ($equery = $conn->prepare($stmt)) {
                                                                $equery->bind_param("s", $shopid);
                                                                $equery->execute();
                                                                $equery->store_result();
                                                                $equery->bind_result($employeelast, $employeefirst);
                                                                while ($equery->fetch()) {
                                                                    $name = $employeelast . ", " . $employeefirst;
                                                                    echo "<option value='" . $name . "'>" . $name . "</option>";
                                                                }
                                                                $equery->close();
                                                            } else {
                                                                echo "employees prepare failed: (" . $conn->errno . ") " . $conn->error;
                                                            }
                                                            ?>
                                                        </select>
                                                        <label id="writerlabel" for="material-text2">Writer</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div style="margin-top:20px;" class="modal-footer">
                            <?php if ($cfl != 'no') { ?>
                                <button class="btn btn-sm btn-warning btn-md pull-left" type="button" onclick="$('#plateModal').modal('show')">Plate Lookup</button><?php } ?>
                            <button class="btn btn-md btn-primary" type="button" onclick="check_customer()">Create</button>
                            <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="custconfirm" class="modal fade" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true" style="z-index:10001 !important;">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="block block-themed block-transparent remove-margin-b">
                            <div class="block-header bg-primary-dark">
                                <ul class="block-options">
                                    <li>
                                        <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                    </li>
                                </ul>
                                <h3 class="block-title">Edit Customer</h3>
                            </div>
                            <div id="vehinfo" class="block-content"></div>
                            <div class="block-content">
                                Customer <span id="ecust"></span> information has been changed, Do you want to update the Customer information or update the quote only?
                            </div>
                        </div>
                        <div style="margin-top:20px;" class="modal-footer">
                            <button class="btn btn-md btn-info" type="button" onclick="update_customer()">Update Customer</button>
                            <button class="btn btn-md btn-default" type="button" onclick="check_vehicle()" data-dismiss="modal">Update Quote</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="vehconfirm" class="modal fade" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true" style="z-index:10001 !important;">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="block block-themed block-transparent remove-margin-b">
                            <div class="block-header bg-primary-dark">
                                <ul class="block-options">
                                    <li>
                                        <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                    </li>
                                </ul>
                                <h3 class="block-title">Edit Vehicle</h3>
                            </div>
                            <div id="vehinfo" class="block-content"></div>
                            <div class="block-content">
                                Vehicle information has been changed, Do you want to create a new vehicle with new information or update the current vehicle.
                            </div>
                        </div>
                        <div style="margin-top:20px;" class="modal-footer">
                            <button class="btn btn-md btn-info" type="button" onclick="create_vehicle('create_vehicle')">Create New Vehicle</button>
                            <button class="btn btn-md btn-default" type="button" onclick="update_vehicle('update_vehicle')" data-dismiss="modal">Update Vehicle</button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    </main>
    <!-- END Main Container -->

    <!-- Footer -->
    <!-- END Footer -->
</div>
<!-- END Page Container -->
<!-- Modals -->

<script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
<script src="<?= SCRIPT ?>/tipped.js"></script>

<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
<script src="<?= SCRIPT ?>/app.js"></script>
<script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
<script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
<script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
<script src='<?= SCRIPT ?>/plugins/jquery-ui/jquery-ui.js'></script>
<script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/underscorejs/underscore.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

<!-- Page Plugins -->

<!-- Page JS Code
<script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
<script>


    $('.deletecheckbox').each(function () {
        $(this).prop('checked', true);
    })


    $('.deletecheckbox').each(function () {
        $(this).prop('checked', false);
    })

    function checkAll() {

        x = setTimeout(function () {
            d = document.getElementsByClassName("deletecheckbox")
            c = document.getElementById("checkall").checked

            for (j = 0; j < d.length; j++) {
                if (c == true) {
                    document.getElementById(d[j].id).checked = true

                } else {
                    document.getElementById(d[j].id).checked = false

                }
            }
        }, 10)

    }

    function deleteSelected() {

        shopid = '<?php echo $shopid; ?>';
        var checks = []

        $.each($("input[class='deletecheckbox']:checked"), function () {
            checks.push($(this).attr('id'));
        });

        if (checks.length === 0) {
            swal("Please click on the checkbox next to the Quote you want to delete.")
            return;
        }

        swal({
                title: "Are you sure?",
                text: "You will not be able to recover these quotes once deleted!",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Yes, delete them!",
                closeOnConfirm: false
            },
            function () {

                // build a list of the checked quote id numbers

                var checkboxids = checks.join(",");


                $.ajax({
                    type: "post",
                    url: "deleteselectedquotes.php",
                    data: "checkboxids=" + checkboxids + "&shopid=" + shopid,
                    success: function () {
                        location.reload();
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        swal("Error in mass delete")
                    }
                });

            });

    }


    function delQuote(id) {

        swal({
                title: "Are you sure?",
                text: "Are you sure you want to delete this quote?",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Yes, delete it",
                closeOnConfirm: false
            },
            function () {
                location.href = 'delquote.php?quoteid=' + id

            });

    }

    function searchTable(inputVal) {

        var table = $('#quotes-table');

        table.find('tr').each(function (index, row) {
            var allCells = $(row).find('td');
            if (allCells.length > 0) {
                var found = false;
                allCells.each(function (index, td) {
                    var regExp = new RegExp(inputVal, 'i');
                    if (regExp.test($(td).text())) {
                        found = true;
                        return false;
                    }
                });
                if (found == true) {
                    $(row).show();
                    $('#spinner').hide()
                } else {
                    $(row).hide();
                    $('#hide').toggle()
                }
            }
        });

    }

    function decodeVIN() {

        vin = $('#vin').val()
        p = false

        if (vin.length == 17) {
            p = true
        }

        if (p == true) {

            $('#spinner').show()
            $('#decoding').show()
            $.ajax({
                data: "vin=" + vin + "&carfaxloc=<?= $cfl?>",
                url: "<?= COMPONENTS_PRIVATE ?>/customer/vehicle-vin-decode.php",
                dataType: "json",
                success: function (r) {

                    if (r.status == 'success') {
                        rvin = r.vin
                        yr = r.yr
                        mk = r.mk
                        md = r.md
                        tr = r.tr
                        bd = r.bd
                        en = r.en

                        $('#year').val(yr)
                        $('#make').val(mk)
                        $('#model').val(md + " " + tr + " " + bd)
                        $('#spinner').hide()
                        $('#decoding').fadeOut('slow')
                    } else {
                        $('#spinner').hide()
                        $('#decoding').fadeOut('slow')
                        swal("The VIN was not able to be decoded.  Please verify the VIN number")
                    }
                }
            });
        } else {
            swal("You must enter a 17 digit VIN to decode it")
        }


    }

    function check_customer() {
        if (($("#vid").val() != "" && $("#vid").val() != 0) && ($("#cid").val() != "" && $("#cid").val() != 0)) {
            var ds = $('#newquoteform').serialize() + "&t=check_customer";
            $.ajax({
                data: ds,
                url: "vehicle_actions.php",
                type: "post",
                dataType: "json",
                success: function (r) {
                    console.log("check cust response " + r);
                    if (r.hasOwnProperty('response') && r.response == "success") {
                        //customer information hasn't changed, continue with saving.
                        check_vehicle();
                    } else {
                        // customer information has been changed
                        var customer = "";
                        if (r.hasOwnProperty('data')) {
                            customer = r.data.customer;
                            console.log(r);
                        }
                        console.log(customer);
                        $("#ecust").html(customer)
                        $("#custconfirm").modal("show");
                    }
                }
            });
        } else {
            createQuote();
        }
    }

    function update_customer() {
        var ds = $('#newquoteform').serialize() + "&t=update_customer";
        $.ajax({
            data: ds,
            url: "vehicle_actions.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    //vehicle information hasn't changed, continue with saving.
                    check_vehicle();
                    console.log("customer inforamtion updated");
                }
            }
        });
    }

    function check_vehicle() {
        if (($("#vid").val() != "" && $("#vid").val() != 0) && ($("#cid").val() != "" && $("#cid").val() != 0)) {

            var ds = $('#newquoteform').serialize() + "&t=check_vehicle";
            $.ajax({
                data: ds,
                url: "vehicle_actions.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        //vehicle information hasn't changed, continue with saving.
                        createQuote();
                    } else {
                        // vehicle information has been changed
                        $("#vehconfirm").modal("show");
                    }
                }
            });
        } else {
            createQuote();
        }
    }

    function update_vehicle() {
        var ds = $('#newquoteform').serialize() + "&t=update_vehicle";
        $.ajax({
            data: ds,
            url: "vehicle_actions.php",
            type: "post",
            success: function (r) {
                if (r == "success") {
                    //vehicle information hasn't changed, continue with saving.
                    createQuote();
                    console.log("vehicle inforamtion updated");
                }
            }
        });
    }

    function create_vehicle() {
        var ds = $('#newquoteform').serialize() + "&t=create_vehicle";
        $.ajax({
            data: ds,
            url: "vehicle_actions.php",
            type: "post",
            success: function (r) {
                if (r.length > 0) {
                    console.log("vehicle id : " + r + " new vehicle created");
                    $("#vid").val(r);
                    createQuote();
                } else {
                    console.log("ERROR : " + r);
                }
            }
        });
    }

    function createQuote() {
        $('.btn-md').attr('disabled', 'disabled')

        formdata = $('#newquoteform').serialize()

        $.ajax({
            data: formdata + "&t=createquote",
            url: "quoteactions.php",
            type: "post",
            success: function (r) {

                if ($.isNumeric(r))
                    document.location = 'quote.php?quoteid=' + r
                else {
                    swal(r)
                    $('.btn-md').attr('disabled', false)
                }

            }
        })
    }

    function plateLookup() {
        $('.btn-md').attr('disabled', 'disabled')

        var license = $('#platelicense').val();
        var state = $('#platestate').val();
        ds = "vin=&lic=" + license + "&lst=" + state + "&shopid=<?php echo $shopid; ?>"
        $.ajax({
            data: ds,
            url: "<?= INTEGRATIONS ?>/carfax/carfax-quickvin.php",
            success: function (r) {
                if (r.indexOf("success") >= 0) {
                    rar = r.split("|")
                    $('#year').val(rar[1])
                    $('#make').val(rar[2])
                    $('#model').val(rar[3])
                    $('#vin').val(rar[8])
                    $('#plateModal').modal('hide')
                    $('.btn-md').attr('disabled', false)
                    $('#platelicense').val('')
                    $('#platestate').val('<?= $state?>')
                } else {
                    swal("No Results found");
                    $('.btn-md').attr('disabled', false)
                }
            }
        })

    };


    $(document).ready(function () {
        var isChecked = localStorage.getItem('toggleConvertedChecked');
        if (isChecked === null) {
            isChecked = true;
            localStorage.setItem('toggleConvertedChecked', isChecked);
        } else {
            isChecked = isChecked === 'true';
        }
        $('#showconverted').prop('checked', isChecked);
        $('.converted').toggle(isChecked);
    });

    function toggleConverted() {
        var isChecked = $('#showconverted').is(':checked');
        $('.converted').toggle(isChecked);
        localStorage.setItem('toggleConvertedChecked', isChecked);
    }


    $(document).ready(function () {

        $('#spinner').hide()
        $('#srch').keyup(function () {
            $('#spinner').show()
            searchTable($(this).val());
        });

        $('#newquotemodal').on('hide.bs.modal', function () {

            $('.sbp-form-control').each(function () {
                $(this).val('')
            });
            $('#cid').val('')
            $('#vid').val('')
            $('#writer').val('none')
        })

        $('#customer').autocomplete({
            source: 'customerdata.php', minLength: 2,
            select: function (event, ui) {

                tar = ui.item.orival.split("~")

                $('#customer').val($.trim(tar[0]) + ", " + $.trim(tar[1]))
                $('#address').val($.trim(tar[2]))
                $('#city').val($.trim(tar[3]))
                $('#state').val($.trim(tar[4]))
                $('#zip').val($.trim(tar[5]))
                $('#phone').val($.trim(tar[6]))
                $('#email').val($.trim(tar[7]))
                $('#year').val($.trim(tar[8]))
                $('#make').val($.trim(tar[9]))
                $('#model').val($.trim(tar[10]))
                $('#vin').val($.trim(tar[11]))
                $('#color').val($.trim(tar[12]))
                $('#cid').val($.trim(tar[13]))
                $('#vid').val($.trim(tar[14]))
                return false
            }
        })

    })

</script>
<img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>
</html>
