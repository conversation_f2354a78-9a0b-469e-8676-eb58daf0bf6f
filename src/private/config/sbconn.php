<?php

$sn = $_SERVER['SERVER_NAME'];

if (!defined("NO_LOGIN_REQUIRED")) 
{
	if (session_status() === PHP_SESSION_NONE) {
    session_start();
    }
    
    if (defined("CP_LOGIN_REQUIRED")) 
    {
	    if (!isset($_COOKIE['username']) || !isset($_COOKIE['loggedin']) || $_COOKIE['loggedin']!='yes')
	    {
			echo "<script>parent.location.href='https://".$_SERVER['SERVER_NAME']."/control_panel/login.php';</script>";
			exit;
		}
	}

	else
	{
		if (!isset($_SESSION['mode']) || !isset($_SESSION['shopid']) || !isset($_SESSION['shopname']) || !isset($_SESSION['usr']) || !isset($_SESSION['empid']))
		{
		 echo "<script>parent.location.href='https://".$_SERVER['SERVER_NAME']."/logoff.php';</script>";
		 exit;
		}

		if ($_COOKIE['mode'] == "tech" && !preg_match('/inspection|integrations/i', $_SERVER['REQUEST_URI'])) {
		    header("Location: https://tech.shopbosspro.com/new/wip.php");
		    exit;
		}
	}
}


// Functions



function redirect_to ($new_location) {
	 header("Location: " . $new_location);
	 exit;
 }

function asDollars($value) {
 return '$' . number_format(floatval($value), 2);
}

function inlineleft($string, $count){
    return substr($string, 0, $count);
}

function dateDifference($date_1 , $date_2 , $differenceFormat = '%a' )
{
    $datetime1 = date_create($date_1);
    $datetime2 = date_create($date_2);

    $interval = date_diff($datetime1, $datetime2);

    return $interval->format($differenceFormat);
}

$dbsrc = "shopboss";

if (!empty($_GET['dbsrc'])) {
	if ($_GET['dbsrc'] == 'ent'){
		$dbsrc = "sbpent";
	}
}

require_once("security.php");
require_once("permissions.php");

function recordAudit($cat,$ev){

	if (strtolower($_COOKIE['shopid']) != "demo"){
		$dbsrc = "shopboss";

		if (!empty($_GET['dbsrc'])) {
			if ($_GET['dbsrc'] == 'ent'){
				$dbsrc = "sbpent";
			}
		}

		if((strpos($_SERVER['SERVER_NAME'],'localhost') !== false)){
		$servername = "localhost";
	} else {
		$servername = HOST;
	}

		$username = USER;
		$password = PASS;
		$dbname = $dbsrc;
		$port = "3307";
		$timeout = 30;

		$conn = mysqli_init();
		if (!$conn) {
		    die('mysqli_init failed');
		}

		if (!$conn->options(MYSQLI_INIT_COMMAND, 'SET AUTOCOMMIT = 0')) {
		    die('Setting MYSQLI_INIT_COMMAND failed');
		}

		if (!$conn->options(MYSQLI_OPT_CONNECT_TIMEOUT, $timeout)) {
		    die('Setting MYSQLI_OPT_CONNECT_TIMEOUT failed');
		}

		if (!$conn->options(MYSQLI_OPT_CONNECT_TIMEOUT, 5)) {
		    die('Setting MYSQLI_OPT_CONNECT_TIMEOUT failed');
		}

		if (!$conn->real_connect($servername, $username, $password, $dbname, $port)) {
		    /*die('Connect Error (' . mysqli_connect_errno() . ') '
		            . mysqli_connect_error());*/
		}

		$shopid = $_COOKIE['shopid'];
		$dts = localTimeStamp($shopid);
		$usr = $_COOKIE['usr'];
		$stmt = "insert into `audit` (shopid,`category`,`event`,`useraccount`,`eventdatetime`) values (?,?,?,?,?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sssss",$shopid,$cat,$ev,$usr,$dts);
			if ($query->execute()){
				$conn->commit();
				//echo "success";
			}else{
				echo $conn->errno;
			}
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$catlist = "Customer Approval|EMail Sent|Follow Up Email Sent|Inspection Sent|Invoice/Estimate Sent|Remote E-Signature|Text Message Sent";
		if (strpos($catlist,$cat) > 0 && strpos($ev,"RO#") > 0){
			//get the RO number from the ev
			$catar = explode("#",$ev);
			$ronum = trim(end($catar));
			if (is_numeric($ronum)){
				$stmt = "insert into repairordercommhistory (shopid,roid,`datetime`,`comm`,`by`) values (?,?,?,?,?)";
				if ($query = $conn->prepare($stmt)){
					$query->bind_param("sisss",$shopid,$ronum,$dts,$ev,$usr);
					if ($query->execute()){
						$conn->commit();
					}else{
						echo $conn->errno;
					}
				}else{
					echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}

			}
		}
	}

}

function sbpround($initval,$p=2){

	if (strpos($initval,".") > 0){
		$initarray = explode(".",$initval);
		$decimals = $initarray[1];
		$len = strlen($decimals);
		if ($len > 3){
			$decimals = substr($decimals,0,3);
			$newval = $initarray[0].".".$decimals;
			$len = 3;
		}else{
			$newval = $initval;
		}

		switch ($len){

			case 3:
				$fr =  round($newval,2);
				return $fr;
				break;
			case 2:
				return $newval;
				break;
			case 1:
				return $newval."0";
				break;
			case 0:
				return $newval.".00";
		}
	}else{
		return $initval.".00";
	}
}

function generateRandomStr($length = 10) {
    return substr(str_shuffle("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, $length);
}


function formatPhone($p){
	if (strlen($p) == 10){

		$ac = substr($p,0,3);
		$pf = substr($p,3,3);
		$lf = substr($p,6,4);
		return "($ac) $pf-$lf";

	}elseif (strlen($p) > 10){
		$ac = substr($p,0,3);
		$pf = substr($p,3,3);
		$lf = substr($p,6,4);
		$ex = substr($p,10,strlen($p)-10);
		return "($ac) $pf-$lf x $ex";
	}else{
		return $p;
	}
}

function cleanPhone($p){

	$newphone = preg_replace('/\D/', '', $p);
	return $newphone;

}


function getCurrentLocalTime($tz,$type="time"){

	switch ($tz) {
		case "est":
			$date = new DateTime("now", new DateTimeZone('America/New_York'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
		case "cst":
			$date = new DateTime("now", new DateTimeZone('America/Chicago'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
		case "mst":
			$date = new DateTime("now", new DateTimeZone('America/Denver'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
		case "azst":
			$date = new DateTime("now", new DateTimeZone('America/Phoenix'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
		case "pst":
			$date = new DateTime("now", new DateTimeZone('America/Los_Angeles'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
		case "akst":
			$date = new DateTime("now", new DateTimeZone('America/Anchorage'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
		case "hst":
			$date = new DateTime("now", new DateTimeZone('Pacific/Honolulu'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
		case "ast":
			$date = new DateTime("now", new DateTimeZone('America/Halifax'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
		case "astnd":
			$date = new DateTime("now", new DateTimeZone('America/Blanc-Sablon'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
		case "eat":
			$date = new DateTime("now", new DateTimeZone('Africa/Addis_Ababa'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
		case "chst":
			$date = new DateTime("now", new DateTimeZone('Pacific/Guam'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
		case "aest":
			$date = new DateTime("now", new DateTimeZone('Australia/Brisbane'));
			if($type=='date')
			return $date->format('Y-m-d');
		    else
			return $date->format('H:i:s');
			break;
	}


}

function left($str, $length) {
     return substr($str, 0, $length);
}

function right($str, $length) {
     return substr($str, -$length);
}

function localTimeStamp($tshopid){
	$timedbsrc = "shopboss";

	if((strpos($_SERVER['SERVER_NAME'],'localhost') !== false)){
	$timeservername = "localhost";
} else {
	$timeservername = HOST;
}

	$timeusername = USER;
	$timepassword = PASS;
	$timedbname = $timedbsrc;
	$timeport = "3307";
	$timetimeout = 30;

	$timeconn = mysqli_init();
	if (!$timeconn) {
	    die('mysqli_init failed');
	}

	if (!$timeconn->options(MYSQLI_INIT_COMMAND, 'SET AUTOCOMMIT = 0')) {
	    die('Setting MYSQLI_INIT_COMMAND failed');
	}

	if (!$timeconn->options(MYSQLI_OPT_CONNECT_TIMEOUT, $timetimeout)) {
	    die('Setting MYSQLI_OPT_CONNECT_TIMEOUT failed');
	}

	if (!$timeconn->options(MYSQLI_OPT_CONNECT_TIMEOUT, 5)) {
	    die('Setting MYSQLI_OPT_CONNECT_TIMEOUT failed');
	}

	if (!$timeconn->real_connect($timeservername, $timeusername, $timepassword, $timedbname, $timeport)) {
	    /*die('Connect Error (' . mysqli_connect_errno() . ') '
	            . mysqli_connect_error());*/
	}

	$timestmt = "select timezone from company where shopid = ?";
	if ($timequery = $timeconn->prepare($timestmt)){

		$timequery->bind_param("s",$tshopid);
	    $timequery->execute();
	    $timequery->store_result();
	    $timenum_roid_rows = $timequery->num_rows;
	    if ($timenum_roid_rows > 0){
	    	$timequery->bind_result($ttz);
	    	$timequery->fetch();
	    }else{
	    	$phptz = date_default_timezone_get();
	    }
	    $timequery->close();
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	switch($ttz){
		case "est":
			$phptz = 'America/New_York';
			break;
		case "mst":
			$phptz = 'America/Denver';
			break;
		case "pst":
			$phptz = 'America/Los_Angeles';
			break;
		case "cst":
			$phptz = 'America/Chicago';
			break;
		case "azst":
			$phptz = 'America/Phoenix';
			break;
		case "hst":
			$phptz = 'Pacific/Honolulu';
			break;
		case "akst":
			$phptz = 'America/Anchorage';
			break;
		case "gst":
			$phptz = 'Pacific/Guam';
			break;
		case "ast":
			$phptz = 'America/Halifax';
			break;
		case "astnd":
			$phptz = 'America/Blanc-Sablon';
			break;
		case "eat":
			$phptz = 'Africa/Addis_Ababa';
			break;
		case "chst":
			$phptz = 'Pacific/Guam';
			break;
		case "aest":
			$phptz = 'Australia/Brisbane';
			break;
		default:
			$phptz = date_default_timezone_get();

	}

	$date = new DateTime("now", new DateTimeZone($phptz) );
	return $date->format('Y-m-d H:i:s');
	mysqli.close();

}

function writeConn(){

	$dbsrc = "shopboss";

	if((strpos($_SERVER['SERVER_NAME'],'localhost') !== false)){
	$servername = "localhost";
} else {
	$servername = HOST;
}
	$username = USER;
	$password = PASS;
	$dbname = $dbsrc;
	$port = "3308";
	$timeout = 30;

	$writeconn = mysqli_init();
	if (!$writeconn) {
	    die('mysqli_init failed');
	}

	if (!$writeconn->options(MYSQLI_INIT_COMMAND, 'SET AUTOCOMMIT = 0')) {
	    die('Setting MYSQLI_INIT_COMMAND failed');
	}

	if (!$writeconn->options(MYSQLI_OPT_CONNECT_TIMEOUT, $timeout)) {
	    die('Setting MYSQLI_OPT_CONNECT_TIMEOUT failed');
	}

	if (!$writeconn->options(MYSQLI_OPT_CONNECT_TIMEOUT, 5)) {
	    die('Setting MYSQLI_OPT_CONNECT_TIMEOUT failed');
	}

	if (!$writeconn->real_connect($servername, $username, $password, $dbname, $port)) {
	    /*die('Connect Error (' . mysqli_connect_errno() . ') '
	            . mysqli_connect_error());*/
	}

	return $writeconn;




	/*

	usage for an update or insert query

	$wconn = writeConn();
	$wstmt = "insert into company (field1,field2,field3...) values ('value1,'value2','value3')";
	if ($wquery = $wconn->prepare($wstmt)){
		$wquery->execute();
		$wconn->commit();
		$wquery->close();
	}
	*/

}

function sendEmailMandrill($to, $subj, $msg, $shopname,$replyto,$attachment=NULL,$from="<EMAIL>")
{
	$attach=array();

	if(is_array($attachment) && !empty($attachment['string']))
	{
		$attach[]=array(
                'content' => base64_encode($attachment['string']),
                'name' => $attachment['name']
            );
	}
	elseif(!empty($attachment))
	{
		$exparr = explode(',', $attachment);
		foreach($exparr as $a)
		{
		$file = file_get_contents($a);
		$attach[]=array(
                'content' => base64_encode($file),
                'name' => basename($a)
        );
	    }
	};

	$toarr = array();
	$sendlist = explode(";",$to);
	$ct = 1;
	foreach ($sendlist as $sendaddr)
	{
	 if($ct++==1)
	 $toarr[]=array('email'=>$sendaddr,'type'=>'to');
	 else
	 $toarr[]=array('email'=>$sendaddr,'type'=>'cc');
    }

	if (strpos($replyto,";") > 0)
	{
		$replyarr = explode(';',$replyto);
		$replyto = $replyarr[0];
	}

	if(isset($_COOKIE['matco']) && $_COOKIE['matco']=='yes')$from = "<EMAIL>";
	elseif(isset($_COOKIE['protractor']) && $_COOKIE['protractor']=='yes')$from = "<EMAIL>";

	try
	{
	 $mandrill = new Mandrill('md-**********************');
     $message = array(
          'html' => $msg,
          'subject' => $subj,
          'from_email' => $from,
          'from_name' => $shopname,
          'to' => $toarr,
          'headers' => array('Reply-To' => $replyto),
          'preserve_recipients' => true,
		  'inline_css' => true,
          'important' => false,
        'track_opens' => true,
        'track_clicks' => true,
        "attachments" => $attach
    );
    $async = false;
    $ip_pool = 'Main Pool';
    $result = $mandrill->messages->send($message, $async, $ip_pool);

    return $result[0]['status'];

    } catch(Mandrill_Error $e) {}

}

function sendEmailMandrillTemplate($to,$shopname,$replyto,$TemplateName,$params=NULL,$attachment=NULL,$from="<EMAIL>")
{
	$attach=array();

	if(is_array($attachment) && !empty($attachment['string']))
	{
		$attach[]=array(
                'content' => base64_encode($attachment['string']),
                'name' => $attachment['name']
            );
	}
	elseif(!empty($attachment))
	{
		$exparr = explode(',', $attachment);
		foreach($exparr as $a)
		{
		$file = file_get_contents($a);
		$attach[]=array(
                'content' => base64_encode($file),
                'name' => basename($a)
        );
	    }
	};

	$toarr = array();
	$sendlist = explode(";",$to);
	$ct = 1;
	foreach ($sendlist as $sendaddr)
	{
	 if($ct++==1)
	 $toarr[]=array('email'=>$sendaddr,'type'=>'to');
	 else
	 $toarr[]=array('email'=>$sendaddr,'type'=>'cc');
    }

	if (strpos($replyto,";") > 0)
	{
		$replyarr = explode(';',$replyto);
		$replyto = $replyarr[0];
	}

	$MergeVar = array();
	foreach( $params  as $k=>$v){
		$MergeVar[] = array(
			'name' => $k,
			'content' => $v
		);
	}

	if(isset($_COOKIE['matco']) && $_COOKIE['matco']=='yes')$from = "<EMAIL>";
	elseif(isset($_COOKIE['protractor']) && $_COOKIE['protractor']=='yes')$from = "<EMAIL>";

	if($_COOKIE['shopid'] == '18930')die();

	try
	{
	 $mandrill = new Mandrill('md-**********************');
     $message = array(
          'from_email' => $from,
          'from_name' => $shopname,
          'to' => $toarr,
          'headers' => array('Reply-To' => $replyto),
          'preserve_recipients' => true,
		  'inline_css' => true,
          'important' => false,
          'track_opens' => true,
          'track_clicks' => true,
          "attachments" => $attach,
          'merge' => true,
		  'global_merge_vars' => $MergeVar
		);

    $async = false;
    $ip_pool = 'Main Pool';

    $result = $mandrill->messages->sendTemplate($TemplateName, '', $message, $async, $ip_pool);

    return $result[0]['status'];

    } catch(Mandrill_Error $e) {}

}



function makeCurlRequest($url, $headers = [], $method = 'POST', $data = null, $options = [] ) {

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $url);

    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, strtoupper($method));

    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, is_array($data) ? http_build_query($data) : $data);
    }

    // Check if User-Agent is already set
    $userAgentSet = false;

    if(!empty($headers))
    {
	    foreach ($headers as $header) {
	        if (stripos($header, 'User-Agent:') === 0) {
	            $userAgentSet = true;
	            break;
	        }
	    }
	}

    // If User-Agent is not set, add it
    if (!$userAgentSet) {
        $userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Mozilla/5.0';
        $headers[] = 'User-Agent: ' . $userAgent;
    }

    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    if (!empty($options))
    {
        foreach ($options as $key => $value) {
        curl_setopt($ch, $key, $value);
     }
    }

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);

    $error = null;
    $http_code = null;

    if (curl_errno($ch)) {
        $error = curl_error($ch);
    } else {
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    }

    curl_close($ch);

    return [
        'response' => $response,
        'http_code' => $http_code,
        'error' => $error
    ];

}



// Accounting/Settings/Reports permissions check for any kind of access.
if (!defined("NO_LOGIN_REQUIRED") && !defined("CP_LOGIN_REQUIRED")) 
{
	if(!empty($_COOKIE['empid']) && !empty($_COOKIE['shopid']) && $_COOKIE['empid']!='Admin')
	{
	 $securitystmt = "select id from employees where shopid = ? and id = ? and active = 'yes'";
	 if ($securityquery = $conn->prepare($securitystmt)) {
	    $securityquery->bind_param("si", $_COOKIE['shopid'], $_COOKIE['empid']);
	    $securityquery->execute();
	    $securityquery->store_result();
	    $numrows = $securityquery->num_rows;
	    if ($numrows == 0) {
	        header("Location:" . COMPONENTS_PUBLIC . "/login/logoff.php");
	        exit;
	    }
	 }

	 $uri=explode('/',$_SERVER['REQUEST_URI']);
	 $urivar='no';
	 if(isset($uri[3]) && in_array($uri[3],array('accounting','reports','customreports','settings')))
	 {
	   if($uri[3]=='accounting')
	   $stmt = "select upper(accounting) from employees where id = ? and shopid = ?";
	   elseif($uri[3]=='settings')
	   $stmt = "select upper(CompanyAccess) from employees where id = ? and shopid = ?";
	   else
	   $stmt = "select upper(ReportAccess) from employees where id = ? and shopid = ?";

	   if ($query = $conn->prepare($stmt))
	   {
	    $query->bind_param("is",$_COOKIE['empid'],$_COOKIE['shopid']);
	    $query->execute();
	    $query->bind_result($urivar);
	    $query->fetch();
	    $query->close();
	   }
	   if($urivar!='YES'){echo("You do not have access to this function. <a href='https://www.shopbosspro.com/wip/wip.php'>Click here</a> to return to the WIP list");die();}

	 }

	}
}


if (!defined("NO_LOGIN_REQUIRED") && !defined("CP_LOGIN_REQUIRED")) 
require_once REDISCONN;
?>
