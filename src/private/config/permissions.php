<?php
    $shopid = $_COOKIE['shopid'];
    $shopIsReadOnly = $_COOKIE['readonly'] == 'yes';

    $forbiddenPages = [
        'ro.php',
        'status.php'
    ];

    $uri = $_SERVER['REQUEST_URI'];

    $currentPage = basename(parse_url($uri, PHP_URL_PATH));

    $forbiddenPages = [
        "/settings/",
        "/settings-v2/",
    ];

    if ($shopIsReadOnly && in_array($currentPage, $forbiddenPages)) {
        header("HTTP/1.0 404 Not Found");
        exit();
    }
?>