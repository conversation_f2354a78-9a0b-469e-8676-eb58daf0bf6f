<?php
require_once CONN;

$shopid = $_COOKIE['shopid'];
$tempstmt = "select readonly from company where shopid = '$shopid'";
if ($tempquery = $conn->prepare($tempstmt)) {
    $tempquery->execute();
    $tempquery->bind_result($readonly);
    $tempquery->fetch();
    $tempquery->close();
}

$shopIsReadOnly = $_COOKIE['readonly'] == 'yes';
?>
<ul style="padding:5px;background-color:#2C343F" class="nav-main">
    <li id="shopstats">
    </li>
    <li>
        <a href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php"><i class="fa fa-wrench"></i><span class="sidebar-mini-hide">Work In Process</span></a>
    </li>
    <?php
    if (!$shopIsReadOnly){
    ?>

    <li>
        <a class="nav-submenu" data-toggle="nav-submenu" href="#"><i class="fa fa-file-text-o"></i><span
                    class="sidebar-mini-hide">Create RO</span></a>
        <ul>
            <li>
                <a onclick="location.href='<?= COMPONENTS_PRIVATE ?>/customer/customer-search.php'" href="#"><i class="fa fa-search"></i>
                    Search</a>
            </li>
            <li>
                <a onclick="$('#scanmodal').modal('show')" href="#"><i class="fa fa-barcode"></i> Scan VIN</a>
            </li>
        </ul>
    <li>
        <?php
        }
        ?>
    <li>
        <a href="<?= COMPONENTS_PRIVATE ?>/customer/customer-find.php"><i class="fa fa-users"></i><span class="sidebar-mini-hide">Customers</span></a>
    </li>
    <li>
        <a class="nav-submenu" data-toggle="nav-submenu" href="#"><i class="fa fa-calendar-check-o"></i><span
                    class="sidebar-mini-hide">Schedule</span></a>
        <ul>
            <li>
                <a href="<?= COMPONENTS_PRIVATE ?>/calendar/calendar.php"><i class="fa fa-calendar-o"></i>Customer</a>
            </li>
            <li>
                <a href="<?= COMPONENTS_PRIVATE ?>/empschedule/calendar.php"><i class="fa fa-calendar"></i>Employee</a>
            </li>
        </ul>
    </li>
    <li>
        <a href="<?= COMPONENTS_PRIVATE ?>/newquotes/quotes.php"><i class="fa fa-calculator"></i><span class="sidebar-mini-hide">Quotes</span></a>
    </li>
    <li>
        <a href="<?= COMPONENTS_PRIVATE ?>/dispatch/dispatch.php"><i class="fa fa-share-square"></i><span
                    class="sidebar-mini-hide">Dispatch</span></a>
    </li>
    <li>
        <a href="<?= COMPONENTS_PRIVATE ?>/ro/findro.php"><i class="fa fa-search"></i><span class="sidebar-mini-hide">Find an RO</span></a>
    </li>
    <li>
        <a href="<?= COMPONENTS_PRIVATE ?>/history/history.php"><i class="fa fa-history"></i><span class="sidebar-mini-hide">History</span></a>
    </li>
    <li>
        <a href="<?= COMPONENTS_PRIVATE ?>/follow/main.php"><i class="fa fa-user"></i><span class="sidebar-mini-hide">Follow Up</span></a>
    </li>
    <li>
        <a href="<?= COMPONENTS_PRIVATE ?>/po/po.php"><i class="fa fa-sign-in"></i><span class="sidebar-mini-hide">Receive PO</span></a>
    </li>
    <li>
        <a href="<?= COMPONENTS_PRIVATE ?>/inventory/inventory.php"><i class="fa fa-folder-open"></i><span
                    class="sidebar-mini-hide">Inventory</span></a>
    </li>

    <li>
        <a href="<?= COMPONENTS_PRIVATE ?>/reports/reports.php"><i class="fa fa-clipboard"></i><span class="sidebar-mini-hide">Reports</span></a>
    </li>
    <li>
        <a href="<?= COMPONENTS_PRIVATE ?>/accounting/default.php"><i class="fa fa-calculator"></i><span
                    class="sidebar-mini-hide">Accounting</span></a>
    </li>

    <?php if ( !$shopIsReadOnly ): ?>
        <li>
            <a href="<?= COMPONENTS_PRIVATE ?>/settings/main.php"><i class="fa fa-cog"></i><span class="sidebar-mini-hide">Settings</span></a>
        </li>
        <li>
            <a href="<?php echo INTEGRATIONS; ?>/happyfox/jwtlogin.php" target="_blank"><i class="fa fa-life-ring"></i><span
                        class="sidebar-mini-hide">Support<br>(Opens in New Window)</span></a>
        </li>
        <li>
            <a href="<?= PAGES ?>videos.php"><i class="fa fa-youtube"></i><span class="sidebar-mini-hide">Videos</span></a>
        </li>
    <?php endif; ?>
</ul>
